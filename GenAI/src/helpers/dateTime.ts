import moment from 'moment'

const DATE_TIME_FORMAT = {
  DDMMYYYY: 'DD/MM/YYYY',
  HMMADDMMM: 'H:mmA, DD MMM',
  DDMMMYY: 'DD MMM, YYYY',
  MMMDD: 'MMM DD',
  MMMDDYYYY: 'MMM DD, YYYY',
}

const convertUTCToLocalTime = (
  utcTime: string,
  format = DATE_TIME_FORMAT.HMMADDMMM,
  local = true
) => {
  if (!moment.utc(utcTime).isValid()) return '--'
  if (local) {
    return moment.utc(utcTime).local().format(format)
  }
  return moment.utc(utcTime).format(format)
}

function convertTimeFromNow(utcTime: string, hasAgoText = false) {
  // Convert timestamp to milliseconds
  const date = moment.utc(utcTime)

  const currentDate = moment().utc()

  const date_diff = currentDate.diff(date) / 1000 // in  second

  if (date_diff <= 60) return 'just now'
  if (date_diff <= 3600) {
    const date_diff_in_minute = parseInt((date_diff / 60) as any)

    if (date_diff_in_minute === 1)
      return date_diff_in_minute + ' minute' + (hasAgoText ? ' ago' : '')
    return date_diff_in_minute + ' minutes' + (hasAgoText ? ' ago' : '')
  }
  if (date_diff <= 86400) {
    const date_diff_in_hour = parseInt((date_diff / 3600) as any)

    if (date_diff_in_hour === 1)
      return date_diff_in_hour + ' hour' + (hasAgoText ? ' ago' : '')
    return date_diff_in_hour + ' hours' + (hasAgoText ? ' ago' : '')
  }
  if (date_diff <= 604800) {
    const date_diff_in_day = parseInt((date_diff / 86400) as any)

    if (date_diff_in_day === 1) return '1 day' + (hasAgoText ? ' ago' : '')
    return date_diff_in_day + ' days' + (hasAgoText ? ' ago' : '')
  }
  if (date_diff <= 31536000) {
    const date_diff_in_week = parseInt((date_diff / 604800) as any)

    if (date_diff_in_week === 1) return '1 week' + (hasAgoText ? ' ago' : '')

    return convertUTCToLocalTime(utcTime, DATE_TIME_FORMAT.MMMDD)
  }

  return convertUTCToLocalTime(utcTime, DATE_TIME_FORMAT.MMMDDYYYY)
}

export { DATE_TIME_FORMAT, convertTimeFromNow, convertUTCToLocalTime }
