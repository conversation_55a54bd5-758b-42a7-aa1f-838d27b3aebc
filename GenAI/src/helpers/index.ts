import { GEN_AI_PATH } from '@/constants'
import clsx, { ClassValue } from 'clsx'
import { isEmpty } from 'lodash'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

const generateBackgroundColor = (name: string) => {
  const colors = [
    '#FDE8E8',
    '#FCECE8',
    '#FFF7E8',
    '#F3FCE8',
    '#E8FCE8',
    '#E8FCF7',
    '#E8F3FC',
    '#E8E8FC',
    '#F3E8FC',
    '#FCE8FC',
    '#FCE8F3',
    '#FCE8E8',
  ]

  const index = name
    .split('')
    .map((c) => c.charCodeAt(0))
    .reduce((a, b) => a + b, 0)

  return colors[index % colors.length]
}

const getAccessTokenLocalStorage = () => localStorage.getItem('access_token')

const getState = (obj: any, keys: string[]) => {
  let temp = obj
  for (let idx = 0; idx < keys.length; idx++) {
    if (isEmpty(temp)) return null
    const key = keys[idx]
    temp = temp[key]
  }
  return temp
}

const getUrlImage = (url?: string | null) => {
  if (!url) return ''
  return GEN_AI_PATH + url
}

const getWsProtocol = (url: string) => {
  const p = url.startsWith('https') ? 'wss' : 'ws'

  return `${p}://${url.split('//')[1]}`
}

const getPlaygroundUrl = (workerId: string, token: string, sessionId: string) =>
  `${getWsProtocol(GEN_AI_PATH)}/api/v1/workers/${workerId}/${sessionId}/playground?token=Bearer ${token}&timestamp=${Date.now()}&from=workers`

export const getWsKbFileUrl = (token: string) =>
  `${getWsProtocol(GEN_AI_PATH)}/api/v1/knowledge-base-file-websocket/?token=Bearer ${token}`

export const getWsWorkflowUrl = (
  token: string,
  workflow_id: string,
  sessionId: string
) =>
  `${getWsProtocol(GEN_AI_PATH)}/api/v1/workflow/${workflow_id}/${sessionId}/playground?token=Bearer ${token}&timestamp=${Date.now()}&from=playground`

export const getSessionIdFromWsUrl = (url?: string) => {
  if (!url) return
  const pathMatch = url.match(/\/api\/v1\/workflow\/[^/]+\/([^/]+)\/playground/)

  if (pathMatch && pathMatch[1]) {
    return pathMatch[1]
  }

  return
}

export const getSessionIdFromWsUrlHomeChat = (url?: string) => {
  if (!url) return
  const pathMatch = url.match(/\/api\/v1\/workflow\/[^/]+\/([^/]+)\/home_chat/)

  if (pathMatch && pathMatch[1]) {
    return pathMatch[1]
  }

  return
}

export const getWSCiteMindUrl = (workflowId: string, sessionId: string) =>
  `${getWsProtocol(GEN_AI_PATH)}/api/v1/workflow/${workflowId}/${sessionId}/citation`

export const getWsExternalKbUrl = (token: string) => {
  if (!token) return
  return `${getWsProtocol(GEN_AI_PATH)}/api/v1/external-knowledge-base/?token=Bearer ${token}`
}

const copyToClipboard = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    return Promise.resolve(true)
  } catch (error) {
    return Promise.reject(error)
  }
}

const isDevelopment = process.env.NODE_ENV === 'development'

const validateDomainByRegex = (domain?: string) => {
  if (!domain) return false

  return /(?!.*\.\.)(?!-)([A-Za-z0-9]{1,63}(?<!-)\.)+[A-Za-z]{2,6}$/.test(
    domain
  )
}

interface FormatBytesProps {
  bytes: number
  decimals?: number
  standard?: number
  maximum?: number
  convertWithSize?: boolean
  unit?:
    | string
    | 'auto'
    | 'Bytes'
    | 'KB'
    | 'MB'
    | 'GB'
    | 'TB'
    | 'PB'
    | 'EB'
    | 'ZB'
    | 'YB'
}
const formatBytes = ({
  bytes,
  decimals = 3,
  standard = 2,
  maximum = 8,
  convertWithSize = true,
  unit = 'auto', // 'auto' | 'Bytes' | 'KB' | 'MB' | 'GB' | 'TB' | 'PB' | 'EB' | 'ZB' | 'YB'
}: FormatBytesProps) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  if (!+bytes) return `0 ${sizes[standard]}`

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals

  let index = 0
  if (unit === 'auto') {
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    index = i > standard ? i : standard
    index = index < maximum ? index : maximum
  } else if (unit) {
    index = sizes.indexOf(unit)
  }

  if (convertWithSize) {
    return `${parseFloat((bytes / Math.pow(k, index)).toFixed(dm))}${sizes[index]}`
  }

  return parseFloat((bytes / Math.pow(k, index)).toFixed(dm))
}

const removeSpaceBetween = (str: string) => str.replace(/\s/g, '')

const addMultipleLineBreak = (content: string = '') => {
  // const codeBlock = content.match(/`{3}([\w]*)\n([\S\s]+?)\n`{3}/gi) // get code blocks
  // To do ignore replace \n to </br> in codeBlock

  // return content?.replace(/(?<=\n\n)(?![*-])\n|↵/gi, '&nbsp;\n ')
  return content.replace(/[\n↵]+/gi, (item) => {
    return item.length > 1 ? item.replace(/\n|↵/gi, '&nbsp;\n ') : item
  })
}

const getOffset = (el: Element, widthPopover = 0, offsetTop = 0) => {
  const rect = el.getBoundingClientRect()

  return {
    x: rect.left + window.scrollX - widthPopover + el.clientWidth,
    y: rect.top + window.scrollY + offsetTop,
  }
}

export const getWsWorkflowUrlHome = (
  token: string,
  workflow_id: string,
  sessionId: string
) =>
  `${getWsProtocol(GEN_AI_PATH)}/api/v1/workflow/${workflow_id}/${sessionId}/home_chat?token=Bearer ${token}&timestamp=${Date.now()}&from=home`

export {
  addMultipleLineBreak,
  copyToClipboard,
  formatBytes,
  generateBackgroundColor,
  getAccessTokenLocalStorage,
  getOffset,
  getPlaygroundUrl,
  getState,
  getUrlImage,
  isDevelopment,
  removeSpaceBetween,
  validateDomainByRegex,
}
