// This file is auto-generated by @hey-api/openapi-ts

import { type Options, createClient, createConfig } from '@hey-api/client-axios'
import {
  type AddPetData,
  type AddPetError,
  type AddPetResponse,
  type CreateUserData,
  type CreateUserError,
  type CreateUserResponse,
  type CreateUsersWithListInputData,
  type CreateUsersWithListInputError,
  type CreateUsersWithListInputResponse,
  type DeleteOrderData,
  type DeletePetData,
  type DeleteUserData,
  type FindPetsByStatusData,
  type FindPetsByStatusError,
  type FindPetsByStatusResponse,
  type FindPetsByTagsData,
  type FindPetsByTagsError,
  type FindPetsByTagsResponse,
  type GetInventoryError,
  type GetInventoryResponse,
  type GetOrderByIdData,
  type GetOrderByIdError,
  type GetOrderByIdResponse,
  GetOrderByIdResponseTransformer,
  type GetPetByIdData,
  type GetPetByIdError,
  type GetPetByIdResponse,
  type GetUserByNameData,
  type GetUserByNameError,
  type GetUserByNameResponse,
  type LoginUserData,
  type LoginUserError,
  type LoginUserResponse,
  type LogoutUserError,
  type LogoutUserResponse,
  type PlaceOrderData,
  type PlaceOrderError,
  type PlaceOrderResponse,
  PlaceOrderResponseTransformer,
  type UpdatePetData,
  type UpdatePetError,
  type UpdatePetResponse,
  type UpdatePetWithFormData,
  type UpdateUserData,
  type UpdateUserError,
  type UpdateUserResponse,
  type UploadFileData,
  type UploadFileError,
  type UploadFileResponse,
} from './types.gen'

export const client = createClient(createConfig())

/**
 * Add a new pet to the store
 * Add a new pet to the store
 */
export const addPet = <ThrowOnError extends boolean = false>(
  options: Options<AddPetData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    AddPetResponse,
    AddPetError,
    ThrowOnError
  >({
    ...options,
    url: '/pet',
  })
}

/**
 * Update an existing pet
 * Update an existing pet by Id
 */
export const updatePet = <ThrowOnError extends boolean = false>(
  options: Options<UpdatePetData, ThrowOnError>
) => {
  return (options?.client ?? client).put<
    UpdatePetResponse,
    UpdatePetError,
    ThrowOnError
  >({
    ...options,
    url: '/pet',
  })
}

/**
 * Finds Pets by status
 * Multiple status values can be provided with comma separated strings
 */
export const findPetsByStatus = <ThrowOnError extends boolean = false>(
  options?: Options<FindPetsByStatusData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    FindPetsByStatusResponse,
    FindPetsByStatusError,
    ThrowOnError
  >({
    ...options,
    url: '/pet/findByStatus',
  })
}

/**
 * Finds Pets by tags
 * Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing.
 */
export const findPetsByTags = <ThrowOnError extends boolean = false>(
  options?: Options<FindPetsByTagsData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    FindPetsByTagsResponse,
    FindPetsByTagsError,
    ThrowOnError
  >({
    ...options,
    url: '/pet/findByTags',
  })
}

/**
 * Find pet by ID
 * Returns a single pet
 */
export const getPetById = <ThrowOnError extends boolean = false>(
  options: Options<GetPetByIdData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetPetByIdResponse,
    GetPetByIdError,
    ThrowOnError
  >({
    ...options,
    url: '/pet/{petId}',
  })
}

/**
 * Updates a pet in the store with form data
 */
export const updatePetWithForm = <ThrowOnError extends boolean = false>(
  options: Options<UpdatePetWithFormData, ThrowOnError>
) => {
  return (options?.client ?? client).post<void, unknown, ThrowOnError>({
    ...options,
    url: '/pet/{petId}',
  })
}

/**
 * Deletes a pet
 */
export const deletePet = <ThrowOnError extends boolean = false>(
  options: Options<DeletePetData, ThrowOnError>
) => {
  return (options?.client ?? client).delete<void, unknown, ThrowOnError>({
    ...options,
    url: '/pet/{petId}',
  })
}

/**
 * uploads an image
 */
export const uploadFile = <ThrowOnError extends boolean = false>(
  options: Options<UploadFileData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    UploadFileResponse,
    UploadFileError,
    ThrowOnError
  >({
    ...options,
    url: '/pet/{petId}/uploadImage',
  })
}

/**
 * Returns pet inventories by status
 * Returns a map of status codes to quantities
 */
export const getInventory = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetInventoryResponse,
    GetInventoryError,
    ThrowOnError
  >({
    ...options,
    url: '/store/inventory',
  })
}

/**
 * Place an order for a pet
 * Place a new order in the store
 */
export const placeOrder = <ThrowOnError extends boolean = false>(
  options?: Options<PlaceOrderData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PlaceOrderResponse,
    PlaceOrderError,
    ThrowOnError
  >({
    ...options,
    url: '/store/order',
    responseTransformer: PlaceOrderResponseTransformer,
  })
}

/**
 * Find purchase order by ID
 * For valid response try integer IDs with value <= 5 or > 10. Other values will generate exceptions.
 */
export const getOrderById = <ThrowOnError extends boolean = false>(
  options: Options<GetOrderByIdData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetOrderByIdResponse,
    GetOrderByIdError,
    ThrowOnError
  >({
    ...options,
    url: '/store/order/{orderId}',
    responseTransformer: GetOrderByIdResponseTransformer,
  })
}

/**
 * Delete purchase order by ID
 * For valid response try integer IDs with value < 1000. Anything above 1000 or nonintegers will generate API errors
 */
export const deleteOrder = <ThrowOnError extends boolean = false>(
  options: Options<DeleteOrderData, ThrowOnError>
) => {
  return (options?.client ?? client).delete<void, unknown, ThrowOnError>({
    ...options,
    url: '/store/order/{orderId}',
  })
}

/**
 * Create user
 * This can only be done by the logged in user.
 */
export const createUser = <ThrowOnError extends boolean = false>(
  options?: Options<CreateUserData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CreateUserResponse,
    CreateUserError,
    ThrowOnError
  >({
    ...options,
    url: '/user',
  })
}

/**
 * Creates list of users with given input array
 * Creates list of users with given input array
 */
export const createUsersWithListInput = <ThrowOnError extends boolean = false>(
  options?: Options<CreateUsersWithListInputData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CreateUsersWithListInputResponse,
    CreateUsersWithListInputError,
    ThrowOnError
  >({
    ...options,
    url: '/user/createWithList',
  })
}

/**
 * Logs user into the system
 */
export const loginUser = <ThrowOnError extends boolean = false>(
  options?: Options<LoginUserData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    LoginUserResponse,
    LoginUserError,
    ThrowOnError
  >({
    ...options,
    url: '/user/login',
  })
}

/**
 * Logs out current logged in user session
 */
export const logoutUser = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    LogoutUserResponse,
    LogoutUserError,
    ThrowOnError
  >({
    ...options,
    url: '/user/logout',
  })
}

/**
 * Get user by user name
 */
export const getUserByName = <ThrowOnError extends boolean = false>(
  options: Options<GetUserByNameData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetUserByNameResponse,
    GetUserByNameError,
    ThrowOnError
  >({
    ...options,
    url: '/user/{username}',
  })
}

/**
 * Update user
 * This can only be done by the logged in user.
 */
export const updateUser = <ThrowOnError extends boolean = false>(
  options: Options<UpdateUserData, ThrowOnError>
) => {
  return (options?.client ?? client).put<
    UpdateUserResponse,
    UpdateUserError,
    ThrowOnError
  >({
    ...options,
    url: '/user/{username}',
  })
}

/**
 * Delete user
 * This can only be done by the logged in user.
 */
export const deleteUser = <ThrowOnError extends boolean = false>(
  options: Options<DeleteUserData, ThrowOnError>
) => {
  return (options?.client ?? client).delete<void, unknown, ThrowOnError>({
    ...options,
    url: '/user/{username}',
  })
}
