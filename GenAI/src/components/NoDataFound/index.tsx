import Text, { ITextProps } from '@/components/Text'
import { memo } from 'react'
import IconNoData from './IconNoData'
import { cn } from '@/helpers'

const NoDataFound = ({
  className,
  icNodata,
  textType = 'heading',
  textVariant = 'semibold',
  subTextType = 'body',
  subTextVariant = 'regular',
  size = 200,
  showSubText = true,
}: {
  className?: string
  icNodata?: any
  textType?: ITextProps['type']
  textVariant?: ITextProps['variant']
  subTextType?: ITextProps['type']
  subTextVariant?: ITextProps['variant']
  size?: number
  showSubText?: boolean
}) => {
  return (
    <div className={cn(className, 'flex flex-col items-center')}>
      {icNodata ? icNodata : <IconNoData size={size} />}

      <Text
        type={textType}
        variant={textVariant}
        className="mt-[8px] text-Primary-Color"
        elementType="div"
      >
        No Data Found
      </Text>
      {showSubText && (
        <Text
          type={subTextType}
          variant={subTextVariant}
          className="mt-[4px] w-[335px] whitespace-pre-wrap text-center text-Secondary-Color"
          elementType="div"
        >
          {
            'Oops, we couldn’t find what you are looking for\nPlease try again and remember to check spelling!'
          }
        </Text>
      )}
    </div>
  )
}

export default memo(NoDataFound)
