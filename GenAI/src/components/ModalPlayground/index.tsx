import { colors } from '@/theme'
import { memo } from 'react'
import Playground from '../../pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground'
import BaseModal from '../BaseModal'
import Text from '../Text'
import IconButton from '../IconButton'

const ModalPlayground = ({
  isOpen,
  onClose,
  playgroundId,
  avatar,
  name,
  workerType,
  personality,
  communicationStyle,
  language,
}: IModalPlaygroundProps) => {
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div className="relative flex h-[654px] w-[913px] flex-col overflow-hidden rounded-[20px] border-[1px] border-neutral-200 bg-Base-03 p-3 shadow-md">
        <Text
          type="subheading"
          variant="medium"
          elementType="div"
          className="mb-[4px] text-Primary-Color"
        >
          Playground
        </Text>
        <Text
          type="subBody"
          variant="regular"
          elementType="div"
          className="mb-[16px] text-Secondary-Color"
        >
          Run your worker here to test his ability.
        </Text>
        <Playground
          playgroundId={playgroundId}
          avatar={avatar}
          name={name}
          workerType={workerType}
          personality={personality}
          communicationStyle={communicationStyle}
          language={language}
        />
        <IconButton
          className="absolute right-[10px] top-[10px] border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
      </div>
    </BaseModal>
  )
}

export default memo(ModalPlayground)

interface IModalPlaygroundProps {
  isOpen: boolean
  onClose: () => void
  playgroundId: string
  avatar: string
  name: string
  workerType: string
  personality: string
  language?: string
  communicationStyle?: string
}
