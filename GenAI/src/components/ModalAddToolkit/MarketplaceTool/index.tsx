import { MarketToolPublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import IconBuiltInTool from '@/components/IconBuiltInTool'
import Text from '@/components/Text'
import { useMyProfile } from '@/hooks/useMyProfile'
import { TOOL_TYPE } from '@/pages/Tools/const'
import { isNil } from 'lodash'
import { useMemo } from 'react'
import { twMerge } from 'tailwind-merge'
import { useDebouncedCallback } from 'use-debounce'

const MarketplaceTool = ({
  tool,
  onClick,
  onClickBuy,
}: {
  tool: MarketToolPublic
  onClick?: () => void
  onClickBuy?: (tool: MarketToolPublic) => void
}) => {
  const profile = useMyProfile()
  const myID = profile?.myProfile?.id
  const { tool_name, description, logo, tool_type } = tool

  const isBuiltInTool = useMemo(
    () => tool_type === TOOL_TYPE.BUILT_IN,
    [tool_type]
  )

  const debouncedCallback = useDebouncedCallback(
    () => {
      onClickBuy?.(tool)
    },
    300,
    {
      leading: true,
      trailing: false,
    }
  )

  const handleBuy = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.preventDefault()
    e.stopPropagation()

    debouncedCallback()
  }

  return (
    <div
      className={twMerge(
        'relative flex h-[144px] w-[272px] min-w-[272px] flex-col rounded-[12px] bg-white pb-2 pl-3 pr-2 pt-3 shadow-base',
        'cursor-pointer hover:bg-Hover-Color'
      )}
      onClick={onClick}
    >
      {isBuiltInTool && (
        <div className="absolute right-2 top-2">
          <IconBuiltInTool />
        </div>
      )}
      <div className="h-[104px]">
        <Avatar
          name={tool_name}
          avatarUrl={logo ?? ''}
          avatarDefault={
            <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
              <Icon
                name="tool-01"
                size={18}
                gradient={['#642B734D', '#C6426E4D']}
              />
            </div>
          }
          hasBorder
          variant="square"
        />

        <div className="mt-[8px] flex flex-col gap-[4px]">
          <Text
            variant="medium"
            className="text-Primary-Color"
            value={tool_name}
            elementType="div"
            ellipsis
          />
          <Text
            className={'!text-Secondary-Color'}
            value={description}
            type="subBody"
            variant="regular"
            elementType="div"
            ellipsis
            multipleLine={2}
            tooltipPosition="bottom"
          />
        </div>
      </div>

      {!isNil(myID) && myID !== tool.owner_id && (
        <div
          onClick={handleBuy}
          className={twMerge(
            'group flex h-[16px] items-center justify-center rounded-full bg-emerald-50 px-[8px] py-[2px] hover:bg-emerald-100',
            'absolute bottom-[8px] right-[8px] select-none'
          )}
        >
          <Text
            type="supportText"
            variant="medium"
            className="!text-emerald-700"
          >
            Get it free
          </Text>
        </div>
      )}
    </div>
  )
}

export default MarketplaceTool
