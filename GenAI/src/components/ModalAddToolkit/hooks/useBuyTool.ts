import {
  MarketToolPublic,
  toolsBuyTool<PERSON>pi,
  toolsBuyToolVerifyApi,
} from '@/apis/client'
import Message from '@/components/Message'
import { useRef, useState } from 'react'

export const useBuyTool = () => {
  const [isOpenModalBuyTool, setOpenModalBuyTool] = useState(false)
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false)

  const buyToolSelected = useRef<MarketToolPublic>()
  const isVerifyBuyTool = useRef(false)

  const handleBuyToolVerify = async (tool: MarketToolPublic) => {
    if (!tool || isVerifyBuyTool.current) return

    buyToolSelected.current = tool
    isVerifyBuyTool.current = true

    const { error } = await toolsBuyToolVerifyApi({
      body: {
        tool_id: tool.tool_id,
      },
    })

    isVerifyBuyTool.current = false

    if (error && error.detail) {
      Message.error({ message: error.detail })
      return
    }

    setOpenModalBuyTool(true)
  }

  const handleConfirmBuy = async () => {
    if (!buyToolSelected.current) return

    setIsLoadingConfirm(true)

    try {
      const { data, error } = await toolsBuyToolApi({
        body: {
          tool_id: buyToolSelected.current.tool_id,
        },
      })

      if (error && error.detail) {
        Message.error({ message: error.detail })
        return data
      }

      if (data && data.data) {
        Message.success({ message: 'Successfully bought tool' })
        return data
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setIsLoadingConfirm(false)
      setOpenModalBuyTool(false)
    }
  }

  return {
    isOpenModalBuyTool,
    buyToolSelected,
    isLoadingConfirm,
    setOpenModalBuyTool,
    handleBuyToolVerify,
    handleConfirmBuy,
  }
}
