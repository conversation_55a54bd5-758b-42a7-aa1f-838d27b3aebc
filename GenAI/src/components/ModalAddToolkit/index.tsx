import {
  Tool<PERSON>ategor<PERSON>,
  ToolItemHTTPRequestParameter,
  ToolItem_Input,
  ToolParamRecommendParameter_Input,
  externalKnowledgeBaseGetListColumns,
  toolCategoriesReadToolCategoriesApi,
  toolsGetListMyToolAndPurchasedApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { useGroupTool } from '@/hooks/useGroupTool'
import { useMyProfile } from '@/hooks/useMyProfile'
import { TOOL_FUNCTIONALITY, TOOL_TYPE } from '@/pages/Tools/const'
import ModalUpdateTools from '@/pages/Tools/ToolCollection/components/ModalUpdateTools'
import ModalGeneralToolParameters from '@/pages/WorkflowDetail/components/ModalGeneralToolParameters'
import ModalToolParameters from '@/pages/WorkflowDetail/components/ModalToolParameters'
import ModalToolParametersDoc from '@/pages/WorkflowDetail/components/ModalToolParametersDoc'
import ModalToolParametersExcel from '@/pages/WorkflowDetail/components/ModalToolParametersExcel'
import ModalToolParametersHttp from '@/pages/WorkflowDetail/components/ModalToolParametersHttp'
import ModalToolParametersPdf from '@/pages/WorkflowDetail/components/ModalToolParametersPdf'
import { ISelectBaseItem } from '@/pages/WorkflowDetail/components/SelectAssignee/components/Select'
import useMarketToolsCategory, {
  DEFAULT_TOOLS_BY_CATEGORY_PAGINATION,
} from '@/store/marketTools'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isEmpty } from 'lodash'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import BaseModal from '../BaseModal'
import Button from '../Button'
import EmptyData from '../EmptyData'
import IconButton from '../IconButton'
import Message from '../Message'
import NoDataFound from '../NoDataFound'
import Pagination from '../Pagination'
import SearchBar, { SearchBarInterface } from '../SearchBar'
import Text from '../Text'
import { DISPLAY_TYPE } from './consts'
import { GroupToolItem } from './GroupToolItem'
import { LIMIT_TOOLKIT, convertDataFromMyToolToToolItem } from './helpers'
import ListCategory from './ListCategory'
import ListToolByCategory from './ListToolByCategory'
import { ToolkitItem } from './ToolkitItem'
import { ToolkitItemMode } from './ToolkitItem/ToolkitItem'
import {
  ChangingGeneralToolParam,
  GeneralToolParamConfigurationData,
  GeneralToolParamConfigurations,
  SettingBuiltInTool,
  SettingBuiltInToolHttp,
} from './types'

const ALL_CATEGORIES: ISelectBaseItem = {
  id: 'all',
  name: 'Category',
}

interface IModalAddToolkitProps {
  isOpen: boolean
  onClose: () => void
  selected: ToolItem_Input[]
  onConfirm: (
    selected: ToolItem_Input[],
    settingBuiltInTools: SettingBuiltInTool,
    generalToolParamConfigurations: GeneralToolParamConfigurations,
    settingBuiltInToolsHttp: SettingBuiltInToolHttp
  ) => void
  generalToolParamConfigurations: GeneralToolParamConfigurations
  settingBuiltInTools: SettingBuiltInTool
  settingBuiltInToolsHttp: SettingBuiltInToolHttp
  limitToolkit?: number
}

const ModalAddToolkit = ({
  isOpen,
  onClose,
  selected,
  onConfirm,
  settingBuiltInTools,
  generalToolParamConfigurations,
  settingBuiltInToolsHttp,
  limitToolkit = LIMIT_TOOLKIT,
}: IModalAddToolkitProps) => {
  const searchBarRef = useRef<SearchBarInterface>(null)
  const { isSuperAdmin } = useMyProfile()

  const { groupTool, isFetchingGroupTool } = useGroupTool()

  const [searchText, setSearchText] = useState('')
  const [searchTextDisplay, setSearchTextDisplay] = useState('')
  const [categoryLoading, setCategoryLoading] = useState(false)
  const [categories, setCategories] = useState<ISelectBaseItem[]>([])
  const [selectedCategory, setSelectedCategory] =
    useState<ISelectBaseItem>(ALL_CATEGORIES)
  const [tools, setTools] = useState<ToolItem_Input[]>([])
  const [selectedTools, setSelectedTools] = useState<ToolItem_Input[]>([])
  const [page, setPage] = useState(1)
  const [totalPage, setTotalPage] = useState(1)
  const [categoryPage, setCategoryPage] = useState(1)
  const [totalCategoryPage, setTotalCategoryPage] = useState(1)
  const [changed, setChanged] = useState(false)

  const [_settingBuiltInTools, _setSettingBuiltInTools] =
    useState<SettingBuiltInTool>({})

  const [_settingBuiltInToolsHttp, _setSettingBuiltInToolsHttp] =
    useState<SettingBuiltInToolHttp>({})

  const [selectedToolInfo, setSelectedToolInfo] = useState<ToolItem_Input>()
  const [isOpenModalToolParameters, setOpenModalToolParameters] =
    useState(false)

  const [_generalToolParamConfigurations, _setGeneralToolParamConfigurations] =
    useState<GeneralToolParamConfigurations>({})

  const handleChangeGeneralTool = useCallback(
    ({
      toolId,
      inputParameters,
      environmentVariables,
    }: ChangingGeneralToolParam) => {
      if (!toolId) {
        return
      }

      _setGeneralToolParamConfigurations((prop) => ({
        ...prop,
        [toolId]: {
          inputParameters,
          environmentVariables,
        },
      }))
    },
    []
  )

  const [isOpenModalToolParametersHttp, setOpenModalToolParametersHttp] =
    useState(false)
  const [isOpenModalPdfParameters, setOpenModalPdfParameters] = useState(false)
  const [isOpenModalExcelParameters, setOpenModalExcelParameters] =
    useState(false)
  const [isOpenModalDocParameters, setOpenModalDocParameters] = useState(false)

  const [isOpenModalCreateTool, setOpenModalCreateTool] = useState(false)

  const [
    setCurrentPageToolByCategory,
    setPaginationToolByCategory,
    activeCategory,
    setActiveCategory,
    search,
    setSearch,
    clearSearch,
    clearActiveCategory,
  ] = useMarketToolsCategory((state) => [
    state.setCurrentPageToolByCategory,
    state.setPaginationToolByCategory,
    state.activeCategory,
    state.setActiveCategory,
    state.search,
    state.setSearch,
    state.clearSearch,
    state.clearActiveCategory,
  ])

  const handleChangeCategory = (category: any) => {
    searchBarRef?.current?.resetSearchBar()
    setSearch('')
    setActiveCategory(category)

    if (category) {
      setCurrentPageToolByCategory(1)
      setPaginationToolByCategory(DEFAULT_TOOLS_BY_CATEGORY_PAGINATION)
      if (!activeCategory) {
        setSearchTextDisplay('')
        setSearchText('')
      }
    }
  }

  useEffect(() => {
    return () => {
      clearSearch()
      clearActiveCategory()
    }
  }, [])

  useEffect(() => {
    _setSettingBuiltInTools(settingBuiltInTools)
  }, [settingBuiltInTools])

  useEffect(() => {
    _setGeneralToolParamConfigurations(generalToolParamConfigurations)
  }, [generalToolParamConfigurations])

  useEffect(() => {
    _setSettingBuiltInToolsHttp(settingBuiltInToolsHttp)
  }, [settingBuiltInToolsHttp])

  const selectAll = async () => {
    if (tools.length > 0 && selectedTools.length < limitToolkit) {
      setChanged(true)
      const filteredDuplicate = tools
        .filter((tool) => !selectedTools.some((item) => item.id === tool.id))
        .slice(0, limitToolkit - selectedTools.length)
      setSelectedTools([...filteredDuplicate, ...selectedTools])
    }
  }

  const unselectAll = () => {
    setSelectedTools([])
    setChanged(true)
  }

  const fetchCategories = async (currentPage = categoryPage) => {
    try {
      if (categoryLoading) {
        return
      }

      setCategoryLoading(true)

      const res = await toolCategoriesReadToolCategoriesApi({
        query: {
          page_size: PAGE_SIZE.LARGE,
          page_number: currentPage,
          include_my_tool: true,
          exclude_built_in: true,
        },
      })
      if (res.status === 200) {
        const list = (res?.data?.data?.data || [])?.map((item) => ({
          id: item.id,
          name: item.name,
        }))

        if (currentPage === 1) {
          setCategories([...list])
        } else {
          const removedDuplicate = list.filter(
            (item) => !categories.find((category) => category.id === item.id)
          )
          setCategories([...categories, ...removedDuplicate])
        }
        setCategoryPage((current) => {
          if (currentPage === current) {
            return current + 1
          }

          return currentPage + 1
        })
        setTotalCategoryPage(res?.data?.data?.total_pages || 1)
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setCategoryLoading(false)
    }
  }

  useEffect(() => {
    if (categoryPage > 1 && categoryPage <= totalCategoryPage) {
      fetchCategories()
    }
  }, [categoryPage, totalCategoryPage])

  const fetchAllTools = async () => {
    try {
      const res = await toolsGetListMyToolAndPurchasedApi({
        query: {
          name: searchText,
          page_size: PAGE_SIZE.LARGE,
          page_number: page,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setTotalPage(res?.data?.data?.total_pages || 0)

        const newData = (res?.data?.data?.data || []).map((item) =>
          convertDataFromMyToolToToolItem(item)
        )
        setTools(newData)
      } else {
        setTools([])
      }
    } catch (error) {
      setTools([])
    }
  }

  // group tool by group
  const displayToolData = useMemo(() => {
    if (isFetchingGroupTool) {
      return tools
    }

    const result: any = []

    tools?.map((tool) => {
      // if tool is belong to group replace tool by group
      if (tool.tool_group_id) {
        const group = groupTool?.find(
          (group) => group.id === tool.tool_group_id
        )
        if (
          group &&
          result.findIndex((item: any) => item.id === group.id) === -1
        ) {
          result.push({ ...group, displayType: DISPLAY_TYPE.GROUP })
        }
      } else {
        result.push({ ...tool, displayType: DISPLAY_TYPE.TOOL })
      }
    })

    return result
  }, [tools, isFetchingGroupTool, groupTool])

  // group selected tool by group
  const displaySelectedToolData = useMemo(() => {
    if (isFetchingGroupTool) {
      return selectedTools
    }

    let result: any = []

    selectedTools?.map((tool) => {
      // if tool is belong to group replace tool by group
      if (tool.tool_group_id) {
        const group = groupTool?.find(
          (group) => group.id === tool.tool_group_id
        )
        if (group) {
          const groupItemInResult = result.find(
            (item: any) => item.id === group.id
          )
          if (isEmpty(groupItemInResult)) {
            result.push({
              ...group,
              displayType: DISPLAY_TYPE.GROUP,
              tools: [tool],
            })
          } else {
            result = result.map((resultItem: any) => {
              if (resultItem.id === tool.tool_group_id) {
                return {
                  ...resultItem,
                  tools: [...resultItem.tools, tool],
                }
              }
              return resultItem
            })
          }
        } else {
          result.push({ ...tool, displayType: DISPLAY_TYPE.TOOL })
        }
      } else {
        result.push({ ...tool, displayType: DISPLAY_TYPE.TOOL })
      }
    })

    return result
  }, [selectedTools, isFetchingGroupTool, groupTool])

  const fetchToolsByCategory = async () => {
    try {
      const res = await toolsGetListMyToolAndPurchasedApi({
        query: {
          name: searchText,
          page_size: PAGE_SIZE.SMALL,
          page_number: page,
          tool_category_id: [selectedCategory?.id],
        },
        paramsSerializer: {
          indexes: null,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setTotalPage(res?.data?.data?.total_pages || 0)
        const newData = (res?.data?.data?.data || []).map((item) =>
          convertDataFromMyToolToToolItem(item)
        )
        setTools(newData)
      } else {
        setTools([])
      }
    } catch (error) {
      setTools([])
    }
  }

  useEffect(() => {
    if (!isOpen) {
      setCategories([])
      setSelectedCategory(ALL_CATEGORIES)
      setTools([])
      setSelectedTools([])
      setPage(1)
      setTotalPage(1)
      setCategoryPage(1)
      setTotalCategoryPage(1)
      setSearchText('')
      setSearchTextDisplay('')
      setChanged(false)
    } else {
      fetchCategories()
    }
  }, [isOpen])

  useEffect(() => {
    if (isOpen) {
      if (selectedCategory?.id === ALL_CATEGORIES.id) {
        fetchAllTools()
      } else {
        fetchToolsByCategory()
      }
    }
  }, [searchText, selectedCategory, page, isOpen])

  useEffect(() => {
    if (isOpen) {
      setSelectedTools(selected)
    }
  }, [selected, isOpen])

  const selectedTool = useMemo(() => {
    if (selectedToolInfo) {
      return selectedToolInfo.id
    }
    return undefined
  }, [selectedToolInfo])

  const validateSettingBuiltInTools = async (
    selectedTools: ToolItem_Input[]
  ) => {
    const listBuiltInTools = selectedTools.filter(
      (tool) => tool.tool_type === TOOL_TYPE.BUILT_IN
    )

    if (!listBuiltInTools?.length) {
      return true
    }

    const invalidTool = []

    for (const tool of listBuiltInTools) {
      if (tool.tool_functionality === TOOL_FUNCTIONALITY.RECOMMENDATION) {
        if (isEmpty(_settingBuiltInTools[tool.id])) {
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              isError: true,
            },
          }))
          invalidTool.push(tool)
          continue
        }

        if (_settingBuiltInTools[tool.id]?.isError) {
          invalidTool.push(tool)
          continue
        }

        const {
          criterion_parameter,
          profile_parameter,
          destination_parameter,
          output_parameter,
        } = _settingBuiltInTools[tool.id]

        if (
          !criterion_parameter?.criterion_source_id ||
          !criterion_parameter?.columns?.length ||
          !profile_parameter?.profile_source_id ||
          !profile_parameter?.columns?.length ||
          !destination_parameter?.destination_source_id ||
          !destination_parameter?.columns?.length ||
          !output_parameter?.output_source_id ||
          !output_parameter?.columns?.length
        ) {
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              isError: true,
            },
          }))
          invalidTool.push(tool)
          continue
        }

        const [destinationColumnsRes, criterionColumnsRes] = await Promise.all([
          externalKnowledgeBaseGetListColumns({
            query: {
              external_knowledge_base_id:
                destination_parameter?.destination_source_id || '',
            },
          }),
          externalKnowledgeBaseGetListColumns({
            query: {
              external_knowledge_base_id:
                criterion_parameter?.criterion_source_id || '',
            },
          }),
        ])

        if (
          destinationColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS &&
          criterionColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS
        ) {
          const { data: destinationColumnsData } = destinationColumnsRes
          const { data: criterionColumnsData } = criterionColumnsRes

          if (
            !destination_parameter?.columns?.some(
              (column: { column_name: string }) =>
                !isEmpty(
                  destinationColumnsData?.data?.find(
                    (col) => col.column_name === column.column_name
                  )
                )
            ) ||
            !criterion_parameter?.columns?.some(
              (column: { column_name: string }) =>
                !isEmpty(
                  criterionColumnsData?.data?.find(
                    (col) => col.column_name === column.column_name
                  )
                )
            )
          ) {
            _setSettingBuiltInTools((prev) => ({
              ...prev,
              [tool.id]: {
                ..._settingBuiltInTools[tool.id],
                isError: true,
              },
            }))

            invalidTool.push(tool)
            continue
          }

          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              ..._settingBuiltInTools[tool.id],
              isError: false,
            },
          }))
          continue
        } else {
          // Error handling
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              ..._settingBuiltInTools[tool.id],
              isError: true,
            },
          }))

          invalidTool.push(tool)
          continue
        }
      } else if (
        tool.tool_functionality === TOOL_FUNCTIONALITY.PDF_GENERATE_FILE ||
        tool.tool_functionality === TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
      ) {
        const params = _settingBuiltInTools[tool.id]
        if (!params?.prompt) {
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              isError: true,
            },
          }))
          invalidTool.push(tool)
        }
      } else if (
        tool.tool_functionality === TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
      ) {
        const params = _settingBuiltInTools[tool.id]
        if (!params?.prompt) {
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              isError: true,
            },
          }))
          invalidTool.push(tool)
        }
      } else if (
        tool.tool_functionality === TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
      ) {
        const params = _settingBuiltInTools[tool.id]
        if (!params?.prompt) {
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              isError: true,
            },
          }))
          invalidTool.push(tool)
        }
      } else if (tool.tool_functionality === TOOL_FUNCTIONALITY.HTTPREQUEST) {
        if (isEmpty(_settingBuiltInToolsHttp[tool.id])) {
          _setSettingBuiltInTools((prev) => ({
            ...prev,
            [tool.id]: {
              ..._settingBuiltInTools[tool.id],
              isError: true,
            },
          }))
          invalidTool.push(tool)
        }
      }
    }

    if (invalidTool.length) {
      return false
    }

    return true
  }

  const handleSelectTool = (tool: ToolItem_Input) => {
    if (selectedCategory?.id === ALL_CATEGORIES.id) {
      const index = selectedTools.findIndex((item) => item.id === tool.id)

      if (index > -1) {
        setSelectedTools((prev) => prev.filter((item) => item.id !== tool.id))
      } else if (selectedTools.length < limitToolkit) {
        setSelectedTools((prev) => {
          const newSelectedTools = [tool, ...prev]
          if (newSelectedTools.length > limitToolkit) {
            return prev
          }
          return newSelectedTools
        })
      }
    } else {
      const tTool = {
        ...tool,
        tool_category: {
          ...tool.tool_category,
          name: selectedCategory.name,
        } as ToolCategory,
      }
      const index = selectedTools.findIndex((item) => item.id === tool.id)
      if (index > -1) {
        setSelectedTools((prev) => prev.filter((item) => item.id !== tool.id))
      } else if (selectedTools.length < limitToolkit) {
        setSelectedTools((prev) => [tTool, ...prev])
      }
    }

    _setSettingBuiltInTools((prev) => ({
      ...prev,
      [tool.id]: {
        isError: false,
      },
    }))
    if (tool.tool_functionality === TOOL_FUNCTIONALITY.GENERAL) {
      handleChangeGeneralTool({
        toolId: tool.id,
        inputParameters: undefined,
        environmentVariables: undefined,
      })
    }
    setChanged(true)
  }

  useEffect(() => {
    if (searchTextDisplay.trim() !== searchText.trim()) {
      setSearchText(searchTextDisplay.trim())
      setPage(1)
      setTotalPage(1)
    }
  }, [searchTextDisplay])

  const handleSettingBuiltInToolByToolId = (
    toolSettings?: ToolParamRecommendParameter_Input
  ) => {
    _setSettingBuiltInTools((prev) => ({
      ...prev,
      [selectedTool!]: {
        ...toolSettings,
        isError: false,
      },
    }))
  }

  const handleSaveGeneralTool = ({
    inputParameters,
    environmentVariables,
  }: GeneralToolParamConfigurationData) => {
    setOpenModalToolParameters(false)

    handleChangeGeneralTool({
      toolId: selectedTool,
      inputParameters,
      environmentVariables,
    })
  }

  const handleSettingBuiltInToolHttpByToolId = (
    toolSettings?: Array<ToolItemHTTPRequestParameter>
  ) => {
    if (toolSettings) {
      _setSettingBuiltInToolsHttp((prev) => ({
        ...prev,
        [selectedTool!]: toolSettings,
      }))

      _setSettingBuiltInTools((prev) => ({
        ...prev,
        [selectedTool!]: {
          isError: false,
        },
      }))
    }
  }

  const modalEditToolParameters = useMemo(() => {
    if (!isOpenModalToolParameters) {
      return null
    }

    if (selectedToolInfo?.tool_functionality === TOOL_FUNCTIONALITY.GENERAL) {
      return (
        <ModalGeneralToolParameters
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          toolId={selectedTool!}
          generalToolParamConfigurations={
            _generalToolParamConfigurations?.[selectedTool!]
          }
          onSave={handleSaveGeneralTool}
        />
      )
    }

    return (
      <ModalToolParameters
        isOpen
        onClose={() => {
          setSelectedToolInfo(undefined)
          setOpenModalToolParameters(false)
        }}
        settingBuiltInTools={_settingBuiltInTools?.[selectedTool!]}
        handleSettingBuiltInToolByToolId={handleSettingBuiltInToolByToolId}
      />
    )
  }, [isOpenModalToolParameters, selectedToolInfo, selectedTool])

  const renderMyToolMode = () => (
    <div className="mt-[12px] flex max-h-[464px] flex-1 gap-3">
      <div className="flex h-full w-[calc(50%-6px)] flex-col">
        <div className="flex gap-3">
          <SearchBar
            className="w-full"
            placeholder="Search by tool name"
            onSearch={(value) => setSearchTextDisplay(value)}
            filterOptions={[
              {
                contentClassName: '!min-w-[135px] !w-full h-[142px]',
                overlayClassName: '[--anchor-gap:12px] ml-3',
                data: categories,
                selected: selectedCategory,
                loading: categoryLoading,
                onChangeSelectedValue: (value) => {
                  if (value) {
                    setSelectedCategory(value)
                  } else {
                    setSelectedCategory(ALL_CATEGORIES)
                  }
                  setPage(1)
                  setTotalPage(1)
                },
              },
            ]}
            hasFilter
          />
        </div>
        <button
          className="mt-3 self-end p-0 text-Primary-Color hover:text-Main-Color"
          onClick={selectAll}
        >
          <Text
            type="subBody"
            variant="medium"
            className={
              selectedTools.length >= limitToolkit || !tools.length
                ? 'text-Disable-Text'
                : ''
            }
          >
            Select all
          </Text>
        </button>
        <div className="genai-scrollbar mt-1 flex h-[348px] flex-col gap-3 overflow-y-auto pr-1">
          {displayToolData?.map((item: any) => {
            if (item.displayType === DISPLAY_TYPE.GROUP)
              return (
                <GroupToolItem
                  key={item.id}
                  group={item}
                  selectedTools={selectedTools}
                  onSelect={handleSelectTool}
                  disableSelect={selectedTools.length >= limitToolkit}
                  searchText={searchText}
                />
              )

            const isActive = !isEmpty(
              selectedTools.find((tool) => tool.id === item.id)
            )

            return (
              <ToolkitItem
                key={item.id}
                toolkit={item}
                onAdd={() => handleSelectTool(item)}
                isActive={isActive}
                disableSelect={selectedTools.length >= limitToolkit}
                openModalToolParameters={() => {
                  isActive && setChanged(true)
                  setSelectedToolInfo(item)

                  if (
                    item.tool_functionality === TOOL_FUNCTIONALITY.HTTPREQUEST
                  ) {
                    setOpenModalToolParametersHttp(true)
                  } else if (
                    item.tool_functionality ===
                      TOOL_FUNCTIONALITY.PDF_GENERATE_FILE ||
                    item.tool_functionality ===
                      TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
                  ) {
                    setOpenModalPdfParameters(true)
                  } else if (
                    item.tool_functionality ===
                    TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
                  ) {
                    setOpenModalExcelParameters(true)
                  } else if (
                    item.tool_functionality ===
                    TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
                  ) {
                    setOpenModalDocParameters(true)
                  } else {
                    setOpenModalToolParameters(true)
                  }
                }}
                isConfigured={false}
              />
            )
          })}

          {searchText.trim().length === 0 && tools.length === 0 && (
            <EmptyData type="02" className="mt-8 self-center" size="small" />
          )}
          {searchText.trim().length > 0 && tools.length === 0 && (
            <NoDataFound
              className="mt-8"
              textType="subheading"
              textVariant="semibold"
              subTextType="subBody"
              subTextVariant="regular"
              size={150}
            />
          )}
        </div>
        {totalPage > 1 && (
          <Pagination
            page={page}
            totalPage={totalPage}
            onChangePage={(page) => setPage(page)}
            className="mt-3 self-end"
            type="mini"
          />
        )}
      </div>
      <div className="flex w-[calc(50%-6px)] flex-1 flex-col rounded-lg border border-neutral-200 px-3 py-2">
        <Text type="body" variant="medium" className="text-Primary-Color">
          Selected
        </Text>
        <button
          className="my-1 self-end p-0 text-Primary-Color hover:text-Main-Color"
          onClick={unselectAll}
        >
          <Text
            type="subBody"
            variant="medium"
            className={selectedTools.length === 0 ? 'text-Disable-Text' : ''}
          >
            Unselect all
          </Text>
        </button>
        <div className="genai-scrollbar flex h-[501px] flex-col gap-3 overflow-y-auto">
          {displaySelectedToolData?.map((item: any) => {
            if (item.displayType === DISPLAY_TYPE.GROUP) {
              return (
                <GroupToolItem
                  key={item.id}
                  group={item}
                  selectedTools={selectedTools}
                  onSelect={handleSelectTool}
                  disableSelect={selectedTools.length >= limitToolkit}
                  isSelected
                />
              )
            }

            return (
              <ToolkitItem
                key={item.id}
                toolkit={item}
                mode={ToolkitItemMode.SELECTED}
                onRemove={() => handleSelectTool(item)}
                isActive
                disableSelect={selectedTools.length >= limitToolkit}
                openModalToolParameters={() => {
                  if (
                    item.tool_functionality === TOOL_FUNCTIONALITY.HTTPREQUEST
                  ) {
                    setChanged(true)
                    setSelectedToolInfo(item)
                    setOpenModalToolParametersHttp(true)
                  } else if (
                    item.tool_functionality ===
                      TOOL_FUNCTIONALITY.PDF_GENERATE_FILE ||
                    item.tool_functionality ===
                      TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
                  ) {
                    setChanged(true)
                    setSelectedToolInfo(item)
                    setOpenModalPdfParameters(true)
                  } else if (
                    item.tool_functionality ===
                    TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
                  ) {
                    setChanged(true)
                    setSelectedToolInfo(item)
                    setOpenModalExcelParameters(true)
                  } else if (
                    item.tool_functionality ===
                    TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
                  ) {
                    setChanged(true)
                    setSelectedToolInfo(item)
                    setOpenModalDocParameters(true)
                  } else {
                    setChanged(true)
                    setSelectedToolInfo(item)
                    setOpenModalToolParameters(true)
                  }
                }}
                isConfigured={false}
              />
            )
          })}
        </div>
      </div>
    </div>
  )

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div
        className={clsx(
          'relative flex min-h-[595px] w-[1125px] flex-col rounded-[20px] bg-white p-3 shadow-md',
          selectedCategory && '!w-[1156px]'
        )}
      >
        <Text
          type="body"
          variant="medium"
          className="px-[4px] text-Primary-Color"
          elementType="div"
        >
          Add toolkit
        </Text>
        <div className="mt-3 flex items-center justify-between px-2">
          <ListCategory
            className={clsx('', isSuperAdmin && '!w-[calc(100%-86px)]')}
            search={search}
            onChangeCategory={handleChangeCategory}
          />

          {isSuperAdmin && (
            <Button
              className="!w-[62px] !min-w-[62px]"
              size="small"
              text="Tool"
              leftIcon={
                <Icon name="plus" size={16} color={colors['Tertiary-Color']} />
              }
              onClick={() => {
                setOpenModalCreateTool(true)
              }}
            />
          )}
        </div>

        {activeCategory ? (
          <ListToolByCategory
            searchBarRef={searchBarRef}
            onCompleteBuyingTool={(tool: any) => {
              if (
                !categories.some(
                  (category) => category.id === tool?.tool_category?.id
                )
              ) {
                fetchCategories(1)
              }
              fetchAllTools()
              handleChangeCategory(undefined)
              handleSelectTool(tool)
            }}
          />
        ) : (
          renderMyToolMode()
        )}

        <Button
          type="primary"
          size="small"
          disabled={!changed}
          onClick={async () => {
            await validateSettingBuiltInTools(selectedTools)
            onConfirm(
              selectedTools,
              _settingBuiltInTools,
              _generalToolParamConfigurations,
              _settingBuiltInToolsHttp
            )
          }}
          text="Confirm"
          className="mt-[12px] w-[96px] self-end"
        />
        <IconButton
          className="absolute right-[10px] top-[10px] z-30 border-0 bg-transparent p-0 duration-300 hover:bg-slate-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
      </div>

      {isOpenModalToolParameters && (
        <ModalToolParameters
          isOpen={isOpenModalToolParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          settingBuiltInTools={_settingBuiltInTools?.[selectedTool!]}
          handleSettingBuiltInToolByToolId={handleSettingBuiltInToolByToolId}
        />
      )}

      <ModalToolParametersHttp
        isOpen={isOpenModalToolParametersHttp}
        onClose={() => {
          setSelectedToolInfo(undefined)
          setOpenModalToolParametersHttp(false)
        }}
        settingBuiltInToolsHttp={_settingBuiltInToolsHttp?.[selectedTool!]}
        handleSettingBuiltInToolHttpByToolId={
          handleSettingBuiltInToolHttpByToolId
        }
      />

      {isOpenModalPdfParameters && (
        <ModalToolParametersPdf
          isOpen={isOpenModalPdfParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalPdfParameters(false)
          }}
          handleSaveParamsPdf={handleSettingBuiltInToolByToolId}
          params={_settingBuiltInTools?.[selectedTool!]}
        />
      )}

      {isOpenModalExcelParameters && (
        <ModalToolParametersExcel
          isOpen={isOpenModalExcelParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalExcelParameters(false)
          }}
          handleSaveParamsExcel={handleSettingBuiltInToolByToolId}
          params={_settingBuiltInTools?.[selectedTool!]}
        />
      )}

      {isOpenModalDocParameters && (
        <ModalToolParametersDoc
          isOpen={isOpenModalDocParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalDocParameters(false)
          }}
          handleSaveParamsDoc={handleSettingBuiltInToolByToolId}
          params={_settingBuiltInTools?.[selectedTool!]}
        />
      )}

      {modalEditToolParameters}

      {isOpenModalCreateTool && (
        <ModalUpdateTools
          isOpen={isOpenModalCreateTool}
          onComplete={(tool) => {
            if (tool?.id) {
              if (
                !categories.some(
                  (category) => category.id === tool?.tool_category_id
                )
              ) {
                fetchCategories(1)
              }
              fetchAllTools()
              handleSelectTool(tool)
              setOpenModalCreateTool(false)
            }
          }}
          refetchData={fetchAllTools}
          onClose={() => {
            setOpenModalCreateTool(false)
          }}
        />
      )}
    </BaseModal>
  )
}

export default memo(ModalAddToolkit)
