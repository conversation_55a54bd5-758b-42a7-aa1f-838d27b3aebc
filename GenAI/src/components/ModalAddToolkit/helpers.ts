import {
  MarketToolPublic,
  MyToolPublic,
  ToolGroupPublic,
  ToolItem_Input,
} from '@/apis/client'
import { ModifyToolGroupPublic } from '@/components/ModalAddToolkit/types'

export const convertDataFromMarketToolToToolItem = (
  item: MarketToolPublic
): ToolItem_Input => ({
  id: item.tool_id,
  logo: item.logo || '',
  name: item.tool_name,
  description: item.description || '',
  tool_category: {
    id: item.tool_category_id,
    name: item.category_name || '',
  },
  content_execute: '',
  tool_type: item?.tool_type || '',
  tool_functionality: item.tool_functionality || '',
  tool_parameters: null,
  tool_group_id: item.group_id,
})

export const convertDataFromMyToolToToolItem = (
  item: MyToolPublic
): ToolItem_Input => ({
  id: item.id,
  logo: item.logo || '',
  name: item.name,
  description: item.description || '',
  tool_category: item.tool_category,
  content_execute: '',
  tool_type: item?.type || '',
  tool_functionality: item.functionality || '',
  tool_parameters: null,
  tool_group_id: item.group_id,
})

export const convertDataFromToolGroupPublicToModifyToolGroupPublic = (
  item: ToolGroupPublic,
  userId: number
): ModifyToolGroupPublic => ({
  ...item,
  tools: item?.tools?.map((item) => ({
    ...item,
    tool_category: {
      id: item.tool_category_id!,
      name: '',
    },
    tool_group_id: item.group_id,
    tool_auth_type: item.auth_type,
    tool_parameter_type: item.parameter_type,
    tool_functionality: item.functionality || '',
    tool_type: item.type,
    is_my_tool: item.owner_id === userId,
  })),
})

export const LIMIT_TOOLKIT = 10
export const LIMIT_AUTO_PROCESSING_TOOLKIT = 1

export const checkOverflow = (element: HTMLElement | null): boolean => {
  if (!element) return false
  return element.scrollWidth > element.clientWidth
}
