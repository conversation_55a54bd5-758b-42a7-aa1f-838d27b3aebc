import { ToolItem_Input } from '@/apis/client/types.gen'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { colors } from '@/theme'
import { Popover } from 'antd'
import clsx from 'clsx'
import { memo, useState } from 'react'

interface IShowMoreToolsProps {
  tools: ToolItem_Input[]
  selectedTools: ToolItem_Input[]
  toggleSelect: (tool: ToolItem_Input) => void
  disableSelect?: boolean
}

const ShowMoreTools = ({
  tools,
  selectedTools,
  toggleSelect,
  disableSelect = false,
}: IShowMoreToolsProps) => {
  const [isOpenDropdown, setIsOpenDropdown] = useState(false)

  return (
    <Popover
      content={
        <div className="genai-scrollbar-invisible flex max-h-[210px] w-[245px] flex-col gap-2 overflow-y-auto overflow-x-hidden">
          {tools.map((tool) => {
            const isSelected =
              selectedTools?.findIndex((t) => t.id === tool.id) !== -1

            return (
              <div key={tool.id} className="flex items-center gap-2">
                <Avatar
                  name={tool.name}
                  avatarUrl={tool.logo!}
                  size="medium"
                  variant="square"
                  hasBorder
                  avatarDefault={
                    <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
                      <Icon
                        name="tool-01"
                        size={18}
                        gradient={['#642B734D', '#C6426E4D']}
                      />
                    </div>
                  }
                />
                <div className="flex w-[calc(100%-68px)] flex-col gap-1">
                  <Text variant="medium" ellipsis elementType="div">
                    {tool.name}
                  </Text>
                  <Text
                    type="subBody"
                    ellipsis
                    elementType="div"
                    multipleLine={2}
                    className="text-Secondary-Color"
                  >
                    {tool.description}
                  </Text>
                </div>
                <IconButton
                  nameIcon={isSelected ? 'check' : 'plus'}
                  sizeIcon={20}
                  colorIcon={colors.neutral[400]}
                  onClick={() => toggleSelect(tool)}
                  className={clsx(
                    disableSelect && !isSelected ? '' : 'hover:bg-neutral-200'
                  )}
                  disabled={disableSelect && !isSelected}
                  disableColor={colors['Disable-Text']}
                />
              </div>
            )
          })}
        </div>
      }
      onOpenChange={setIsOpenDropdown}
      placement="bottom"
      arrow={false}
      classNames={{
        body: 'rounded-xl border border-border-base-icon shadow-sm',
      }}
    >
      <div
        className={clsx(
          'flex cursor-pointer items-center gap-1 rounded-full bg-neutral-100 px-3 py-1 hover:bg-neutral-200',
          isOpenDropdown && 'bg-neutral-200'
        )}
        onClick={() => setIsOpenDropdown(!isOpenDropdown)}
      >
        <Text type="subBody" className="text-Secondary-Color" variant="medium">
          {tools.length} more
        </Text>
      </div>
    </Popover>
  )
}

export default memo(ShowMoreTools)
