import { ToolItem_Input } from '@/apis/client/types.gen'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import Button from '@/components/Button'
import Text from '@/components/Text'
import clsx from 'clsx'
import { memo, useCallback, useMemo } from 'react'
import IconBuiltInTool from '../ToolkitItem/IconBuiltInTool'
import { ModifyToolGroupPublic } from '../types'
import ShowMoreTools from './ShowMoreTools'
import ToolItem from './ToolItem'

interface IGroupToolItemProps {
  group: ModifyToolGroupPublic
  selectedTools: ToolItem_Input[]
  onSelect: (tool: ToolItem_Input) => void
  disableSelect?: boolean
  isSelected?: boolean
  searchText?: string
}

const GroupToolItem = ({
  group,
  selectedTools,
  onSelect,
  disableSelect = false,
  isSelected = false,
  searchText = '',
}: IGroupToolItemProps) => {
  const { tools } = group

  const toolsOrderedBySearchText = useMemo(() => {
    if (!searchText || isSelected) return tools

    return [...(tools ?? [])].sort((a, b) => {
      const aMatches = a.name.toLowerCase().includes(searchText.toLowerCase())
      const bMatches = b.name.toLowerCase().includes(searchText.toLowerCase())

      if (aMatches && !bMatches) return -1
      if (!aMatches && bMatches) return 1
      return 0
    })
  }, [tools, searchText, isSelected])

  const isAllToolsSelected = useMemo(() => {
    return tools?.every((tool: ToolItem_Input) =>
      selectedTools?.find((t) => t.id === tool.id)
    )
  }, [tools, selectedTools])

  const handleAction = useCallback(() => {
    if (isSelected) {
      tools?.forEach((tool: ToolItem_Input) => {
        if (selectedTools?.findIndex((t) => t.id === tool.id) !== -1) {
          onSelect(tool)
        }
      })
    } else {
      tools?.forEach((tool: ToolItem_Input) => {
        if (selectedTools?.findIndex((t) => t.id === tool.id) === -1) {
          onSelect(tool)
        }
      })
    }
  }, [tools, onSelect, selectedTools])

  const renderListToolInGroup = useCallback(() => {
    if (toolsOrderedBySearchText!.length > 3) {
      const firstPart = toolsOrderedBySearchText!.slice(0, 2)
      const restPart = toolsOrderedBySearchText!.slice(2)
      return (
        <>
          {firstPart.map((tool: ToolItem_Input) => (
            <ToolItem
              key={tool.id}
              tool={tool}
              isSelected={
                selectedTools?.findIndex((t) => t.id === tool.id) !== -1
              }
              toggleSelect={onSelect}
              disableSelect={disableSelect}
            />
          ))}
          <ShowMoreTools
            tools={restPart}
            selectedTools={selectedTools}
            toggleSelect={onSelect}
            disableSelect={disableSelect}
          />
        </>
      )
    }

    return toolsOrderedBySearchText?.map((tool: ToolItem_Input) => (
      <ToolItem
        key={tool.id}
        tool={tool}
        isSelected={selectedTools?.findIndex((t) => t.id === tool.id) !== -1}
        toggleSelect={onSelect}
        disableSelect={disableSelect}
      />
    ))
  }, [toolsOrderedBySearchText, selectedTools, disableSelect, searchText])

  return (
    <div className="flex gap-4 px-3 py-1.5">
      <div className="flex flex-col gap-2">
        <div className="flex items-start gap-2">
          <div className="relative">
            <Avatar
              name={group.name}
              avatarUrl={group.logo!}
              size="medium"
              variant="square"
              hasBorder
              avatarDefault={
                <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
                  <Icon
                    name="tool-01"
                    size={18}
                    gradient={['#642B734D', '#C6426E4D']}
                  />
                </div>
              }
            />

            <div className="absolute -left-1 -top-1">
              <IconBuiltInTool width={14} height={16} />
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <Text className="text-Primary-Color" variant="medium">
              {group.name}
            </Text>
            <Text
              className="whitespace-pre-wrap text-Secondary-Color"
              multipleLine={2}
              ellipsis
              elementType="div"
              type="subBody"
            >
              {group.description}
            </Text>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">{renderListToolInGroup()}</div>
      </div>
      <Button
        onClick={handleAction}
        type={isAllToolsSelected && !isSelected ? 'default' : 'secondary'}
        size="small"
        className={clsx(
          'self-center rounded-md px-2',
          isAllToolsSelected ? '!min-w-[77px]' : '!min-w-[64px]',
          isSelected && '!min-w-[84px]'
        )}
        disabled={(isAllToolsSelected || disableSelect) && !isSelected}
      >
        {isSelected
          ? 'Remove all'
          : isAllToolsSelected
            ? 'Added all'
            : 'Add all'}
      </Button>
    </div>
  )
}

export default memo(GroupToolItem)
