import { isExitsUrl } from '@/helpers/routes'
import { useMyProfile } from '@/hooks/useMyProfile'
import { useSidebar } from '@/hooks/useSidebar'
import { supperAdminUrls, userUrls } from '@/routes/rootUrls'
import { PropsWithChildren, forwardRef, memo, useMemo } from 'react'
import { useLocation } from 'react-router-dom'
import { AccessDenied } from '../ErrorPage'
import Sidebar from '../Sidebar'
import Spin from '../Spin'

const Layout = forwardRef(({ children }: PropsWithChildren<LayoutProps>) => {
  const { myProfile, loadingProfile } = useMyProfile()
  const { isExpanded, setExpanded } = useSidebar()

  const location = useLocation()

  const isAccessUrl = useMemo(() => {
    switch (myProfile?.roles?.[0]?.name) {
      case 'SA':
        return isExitsUrl(supperAdminUrls, location.pathname)
      case 'User':
        return isExitsUrl(userUrls, location.pathname)
      default:
        break
    }
  }, [myProfile?.roles?.[0]?.name, location.pathname])

  return (
    <div className="flex h-full w-full bg-Base p-3">
      <Sidebar isExpanded={isExpanded} setExpanded={setExpanded} />
      <div className="flex flex-grow flex-col overflow-hidden py-3 pl-5 pr-0">
        {/* <Header onSearch={onSearch} ref={ref} hasSearchBar={hasSearchBar} /> */}
        <div className="h-full w-full overflow-hidden">
          {loadingProfile ? (
            <div className="flex h-full w-full items-center justify-center">
              <Spin />
            </div>
          ) : isAccessUrl ? (
            children
          ) : (
            <AccessDenied />
          )}
        </div>
      </div>
    </div>
  )
})

interface LayoutProps {}

export default memo(Layout)
