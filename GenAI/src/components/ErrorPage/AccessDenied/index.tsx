import { rootUrls } from '@/routes/rootUrls'
import { useNavigate } from 'react-router-dom'
import Button from '../../Button'
import Text from '../../Text'
import AccessDeniedImage from './AccessDeniedImage'

const AccessDenied = () => {
  const navigate = useNavigate()
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-[24px]">
      <AccessDeniedImage />
      <div className="flex flex-col items-center justify-center gap-[4px]">
        <Text type="title" variant="semibold" className="text-Primary-Color">
          FORBIDDEN
        </Text>
        <Text
          type="subheading"
          variant="medium"
          className="text-Secondary-Color"
        >
          You do not have the permission to access this page
        </Text>
      </div>
      <Button
        type="primary"
        onClick={() => navigate(rootUrls.Home, { replace: true })}
        text={'Go back'}
      />
    </div>
  )
}

export default AccessDenied
