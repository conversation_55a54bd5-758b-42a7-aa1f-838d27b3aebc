import { rootUrls } from '@/routes/rootUrls'
import { useNavigate } from 'react-router-dom'
import Button from '../../Button'
import Text from '../../Text'
import NotFoundImage from './NotFoundImage'

const NotFound = () => {
  const navigate = useNavigate()
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-[24px]">
      <NotFoundImage />
      <div className="flex flex-col items-center justify-center gap-[4px]">
        <Text type="title" variant="semibold" className="text-Primary-Color">
          YOU’RE LOST
        </Text>
        <Text
          type="subheading"
          variant="medium"
          className="text-Secondary-Color"
        >
          Sorry! We couldn’t find the space you’re looking for
        </Text>
      </div>
      <Button
        type="primary"
        onClick={() => navigate(rootUrls.Home, { replace: true })}
        text="Go home"
      />
    </div>
  )
}

export default NotFound
