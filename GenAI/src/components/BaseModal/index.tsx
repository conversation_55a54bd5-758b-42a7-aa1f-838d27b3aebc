import { PropsWithChildren, memo } from 'react'
import ReactModal from 'react-modal'

import Button from '@/components/Button'
import Text from '@/components/Text'
import { colors } from '@/theme'
import clsx from 'clsx'
import './styles.scss'
import IconButton from '../IconButton'

ReactModal.setAppElement('#root')

interface IBaseModal {
  isOpen: boolean
  title?: string
  subTitle?: string
  isShowCloseButton?: boolean
  agreeLabel?: string
  onAgree?: () => void
  onClose?: () => void
  actions?: {
    name: string
    label: string
    type?: 'primary' | 'secondary' | 'outline' | 'default'
    onClick?: () => void
  }[]
  customFooter?: () => JSX.Element
  hideTitle?: boolean
  hideFooter?: boolean
  isPureModal?: boolean
  shouldCloseOnOverlayClick?: boolean
  shouldCloseOnEsc?: boolean
  overlayClassName?: string
  isLoading?: boolean
  isInvalid?: boolean
  className?: string
  hasCancelButton?: boolean
  bodyClassName?: string
  headerClassName?: string
  footerClassName?: string
}

const BaseModal = (props: PropsWithChildren<IBaseModal>) => {
  const {
    isOpen,
    title,
    subTitle,
    children,
    agreeLabel = 'Save',
    onAgree = () => {},
    onClose = () => {},
    actions = [],
    customFooter,
    hideTitle = false,
    hideFooter = false,
    isPureModal = false,
    shouldCloseOnOverlayClick = true,
    shouldCloseOnEsc = true,
    overlayClassName = '',
    isShowCloseButton = false,
    isLoading = false,
    isInvalid = false,
    className,
    hasCancelButton = true,
    bodyClassName,
    headerClassName,
    footerClassName,
  } = props

  const renderButtonClose = () => (
    <IconButton
      className="absolute right-[10px] top-[10px] cursor-pointer duration-300 hover:bg-neutral-200"
      nameIcon="x-close"
      sizeIcon={16}
      colorIcon={colors.neutral[500]}
      onClick={onClose}
    />
  )

  const renderHeader = () => (
    <div
      className={clsx(
        'flex flex-col gap-1 bg-Base-03 px-3 pb-4 pt-3',
        headerClassName
      )}
    >
      <Text
        value={title}
        type="subheading"
        variant="medium"
        className="text-Primary-Color"
      />
      <Text value={subTitle} type="subBody" className="text-Secondary-Color" />
    </div>
  )

  const renderActions = () =>
    actions.map((action: any) => {
      const onClick = () =>
        action.onClick && action.onClick({ name: action.name })
      return (
        <Button
          key={action.name}
          type={action.type}
          onClick={onClick}
          text={action.name}
        />
      )
    })

  const renderFooter = () => (
    <div
      className={clsx(
        'flex items-center justify-end gap-3 bg-Base-03 p-3',
        footerClassName
      )}
    >
      {customFooter ? (
        customFooter()
      ) : (
        <>
          {actions.length > 0 ? (
            renderActions()
          ) : (
            <>
              {hasCancelButton && (
                <Button type="secondary" onClick={onClose} text="Cancel" />
              )}

              <Button
                type="primary"
                onClick={onAgree}
                text={agreeLabel}
                loading={isLoading}
                disabled={isInvalid}
              />
            </>
          )}
        </>
      )}
    </div>
  )

  const renderContent = () => {
    if (isPureModal) {
      return <div className="modal-dialog">{children}</div>
    }

    return (
      <div className="modal-dialog">
        <div className="modal-content relative overflow-hidden rounded-[20px] bg-white shadow-md">
          {!hideTitle && renderHeader()}
          {isShowCloseButton && renderButtonClose()}

          <div className={clsx('modal-body bg-Base-03 px-5', bodyClassName)}>
            {children}
          </div>
          {!hideFooter && renderFooter()}
        </div>
      </div>
    )
  }

  return (
    <ReactModal
      isOpen={isOpen}
      className={clsx('genai-modal', className)}
      onRequestClose={onClose}
      overlayClassName={clsx('Overlay genai-scrollbar', overlayClassName)}
      shouldCloseOnOverlayClick={shouldCloseOnOverlayClick}
      closeTimeoutMS={100}
      shouldCloseOnEsc={shouldCloseOnEsc}
      ariaHideApp={false}
    >
      {renderContent()}
    </ReactModal>
  )
}

export default memo(BaseModal)
