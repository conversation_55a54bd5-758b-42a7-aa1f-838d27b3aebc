/* eslint-disable @typescript-eslint/ban-ts-comment */
import { rootUrls } from '@/routes/rootUrls'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isArray } from 'lodash'
import { memo, useCallback } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import IconButton from '../IconButton'
import Text from '../Text'
import MenuItem, { IMenuItemProps } from './MenuItem'
import useSidebarStore from './SidebarStore'

interface IProps {
  title: string
  menuItems?: IMenuItemProps[]
  isExpanded: boolean
  index: number
}
const GroupMenu = ({ title, menuItems, isExpanded, index }: IProps) => {
  // @ts-ignore
  const isGroupExpanded = useSidebarStore.use[title]()
  const location = useLocation()

  const navigate = useNavigate()

  const toggleActiveKeysStore = useSidebarStore.use.toggleActiveKeys()

  const toggleActiveKeys = useCallback(
    (value: boolean) => {
      toggleActiveKeysStore(title, value)

      // Only check logic in studio group
      // Business logic by davis 220125
      // Default redirect to workflow page when click on studio if havent subpage active
      if (value && title === 'studio') {
        const hasSubpageActive = menuItems?.some((item) => {
          const { path } = item
          if (isArray(path)) {
            return path.some((p) => location.pathname?.includes(p))
          }
          return location.pathname?.includes(path)
        })

        if (hasSubpageActive) return

        navigate(rootUrls.Workflows)
      }
    },
    [location.pathname]
  )

  return (
    <div className="flex flex-col gap-2">
      {isExpanded ? (
        <div
          className="ml-1 mr-2 flex cursor-pointer items-center justify-between"
          onClick={() => toggleActiveKeys(!isGroupExpanded)}
        >
          <Text
            // type="subBody"
            variant="semibold"
            className="text-Secondary-Color-2"
            type="subBody"
          >
            {title?.toLocaleUpperCase()}
          </Text>
          <IconButton
            nameIcon="chevron-up"
            colorIcon={colors['Tertiary-Color']}
            hoverColor={colors['Primary-Color']}
            className={clsx(
              'transition-all',
              isGroupExpanded ? '' : 'rotate-180'
            )}
            onClick={() => toggleActiveKeys(!isGroupExpanded)}
            sizeIcon={16}
          />
        </div>
      ) : (
        !!index && (
          <div className="h-[1px] w-5 self-center bg-Secondary-Color-2" />
        )
      )}
      {(isGroupExpanded || !isExpanded) &&
        menuItems?.map((item: any, index) => {
          return (
            <MenuItem
              key={`menu-item-${index}`}
              path={item.path}
              icon={item.icon}
              iconActive={item.iconActive}
              name={item.name}
              children={item.children}
              isExpanded={isExpanded}
              isExact={item.isExact}
              showInNewTab={item.showInNewTab}
              isHidden={item.isHidden}
            />
          )
        })}
    </div>
  )
}

export default memo(GroupMenu)
