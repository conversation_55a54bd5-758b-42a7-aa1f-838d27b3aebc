import { workflowPublishWorkflowApi } from '@/apis/client'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Select from '@/components/Select'
import { HTTP_STATUS_CODE, WORKFLOW_TOOLS_NOT_PUBLISHED } from '@/constants'
import { useLoadWorkflowCategory } from '@/pages/Workflows/components/WorkflowItem/hooks/useLoadWorkflowCategory'
import { memo, useEffect, useState } from 'react'
import { MessageDialog } from '../DialogMessage'

interface Props {
  isOpen: boolean
  id: string
  setIsOpen: (value: boolean) => void
  handlePublishingSuccess?: () => void
  handlePublishingError?: () => void
}

const ModalPublishWorkflowWithCategory = ({
  isOpen,
  id,
  setIsOpen,
  handlePublishingSuccess,
  handlePublishingError,
}: Props) => {
  const [selectedCategory, setSelectedCategory] = useState<any>(undefined)

  const { isLoading, data, inputValue, handleReachEnd, onChangeInputValue } =
    useLoadWorkflowCategory()

  const [isLoadingPublishing, setIsLoadingPublishing] = useState(false)

  const publishCallApi = async () => {
    if (!selectedCategory) return

    setIsLoadingPublishing(true)

    try {
      const res = await workflowPublishWorkflowApi({
        path: {
          workflow_id: id,
        },
        body: {
          is_published: true,
          business_category_id: selectedCategory.id,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({
          message: 'Successfully published workflow template',
        })
        handlePublishingSuccess && handlePublishingSuccess()
      } else if (res.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        const message = res?.error?.detail ?? 'Something went wrong!'
        if (WORKFLOW_TOOLS_NOT_PUBLISHED === message) {
          MessageDialog.require({
            mainMessage: 'Publish your tool to proceed',
            subMessage:
              'This workflow contains unpublished Tools. Please go to your Tool Collection and publish them all',
            onClick: handlePublishingError,
          })
        } else {
          Message.error({
            message,
          })
        }
      }

      setIsOpen(false)
      setIsLoadingPublishing(false)
    } catch (error) {
      setIsLoadingPublishing(false)
      Message.error({
        message: (error as any)?.message ?? 'Something went wrong!',
      })
    }
  }

  const handlePublishing = async () => {
    if (!selectedCategory) return
    publishCallApi()
  }

  useEffect(() => {
    if (!isOpen) {
      setSelectedCategory(undefined)
    }
  }, [isOpen])

  return (
    <Modal
      open={isOpen}
      title="Publish workflow"
      subTitle="Choose the appropriate business category to publish your workflow"
      className="w-[496px]"
      okText="Publish"
      classNameFooter="justify-center gap-[12px]"
      classNameCancelButton="w-[218px]"
      classNameOkButton="w-[218px]"
      okLoading={isLoadingPublishing}
      onClickCancel={() => {
        setIsOpen(false)
      }}
      onClickOk={handlePublishing}
      okDisable={!selectedCategory}
    >
      <div className="flex h-[105px] w-full flex-col gap-[8px] rounded-[12px] border border-neutral-200 bg-white px-[24px] pb-[24px] pt-[16px]">
        <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
          Category
        </div>

        <Select
          placeholder="Select category or type in to search"
          isLoading={isLoading}
          data={data}
          selected={selectedCategory}
          inputValue={inputValue}
          onChangeSelectedValue={(item) => {
            setSelectedCategory(item)
          }}
          displayValue={(item) => item?.name ?? ''}
          onChangeInputValue={onChangeInputValue}
          onReachEnd={handleReachEnd}
        />
      </div>
    </Modal>
  )
}

export default memo(ModalPublishWorkflowWithCategory)
