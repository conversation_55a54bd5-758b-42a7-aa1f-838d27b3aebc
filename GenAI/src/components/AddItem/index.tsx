import Icon from '@/assets/icon/Icon'
import clsx from 'clsx'
import { ReactNode, memo } from 'react'
import { twMerge } from 'tailwind-merge'
import Avatar from '../Avatar'
import Text from '../Text'
import './styles.scss'
import IconButton from '../IconButton'
import Button from '../Button'

export enum KnowledgeBaseMode {
  DEFAULT = 'default',
  SELECTED = 'selected',
}

const KnowledgeBase = ({
  avatar,
  content,
  selected,
  title,
  onClick,
  directory,
  hideButton,
  kbIcon,
  disabledSelect,
  className,
  showPrice = false,
  isDelete = false,
  mode = KnowledgeBaseMode.DEFAULT,
  onDelete,
}: IKnowledgeBaseProps) => {
  const w = hideButton ? 'add-item-hide-button' : 'add-item-show-button'
  return (
    <div
      className={twMerge(
        'flex w-full select-none items-center gap-2 p-1',
        className
      )}
    >
      <div className="flex w-full max-w-full flex-col items-start justify-center overflow-hidden">
        <div className="flex w-full items-center">
          {kbIcon || (
            <Avatar
              name={'logo'}
              avatarUrl={avatar}
              avatarDefault={
                <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
                  <Icon
                    name="tool-01"
                    size={18}
                    gradient={['#642B734D', '#C6426E4D']}
                  />
                </div>
              }
              size="medium"
              hasBorder
              variant="square"
            />
          )}
          <div className={clsx('ml-2 mr-3 flex flex-1 flex-col gap-1', w)}>
            <Text
              type="subBody"
              variant="medium"
              className="text-Primary-Color"
              elementType="div"
              ellipsis
            >
              {title}
            </Text>
            {content && (
              <Text
                type="supportText"
                variant="regular"
                className="text-Secondary-Color"
                elementType="div"
                ellipsis
                multipleLine={2}
              >
                {content}
              </Text>
            )}
          </div>
        </div>
        {directory && (
          <div className="mt-1 flex w-full items-center justify-between">
            <Text
              type="subBody"
              variant="medium"
              elementType="div"
              className="bg-Main-Color bg-clip-text px-1 text-transparent"
            >
              {directory}
            </Text>

            {showPrice && (
              <div className="flex h-[15px] w-[38px] items-center justify-center rounded-full bg-Main-03 px-[8px]">
                <Text
                  type="supportText"
                  variant="medium"
                  className="bg-Main2-02 bg-clip-text text-transparent"
                >
                  Free
                </Text>
              </div>
            )}
          </div>
        )}
      </div>
      {!hideButton &&
        (mode === KnowledgeBaseMode.DEFAULT ? (
          <Button
            className={clsx(
              'rounded-md px-2',
              selected ? '!min-w-14' : '!min-w-16'
            )}
            text={selected ? 'Added' : 'Add'}
            type={selected ? 'default' : 'secondary'}
            disabled={disabledSelect || selected}
            size="small"
            onClick={() => !selected && onClick?.()}
          />
        ) : (
          <Button
            className="!min-w-16 rounded-md px-2"
            text="Remove"
            type="secondary"
            size="small"
            onClick={onClick}
          />
        ))}
      {isDelete && (
        <IconButton
          className="rounded-lg p-1"
          nameIcon="Trash-04"
          sizeIcon={16}
          // colorIcon={isError ? colors['Error-Color'] : '#766D72'}
          hoverColor={['#642B73', '#C6426E']}
          onClick={onDelete}
        />
      )}
    </div>
  )
}

export default memo(KnowledgeBase)

interface IKnowledgeBaseProps {
  className?: string
  showPrice?: boolean
  avatar: string
  title: string
  content: string
  selected: boolean
  onClick: () => void
  directory: string
  hideButton?: boolean
  kbIcon?: ReactNode
  disabledSelect?: boolean
  mode?: KnowledgeBaseMode.DEFAULT | KnowledgeBaseMode.SELECTED
  isDelete?: boolean
  onDelete?: () => void
}
