import Icon from '@/assets/icon/Icon'
import { getUrlImage } from '@/helpers'
import { useEffect, useRef, useState } from 'react'
import Message from '../Message'
import clsx from 'clsx'
import Avatar from '../Avatar'

interface UploadProps {
  className?: string
  multiple?: boolean
  dataTypes?: string
  onChange: (image: any) => void
  disabled?: boolean
  image: string | undefined | null
  onChangeFile?: (image: File | null) => void
  limit?: number
  nameUser?: string | null | undefined
}

function isBase64(str: string) {
  if (typeof str !== 'string') {
    return false
  }

  const base64PrefixPattern = /^data:image\/[a-zA-Z]+;base64,/
  if (base64PrefixPattern.test(str)) {
    str = str.replace(base64PrefixPattern, '')
  }

  const base64Pattern =
    /^(?:[A-Za-z0-9+/]{4})*?(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/
  return base64Pattern.test(str)
}
const UploadAvatar = (props: UploadProps) => {
  const {
    limit = 1048576,
    className,
    multiple = false,
    dataTypes = 'image/png, image/jpeg',
    onChange,
    onChangeFile,
    disabled = false,
    image,
    nameUser,
  } = props

  const inputFile = useRef(null)
  const background = useRef(null)

  const [hover, setHover] = useState(false)

  const [isDragging, setIsDragging] = useState(false)

  const [dataFormat, setDataFormat] = useState('')

  const handleFileInput = (e: any) => {
    const file = e.target.files[0]
    setHover(false)

    if (file) {
      if (file.size <= limit) {
        if (dataTypes.split(',').find((n: any) => n?.trim() === file.type)) {
          if (onChangeFile) onChangeFile(file)

          const reader = new FileReader()

          reader.onloadend = () => {
            onChange(reader.result)
          }
          reader.readAsDataURL(file)
        } else {
          onChange(null)
          Message.error({
            message: `Only accept ${dataFormat} format`,
          })
        }
      } else {
        onChange(null)
        Message.error({
          message: `Maximum file size: ${limit / 1024 ** 2}MB`,
        })
      }
      // @ts-expect-error: Unreachable code error
      inputFile.current.value = null
    }
  }

  const handleDrop = (e: any) => {
    e.preventDefault()
    setIsDragging(false)

    const file = e.dataTransfer.files[0]

    if (
      file &&
      dataTypes.split(',').find((n: any) => n?.trim() === file.type)
    ) {
      if (file.size <= limit) {
        if (onChangeFile) onChangeFile(file)

        const reader = new FileReader()

        reader.onloadend = () => {
          onChange(reader.result)
        }
        reader.readAsDataURL(file)
      } else {
        onChange(null)
        Message.error({
          message: `Maximum file size: ${limit / 1024 ** 2}MB`,
        })
      }
      inputFile.current = null
    } else if (
      !dataTypes.split(',').find((n: any) => n?.trim() === file.type)
    ) {
      onChange(null)
      Message.error({
        message: `Only accept ${dataFormat} format`,
      })
    }
  }

  const handleButtonClick = () => {
    // @ts-expect-error: Object is possibly 'null'.
    document.getElementById('fileInput').click()
  }

  const handleRemoveImage = (e: any) => {
    e.stopPropagation()
    onChange(undefined)
    if (onChangeFile) onChangeFile(null)
    // @ts-expect-error: Object is possibly 'null'.
    document.getElementById('fileInput').value = ''
  }
  const handleDragOver = (e: { preventDefault: () => void }) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (dataTypes) {
      setDataFormat(
        dataTypes
          .split(',')
          .map((n: string) => {
            return n.split('/')[1] === 'jpeg'
              ? ' jpg, jpeg'
              : ' '.concat(n.split('/')[1])
          })
          .join(',')
      )
    }
  }, [dataTypes])

  useEffect(() => {
    setHover(false)
  }, [image])

  return (
    <div
      ref={background}
      style={{
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundImage: image
          ? `url(${isBase64(image) ? image : getUrlImage(image)})`
          : 'none',
      }}
      onDrop={handleDrop}
      onClick={handleButtonClick}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      className={clsx(
        'relative flex h-[64px] w-[64px] cursor-pointer items-center justify-center rounded-full',
        className
      )}
    >
      <input
        ref={inputFile}
        id="fileInput"
        type="file"
        name="file"
        multiple={multiple}
        accept={dataTypes}
        onChange={handleFileInput}
        style={{ display: 'none' }}
        disabled={disabled}
      />
      {!image && (
        <Avatar
          className="h-[64px] w-[64px]"
          name={nameUser || ''}
          size="larger"
        />
      )}
      {!image && (hover || isDragging) && (
        <div className="absolute flex h-[64px] w-[64px] items-center justify-center rounded-full bg-[#00000066]">
          <Icon name="Customize-Upload" size={32} color="#FFFFFF" />
        </div>
      )}
      {image && hover && (
        <div
          onClick={(e) => handleRemoveImage(e)}
          className="absolute flex h-[64px] w-[64px] items-center justify-center rounded-full bg-[#00000066]"
        >
          <Icon name="Customize-DeleteFilled" size={32} color="#B91C1C" />
        </div>
      )}
    </div>
  )
}

export default UploadAvatar
