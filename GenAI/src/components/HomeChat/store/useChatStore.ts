import {
  HomeChatSessionPublic,
  WorkflowPublic,
  WorkflowPublicList,
} from '@/apis/client'
import { IMessage } from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { createSelectors } from '@/store/helpers'
import { omit } from 'lodash'
import { create } from 'zustand'
import {
  DEFAULT_HOME_CHAT_WORKFLOW_ID,
  DEFAULT_HOME_CHAT_WORKFLOW_NAME,
} from '../const'
import { ILocalFile } from '../ChatWrapper/helper'
import { v4 as uuid } from 'uuid'
import { PAGE_SIZE } from '@/constants'

export const FIELD_PRESERVE_FOR_NEW_CHAT = [
  'currentWorkflow',
  'currentWorkflowData',
  'isChatHistoryOpen',
  'histories',
  'isLoadInitHistory',
  'historiesCount',
  'topWfIds',
  'topWorkflowSelected',
] as (keyof State)[]

export const MESSAGES_PAGE_SIZE = PAGE_SIZE.FULL
export const DEFAULT_TOP_WORKFLOW_SELECTED = 'All'
export interface ICurrentWorkflow {
  id: string
  name: string
}
interface State {
  isChatHistoryOpen: boolean
  isLoadInitHistory: boolean
  activeHistory?: HomeChatSessionPublic
  historyTextSearch: string
  topWorkflowSelected: WorkflowPublicList | typeof DEFAULT_TOP_WORKFLOW_SELECTED
  histories?: Array<HomeChatSessionPublic>
  nextSessionId?: string
  nextCreatedAt?: number
  historiesCount: number

  isChatAtBottom: boolean
  currentWorkflow?: ICurrentWorkflow
  currentWorkflowData?: WorkflowPublic
  workflowData?: Record<string, WorkflowPublic>
  message: IMessage[]
  text: string

  files?: ILocalFile[]

  chatTitle?: string
  sessionId: string
  isLoadingPrevMessages?: boolean

  messagesCurrentPage: number
  messagesTotalPage: number

  topWfIds: string[]

  showScrollIcon: boolean
  isDisableChat: boolean
}

interface Action {
  resetStore: (ignoreFields?: (keyof State)[]) => void
  setChatAtBottom: (value: boolean) => void
  setIsChatHistoryOpen: (value: boolean) => void
  setCurrentWorkflow: (workflow?: ICurrentWorkflow) => void
  setCurrentWorkflowData: (workflow?: WorkflowPublic) => void
  setMessage: (
    nextAge:
      | State['message']
      | ((currentAge: State['message']) => State['message'])
  ) => void
  setText: (text: string) => void
  setFiles: (
    nextFiles: ILocalFile[] | ((currentFiles: ILocalFile[]) => ILocalFile[])
  ) => void
  setIsLoadInitHistory: (isLoadInitHistory: boolean) => void
  setHistories: (histories: HomeChatSessionPublic[]) => void
  addHistories: (histories: HomeChatSessionPublic[]) => void
  unshiftHistory: (history: HomeChatSessionPublic) => void
  moveHistoryToTop: (history: HomeChatSessionPublic) => void
  setHistoriesCount: (count: number) => void
  deleteAllHistories: () => void
  deleteHistory: (historyId: string) => void
  setNextSessionId: (nextSessionId?: string) => void
  setNextCreatedAt: (nextCreatedAt?: number) => void
  setChatTitle: (title?: string) => void
  setSessionId: (sessionId?: string) => void
  setIsLoadingPrevMessages: (isLoading?: boolean) => void
  setMessagesCurrentPage: (page: number) => void
  setMessagesTotalPage: (totalPage: number) => void
  addMessages: (messages: IMessage[]) => void
  setActiveHistory: (history?: HomeChatSessionPublic) => void
  setTopWfIds: (topWfIds: string[]) => void
  addTopWfIdsToBottom: (topWfIds: string[]) => void
  addWorkflowData: (...data: WorkflowPublic[]) => void
  setShowScrollIcon: (showScrollIcon: boolean) => void
  setTopWorkflowSelected: (
    topWorkflowSelected:
      | WorkflowPublicList
      | typeof DEFAULT_TOP_WORKFLOW_SELECTED
  ) => void
  setHistoryTextSearch: (textSearch?: string) => void
  setIsDisableChat: (isDisableChat: boolean) => void
}

const initialState: State = {
  isChatHistoryOpen: false,
  isLoadInitHistory: true,
  isChatAtBottom: false,
  currentWorkflow: {
    id: DEFAULT_HOME_CHAT_WORKFLOW_ID,
    name: DEFAULT_HOME_CHAT_WORKFLOW_NAME,
  },
  currentWorkflowData: undefined,
  workflowData: undefined,
  message: [],
  text: '',
  files: [],

  topWorkflowSelected: DEFAULT_TOP_WORKFLOW_SELECTED,
  historyTextSearch: '',
  histories: [],
  historiesCount: 0,
  nextSessionId: undefined,
  nextCreatedAt: undefined,
  chatTitle: undefined,
  sessionId: uuid(),
  isLoadingPrevMessages: false,
  messagesCurrentPage: 1,
  messagesTotalPage: 0,
  activeHistory: undefined,
  topWfIds: [],

  showScrollIcon: false,
  isDisableChat: false,
}

const useUniversalChatStoreBase = create<State & Action>((set) => ({
  ...initialState,

  resetStore: (ignoreFields = []) => {
    set(omit(initialState, ignoreFields))
  },
  setChatAtBottom: (value) => {
    set({ isChatAtBottom: value })
  },
  setIsChatHistoryOpen: (value) => {
    set({ isChatHistoryOpen: value })
  },
  setCurrentWorkflow: (workflow) => {
    set({ currentWorkflow: workflow })
  },
  setCurrentWorkflowData: (workflow) => {
    set({ currentWorkflowData: workflow })
  },
  addWorkflowData: (...data) => {
    set((state) => {
      const newWorkflowData = { ...state.workflowData }
      data.forEach((workflow) => {
        newWorkflowData[workflow.id] = workflow
      })
      return { workflowData: newWorkflowData }
    })
  },
  setMessage: (updateMessage) => {
    set((state) => {
      return {
        message:
          typeof updateMessage === 'function'
            ? updateMessage(state.message)
            : updateMessage,
      }
    })
  },
  addMessages: (messages) => {
    set((state) => {
      // remove duplicate messages
      const uniqueMessages = messages.filter(
        (message) => !state.message.some((m) => m.id === message.id)
      )

      return {
        message: [...state.message, ...uniqueMessages],
      }
    })
  },
  setText: (text) => {
    set({ text })
  },
  setFiles: (nextFiles) => {
    set((state) => {
      return {
        files:
          typeof nextFiles === 'function'
            ? nextFiles(state.files ?? [])
            : nextFiles,
      }
    })
  },
  setIsLoadInitHistory: (isLoadInitHistory) => {
    set({ isLoadInitHistory })
  },
  moveHistoryToTop: (history) => {
    set((state) => {
      // Only move history to top if topWorkflowSelected is DEFAULT_TOP_WORKFLOW_SELECTED
      if (state.topWorkflowSelected !== DEFAULT_TOP_WORKFLOW_SELECTED) {
        return { histories: state.histories }
      }

      // check if historyId is in top, do not move
      const isHistoryIdInTop =
        state.histories?.[0]?.session_id === history.session_id
      if (isHistoryIdInTop) return { histories: state.histories }

      const histories = state.histories?.filter(
        (h) => h.session_id !== history.session_id
      ) as HomeChatSessionPublic[]

      // update topWfIds
      const uniqueWfIds = Array.from(
        new Set([history.workflow_id, ...state.topWfIds])
      ).slice(0, MAX_TOP_WORKFLOWS)

      return {
        histories: [
          {
            ...history,
            created_at: new Date().toISOString(),
          },
          ...histories,
        ],
        topWfIds: uniqueWfIds,
      }
    })
  },
  unshiftHistory: (history) => {
    set((state) => {
      if (state.isLoadInitHistory) return {}

      let currentCount = state.historiesCount ?? 0
      const histories = [...(state.histories ?? [])]
      if (
        state.topWorkflowSelected === DEFAULT_TOP_WORKFLOW_SELECTED ||
        state.topWorkflowSelected.id === history.workflow_id
      ) {
        currentCount += 1
        histories.unshift(history)
      }

      // Update topWfIds if the history's workflow_id is not in the topWfIds
      const uniqueWfIds = Array.from(
        new Set([history.workflow_id, ...state.topWfIds])
      ).slice(0, MAX_TOP_WORKFLOWS)

      return {
        histories,
        historiesCount: currentCount,
        topWfIds: uniqueWfIds,
      }
    })
  },
  setHistories: (histories) => {
    set((state) => {
      // update topWfIds
      let topWfIds = state.topWfIds
      if (state.topWorkflowSelected === 'All' && !state.historyTextSearch) {
        topWfIds = Array.from(
          new Set([
            ...topWfIds,
            ...histories.map((history) => history.workflow_id),
          ])
        ).slice(0, MAX_TOP_WORKFLOWS)
      }

      return {
        topWfIds,
        histories,
      }
    })
  },
  addHistories: (histories) => {
    set((state) => {
      if (state.isLoadInitHistory || !histories || histories.length === 0)
        return {}

      // remove duplicate histories by session_id and push new histories
      const uniqueHistories = histories.filter(
        (history) =>
          !state.histories?.some((h) => h.session_id === history.session_id)
      )

      // update topWfIds
      let uniqueWfIds = state.topWfIds
      if (state.topWorkflowSelected === 'All' && !state.historyTextSearch) {
        uniqueWfIds = Array.from(
          new Set([
            ...uniqueHistories.map((history) => history.workflow_id),
            ...state.topWfIds,
          ])
        ).slice(0, MAX_TOP_WORKFLOWS)
      }

      return {
        topWfIds: uniqueWfIds,
        histories: [...(state.histories ?? []), ...uniqueHistories],
      }
    })
  },
  deleteHistory: (historyId) => {
    set((state) => {
      const histories = state.histories?.filter(
        (history) => history.id !== historyId
      )

      return {
        histories,
        historiesCount: state.historiesCount - 1,
      }
    })
  },
  deleteAllHistories: () => {
    set(() => ({
      histories: [],
      historiesCount: 0,
    }))
  },
  setNextSessionId: (nextSessionId) => {
    set({ nextSessionId })
  },
  setNextCreatedAt: (nextCreatedAt) => {
    set({ nextCreatedAt })
  },
  setChatTitle: (title) => {
    set({ chatTitle: title })
  },
  setSessionId: (sessionId) => {
    set({ sessionId })
  },
  setIsLoadingPrevMessages: (isLoading) => {
    set({ isLoadingPrevMessages: isLoading })
  },
  setMessagesCurrentPage: (page) => {
    set({ messagesCurrentPage: page })
  },
  setMessagesTotalPage: (totalPage) => {
    set({ messagesTotalPage: totalPage })
  },
  setActiveHistory: (history) => {
    set({ activeHistory: history })
  },
  setTopWfIds: (topWfIds) => {
    set({ topWfIds })
  },
  addTopWfIdsToBottom: (Ids) => {
    set((state) => {
      if (state.topWfIds.length >= MAX_TOP_WORKFLOWS) {
        return {
          topWfIds: state.topWfIds,
        }
      }

      const newTopWfIds = [...state.topWfIds, ...Ids]
      const uniqueTopWfIds = Array.from(new Set(newTopWfIds))
      return {
        topWfIds: uniqueTopWfIds.slice(0, MAX_TOP_WORKFLOWS),
      }
    })
  },
  setHistoriesCount: (count) => {
    set({ historiesCount: count })
  },
  setShowScrollIcon: (showScrollIcon) => {
    set({ showScrollIcon })
  },
  setTopWorkflowSelected: (topWorkflowSelected) => {
    set({ topWorkflowSelected })
  },
  setHistoryTextSearch: (textSearch) => {
    set({ historyTextSearch: textSearch })
  },
  setIsDisableChat: (isDisableChat) => {
    set({ isDisableChat })
  },
}))

export const MAX_TOP_WORKFLOWS = 3

const useChatStore = createSelectors(useUniversalChatStoreBase)

export default useChatStore
