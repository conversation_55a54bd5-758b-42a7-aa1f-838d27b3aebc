import { cn } from '@/helpers'
import {
  EMessageType,
  MAX_LENGTH,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { nanoid } from 'nanoid'
import { forwardRef, memo, useEffect } from 'react'
import useChatStore, { ICurrentWorkflow } from '../store/useChatStore'
import TextAreaMessage, { TextAreaRef } from './TextAreaMessage'
import {
  ILocalMessageParams,
  MAX_FILE_SIZE_MB,
  getErrorValidateFileWithMax,
} from './helper'

interface Props {
  responding: boolean
  text: string

  userMessageAvatar?: React.ReactNode

  setText: (text: string) => void

  // resetHandler
  // onClickNewChat: () => void

  // chatHandler
  onSend: (message: string) => void

  // attach files
  handleFiles: (type: EMessageType, params: ILocalMessageParams) => void
  isAttachingFile?: boolean

  isWebSearchOn?: boolean
  setWebSearchMode: (value: boolean) => void

  isChatAtBottom: boolean
  currentWorkflow: ICurrentWorkflow | undefined
  isDisableChat: boolean
}

const SendMessageWrapper = forwardRef<TextAreaRef, Props>(
  (
    {
      responding,

      text,

      setText,
      // onClickNewChat,
      onSend,

      handleFiles,
      isAttachingFile = false,

      isWebSearchOn = false,
      isChatAtBottom,
      setWebSearchMode,
      currentWorkflow,
      isDisableChat,
    }: Props,
    ref
  ) => {
    const [files, setFiles] = useChatStore((state) => [
      state.files,
      state.setFiles,
    ])

    useEffect(() => {
      if (isWebSearchOn) setFiles([])
    }, [isWebSearchOn])

    const _handleFiles = (files: FileList) => {
      const localFiles = Array.from(files).map((file) => {
        return {
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          id: nanoid(),
          error: getErrorValidateFileWithMax(
            file.size,
            file.type,
            MAX_FILE_SIZE_MB
          ),
        }
      })

      setFiles((prev) => [...prev, ...localFiles])
    }

    const _handleSend = (type: EMessageType, params: ILocalMessageParams) => {
      switch (type) {
        case EMessageType.user:
          onSend(params.text ?? '')
          break
        case EMessageType.fileUploadMessage: {
          const { files, text } = params
          const filesFilteredError = files?.filter((file) => !file.error)

          if (filesFilteredError?.length === 0) {
            onSend(text!)
            setFiles([])

            return
          }
          setFiles([])
          handleFiles(type, {
            ...params,
            files: filesFilteredError,
          })
          break
        }

        default:
          break
      }
    }

    return (
      <TextAreaMessage
        ref={ref}
        className={cn(
          'h-[unset] max-h-[128px] min-h-[80px] text-Primary-Color',
          {
            'min-h-[55px]': isChatAtBottom,
          }
        )}
        classNameTextAreaWrapper={cn({
          'min-h-[55px]': isChatAtBottom,
        })}
        maxLength={MAX_LENGTH}
        maxHeight={128}
        onChange={setText}
        value={text}
        onSend={_handleSend}
        onBlur={() => setText(text.trim())}
        iconsBtnDisabled={text.trimStart().trimEnd().length === 0 || responding}
        files={files}
        handleDeleteFile={(file) => {
          setFiles((prev) => prev.filter((item) => item.id !== file.id))
        }}
        handleFiles={_handleFiles}
        isAttachingFile={isAttachingFile}
        isWebSearchOn={isWebSearchOn}
        setWebSearchMode={setWebSearchMode}
        currentWorkflow={currentWorkflow}
        isDisableChat={isDisableChat}
      />
    )
  }
)

export default memo(SendMessageWrapper)
