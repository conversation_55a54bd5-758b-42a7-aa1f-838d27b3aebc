import Tooltip from '@/components/Tooltip'
import { cn } from '@/helpers'
import { colors } from '@/theme'
import { useCallback, useRef } from 'react'

interface Props {
  multiple?: boolean
  loading?: any
  disabled?: boolean
  dataTypes?: any
  sendFiles?: (value: any) => void
}

const AttachFile = ({
  multiple = true,
  dataTypes = [],
  loading = false,
  disabled = false,
  sendFiles,
}: Props) => {
  const inpRef = useRef<HTMLInputElement>(null)

  const onUpload = useCallback(
    (e: any) => {
      e.preventDefault()

      const files = [...e.target.files]

      sendFiles?.(files)
      e.target.value = ''
    },
    [multiple]
  )

  return (
    <>
      <input
        ref={inpRef}
        type="file"
        name="file"
        multiple={multiple}
        accept={dataTypes?.join(', ')}
        onChange={onUpload}
        style={{ display: 'none' }}
        disabled={disabled || loading}
      />

      <Tooltip
        text={
          disabled
            ? 'Attachments disabled for Web search mode'
            : 'Max 200MB, support jpg, png, csv, pdf, txt, docx'
        }
        position="top"
        align="center"
        tooltipClassName={{
          root: '!max-w-[400px]',
        }}
      >
        <div
          className={cn(
            'flex size-[32px] min-w-[32px] items-center justify-center rounded-full border bg-Input-Field p-[6px]',
            disabled
              ? 'cursor-not-allowed border-Disable'
              : 'cursor-pointer border-border-base-icon hover:bg-neutral-200'
          )}
          onClick={() => inpRef?.current?.click()}
        >
          <div className="size-[20px]">
            <svg
              width="21"
              height="21"
              viewBox="0 0 21 21"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                // eslint-disable-next-line max-len
                d="M15.6228 3.83723C14.7278 2.94228 13.2768 2.94228 12.3819 3.83723L4.86884 11.3502C3.40438 12.8147 3.40438 15.1891 4.86884 16.6535C6.33331 18.118 8.70768 18.118 10.1721 16.6535L17.6852 9.14053C17.9292 8.89645 18.325 8.89645 18.569 9.14053C18.8131 9.38461 18.8131 9.78034 18.569 10.0244L11.056 17.5374C9.1034 19.49 5.93758 19.49 3.98496 17.5374C2.03234 15.5848 2.03234 12.419 3.98496 10.4664L11.498 2.95335C12.8811 1.57024 15.1235 1.57024 16.5066 2.95335C17.8897 4.33645 17.8897 6.57891 16.5066 7.96202L9.28826 15.1804C8.47467 15.994 7.15557 15.994 6.34198 15.1804C5.52839 14.3668 5.52839 13.0477 6.34198 12.2341L12.6765 5.89962C12.9206 5.65555 13.3163 5.65555 13.5604 5.89962C13.8044 6.1437 13.8044 6.53943 13.5604 6.78351L7.22586 13.118C6.90043 13.4434 6.90043 13.9711 7.22586 14.2965C7.5513 14.622 8.07894 14.622 8.40438 14.2965L15.6228 7.07814C16.5177 6.18318 16.5177 4.73218 15.6228 3.83723Z"
                fill={
                  disabled ? colors['Disable-Text'] : colors['Tertiary-Color']
                }
              />
            </svg>
          </div>
        </div>
      </Tooltip>
    </>
  )
}

export default AttachFile
