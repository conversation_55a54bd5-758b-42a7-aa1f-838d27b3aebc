import clsx from 'clsx'
import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'

import Icon from '@/assets/icon/Icon'
import { ArrAIOperation } from '@/components/PlaygroundMultiAgent/helper'
import Text from '@/components/Text'
import Tooltip from '@/components/Tooltip'
import { cn } from '@/helpers'
import {
  EMessageType,
  VALID_DATA_TYPES_FILE_UPLOAD,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { colors } from '@/theme'
import { twMerge } from 'tailwind-merge'
import AttachFile from '../AttachFile'
import { ILocalFile, ILocalMessageParams } from '../helper'
import FileWrapper from './FileWrapper'
import IconCustomizeExtract from './Icons/IconCustomizeExtract'
import IconCustomizeSummarize from './Icons/IconCustomizeSummarize'
import IconQueryAndSearch from './Icons/IconQueryAndSearch'
import IconSend from './Icons/IconSend'
import { ICurrentWorkflow } from '../../store/useChatStore'

interface IProps {
  label?: string
  className?: string
  classNameWrapper?: string
  value?: string
  onChange: (value: string) => void
  maxLength?: number
  autoResize?: boolean
  maxHeight?: number
  onSend?: (type: EMessageType, params: ILocalMessageParams) => void
  onBlur?: () => void
  onFocus?: () => void
  isAutofocus?: boolean
  textAreaClassName?: string
  isSubtext?: boolean
  helperText?: string
  files?: ILocalFile[]
  handleDeleteFile: (file: ILocalFile) => void
  iconsBtnDisabled?: boolean
  handleFiles: (files: FileList) => void
  classNameTextAreaWrapper?: string
  responding?: boolean
  isAttachingFile?: boolean
  isWebSearchOn?: boolean
  setWebSearchMode: (value: boolean) => void
  currentWorkflow: ICurrentWorkflow | undefined
  isDisableChat: boolean
}

export interface TextAreaRef {
  blur: () => void
  focus: () => void
}

const TextArea = forwardRef<TextAreaRef, IProps>(
  (
    {
      className,
      classNameTextAreaWrapper,
      value,
      onChange,
      maxLength = 255,
      autoResize = false,
      maxHeight = 200,
      onSend,
      onBlur,
      onFocus,
      isAutofocus = false,
      textAreaClassName = '',
      files,
      handleDeleteFile,
      iconsBtnDisabled,
      handleFiles,
      responding = false,
      isAttachingFile = false,
      setWebSearchMode,
      isWebSearchOn,
      currentWorkflow,
      isDisableChat,
    }: IProps,
    ref
  ) => {
    const [isFocus, setFocus] = useState(isAutofocus ?? false)
    const textAreaRef = useRef<HTMLTextAreaElement>(null)

    const isHaveFiles = Boolean(files?.length)

    const adjustHeight = () => {
      textAreaRef.current!.style.height = 'inherit'

      textAreaRef.current!.style.height = `${textAreaRef.current!.scrollHeight > maxHeight ? maxHeight : textAreaRef.current!.scrollHeight}px`
    }

    useEffect(() => {
      if (isFocus) {
        // Set the cursor to the last character
        textAreaRef.current!.selectionStart = value?.length ?? 0
        textAreaRef.current!.selectionEnd = value?.length ?? 0
      }
    }, [isFocus])

    useEffect(() => {
      if (autoResize) adjustHeight()
    }, [value])

    useImperativeHandle(ref, () => ({
      blur: () => onBlur?.(),
      focus: () => textAreaRef.current?.focus(),
    }))

    const handleSend = (value?: string, intent?: string) => {
      if (isHaveFiles) {
        if (!intent && !value?.trim()) return

        onSend?.(EMessageType.fileUploadMessage, {
          text: value,
          files,
          intent,
        })
        return
      } else {
        onSend?.(EMessageType.user, { text: value })
      }
    }

    return (
      <div
        className={cn(
          'flex w-full flex-col gap-[4px] rounded-[24px] border border-border-base-icon bg-white py-[8px] pl-[12px] pr-[8px] shadow-base',
          {
            'border shadow-focus outline-none ring-transparent': isFocus,
          }
        )}
      >
        {isHaveFiles && (
          <div className="flex w-full flex-col gap-2">
            <div className="flex w-full flex-col gap-[8px]">
              <div className="genai-scrollbar flex max-h-[86px] w-full flex-wrap gap-[6px] overflow-y-auto">
                {files?.map((file) => (
                  <FileWrapper
                    file={file}
                    key={file.id}
                    className="h-[39px] w-[180px] overflow-hidden rounded-[8px] border border-border-base-icon px-[8px] py-0"
                    classNameFileItem="justify-start gap-[8px] overflow-hidden !p-0"
                    onClickDeleteItem={() => {
                      handleDeleteFile(file)
                    }}
                  />
                ))}
              </div>

              <div className="h-[26px] w-full">
                <div className="genai-scrollbar flex select-none items-center gap-[8px] overflow-x-auto">
                  {ArrAIOperation.map((item) => (
                    <div
                      key={item.id}
                      className={cn(
                        'flex h-[26px] min-w-fit cursor-pointer items-center justify-center gap-[4px] rounded-[8px] bg-neutral-100 px-[8px] hover:bg-neutral-200'
                      )}
                      onClick={() => {
                        handleSend(value, item.id)
                      }}
                    >
                      <div className="flex size-[16px] min-w-[16px] items-center justify-center">
                        {item.id === 'Summarize content' ? (
                          <IconCustomizeSummarize />
                        ) : item.id === 'Extract information' ? (
                          <IconCustomizeExtract />
                        ) : item.id === 'Query and search' ? (
                          <IconQueryAndSearch />
                        ) : (
                          <IconCustomizeSummarize />
                        )}
                      </div>

                      <Text
                        type="subBody"
                        variant="medium"
                        className="text-Primary-Color"
                      >
                        {item.name}
                      </Text>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="h-[1px] w-full bg-neutral-100" />
          </div>
        )}

        <div className={clsx(twMerge('h-32', className), 'flex flex-col')}>
          <div
            className={cn(
              'genai-scrollbar flex h-full min-h-[84px] w-full resize-none flex-col rounded-lg px-[6px] py-[8px] text-subBody text-Primary-Color placeholder:text-Placeholder focus:shadow-focus',

              {
                'min-h-[79px]': isHaveFiles,
              },
              classNameTextAreaWrapper
            )}
            style={{
              maxHeight: maxHeight,
            }}
            onClick={() => {
              setFocus(true)
              onFocus?.()
              textAreaRef.current?.focus()
            }}
            onBlur={() => setFocus(false)}
          >
            <textarea
              ref={textAreaRef}
              value={value}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSend?.(e.currentTarget.value)
                }
              }}
              onChange={(e) => {
                onChange(e.target.value)
              }}
              placeholder="Ask anything"
              maxLength={maxLength}
              className={clsx(
                textAreaClassName,
                'genai-scrollbar h-full w-full resize-none bg-transparent text-subBody text-Primary-Color outline-0 placeholder:text-Placeholder-Text'
              )}
              onBlur={onBlur}
              autoFocus={isAutofocus}
              disabled={!currentWorkflow || isDisableChat}
            />
          </div>
        </div>

        <div className="w-full py-[4px]">
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2">
              <AttachFile
                sendFiles={handleFiles}
                dataTypes={VALID_DATA_TYPES_FILE_UPLOAD}
                loading={isAttachingFile || responding}
                disabled={isWebSearchOn || !currentWorkflow || isDisableChat}
              />

              <Tooltip
                text={
                  isWebSearchOn
                    ? 'Answer without internet search'
                    : 'Answer with internet search'
                }
              >
                <div
                  onClick={() => {
                    if (!currentWorkflow || isDisableChat) return
                    setWebSearchMode(!isWebSearchOn)
                  }}
                  className={cn(
                    'flex cursor-pointer select-none items-center justify-center gap-[4px] rounded-full border border-border-base-icon bg-Input-Field px-[8px] py-[6px]',
                    'hover:bg-neutral-200',
                    {
                      'border-lime-100 bg-lime-100 hover:bg-lime-200':
                        isWebSearchOn,
                      'cursor-not-allowed border-Disable hover:bg-Input-Field':
                        !currentWorkflow || isDisableChat,
                    }
                  )}
                >
                  <Icon
                    name="Outline-Map&Location-Global"
                    size={20}
                    color={
                      isWebSearchOn
                        ? colors.lime[500]
                        : !currentWorkflow || isDisableChat
                          ? colors['Disable-Text']
                          : colors['Tertiary-Color']
                    }
                  />
                  <Text
                    elementType="div"
                    type="subBody"
                    className={cn('text-Tertiary-Color', {
                      'text-lime-500': isWebSearchOn,
                      'text-Disable-Text': !currentWorkflow || isDisableChat,
                    })}
                  >
                    Web search
                  </Text>
                </div>
              </Tooltip>
            </div>

            <div
              className={cn(
                'flex size-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-full bg-Secondary-Color-2 p-[6px]',
                {
                  'bg-Base-04': iconsBtnDisabled,
                }
              )}
              onClick={() => {
                if (iconsBtnDisabled) return
                handleSend(value)
              }}
            >
              <IconSend
                iconsBtnDisabled={
                  iconsBtnDisabled || !currentWorkflow || isDisableChat
                }
              />
            </div>
          </div>
        </div>
      </div>
    )
  }
)

export default memo(TextArea)
