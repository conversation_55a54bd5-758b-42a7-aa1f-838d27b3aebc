import Icon from '@/assets/icon/Icon'
import { DEBOUNCE_TIME } from '@/constants'
import { cn } from '@/helpers'
import { colors } from '@/theme'
import { debounce } from 'lodash'
import {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'

interface IProps {
  className?: string
  onPressEnter?: (e: any) => void
  onSearch?: (query: string) => void
  placeholder?: string
  searchText?: string
  setSearchText?: (text: string) => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
}

export interface SearchBarInterface {
  resetSearchBar: () => void
}

const SearchInput = forwardRef(
  (
    {
      placeholder = 'Search by keywords',
      className,
      searchText,
      setSearchText,
      onPressEnter,
      onSearch,
      onKeyDown,
    }: IProps,
    ref
  ) => {
    const inpRef = useRef(null)

    const [isFocus, setFocused] = useState(false)

    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
          onPressEnter?.(event)
          setSearchText?.(searchText?.trim() ?? '')
        }

        onKeyDown?.(event)
      },
      [onPressEnter, searchText]
    )
    useImperativeHandle(
      ref,
      () => ({
        resetSearchBar: () => {
          setSearchText?.('')
        },
      }),
      []
    )

    const debouncedSearch = useCallback(
      debounce((value: string) => {
        onSearch?.(value.trim())
      }, DEBOUNCE_TIME),
      []
    )

    const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newQuery = event.target.value
      setSearchText?.(newQuery)
      debouncedSearch(newQuery)
    }

    const onFocus = useCallback(() => setFocused(true), [])
    const onBlur = useCallback(() => {
      setSearchText?.(searchText?.trim() ?? '')
      setFocused(false)
    }, [searchText])

    return (
      <div
        id="search-bar"
        className={cn(
          'box-border flex h-[28px] w-[191px] items-center gap-[4px] rounded-[8px] bg-white px-[8px] py-[6px]',
          isFocus && 'border border-Base-Neutral shadow-focus',
          className
        )}
      >
        {
          <div className="cursor-pointer" onClick={onPressEnter}>
            <Icon
              name="Icon-Solid-Search"
              size={16}
              color={isFocus ? colors['Base-Neutral'] : colors.neutral[300]}
            />
          </div>
        }
        <input
          className="flex-grow bg-transparent text-supportText font-medium text-Primary-Color outline-0 placeholder:text-supportText placeholder:font-medium"
          ref={inpRef}
          value={searchText}
          placeholder={placeholder}
          onChange={(e) => handleSearch(e)}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          onBlur={onBlur}
        />
      </div>
    )
  }
)

export default memo(SearchInput)
