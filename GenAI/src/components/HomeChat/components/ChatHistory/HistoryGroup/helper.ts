import moment from 'moment'

/**
 * Converts ISO time string to a friendly format:
 * - "Today" for dates from today
 * - "Yesterday" for dates from yesterday
 * - "Feb 10, 2025" format for other dates
 *
 * @param isoTimeString - ISO format time string
 * @returns Friendly formatted date string
 */
export const formatDateFromIso = (isoTimeString: string): string => {
  const date = moment(isoTimeString)
  const today = moment().startOf('day')
  const yesterday = moment().subtract(1, 'days').startOf('day')

  if (date.isSame(today, 'day')) {
    return 'Today'
  }

  if (date.isSame(yesterday, 'day')) {
    return 'Yesterday'
  }

  return date.format('MMM D, YYYY')
}

export function removeSpanTags(input: string): string {
  if (!input) return input

  // Replace opening and closing span tags with empty string
  // This handles basic span tags with or without attributes
  return input
    .replace(/<span\s+class\s*=\s*["']gen-highlight["'][^>]*>/gi, '')
    .replace(/<\/span>/g, '')
}

/**
 * Trims text around a highlight span to fit within maxLength, preserving the span
 * @param input - Input string with highlight span
 * @param maxLength - Maximum allowed length for the result
 * @returns Object with trimmed text and flags indicating if text was cut
 */
export function trimCharactersAroundSpan(
  input: string,
  maxLength: number
): { text: string; isCutBefore: boolean; isCutAfter: boolean } {
  if (!input || input.length <= maxLength) {
    return { text: input, isCutBefore: false, isCutAfter: false }
  }

  const highlightSpanStart = '<span class = "gen-highlight">'
  const highlightSpanEnd = '</span>'

  const spanStartIndex = input.indexOf(highlightSpanStart)
  // If no highlight span was found, handle simple truncation
  if (spanStartIndex === -1) {
    const halfLength = Math.floor(maxLength / 2)
    return {
      text:
        input.substring(0, halfLength) +
        '...' +
        input.substring(input.length - halfLength),
      isCutBefore: true,
      isCutAfter: true,
    }
  }

  // Find the corresponding closing tag for our highlight span
  // We need to count opening and closing tags to find the correct one
  let spanEndIndex = -1
  let openTagCount = 0
  const searchStartPos = spanStartIndex + highlightSpanStart.length

  for (let i = searchStartPos; i < input.length; i++) {
    if (
      input.substring(i, i + highlightSpanStart.length) === highlightSpanStart
    ) {
      openTagCount++
      i += highlightSpanStart.length - 1 // Skip ahead
    } else if (
      input.substring(i, i + highlightSpanEnd.length) === highlightSpanEnd
    ) {
      if (openTagCount === 0) {
        spanEndIndex = i
        break
      } else {
        openTagCount--
        i += highlightSpanEnd.length - 1 // Skip ahead
      }
    }
  }

  // If we couldn't find the closing tag, use simple truncation
  if (spanEndIndex === -1) {
    const halfLength = Math.floor(maxLength / 2)
    return {
      text:
        input.substring(0, halfLength) +
        '...' +
        input.substring(input.length - halfLength),
      isCutBefore: true,
      isCutAfter: true,
    }
  }

  // Calculate indices and lengths
  const spanEndWithTagIndex = spanEndIndex + highlightSpanEnd.length
  const spanTagsLength = highlightSpanStart.length + highlightSpanEnd.length
  const spanContentLength =
    spanEndIndex - (spanStartIndex + highlightSpanStart.length)

  // Get total length we have to work with for text before and after span
  const remainingLength = maxLength - spanTagsLength - spanContentLength

  if (remainingLength <= 0) {
    // Just show the span content if span itself is too long
    return {
      text: input.substring(spanStartIndex, spanEndWithTagIndex),
      isCutBefore: true,
      isCutAfter: true,
    }
  }

  // Divide remaining space before and after the span
  const lengthBefore = Math.floor(remainingLength / 2)
  const lengthAfter = remainingLength - lengthBefore

  // Calculate start and end positions
  let startPos = spanStartIndex - lengthBefore
  let endPos = spanEndWithTagIndex + lengthAfter

  // Adjust if we don't need to trim on one side
  if (startPos < 0) {
    startPos = 0
    endPos = Math.min(input.length, spanEndWithTagIndex + remainingLength)
  } else if (endPos > input.length) {
    endPos = input.length
    startPos = Math.max(
      0,
      spanStartIndex - (remainingLength - (input.length - spanEndWithTagIndex))
    )
  }

  return {
    text: input.substring(startPos, endPos),
    isCutBefore: startPos > 0,
    isCutAfter: endPos < input.length,
  }
}

/**
 * Cleans up a string by removing newlines and replacing multiple consecutive spaces with a single space
 * @param input - String to process
 * @returns Clean string with no newlines and no consecutive spaces
 */
export const cleanupString = (input: string): string => {
  if (!input) return input

  const withoutNewlines = input.trim().replace(/\n/g, '')

  return withoutNewlines.replace(/\s{2,}/g, ' ')
}

export const getTextSearch = (textSearch: string, maxLength = 67) => {
  if (!textSearch) return ''
  const result = trimCharactersAroundSpan(cleanupString(textSearch), maxLength)
  return `${result.isCutBefore ? '...' : ''}${result.text}${
    result.isCutAfter ? '...' : ''
  }`
}
