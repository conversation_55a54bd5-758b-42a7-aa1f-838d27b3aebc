import Text from '@/components/Text'
import { cn } from '@/helpers'
import useChatStore, {
  DEFAULT_TOP_WORKFLOW_SELECTED,
  FIELD_PRESERVE_FOR_NEW_CHAT,
  MESSAGES_PAGE_SIZE,
} from '../../store/useChatStore'
import SearchInput from './SearchInput'
import IconDeleteAll from './IconDeleteAll'
import HistoryGroup from './HistoryGroup'
import WorkflowList from './WorkflowList'
import { Fragment, useEffect, useMemo, useRef, useState } from 'react'
import EmptyHistory from './EmptyHistory'
import NoFoundData from './EmptyHistory/NoFoundData'
import HistorySkeleton from './HistorySkeleton'
import {
  HomeChatSessionPublic,
  WorkflowPublic,
  WorkflowPublicList,
  chatBaseHistoryDeleteHomeChatSessionApi,
  chatBaseHistoryGetChatBaseHistoryApi,
  chatBaseHistorySearchHomeChatSessionApi,
  workflowCheckWorkflowExistsApi,
  workflowReadWorkflowByIdApi,
} from '@/apis/client'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { debounce, groupBy, map, size } from 'lodash'
import moment from 'moment'
import { MessageDialog } from '@/components/DialogMessage'
import { v4 as uuid } from 'uuid'
import SelectWfSkeleton from '../Skeleton/SelectWfSkeleton'
import { transformWorkflowConversationsHistoryToImessage } from '../../helper'
import {
  DEFAULT_HOME_CHAT_WORKFLOW_ID,
  DEFAULT_HOME_CHAT_WORKFLOW_NAME,
  UNKNOWN_WORKFLOW,
} from '../../const'

const ChatHistory = () => {
  const isShowHistory = useChatStore.use.isChatHistoryOpen()
  const {
    histories,
    setHistories,
    nextCreatedAt,
    nextSessionId,
    setNextCreatedAt,
    setNextSessionId,
    isLoadInitHistory,
    setIsLoadInitHistory,
    setChatAtBottom,
    sessionId,
    setSessionId,
    currentWorkflow,
    setCurrentWorkflow,
    setIsLoadingPrevMessages,
    setMessage,
    setChatTitle,
    setMessagesTotalPage,
    activeHistory,
    setActiveHistory,
    deleteHistory,
    resetStore,
    addHistories,
    isChatHistoryOpen,
    topWfIds,
    setTopWfIds,
    historiesCount,
    setHistoriesCount,
    deleteAllHistories,
    addWorkflowData,
    setShowScrollIcon,
    topWorkflowSelected,
    setTopWorkflowSelected,
    historyTextSearch: textSearch,
    setHistoryTextSearch: setTextSearch,
    setText,
    setIsDisableChat,
  } = useChatStore()

  const [isLoadFilterData, setIsLoadFilterData] = useState(false)

  const [topWorkflows, setTopWorkflows] = useState<Array<WorkflowPublicList>>(
    []
  )
  const [isLoadingWfInfo, setIsLoadingWfInfo] = useState(false)

  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const blockCallApiRef = useRef(false)

  const fetchHistoryData = useRef(
    debounce(
      async ({
        textSearch,
        nextSessionId,
        nextCreatedAt,
        topWorkflowSelected,
        isLoadInitHistory,
      }: {
        textSearch?: string
        nextSessionId?: string
        nextCreatedAt?: number
        topWorkflowSelected: WorkflowPublicList | 'All'
        isLoadInitHistory?: boolean
        activeHistory?: HomeChatSessionPublic
      }) => {
        if (blockCallApiRef.current) {
          blockCallApiRef.current = false
          return
        }

        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }

        const abortController = new AbortController()
        abortControllerRef.current = abortController

        if (!isLoadInitHistory) {
          setIsLoadFilterData(true)
        }

        try {
          const res = await chatBaseHistorySearchHomeChatSessionApi({
            query: {
              page_size: PAGE_SIZE.EXTRA_LARGE,
              message_query: textSearch,
              next_session_id: nextSessionId,
              next_created_at: nextCreatedAt,
              workflow_id:
                topWorkflowSelected && topWorkflowSelected !== 'All'
                  ? topWorkflowSelected.id
                  : undefined,
            },
            signal: abortController.signal,
          })

          if (res.status === HTTP_STATUS_CODE.SUCCESS && res.data) {
            const data = res.data?.data.data

            if (data) {
              if (nextCreatedAt && nextSessionId) {
                // Is load more
                addHistories(data)
              } else {
                setHistories(data)
              }
              setNextSessionId(res.data.data.next_session_id ?? undefined)
              setNextCreatedAt(res.data.data.next_created_at ?? undefined)
              setHistoriesCount(res.data.data.total_count ?? 0)
            }
          } else {
            setHistories([])
          }
        } catch (error) {
          console.log(error)
          setHistories([])
        } finally {
          if (abortControllerRef.current === abortController) {
            setIsLoadInitHistory(false)
          }
          setIsLoadFilterData(false)
        }
      },
      300
    )
  ).current

  useEffect(() => {
    if (!isChatHistoryOpen) {
      setHistories([])
      setNextSessionId(undefined)
      setNextCreatedAt(undefined)
      setIsLoadInitHistory(true)
      setIsLoadFilterData(false)
      return
    }

    fetchHistoryData({
      textSearch,
      nextSessionId,
      nextCreatedAt,
      topWorkflowSelected,
      isLoadInitHistory,
      activeHistory,
    })
  }, [textSearch, topWorkflowSelected, isChatHistoryOpen])

  const dataGroupByCreateTime = useMemo(() => {
    return groupBy(histories, (item) =>
      moment(item.created_at).startOf('day').format()
    )
  }, [histories])

  const latestGroupIsoTime = useMemo(() => {
    const keys = Object.keys(dataGroupByCreateTime)
    return keys[keys.length - 1]
  }, [dataGroupByCreateTime])

  // fetch top workflows data from ids
  useEffect(() => {
    const newWfIds = topWfIds.filter(
      (id) => !topWorkflows.some((wf) => wf.id === id)
    )

    if (newWfIds.length <= 0) {
      // change position of topWorkflows to match with topWfIds
      const newTopWorkflows = topWfIds
        .map((id) => topWorkflows.find((wf) => wf.id === id))
        .filter((wf) => !!wf)

      setTopWorkflows(newTopWorkflows)
    } else {
      const fetchNewTopWorkflows = async () => {
        if (isLoadingWfInfo) return

        setIsLoadingWfInfo(true)

        const promises = newWfIds.map((id) =>
          workflowReadWorkflowByIdApi({
            path: {
              workflow_id: id,
            },
          })
        )

        const res = await Promise.all(promises)

        const wfData = res.map((r, i) => {
          if (r.status === HTTP_STATUS_CODE.SUCCESS && r.data) {
            return r.data?.data
          }
          return {
            id: newWfIds[i],
            name: UNKNOWN_WORKFLOW,
            workflow_type: 'conversation',
            thumbnail: '',
            description: '',
            memories: [],
            config: {} as any,
          } as WorkflowPublic
        })
        addWorkflowData(...wfData)

        setIsLoadingWfInfo(false)
        return wfData
      }
      fetchNewTopWorkflows().then((data) => {
        if (data) {
          const newTopWorkflows = topWfIds
            .map((id) => {
              const newDataIndex = data.findIndex((d) => d.id === id)
              if (newDataIndex !== -1) {
                return data[newDataIndex]
              }
              return topWorkflows.find((wf) => wf.id === id)
            })
            .filter((wf) => !!wf)

          setTopWorkflows(newTopWorkflows)
        }
      })
    }
  }, [topWfIds])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop
    const scrollHeight = e.currentTarget.scrollHeight
    const clientHeight = e.currentTarget.clientHeight

    if (scrollTop + clientHeight >= scrollHeight - 20) {
      if (nextSessionId && nextCreatedAt) {
        fetchHistoryData({
          textSearch,
          nextSessionId,
          nextCreatedAt,
          topWorkflowSelected,
          isLoadInitHistory,
          activeHistory,
        })
      }
    }
  }

  const handleChangeSearchText = (text: string) => {
    if (text === textSearch) return

    setTextSearch(text)
    setNextSessionId(undefined)
    setNextCreatedAt(undefined)
    setHistories([])
    setIsLoadFilterData(true)
  }

  const handleChangeSelectedWf = (item: WorkflowPublicList | 'All') => {
    setTopWorkflowSelected(item)
    setNextSessionId(undefined)
    setNextCreatedAt(undefined)
    setHistories([])
    setIsLoadFilterData(true)
  }

  const isSelectedWfAndNotAll = useMemo(() => {
    if (topWorkflowSelected && topWorkflowSelected !== 'All') {
      return true
    }
  }, [topWorkflowSelected, topWorkflows])

  const handleClickHistory = async (history: HomeChatSessionPublic) => {
    if (activeHistory?.session_id === history.session_id) {
      return
    }

    const loadInitChatMessages = async () => {
      const res = await chatBaseHistoryGetChatBaseHistoryApi({
        query: {
          workflow_id: history.workflow_id,
          session_id: history.session_id,
          page_number: 1,
          page_size: MESSAGES_PAGE_SIZE,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS && res.data) {
        const data = res.data?.data.data

        if (!data) return

        const wfDataRef = await workflowReadWorkflowByIdApi({
          path: { workflow_id: history.workflow_id },
        })

        let currentWfData: WorkflowPublic | undefined
        if (wfDataRef.status === HTTP_STATUS_CODE.SUCCESS && wfDataRef.data) {
          const { data } = wfDataRef
          currentWfData = data.data
          addWorkflowData(data.data)
        }

        const nodes = currentWfData?.config.nodes
        const messages = data.map((item) =>
          transformWorkflowConversationsHistoryToImessage(item, nodes)
        )

        setMessagesTotalPage(res.data.data.total_pages ?? 0)
        setMessage(messages)
      }

      setIsLoadingPrevMessages(false)
    }

    setIsDisableChat(false)
    setIsLoadingPrevMessages(true)
    setActiveHistory(history)
    setText('')
    setShowScrollIcon(false)

    try {
      // call chat history
      loadInitChatMessages()

      // call api to get latest workflow info
      const wfInfoRes = await workflowCheckWorkflowExistsApi({
        path: {
          workflow_id: history.workflow_id,
        },
      })

      if (wfInfoRes.status === HTTP_STATUS_CODE.SUCCESS && wfInfoRes.data) {
        const workflow = wfInfoRes.data?.data
        setCurrentWorkflow(workflow)

        if (history.session_id !== sessionId) {
          setSessionId(history.session_id)
        }
        setChatTitle(history.title)
      } else {
        setCurrentWorkflow(undefined)
        setChatTitle('')
      }

      setChatAtBottom(true)
    } finally {
      /* empty */
    }
  }

  const handleCreateNewChat = () => {
    resetStore(FIELD_PRESERVE_FOR_NEW_CHAT)
    //create new session id
    setSessionId(uuid())
  }

  const handleClickDeleteHistory = (history: HomeChatSessionPublic) => {
    const handleDeleteHistory = () => {
      deleteHistory(history.id)

      const isActiveHistory = activeHistory?.session_id === history.session_id
      if (isActiveHistory) {
        if (!currentWorkflow) {
          setCurrentWorkflow({
            id: DEFAULT_HOME_CHAT_WORKFLOW_ID,
            name: DEFAULT_HOME_CHAT_WORKFLOW_NAME,
          })
        }

        setActiveHistory(undefined)
        handleCreateNewChat()
      }

      chatBaseHistoryDeleteHomeChatSessionApi({
        path: {
          session_id: history.session_id,
        },
      })
    }

    MessageDialog.warning({
      mainMessage: 'Delete this conversation?',
      subMessage: 'This action cannot be undone',
      onClick: () => {
        handleDeleteHistory()
      },
    })
  }

  const handleDeleteAllHistory = () => {
    MessageDialog.warning({
      mainMessage: 'Delete all conversations?',
      subMessage: 'This action cannot be undone',
      onClick: () => {
        setTopWorkflowSelected(DEFAULT_TOP_WORKFLOW_SELECTED)
        // maybe data on server is not updated yet
        blockCallApiRef.current = true

        setTopWfIds([])
        setTopWorkflows([])
        deleteAllHistories()
        handleCreateNewChat()
        chatBaseHistoryDeleteHomeChatSessionApi({
          path: {
            session_id: '',
          },
        })
      },
    })
  }

  const renderContent = () => {
    if (isLoadInitHistory) {
      return <HistorySkeleton />
    }

    if (
      !isLoadInitHistory &&
      !isLoadFilterData &&
      !textSearch &&
      !isSelectedWfAndNotAll &&
      (histories?.length ?? 0) === 0
    ) {
      return (
        <div className="flex w-full flex-1 justify-center rounded-[12px] bg-white px-[8px] pt-[8px]">
          <EmptyHistory
            style={{
              marginTop: 149.5 - 8,
            }}
          />
        </div>
      )
    }

    return (
      <Fragment>
        {isLoadingWfInfo ? (
          <SelectWfSkeleton className="bg-white" />
        ) : (
          topWorkflows.length > 0 && (
            <WorkflowList
              topWorkflows={topWorkflows}
              onChangeSelected={handleChangeSelectedWf}
              selectedWf={topWorkflowSelected}
              anchor="bottom end"
            />
          )
        )}

        {(textSearch || isSelectedWfAndNotAll) &&
        !isLoadFilterData &&
        (histories?.length ?? 0) === 0 ? (
          <div className="flex w-full flex-1 justify-center rounded-[12px] bg-white px-[8px] pt-[8px]">
            <NoFoundData
              style={{
                marginTop: 117 - 8,
              }}
            />
          </div>
        ) : (
          <>
            {size(dataGroupByCreateTime) > 0 && (
              <div
                onScroll={handleScroll}
                ref={scrollContainerRef}
                className="genai-scrollbar flex flex-col gap-[8px] overflow-y-auto rounded-[12px] bg-white p-[8px]"
              >
                {map(dataGroupByCreateTime, (histories, isoTime) => {
                  const isLatestGroup = isoTime === latestGroupIsoTime

                  return (
                    <HistoryGroup
                      key={isoTime}
                      time={isoTime}
                      histories={histories}
                      showSeparator={!isLatestGroup}
                      textSearch={textSearch}
                      onClickHistory={handleClickHistory}
                      activeHistory={activeHistory}
                      handleClickDeleteHistory={handleClickDeleteHistory}
                      isLoadFilterData={isLoadFilterData}
                    />
                  )
                })}
              </div>
            )}
          </>
        )}
      </Fragment>
    )
  }

  return (
    <div
      className={cn(
        'absolute bottom-0 right-0 top-0 z-10 w-0 overflow-hidden bg-[#F3F3F3]',
        'transition-all duration-100',
        'shadow-inner',
        {
          'w-[263px]': isShowHistory,
        }
      )}
      style={{ boxShadow: '2px 2px 4px 0px rgba(0, 0, 0, 0.03) inset' }}
    >
      <div className="flex size-full flex-col gap-[12px] overflow-hidden px-[12px] py-[20px]">
        <div className="flex min-h-[21px] items-center gap-[3px] overflow-hidden px-[8px]">
          <Text
            value="Chat History"
            variant="medium"
            type="body"
            className="text-Primary-Color"
            elementType="div"
          />

          <Text
            value={`(${historiesCount})`}
            variant="regular"
            type="subBody"
            className="text-Secondary-Color"
          />
        </div>

        <div className="flex flex-1 flex-col gap-[12px] overflow-hidden">
          <div className="flex h-[28px] items-center justify-between gap-[8px] px-[6px]">
            <SearchInput
              placeholder="Search"
              searchText={textSearch}
              setSearchText={handleChangeSearchText}
            />

            <div
              onClick={handleDeleteAllHistory}
              className="flex h-[28px] w-[28px] min-w-[28px] cursor-pointer items-center justify-center rounded-[8px] bg-white p-[6px] hover:bg-neutral-200"
            >
              <IconDeleteAll />
            </div>
          </div>

          {renderContent()}
        </div>
      </div>
    </div>
  )
}

export default ChatHistory

export const FakeChatHistory = () => {
  const isShowHistory = useChatStore.use.isChatHistoryOpen()

  return (
    <div
      className={cn('bg-transparent', 'transition-all duration-100', {
        // 320 - 20 - 12
        'w-[231px]': isShowHistory,
      })}
    />
  )
}
