/* eslint-disable max-len */
import Text from '@/components/Text'
import { cn } from '@/helpers'
import SelectWfBase from '../../SelectWorkflow/SelectWfBase'
import WfItem from './WfItem'
import { WorkflowPublicList } from '@/apis/client'
import { PopoverPanelProps } from '@headlessui/react'

const WorkflowList = ({
  topWorkflows,
  onChangeSelected,
  selectedWf,
  anchor = 'bottom',
}: {
  topWorkflows: Array<WorkflowPublicList>
  selectedWf?: WorkflowPublicList | 'All'
  onChangeSelected: (item: WorkflowPublicList | 'All') => void
  anchor?: PopoverPanelProps['anchor']
}) => {
  const isSelectedMore =
    selectedWf &&
    selectedWf !== 'All' &&
    !topWorkflows.find((wf) => wf.id === selectedWf?.id)

  return (
    <div className="relative flex min-h-fit w-full flex-wrap gap-1 overflow-hidden">
      <WfItem
        text="All"
        isSelected={selectedWf === 'All'}
        onClick={() => {
          if (selectedWf === 'All') return
          onChangeSelected('All')
        }}
      />

      {topWorkflows.map((workflow) => (
        <WfItem
          key={workflow.id}
          text={workflow.name}
          isSelected={selectedWf !== 'All' && selectedWf?.id === workflow.id}
          onClick={() => {
            if ((selectedWf as WorkflowPublicList)?.id === workflow.id) return
            onChangeSelected(workflow)
          }}
        />
      ))}

      <SelectWfBase
        sort_by="updated_at"
        anchor={anchor}
        className="overflow-hidden"
        classNameButton={cn(
          'h-[23px] gap-0 overflow-hidden border-0 px-[8px] py-[4px] rounded-full bg-white min-w-0',
          'data-[open]:bg-Secondary-Color-2 hover:bg-Secondary-Color-2 max-w-full',
          {
            'bg-Secondary-Color-2 hover:bg-Secondary-Color-2': isSelectedMore,
          }
        )}
        classNamePopup="h-[325px] w-[319px] [--anchor-offset:45px]"
        onClickItem={(item) => {
          onChangeSelected(item)
        }}
        preventList={topWorkflows.map((wf) => wf.id)}
      >
        <div className="flex w-full items-center justify-between overflow-hidden">
          <Text
            tooltipProps={{
              mouseEnterDelay: 0.5,
            }}
            elementType={isSelectedMore ? 'div' : 'span'}
            ellipsis
            value={isSelectedMore ? selectedWf?.name : 'More'}
            variant="regular"
            type="subBody"
            className={cn(
              'overflow-hidden text-center text-Primary-Color group-hover:text-white group-data-[open]:text-white',
              {
                'text-white': isSelectedMore,
              }
            )}
          />

          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="min-w-[14px] transform transition-transform group-hover:fill-white group-data-[open]:rotate-90"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M4.8376 3.08785C5.06541 2.86004 5.43475 2.86004 5.66256 3.08785L9.16256 6.58785C9.39037 6.81565 9.39037 7.185 9.16256 7.4128L5.66256 10.9128C5.43475 11.1406 5.06541 11.1406 4.8376 10.9128C4.6098 10.685 4.6098 10.3157 4.8376 10.0878L7.92512 7.00033L4.8376 3.9128C4.6098 3.685 4.6098 3.31565 4.8376 3.08785Z"
              fill="#2D0136"
              className={cn(
                'group-hover:fill-white group-data-[open]:fill-white',
                {
                  'fill-white': isSelectedMore,
                }
              )}
            />
          </svg>
        </div>
      </SelectWfBase>
    </div>
  )
}

export default WorkflowList
