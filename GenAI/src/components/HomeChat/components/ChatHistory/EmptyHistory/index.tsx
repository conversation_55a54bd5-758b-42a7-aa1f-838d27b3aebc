/* eslint-disable max-len */
import Text from '@/components/Text'
import { CSSProperties } from 'styled-components'

interface Props {
  style?: CSSProperties | undefined
}
const EmptyHistory = ({ style }: Props) => {
  return (
    <div
      className="flex h-[146px] w-[137px] flex-col items-center gap-[8px]"
      style={style}
    >
      <svg
        width="121"
        height="120"
        viewBox="0 0 121 120"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M104.646 53.5771L102.058 63.2364C99.0368 74.5118 97.5262 80.1495 94.1016 83.8059C91.3976 86.693 87.8981 88.7134 84.0458 89.6116C83.5645 89.7238 83.0758 89.8103 82.575 89.8721C77.9997 90.4364 72.4168 88.9405 62.2539 86.2173C50.9785 83.1961 45.3408 81.6855 41.6843 78.2609C38.7973 75.5568 36.7769 72.0574 35.8786 68.2051C34.741 63.3262 36.2517 57.6885 39.2729 46.4131L41.8611 36.7539C42.2955 35.1327 42.6989 33.627 43.081 32.2281C45.3564 23.8979 46.8855 19.3149 49.8176 16.1843C52.5216 13.2973 56.021 11.2769 59.8733 10.3786C64.7522 9.24105 70.3899 10.7517 81.6653 13.7729C92.9407 16.7941 98.5784 18.3048 102.235 21.7294C105.122 24.4334 107.142 27.9328 108.041 31.7851C109.178 36.664 107.667 42.3017 104.646 53.5771ZM55.7621 49.0294C56.2981 47.0289 58.3544 45.8417 60.3549 46.3778L84.503 52.8483C86.5035 53.3843 87.6907 55.4406 87.1546 57.441C86.6186 59.4415 84.5624 60.6287 82.5619 60.0927L58.4137 53.6222C56.4132 53.0862 55.226 51.0299 55.7621 49.0294ZM51.8778 63.5167C52.4138 61.5162 54.4701 60.3291 56.4706 60.8651L70.9595 64.7474C72.96 65.2834 74.1471 67.3397 73.6111 69.3402C73.0751 71.3407 71.0188 72.5278 69.0183 71.9918L54.5294 68.1095C52.5289 67.5735 51.3417 65.5172 51.8778 63.5167Z"
          fill="url(#paint0_linear_17045_1520)"
        />
        <path
          opacity="0.5"
          d="M82.5744 89.8723C81.5319 93.064 79.6991 95.9515 77.2348 98.2595C73.5784 101.684 67.9407 103.195 56.6653 106.216C45.3899 109.237 39.7522 110.748 34.8733 109.61C31.021 108.712 27.5216 106.692 24.8176 103.805C21.3929 100.148 19.8823 94.5104 16.8611 83.235L14.2729 73.5758C11.2517 62.3003 9.74105 56.6627 10.8786 51.7838C11.7769 47.9315 13.7973 44.4321 16.6843 41.728C20.3408 38.3034 25.9785 36.7928 37.2539 33.7716C39.387 33.2 41.3184 32.6825 43.081 32.2267C43.0809 32.2273 43.0812 32.2262 43.081 32.2267C42.6989 33.6257 42.2955 35.1328 41.8611 36.754L39.2729 46.4133C36.2517 57.6887 34.741 63.3264 35.8786 68.2053C36.7769 72.0576 38.7973 75.557 41.6843 78.261C45.3408 81.6856 50.9785 83.1963 62.2539 86.2175C72.4165 88.9406 77.9993 90.4364 82.5744 89.8723Z"
          fill="url(#paint1_linear_17045_1520)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_17045_1520"
            x1="108.419"
            y1="49.9951"
            x2="46.4257"
            y2="77.6462"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.2" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_17045_1520"
            x1="82.5744"
            y1="71.1077"
            x2="21.6404"
            y2="98.7411"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.2" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
          </linearGradient>
        </defs>
      </svg>

      <Text type="subBody" variant="regular" className="text-Primary-Color">
        No history yet
      </Text>
    </div>
  )
}

export default EmptyHistory
