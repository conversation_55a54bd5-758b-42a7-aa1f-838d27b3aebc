/* eslint-disable max-len */
import Text from '@/components/Text'
import { CSSProperties } from 'styled-components'

interface Props {
  style?: CSSProperties | undefined
}
const NoFoundData = ({ style }: Props) => {
  return (
    <div
      className="flex h-[149px] w-[157px] flex-col items-center gap-[8px]"
      style={style}
    >
      <svg
        width="121"
        height="120"
        viewBox="0 0 121 120"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M102.058 63.2364L104.646 53.5771C107.667 42.3017 109.178 36.664 108.041 31.7851C107.142 27.9328 105.122 24.4334 102.235 21.7294C98.5784 18.3048 92.9407 16.7941 81.6653 13.7729C70.3899 10.7517 64.7522 9.24105 59.8733 10.3786C56.021 11.2769 52.5216 13.2973 49.8176 16.1843C46.8855 19.3149 45.3564 23.8979 43.081 32.2281C42.6989 33.6271 42.2957 35.1317 41.8613 36.753L41.8611 36.7539L39.2729 46.4131C36.2517 57.6885 34.741 63.3262 35.8786 68.2051C36.7769 72.0574 38.7973 75.5568 41.6843 78.2609C45.3407 81.6855 50.9784 83.1961 62.2538 86.2173L62.2539 86.2173L62.254 86.2174C72.4169 88.9405 77.9997 90.4364 82.575 89.8721C83.0758 89.8103 83.5645 89.7238 84.0458 89.6116C87.8981 88.7134 91.3976 86.693 94.1016 83.8059C97.5262 80.1495 99.0368 74.5118 102.058 63.2364Z"
          fill="url(#paint0_linear_17045_3005)"
        />
        <path
          opacity="0.5"
          d="M82.5744 89.8723C81.5319 93.064 79.6991 95.9515 77.2348 98.2595C73.5784 101.684 67.9407 103.195 56.6653 106.216C45.3899 109.237 39.7522 110.748 34.8733 109.61C31.021 108.712 27.5216 106.692 24.8176 103.805C21.3929 100.148 19.8823 94.5104 16.8611 83.235L14.2729 73.5758C11.2517 62.3003 9.74105 56.6627 10.8786 51.7838C11.7769 47.9315 13.7973 44.4321 16.6843 41.728C20.3408 38.3034 25.9785 36.7928 37.2539 33.7716C39.387 33.2 41.3184 32.6825 43.081 32.2267C43.0809 32.2273 43.0812 32.2262 43.081 32.2267C42.6989 33.6257 42.2955 35.1328 41.8611 36.754L39.2729 46.4133C36.2517 57.6887 34.741 63.3264 35.8786 68.2053C36.7769 72.0576 38.7973 75.557 41.6843 78.261C45.3408 81.6856 50.9785 83.1963 62.2539 86.2175C72.4165 88.9406 77.9993 90.4364 82.5744 89.8723Z"
          fill="url(#paint1_linear_17045_3005)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M46.9608 48.0002C57.1576 48.0002 65.4222 56.2647 65.4222 66.4616C65.4222 70.4782 64.1394 74.1953 61.9607 77.2263L67.9595 83.2251C69.0522 84.3178 69.0522 86.0879 67.9595 87.1805C66.8669 88.2732 65.0968 88.2732 64.0041 87.1805L58.0472 81.2253C54.9616 83.5461 51.122 84.9229 46.9614 84.9229C36.7646 84.9229 28.5 76.6583 28.5 66.4614C28.5 56.2646 36.7639 48.0002 46.9608 48.0002ZM44.9984 57.1458C44.7252 56.2586 43.7855 55.7625 42.9005 56.0379C38.3638 57.4365 35.2127 61.6434 35.2127 66.4619C35.2127 67.3884 35.9645 68.1402 36.891 68.1402C37.8175 68.1402 38.5693 67.3884 38.5693 66.4619C38.5693 63.1249 40.7524 60.2141 43.8905 59.244C44.7755 58.9709 45.2715 58.0309 44.9984 57.1458ZM48.9669 53.7914C48.7812 53.6887 48.5561 53.6756 48.3594 53.7608C48.0098 53.9094 47.8459 54.3159 47.9966 54.6677L48.6675 56.2346C48.9013 56.7831 48.8882 57.4059 48.6304 57.9435L48.0709 59.1126C47.9923 59.2787 47.9835 59.4688 48.0447 59.6415C48.1715 60.002 48.567 60.19 48.9254 60.061L50.0901 59.648C50.6845 59.4382 51.3445 59.5103 51.8799 59.8447L53.6281 60.9373C53.8313 61.0641 54.0827 61.0772 54.2968 60.9723C54.6399 60.8062 54.782 60.391 54.6159 60.0501L53.7111 58.197C53.4336 57.6288 53.4314 56.9645 53.7046 56.3941L54.2356 55.2817C54.3143 55.1157 54.3252 54.9255 54.264 54.7529C54.1351 54.3923 53.7417 54.2044 53.3812 54.3333L52.1596 54.766C51.598 54.9649 50.9773 54.9146 50.4572 54.624L48.9669 53.7914Z"
          fill="white"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M46.9608 48.0002C57.1576 48.0002 65.4222 56.2647 65.4222 66.4616C65.4222 70.4782 64.1394 74.1953 61.9607 77.2263L67.9595 83.2251C69.0522 84.3178 69.0522 86.0879 67.9595 87.1805C66.8669 88.2732 65.0968 88.2732 64.0041 87.1805L58.0472 81.2253C54.9616 83.5461 51.122 84.9229 46.9614 84.9229C36.7646 84.9229 28.5 76.6583 28.5 66.4614C28.5 56.2646 36.7639 48.0002 46.9608 48.0002ZM44.9984 57.1458C44.7252 56.2586 43.7855 55.7625 42.9005 56.0379C38.3638 57.4365 35.2127 61.6434 35.2127 66.4619C35.2127 67.3884 35.9645 68.1402 36.891 68.1402C37.8175 68.1402 38.5693 67.3884 38.5693 66.4619C38.5693 63.1249 40.7524 60.2141 43.8905 59.244C44.7755 58.9709 45.2715 58.0309 44.9984 57.1458ZM48.9669 53.7914C48.7812 53.6887 48.5561 53.6756 48.3594 53.7608C48.0098 53.9094 47.8459 54.3159 47.9966 54.6677L48.6675 56.2346C48.9013 56.7831 48.8882 57.4059 48.6304 57.9435L48.0709 59.1126C47.9923 59.2787 47.9835 59.4688 48.0447 59.6415C48.1715 60.002 48.567 60.19 48.9254 60.061L50.0901 59.648C50.6845 59.4382 51.3445 59.5103 51.8799 59.8447L53.6281 60.9373C53.8313 61.0641 54.0827 61.0772 54.2968 60.9723C54.6399 60.8062 54.782 60.391 54.6159 60.0501L53.7111 58.197C53.4336 57.6288 53.4314 56.9645 53.7046 56.3941L54.2356 55.2817C54.3143 55.1157 54.3252 54.9255 54.264 54.7529C54.1351 54.3923 53.7417 54.2044 53.3812 54.3333L52.1596 54.766C51.598 54.9649 50.9773 54.9146 50.4572 54.624L48.9669 53.7914Z"
          fill="url(#paint2_linear_17045_3005)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_17045_3005"
            x1="108.419"
            y1="49.9951"
            x2="46.4257"
            y2="77.6462"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.1" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.1" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_17045_3005"
            x1="82.5744"
            y1="71.1077"
            x2="21.6404"
            y2="98.7411"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.1" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.1" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_17045_3005"
            x1="68.779"
            y1="68"
            x2="35.7422"
            y2="84.2771"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.3" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.3" />
          </linearGradient>
        </defs>
      </svg>

      <Text type="body" variant="regular" className="text-Primary-Color">
        No data found
      </Text>
    </div>
  )
}

export default NoFoundData
