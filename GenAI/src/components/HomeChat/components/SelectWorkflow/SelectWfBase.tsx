import {
  Popover,
  PopoverButton,
  PopoverPanel,
  type PopoverPanelProps,
} from '@headlessui/react'
import Text from '../../../Text'
import { cn } from '@/helpers'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import Icon from '@/assets/icon/Icon'
import {
  WorkflowPublicList,
  WorkflowType,
  workflowReadAllOwnerOrGlobalAccessWorkflowsApi,
} from '@/apis/client'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { ICurrentWorkflow } from '../../store/useChatStore'
import SearchInput from '../ChatHistory/SearchInput'
import { debounce } from 'lodash'
import NoDataFound from '@/components/NoDataFound'

interface Props {
  classNameButton?: string
  className?: string
  classNamePopup?: string
  onClickItem?: (item: WorkflowPublicList) => void
  selected?: ICurrentWorkflow
  setSelected?: (item: WorkflowPublicList) => void
  children?: React.ReactNode
  anchor?: PopoverPanelProps['anchor']
  showSearch?: boolean

  // Use for remove first item in list
  preventList?: string[]
  sort_by?: string
}

const SelectWfBase = ({
  anchor = 'top',
  classNameButton,
  classNamePopup,
  className,
  children,
  showSearch = true,
  setSelected,
  onClickItem,
  preventList,
  sort_by = 'created_at',
}: Props) => {
  const [loading, setLoading] = useState(false)

  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const storeWorkflow = useRef<Array<WorkflowPublicList>>([])
  const [workflows, setWorkflows] = useState<Array<WorkflowPublicList>>([])
  const [searchText, setSearchText] = useState('')
  const [isPopupOpen, setPopupOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPage, setTotalPage] = useState(0)

  const fetchData = async (
    page: number,
    searchText: string,
    isLoadMore = false
  ) => {
    if (loading || isPopupOpen === false) return
    setLoading(true)

    const res = await workflowReadAllOwnerOrGlobalAccessWorkflowsApi({
      query: {
        workflow_type: WorkflowType.CONVERSATION,
        page_number: page,
        page_size: PAGE_SIZE.LARGE,
        name: searchText?.trim(),
        sort_by,
      },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS && res.data) {
      const data = res.data?.data.data

      if (data) {
        // Filter out the preventList items from the data
        const filteredData = data.filter((item) => {
          return !preventList?.includes(item.id)
        })
        setWorkflows((prev) =>
          isLoadMore ? [...prev, ...filteredData] : filteredData
        )
        storeWorkflow.current = data
        setTotalPage(res.data?.data.total_pages ?? 0)
        setCurrentPage(page)
      }
    }

    setLoading(false)
  }

  const debouncedFetchData = useCallback(
    debounce((page: number, searchText: string, isLoadMore = false) => {
      fetchData(page, searchText, isLoadMore)
    }, 300),
    [isPopupOpen]
  )

  const handleClickItem = (item: WorkflowPublicList) => {
    setSelected?.(item)
    onClickItem?.(item)
  }

  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current
    if (!container || loading || currentPage >= totalPage) return

    const { scrollTop, scrollHeight, clientHeight } = container
    // Load more when user scrolls to the bottom (with a small threshold)
    if (scrollHeight - scrollTop - clientHeight < 20) {
      debouncedFetchData(currentPage + 1, searchText, true)
    }
  }, [loading, currentPage, totalPage])

  useEffect(() => {
    debouncedFetchData(currentPage, searchText, false)
  }, [isPopupOpen, searchText])

  return (
    <div onClick={(e) => e.stopPropagation()} className={className}>
      <Popover>
        {({ open }) => (
          <PopoverHandlingOpenState
            open={open}
            onClose={() => {
              setSearchText('')
              setCurrentPage(1)
              setTotalPage(0)
              setPopupOpen(false)
            }}
            onOpen={() => {
              setPopupOpen(true)
            }}
          >
            <>
              <PopoverButton
                className={cn(
                  'group flex h-[32px] min-w-[100px] max-w-[215px] items-center justify-center gap-[6px] rounded-full border border-solid border-border-base-icon bg-white p-[6px] focus-visible:outline-none',
                  classNameButton
                )}
              >
                {children}
              </PopoverButton>

              <PopoverPanel
                anchor={anchor}
                className={cn(
                  'z-[99] h-[242px] w-[372px] origin-top-right rounded-[20px] bg-white p-[12px] transition duration-100 [--anchor-gap:8.5px] focus:outline-none data-[closed]:opacity-0',
                  'border border-border-base-icon shadow-lg',
                  '!overflow-hidden',
                  classNamePopup
                )}
              >
                {({ close }) => (
                  <div className="flex size-full flex-col gap-[8px] overflow-hidden">
                    {showSearch && (
                      <SearchInput
                        placeholder="Search workflow"
                        className="w-full rounded-[8px] bg-neutral-100 px-[8px] py-[4px]"
                        searchText={searchText}
                        setSearchText={(t) => {
                          setSearchText(t)
                          setCurrentPage(1)
                        }}
                        onKeyDown={(e) => {
                          e.stopPropagation()
                        }}
                      />
                    )}

                    {searchText && !loading && workflows.length === 0 ? (
                      <div className="flex size-full items-center justify-center overflow-hidden">
                        <NoDataFound
                          size={80}
                          textVariant="semibold"
                          textType="body"
                          subTextType="subBody"
                        />
                      </div>
                    ) : (
                      <div
                        id="select-wf-base"
                        className="genai-scrollbar flex-1 overflow-y-auto"
                        ref={scrollContainerRef}
                        onScroll={handleScroll}
                      >
                        <div className="flex w-full flex-col">
                          {workflows?.map((item) => {
                            return (
                              <div
                                key={item.id}
                                className={cn('rounded-[8px]')}
                                onClick={() => {
                                  close()
                                  handleClickItem?.(item)
                                }}
                              >
                                <div className="flex w-full cursor-pointer flex-col items-center justify-between overflow-hidden rounded-[8px] p-[4px] hover:bg-Hover-Color">
                                  <div className="flex w-full gap-1 overflow-hidden">
                                    <div className="flex size-[16px] min-w-[16px] items-center justify-center">
                                      <Icon
                                        name="message-chat-square"
                                        size={16}
                                      />
                                    </div>

                                    <Text
                                      variant="medium"
                                      type="subBody"
                                      className="text-Primary-Color"
                                      value={item.name}
                                      elementType="div"
                                      ellipsis
                                      tooltipProps={{
                                        mouseEnterDelay: 0.5,
                                      }}
                                    />
                                  </div>

                                  <div className="flex w-full gap-1 overflow-hidden">
                                    <Text
                                      variant="regular"
                                      type="supportText"
                                      className="text-Secondary-Color"
                                      value={item.description}
                                      elementType="div"
                                      ellipsis
                                      tooltipProps={{
                                        mouseEnterDelay: 0.5,
                                        autoAdjustOverflow: false,
                                        getPopupContainer: () =>
                                          document.getElementById(
                                            'select-wf-base'
                                          ) || document.body,
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </PopoverPanel>
            </>
          </PopoverHandlingOpenState>
        )}
      </Popover>
    </div>
  )
}

export default memo(SelectWfBase)

const PopoverHandlingOpenState = ({
  open,
  onOpen,
  onClose,
  children,
}: {
  open: boolean
  onOpen?: () => void
  onClose?: () => void
  children: React.ReactNode
}) => {
  const openStateRef = useRef(false)

  useEffect(() => {
    if (open !== openStateRef.current) {
      openStateRef.current = open
      if (open) {
        onOpen?.()
      } else {
        onClose?.()
      }
    }
  }, [open, onOpen, onClose])

  return <>{children}</>
}
