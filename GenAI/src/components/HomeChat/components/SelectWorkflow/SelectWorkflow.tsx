/* eslint-disable max-len */

import Text from '../../../Text'
import { memo } from 'react'
import Icon from '@/assets/icon/Icon'
import useChatStore from '../../store/useChatStore'
import SelectWfBase from './SelectWfBase'
import { cn } from '@/helpers'
import { WorkflowPublicList } from '@/apis/client'
import { UNKNOWN_WORKFLOW } from '../../const'

interface Props {
  onClickItem?: (item: WorkflowPublicList) => void
}

const SelectWorkflow = ({ onClickItem }: Props) => {
  const { currentWorkflow, setCurrentWorkflow } = useChatStore()

  return (
    <SelectWfBase
      onClickItem={onClickItem}
      selected={currentWorkflow}
      setSelected={setCurrentWorkflow}
      classNameButton="hover:bg-border-base-icon"
    >
      <div
        className={cn(
          'flex flex-1 items-center justify-between gap-[6px] overflow-hidden'
        )}
      >
        <div className="flex gap-2 overflow-hidden">
          <div className="padding-[4px] flex size-[20px] min-w-[20px] items-center justify-center overflow-hidden rounded-full bg-sky-100">
            <svg
              width="13"
              height="13"
              viewBox="0 0 13 13"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.42362 0.500003C1.42771 0.500004 1.43177 0.500005 1.43578 0.500005H3.30734C3.31136 0.500005 3.31541 0.500004 3.31951 0.500003C3.3857 0.499981 3.46183 0.499957 3.52845 0.5054C3.60499 0.511653 3.71143 0.527485 3.82234 0.584C3.96735 0.657884 4.08524 0.775777 4.15913 0.920783C4.21564 1.0317 4.23147 1.13814 4.23773 1.21467C4.24317 1.2813 4.24315 1.35743 4.24312 1.42362C4.24312 1.42771 4.24312 1.43177 4.24312 1.43578V3.30734C4.24312 3.31136 4.24312 3.31541 4.24312 3.31951C4.24315 3.38569 4.24317 3.46183 4.23773 3.52845C4.23147 3.60499 4.21564 3.71143 4.15913 3.82234C4.08524 3.96735 3.96735 4.08524 3.82234 4.15913C3.71143 4.21564 3.60499 4.23147 3.52845 4.23773C3.46183 4.24317 3.38569 4.24315 3.31951 4.24312C3.31541 4.24312 3.31136 4.24312 3.30734 4.24312H1.43578C1.43177 4.24312 1.42771 4.24312 1.42362 4.24312C1.35743 4.24315 1.2813 4.24317 1.21467 4.23773C1.13814 4.23147 1.0317 4.21564 0.920783 4.15913C0.775777 4.08524 0.657884 3.96735 0.584 3.82234C0.527485 3.71143 0.511653 3.60499 0.5054 3.52845C0.499957 3.46183 0.499981 3.3857 0.500003 3.31951C0.500004 3.31541 0.500005 3.31136 0.500005 3.30734V1.43578C0.500005 1.43177 0.500004 1.42771 0.500003 1.42362C0.499981 1.35743 0.499957 1.2813 0.5054 1.21467C0.511653 1.13814 0.527485 1.0317 0.584 0.920783C0.657884 0.775777 0.775777 0.657884 0.920783 0.584C1.0317 0.527485 1.13814 0.511653 1.21467 0.5054C1.2813 0.499957 1.35743 0.499981 1.42362 0.500003ZM10.6506 2.88033C10.4919 2.86736 10.2843 2.86698 9.96789 2.86698H5.94954C5.67593 2.86698 5.45413 2.64517 5.45413 2.37156C5.45413 2.09795 5.67593 1.87615 5.94954 1.87615L9.98737 1.87615C10.279 1.87614 10.5274 1.87614 10.7313 1.8928C10.9455 1.9103 11.1539 1.94863 11.3532 2.05014C11.6535 2.20319 11.8977 2.44739 12.0508 2.74776C12.1523 2.94699 12.1906 3.15539 12.2081 3.36961C12.2248 3.57351 12.2248 3.82186 12.2248 4.11353V4.8648C12.2248 5.1075 12.2248 5.31419 12.2131 5.48472C12.2009 5.66348 12.1743 5.83839 12.1033 6.00997C11.9412 6.40112 11.6305 6.71188 11.2393 6.8739C11.0677 6.94497 10.8928 6.97159 10.7141 6.98379C10.5435 6.99542 10.3369 6.99542 10.0942 6.99541H10.078C9.80437 6.99541 9.58257 6.77361 9.58257 6.5C9.58257 6.22639 9.80437 6.00459 10.078 6.00459C10.3412 6.00459 10.5139 6.00432 10.6466 5.99526C10.7749 5.98651 10.8294 5.97123 10.8602 5.9585C11.0085 5.89704 11.1264 5.77917 11.1878 5.6308C11.2006 5.60007 11.2159 5.54558 11.2246 5.41727C11.2337 5.28452 11.2339 5.11188 11.2339 4.84863V4.13303C11.2339 3.81657 11.2336 3.60902 11.2206 3.45029C11.2081 3.29735 11.1864 3.23386 11.1679 3.19759C11.1099 3.08365 11.0173 2.99102 10.9033 2.93297C10.8671 2.91449 10.8036 2.89283 10.6506 2.88033ZM5.55206 4.62844C5.55615 4.62844 5.56021 4.62844 5.56422 4.62844H7.43578C7.43979 4.62844 7.44385 4.62844 7.44794 4.62844C7.51413 4.62842 7.59026 4.62839 7.65689 4.63384C7.73342 4.64009 7.83986 4.65592 7.95078 4.71244C8.09579 4.78632 8.21368 4.90421 8.28756 5.04922C8.34408 5.16014 8.35991 5.26658 8.36616 5.34311C8.37161 5.40973 8.37158 5.48586 8.37156 5.55205V7.44795C8.37158 7.51414 8.37161 7.59027 8.36616 7.65689C8.35991 7.73342 8.34408 7.83986 8.28756 7.95078C8.21368 8.09579 8.09579 8.21368 7.95078 8.28756C7.83986 8.34408 7.73342 8.35991 7.65689 8.36616C7.59027 8.37161 7.51414 8.37158 7.44795 8.37156H5.55205C5.48586 8.37158 5.40973 8.37161 5.34311 8.36616C5.26658 8.35991 5.16014 8.34408 5.04922 8.28756C4.90421 8.21368 4.78632 8.09579 4.71244 7.95078C4.65592 7.83986 4.64009 7.73342 4.63384 7.65689C4.62839 7.59026 4.62842 7.51413 4.62844 7.44794C4.62844 7.44385 4.62844 7.43979 4.62844 7.43578V5.56422C4.62844 5.56021 4.62844 5.55615 4.62844 5.55206C4.62842 5.48587 4.62839 5.40974 4.63384 5.34311C4.64009 5.26658 4.65592 5.16014 4.71244 5.04922C4.78632 4.90421 4.90421 4.78632 5.04922 4.71244C5.16014 4.65592 5.26658 4.64009 5.34311 4.63384C5.40974 4.62839 5.48587 4.62842 5.55206 4.62844ZM2.90585 6.00459H2.92202C3.19563 6.00459 3.41743 6.22639 3.41743 6.5C3.41743 6.77361 3.19563 6.99541 2.92202 6.99541C2.65876 6.99541 2.48613 6.99568 2.35337 7.00474C2.22507 7.01349 2.17058 7.02877 2.13985 7.0415C1.99148 7.10296 1.87361 7.22083 1.81215 7.3692C1.79942 7.39993 1.78414 7.45442 1.77539 7.58272C1.76633 7.71548 1.76606 7.88811 1.76606 8.15137V8.86697C1.76606 9.18343 1.76644 9.39098 1.77941 9.54971C1.79191 9.70265 1.81357 9.76614 1.83206 9.80241C1.89011 9.91635 1.98274 10.009 2.09667 10.067C2.13294 10.0855 2.19643 10.1072 2.34938 10.1197C2.5081 10.1326 2.71565 10.133 3.03211 10.133H7.05046C7.32407 10.133 7.54587 10.3548 7.54587 10.6284C7.54587 10.902 7.32407 11.1238 7.05046 11.1238H3.01261C2.72095 11.1239 2.4726 11.1239 2.26869 11.1072C2.05447 11.0897 1.84607 11.0514 1.64685 10.9499C1.34648 10.7968 1.10227 10.5526 0.949224 10.2522C0.847712 10.053 0.809381 9.84461 0.791879 9.63039C0.775219 9.42649 0.775226 9.17813 0.775234 8.88646L0.775234 8.1352C0.775229 7.8925 0.775224 7.68581 0.786859 7.51528C0.799055 7.33652 0.825675 7.16161 0.896748 6.99003C1.05877 6.59888 1.36953 6.28812 1.76067 6.1261C1.93226 6.05503 2.10717 6.02841 2.28593 6.01621C2.45645 6.00458 2.66315 6.00458 2.90585 6.00459ZM9.68048 8.75688H11.5764C11.6426 8.75685 11.7187 8.75683 11.7853 8.76227C11.8619 8.76853 11.9683 8.78436 12.0792 8.84087C12.2242 8.91476 12.3421 9.03265 12.416 9.17766C12.4725 9.28857 12.4883 9.39501 12.4946 9.47155C12.5 9.53817 12.5 9.6143 12.5 9.68048V11.5764C12.5 11.6426 12.5 11.7187 12.4946 11.7853C12.4883 11.8619 12.4725 11.9683 12.416 12.0792C12.3421 12.2242 12.2242 12.3421 12.0792 12.416C11.9683 12.4725 11.8619 12.4883 11.7853 12.4946C11.7187 12.5 11.6426 12.5 11.5764 12.5H9.68048C9.6143 12.5 9.53817 12.5 9.47155 12.4946C9.39501 12.4883 9.28857 12.4725 9.17766 12.416C9.03265 12.3421 8.91476 12.2242 8.84087 12.0792C8.78436 11.9683 8.76853 11.8619 8.76227 11.7853C8.75683 11.7187 8.75685 11.6426 8.75688 11.5764V9.68048C8.75685 9.6143 8.75683 9.53817 8.76227 9.47155C8.76853 9.39501 8.78436 9.28857 8.84087 9.17766C8.91476 9.03265 9.03265 8.91476 9.17766 8.84087C9.28857 8.78436 9.39501 8.76853 9.47155 8.76227C9.53817 8.75683 9.6143 8.75685 9.68048 8.75688Z"
                fill="#38BDF8"
              />
            </svg>
          </div>

          <div className="flex flex-col gap-1 overflow-hidden">
            <Text
              variant="regular"
              type="subBody"
              className="text-left text-Tertiary-Color"
              value={currentWorkflow?.name ?? UNKNOWN_WORKFLOW}
              elementType="div"
              ellipsis
              tooltipProps={{
                mouseEnterDelay: 0.5,
              }}
            />
          </div>
        </div>
      </div>

      <div className="rotate-180 transform transition-transform duration-100 ease-out group-data-[open]:rotate-0">
        <Icon name="chevron-up" size={16} />
      </div>
    </SelectWfBase>
  )
}

export default memo(SelectWorkflow)
