import Text from '@/components/Text'
import { useMyProfile } from '@/hooks/useMyProfile'

const ChatHeader = () => {
  const { myProfile } = useMyProfile()

  return (
    <div className="flex w-full flex-col items-center justify-center">
      <Text
        variant="medium"
        type="title"
        className="bg-Hi-home bg-clip-text text-center text-transparent"
        elementType="div"
        ellipsis
      >
        {`Hi, ${myProfile?.first_name ?? myProfile?.username ?? ''}`}
      </Text>

      <Text variant="medium" type="body" className="text-Tertiary-Color">
        How can I assist you today?
      </Text>
    </div>
  )
}

export default ChatHeader
