/* eslint-disable max-len */

import { cn } from '@/helpers'
import Item from './Item'

export enum EActionBar {
  NEW_CHAT = 'New chat',
  HISTORY = 'History',
}

const items = [
  {
    id: EActionBar.NEW_CHAT,
    title: EActionBar.NEW_CHAT,
    icon: (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.3334 0.666748H5.66675C2.33341 0.666748 0.666748 2.33341 0.666748 5.66675V16.5001C0.666748 16.9584 1.04175 17.3334 1.50008 17.3334H12.3334C15.6667 17.3334 17.3334 15.6667 17.3334 12.3334V5.66675C17.3334 2.33341 15.6667 0.666748 12.3334 0.666748ZM8.25841 12.8834C8.05008 13.0917 7.66675 13.2834 7.38342 13.3251L5.65008 13.5667C5.58341 13.5751 5.51675 13.5834 5.45841 13.5834C5.16675 13.5834 4.90008 13.4834 4.70841 13.2917C4.47508 13.0584 4.37508 12.7167 4.43341 12.3501L4.67508 10.6167C4.71675 10.3334 4.90841 9.94175 5.11675 9.74175L8.25841 6.60008C8.30841 6.75008 8.37508 6.90008 8.45008 7.06675C8.52508 7.21675 8.60008 7.36675 8.68341 7.50841C8.75008 7.62508 8.82508 7.74175 8.89175 7.82508C8.97508 7.95008 9.05842 8.05841 9.11675 8.11675C9.15008 8.16675 9.18341 8.20008 9.19175 8.21675C9.37508 8.42508 9.56675 8.62508 9.75008 8.77508C9.80008 8.82508 9.83342 8.85008 9.84175 8.85842C9.95008 8.94175 10.0501 9.03341 10.1501 9.09175C10.2584 9.17508 10.3751 9.25008 10.4917 9.31675C10.6334 9.40008 10.7834 9.48341 10.9417 9.55841C11.1001 9.63341 11.2501 9.69175 11.4001 9.74175L8.25841 12.8834ZM12.7917 8.35841L12.1417 9.00842C12.1001 9.05008 12.0417 9.07508 11.9834 9.07508C11.9667 9.07508 11.9334 9.07508 11.9167 9.06675C10.4834 8.65841 9.34175 7.51675 8.93341 6.08342C8.90841 6.00841 8.93342 5.92508 8.99175 5.86675L9.65008 5.20842C10.7251 4.13341 11.7417 4.15841 12.7917 5.20842C13.3251 5.74175 13.5917 6.25841 13.5834 6.79175C13.5834 7.31675 13.3251 7.82508 12.7917 8.35841Z"
          fill="#766D72"
        />
        <path
          d="M12.7917 8.35841L12.1417 9.00842C12.1001 9.05008 12.0417 9.07508 11.9834 9.07508C11.9667 9.07508 11.9334 9.07508 11.9167 9.06675C10.4834 8.65841 9.34175 7.51675 8.93341 6.08342C8.90841 6.00841 8.93342 5.92508 8.99175 5.86675L9.65008 5.20842C10.7251 4.13341 11.7417 4.15841 12.7917 5.20842C13.3251 5.74175 13.5917 6.25841 13.5834 6.79175C13.5834 7.31675 13.3251 7.82508 12.7917 8.35841Z"
          fill="white"
        />
        <path
          d="M8.25841 12.8834C8.05008 13.0917 7.66675 13.2834 7.38342 13.3251L5.65008 13.5667C5.58341 13.5751 5.51675 13.5834 5.45841 13.5834C5.16675 13.5834 4.90008 13.4834 4.70841 13.2917C4.47508 13.0584 4.37508 12.7167 4.43341 12.3501L4.67508 10.6167C4.71675 10.3334 4.90841 9.94175 5.11675 9.74175L8.25841 6.60008C8.30841 6.75008 8.37508 6.90008 8.45008 7.06675C8.52508 7.21675 8.60008 7.36675 8.68341 7.50841C8.75008 7.62508 8.82508 7.74175 8.89175 7.82508C8.97508 7.95008 9.05842 8.05841 9.11675 8.11675C9.15008 8.16675 9.18341 8.20008 9.19175 8.21675C9.37508 8.42508 9.56675 8.62508 9.75008 8.77508C9.80008 8.82508 9.83342 8.85008 9.84175 8.85842C9.95008 8.94175 10.0501 9.03341 10.1501 9.09175C10.2584 9.17508 10.3751 9.25008 10.4917 9.31675C10.6334 9.40008 10.7834 9.48341 10.9417 9.55841C11.1001 9.63341 11.2501 9.69175 11.4001 9.74175L8.25841 12.8834Z"
          fill="white"
        />
      </svg>
    ),
  },
  {
    id: EActionBar.HISTORY,
    title: EActionBar.HISTORY,
    icon: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.0001 18.3332C14.6025 18.3332 18.3334 14.6022 18.3334 9.99984C18.3334 5.39746 14.6025 1.6665 10.0001 1.6665C5.39771 1.6665 1.66675 5.39746 1.66675 9.99984C1.66675 14.6022 5.39771 18.3332 10.0001 18.3332ZM10.0001 6.0415C10.3453 6.0415 10.6251 6.32133 10.6251 6.6665V9.74095L12.5254 11.6412C12.7694 11.8853 12.7694 12.281 12.5254 12.5251C12.2813 12.7692 11.8856 12.7692 11.6415 12.5251L9.55814 10.4418C9.44093 10.3246 9.37508 10.1656 9.37508 9.99984V6.6665C9.37508 6.32133 9.6549 6.0415 10.0001 6.0415Z"
          fill="#766D72"
        />
        <path
          d="M10.6251 6.6665C10.6251 6.32133 10.3453 6.0415 10.0001 6.0415C9.6549 6.0415 9.37508 6.32133 9.37508 6.6665V9.99984C9.37508 10.1656 9.44093 10.3246 9.55814 10.4418L11.6415 12.5251C11.8856 12.7692 12.2813 12.7692 12.5254 12.5251C12.7694 12.281 12.7694 11.8853 12.5254 11.6412L10.6251 9.74095V6.6665Z"
          fill="white"
        />
      </svg>
    ),
  },
]
const ActionBar = ({
  isChatHistoryOpen,
  onClick,
}: {
  isChatHistoryOpen: boolean
  onClick?: (action: EActionBar) => void
}) => {
  return (
    <div
      className={cn(
        'absolute right-0 top-1/2 flex h-[125px] w-[40px] -translate-y-1/2 items-center justify-center overflow-hidden transition-all duration-100',
        {
          'right-[263px]': isChatHistoryOpen,
        }
      )}
    >
      <div className="flex-1 overflow-hidden">
        <div className="flex size-full flex-col items-center justify-center gap-2 overflow-hidden">
          {items.map((item) => (
            <Item
              icon={item.icon}
              title={item.title}
              key={item.title}
              onClick={() => {
                onClick?.(item.id)
              }}
              isActive={item.id === EActionBar.HISTORY && isChatHistoryOpen}
            />
          ))}
        </div>
        <Background />
      </div>
    </div>
  )
}

export default ActionBar

const Background = () => {
  return (
    <svg
      className="absolute left-0 top-0 z-[-1]"
      width="40"
      height="125"
      viewBox="0 0 40 125"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_i_16877_516)">
        <path
          d="M0 32.1673C0 29.3097 0 27.8809 0.125212 27.0971C0.933178 22.0395 3.48641 19.5201 8.55435 18.7797C9.33974 18.6649 11.381 18.6921 15.4635 18.7466C18.3977 18.7858 21.2696 18.5155 23.5849 17.6887C38.9833 12.1897 40.0943 0 40.0943 0V125C40.0943 125 38.9151 112.028 23.5849 108.491C20.9769 107.889 17.893 107.736 14.83 107.823C11.2644 107.924 9.48164 107.974 8.68733 107.869C3.74307 107.214 0.921698 104.471 0.127577 99.5476C0 98.7566 0 97.4067 0 94.7068V32.1673Z"
          fill="#F3F3F3"
        />
      </g>
      <defs>
        <filter
          id="filter0_i_16877_516"
          x="0"
          y="0"
          width="42.0942"
          height="128"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="5"
            operator="erode"
            in="SourceAlpha"
            result="effect1_innerShadow_16877_516"
          />
          <feOffset dx="2" dy="3" />
          <feGaussianBlur stdDeviation="3" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_16877_516"
          />
        </filter>
      </defs>
    </svg>
  )
}
