import Tooltip from '@/components/Tooltip'
import { cn } from '@/helpers'
import React from 'react'

const Item = ({
  title,
  icon,
  onClick,
  isActive = false,
}: {
  title: string
  icon: React.ReactNode
  onClick?: () => void
  isActive?: boolean
}) => {
  return (
    <Tooltip text={title} mouseEnterDelay={0.5} position="left">
      <div
        className={cn(
          'flex size-[28px] min-w-[28px] cursor-pointer items-center justify-center rounded-lg p-1 hover:bg-[rgba(223,220,222,0.60)]',
          {
            'bg-[rgba(223,220,222,0.60)]': isActive,
          }
        )}
        onClick={onClick}
      >
        {icon}
      </div>
    </Tooltip>
  )
}

export default Item
