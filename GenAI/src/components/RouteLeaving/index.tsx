import { MessageDialog } from '@/components/DialogMessage'
import { memo, useCallback, useEffect } from 'react'
import type { BlockerFunction } from 'react-router-dom'
import { useBlocker } from 'react-router-dom'

interface Props {
  isChanged: boolean
  handleBeforeUnload?: () => void
}

const RouteLeaving = (props: Props) => {
  const { isChanged, handleBeforeUnload } = props

  const shouldBlock = useCallback<BlockerFunction>(() => isChanged, [isChanged])

  const blocker = useBlocker(shouldBlock)

  // Reset the blocker if the user cleans the form
  useEffect(() => {
    if (blocker.state === 'blocked') {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          blocker.proceed()
        },
      })
    }
  }, [blocker])

  const beforeUnloadListener = (e: any) => {
    handleBeforeUnload?.()
    if (isChanged) {
      // Cancel the event as stated by the standard.
      e.preventDefault()
      // Chrome requires returnValue to be set.
      e.returnValue = ''
    }
    return
  }

  useEffect(() => {
    window.addEventListener('beforeunload', beforeUnloadListener)
    return () =>
      window.removeEventListener('beforeunload', beforeUnloadListener)
  }, [isChanged, beforeUnloadListener])

  return <></>
}

export default memo(RouteLeaving)
