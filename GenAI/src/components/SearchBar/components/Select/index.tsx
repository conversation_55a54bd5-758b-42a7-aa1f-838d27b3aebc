import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Popover from '@/components/Popover'
import Text from '@/components/Text'
import { PAGE_SIZE } from '@/constants'
import WorkflowItemSkeleton from '@/pages/Dashboard/components/SessionLog/components/SessionList/WorkflowDropdown/components/WorkflowItemSkeleton'
import { colors } from '@/theme'
import clsx from 'clsx'
import { size } from 'lodash'
import { useEffect, useMemo, useState } from 'react'
import SimpleBar from 'simplebar-react'
import { twMerge } from 'tailwind-merge'
import './style.scss'
import IcNoDataFound from './assets/noDataFound.svg?react'

export interface ISelectBaseItem {
  id: string
  value?: any
  name?: string
  icon?: any
}

interface SelectProps<T extends ISelectBaseItem> {
  data: T[]
  selected: T | undefined
  placeholder?: string
  getPopupContainer?: (
    triggerNode?: HTMLElement
  ) => Element | DocumentFragment | HTMLElement | ParentNode | null | undefined
  onChangeSelectedValue: (value: any) => void
  className?: string
  overlayClassName?: string
  error?: boolean
  disabled?: boolean
  allowRemove?: boolean
  allowClear?: boolean
  multipleChoice?: boolean
  selectedMultiple?: T[]
  icon?: React.ReactNode
  containerClassName?: string
  onClear?: () => void
  onOpen?: () => void
  loading?: boolean
  optionMaxWidth?: number
  contentClassName?: string
  listClassName?: string
  itemClassName?: string
  placement?:
    | 'top'
    | 'top start'
    | 'top end'
    | 'bottom'
    | 'bottom start'
    | 'bottom end'
    | 'left'
    | 'left start'
    | 'left end'
    | 'right'
    | 'right start'
    | 'right end'
}

const Select = <T extends ISelectBaseItem>({
  data,
  selected,
  placeholder,
  getPopupContainer,
  onChangeSelectedValue,
  className,
  overlayClassName,
  error,
  disabled,
  allowRemove,
  allowClear,
  multipleChoice,
  icon,
  containerClassName,
  selectedMultiple,
  onClear,
  onOpen,
  loading,
  optionMaxWidth,
  placement,
  contentClassName,
  listClassName,
  itemClassName,
}: SelectProps<T>) => {
  const [open, setOpen] = useState(false)

  const handleChangeSelected = (value?: T) => {
    if (!multipleChoice) {
      if (!allowRemove) {
        onChangeSelectedValue(value)
      } else {
        if (selected?.id === value?.id) {
          onChangeSelectedValue(undefined)
        } else {
          onChangeSelectedValue(value)
        }
      }
    } else {
      onChangeSelectedValue(value)
    }
  }

  const isSelected = (value: T) => {
    if (multipleChoice) {
      return selectedMultiple?.some((item) => item.id === value.id)
    }
    return selected?.id === value.id
  }

  const selectedName = useMemo(() => {
    if (multipleChoice) {
      const length = selectedMultiple?.length
      if (!length) {
        return 'Empty'
      }
      if (length && length > 1) {
        return `${selectedMultiple?.length} selected`
      }
      return selectedMultiple[0]?.name
    }
    return selected?.name
  }, [selected, selectedMultiple])

  useEffect(() => {
    if (open && onOpen) {
      onOpen()
    }
  }, [open])

  const clearHandler = () => {
    onClear?.()
    setOpen(false)
  }

  const isActiveSelected = useMemo(() => {
    if (multipleChoice) {
      return allowClear
    }

    return data?.some((item) => item.id === selected?.id)
  }, [multipleChoice, allowClear, data, selected])

  return (
    <div className={className}>
      <Popover
        isPure
        overlayClassName={clsx('z-50 !overflow-hidden !p-0', overlayClassName)}
        open={open}
        title="Description"
        getPopupContainer={getPopupContainer}
        onOpenChange={(value) => {
          if (!disabled) {
            setOpen(value)
          }
        }}
        allowCloseWhenClickButton={allowRemove}
        placement={placement}
        content={
          <SimpleBar
            className={clsx(
              'searchbox-filter !max-h-[176px] w-[var(--button-width)] !max-w-[240px] rounded-xl border border-border-base-icon p-2 shadow-base',
              contentClassName
            )}
            style={{ maxWidth: optionMaxWidth || 'unset' }}
          >
            {loading && (
              <div
                className={clsx('flex w-full flex-col gap-1', contentClassName)}
              >
                {Array.from({ length: PAGE_SIZE.EXTRA_SMALL }).map(
                  (_, index) => (
                    <WorkflowItemSkeleton key={index} />
                  )
                )}
              </div>
            )}
            {!loading && !size(data) && (
              <div className="flex h-full w-full flex-col items-center justify-center gap-1">
                <div className="flex h-[60px] w-[60px]">
                  <IcNoDataFound />
                </div>
                <Text
                  type="body"
                  variant="medium"
                  className="text-Primary-Color"
                  elementType="div"
                >
                  No Data Found
                </Text>
              </div>
            )}
            {!loading && size(data) > 0 && (
              <div className={clsx('flex flex-col gap-1', listClassName)}>
                {data.map((item) => (
                  <div
                    key={item.id}
                    className={twMerge(
                      clsx(
                        'rounded-1.5 group flex cursor-pointer select-none items-center px-2 py-[6px]',
                        'hover:bg-Hover-Color data-[focus]:bg-Background-Color',
                        {
                          'bg-Hover-Color': isSelected(item),
                        },
                        itemClassName
                      )
                    )}
                    onClick={() => {
                      handleChangeSelected(item)
                      if (!multipleChoice) {
                        setOpen(false)
                      }
                    }}
                  >
                    {item?.icon}
                    <Text
                      className={clsx(
                        'overflow-hidden text-Primary-Color',
                        {
                          'bg-Main-Color bg-clip-text text-transparent':
                            isSelected(item),
                        },
                        {
                          'group-hover:!text-transparent': isSelected(item),
                        }
                      )}
                      type="subBody"
                      variant="medium"
                      elementType="div"
                      ellipsis
                    >
                      {item.name}
                    </Text>
                  </div>
                ))}
              </div>
            )}
          </SimpleBar>
        }
      >
        <div className="relative flex w-full cursor-pointer items-center">
          <div
            className={twMerge(
              clsx(
                'flex h-5 max-w-[190px] items-center gap-1 px-1',
                'hover:border-Main-03',
                'transition duration-75 ease-in',
                { 'border-Error-Color': error },
                containerClassName
              )
            )}
          >
            {icon}
            {selected ? (
              <Text
                className={clsx(
                  'w-full overflow-hidden text-start text-Tertiary-Color',
                  isActiveSelected && '!text-Primary-Color',
                  disabled && '!text-Disable-Text'
                )}
                type="subBody"
                variant="regular"
                elementType="div"
                ellipsis
              >
                {selectedName ?? ''}
              </Text>
            ) : (
              <Text
                className="text-Tertiary-Text w-full overflow-hidden text-start"
                type="subBody"
                variant="regular"
                elementType="div"
                ellipsis
              >
                {placeholder ?? ''}
              </Text>
            )}
            {allowClear && (
              <IconButton
                nameIcon="vuesax-bold-close-circle"
                sizeIcon={14}
                colorIcon={colors['border-base-icon']}
                hoverColor={colors['Tertiary-Color']}
                className="ml-1"
                onClick={clearHandler}
              />
            )}

            <div
              className={twMerge(
                clsx('group flex', {
                  'genai_button_icon__active rotate-180 transform': open,
                })
              )}
            >
              <Icon
                name="Outline-Chevron-Down"
                size={14}
                color={
                  isActiveSelected
                    ? colors['Primary-Color']
                    : colors['Tertiary-Color']
                }
              />
            </div>
          </div>
        </div>
      </Popover>
    </div>
  )
}

export default Select
