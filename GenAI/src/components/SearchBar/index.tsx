import Icon from '@/assets/icon/Icon'
import { DEBOUNCE_TIME } from '@/constants'
import { colors } from '@/theme'
import clsx from 'clsx'
import { debounce } from 'lodash'
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import { useLocation } from 'react-router-dom'
import Select, { ISelectBaseItem } from './components/Select'
import './index.scss'

type SelectType = {
  overlayClassName?: string
  contentClassName?: string
  listClassName?: string
  containerClassName?: string
  placement?:
    | 'top'
    | 'top start'
    | 'top end'
    | 'bottom'
    | 'bottom start'
    | 'bottom end'
    | 'left'
    | 'left start'
    | 'left end'
    | 'right'
    | 'right start'
    | 'right end'
  loading?: boolean
  optionMaxWidth?: number
  data?: ISelectBaseItem[]
  selected?: ISelectBaseItem
  selectedMultiple?: ISelectBaseItem[]
  multipleChoice?: boolean
  allowClear?: boolean
  onOpen?: () => void
  onClear?: () => void
  onChangeSelectedValue: (value: ISelectBaseItem) => void
}

interface IProps {
  className?: string
  onPressEnter?: (e: any) => void
  onSearch?: (query: string) => void
  placeholder?: string
  hasFilter?: boolean
  filterOptions?: SelectType[]
  debounceTime?: number
}

export interface SearchBarInterface {
  resetSearchBar: () => void
}

const SearchBar = forwardRef(
  (
    {
      className,
      onPressEnter,
      onSearch,
      placeholder = 'Search by keywords',
      hasFilter = false,
      filterOptions,
      debounceTime = DEBOUNCE_TIME,
    }: IProps,
    ref
  ) => {
    const inpRef = useRef(null)

    const location = useLocation()

    const [isFocus, setFocused] = useState(false)

    const [search, setSearch] = useState('')

    const handleKeyDown = useCallback(
      (event: any) => {
        if (event.key === 'Enter') {
          onPressEnter?.(event)
          setSearch(search?.trim())
        }
      },
      [onPressEnter, search]
    )
    useImperativeHandle(
      ref,
      () => ({
        resetSearchBar: () => {
          setSearch('')
        },
      }),
      []
    )

    const debouncedSearch = useCallback(
      debounce((value: string) => {
        onSearch?.(value.trim())
      }, debounceTime),
      []
    )

    const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newQuery = event.target.value
      setSearch(newQuery)
      debouncedSearch(newQuery)
    }

    const onFocus = useCallback(() => setFocused(true), [])
    const onBlur = useCallback(() => {
      setSearch(search?.trim())
      setFocused(false)
    }, [search])

    useEffect(() => {
      setSearch('')
    }, [location])

    return (
      <div
        id="search-bar"
        className={clsx(
          'hover-bg-clip-padding-border box-border flex h-9 min-w-[211px] items-center gap-3 rounded-full border border-border-base-icon bg-white px-3 py-2 hover:border-transparent hover:bg-Input-Main-03 hover:bg-origin-border',
          isFocus && '!border-Base-Neutral shadow-focus',
          className
        )}
      >
        <div className="flex w-full min-w-[187px]">
          <input
            className="flex-grow bg-transparent text-subBody font-regular text-Primary-Color outline-0 placeholder:text-subBody placeholder:text-Placeholder-Text"
            ref={inpRef}
            value={search}
            placeholder={placeholder}
            onChange={(e) => handleSearch(e)}
            onKeyDown={handleKeyDown}
            onFocus={onFocus}
            onBlur={onBlur}
          />
          <div className="cursor-pointer" onClick={onPressEnter}>
            <Icon
              name="Icon-Solid-Search"
              size={20}
              color={isFocus ? colors['Base-Neutral'] : colors.neutral[300]}
            />
          </div>
        </div>

        {hasFilter && filterOptions?.length && (
          <div className="flex w-fit items-center gap-1">
            <div className="flex h-[14px] w-[1px] bg-neutral-300" />
            {filterOptions?.map((option, index) => (
              <Select
                key={index}
                allowRemove
                placement={option?.placement ?? 'bottom end'}
                overlayClassName={clsx(
                  '[--anchor-gap:8px]',
                  option?.overlayClassName
                )}
                contentClassName={option?.contentClassName}
                listClassName={option?.listClassName}
                containerClassName={option?.containerClassName}
                loading={option?.loading}
                optionMaxWidth={option?.optionMaxWidth}
                data={option?.data ?? []}
                selected={option?.selected}
                selectedMultiple={option?.selectedMultiple}
                multipleChoice={option?.multipleChoice}
                allowClear={option?.allowClear}
                onOpen={option?.onOpen}
                onClear={option?.onClear}
                onChangeSelectedValue={option?.onChangeSelectedValue}
              />
            ))}
          </div>
        )}
      </div>
    )
  }
)

export default memo(SearchBar)
