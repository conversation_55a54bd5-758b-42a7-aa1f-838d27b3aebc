import { memo, useMemo } from 'react'
import Text from '../Text'
import { Item01, Item02, Item03, Item04 } from './icons'
import { twMerge } from 'tailwind-merge'

const EmptyData = ({
  size = 'medium',
  type,
  className,
  classNameText,
  icSize,
  title,
  content,
}: EmptyDataProps) => {
  const icon = useMemo(() => {
    const iconSize = icSize ? icSize : size === 'small' ? 120 : 150
    if (type === '01') {
      return <Item01 size={iconSize} />
    }
    if (type === '02') {
      return <Item02 size={iconSize} />
    }
    if (type === '04') {
      return <Item04 size={iconSize} />
    }
    return <Item03 size={iconSize} />
  }, [size, type])

  const titleType = useMemo(() => {
    if (size === 'small') {
      return 'body'
    }
    return 'subheading'
  }, [size])

  const titleVariant = useMemo(() => {
    if (size === 'small') {
      return 'bold'
    }
    return 'medium'
  }, [size])

  const textType = useMemo(() => {
    if (size === 'small') {
      return 'subBody'
    }
    return 'body'
  }, [size])

  return (
    <div className={`flex w-fit flex-col items-center ${className}`}>
      {icon}
      <Text
        type={titleType}
        variant={titleVariant}
        className={twMerge('mt-[8px] text-Primary-Color', classNameText)}
        elementType="div"
      >
        {title ?? 'Empty!'}
      </Text>
      <Text
        type={textType}
        variant="regular"
        className="mt-[4px] text-Secondary-Color"
        elementType="div"
      >
        {content ?? 'No available items here'}
      </Text>
    </div>
  )
}

interface EmptyDataProps {
  size?: 'small' | 'medium'
  type: '01' | '02' | '03' | '04'
  className?: string
  classNameText?: string
  icSize?: number
  title?: string
  content?: string
}

export default memo(EmptyData)
