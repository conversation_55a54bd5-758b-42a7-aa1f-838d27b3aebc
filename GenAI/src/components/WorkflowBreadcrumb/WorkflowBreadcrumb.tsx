import Icon from '@/assets/icon/Icon'
import { colors } from '@/theme'
import clsx from 'clsx'
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useNavigate } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'
import { InputProps } from '../Input'
import PageHeader from '../PageHeader'
import Text from '../Text'

const InputFitContent = ({
  className,
  value,
  placeholder,
  maxLength,
  hasIconEdit,
  isError,
  onFocus,
  onBlur,
  onPressEnter,
  onKeyDown,
  onChange,
  useText,
}: Partial<InputProps> & {
  hasIconEdit?: boolean
  isError?: boolean
  useText?: boolean
}) => {
  const inputReference = useRef<HTMLInputElement | null>(null)
  const ref = useRef<HTMLSpanElement | null>(null)
  const [inputWidth, setInputWidth] = useState(0)
  const [focused, setFocused] = useState(false)

  const iconColor = useMemo(() => {
    if (isError) {
      return colors['Error-Color']
    }
    if (focused || value) {
      return colors['Primary-Color']
    }
    return colors['border-base-icon']
  }, [isError, value, focused])

  const renderInputWidth = useCallback(() => {
    if (!focused && !value?.length && placeholder?.length) {
      return `${inputWidth}px`
    }

    return value?.length ? `${inputWidth}px` : '3px'
  }, [inputWidth, focused, value, placeholder])

  const handleFocus = useCallback(
    (event: React.FocusEvent) => {
      setFocused(true)
      onFocus?.(event)
    },
    [onFocus]
  )

  const handleBlur = useCallback(
    (event: React.FocusEvent) => {
      setFocused(false)
      onBlur?.(event)
    },
    [onBlur]
  )

  const handleKeyDown = useCallback(
    (event: any) => {
      if (event.key === 'Enter') {
        onPressEnter?.(event)
      }

      onKeyDown?.(event)
    },
    [onKeyDown, onPressEnter]
  )

  useLayoutEffect(() => {
    setInputWidth(ref.current?.clientWidth ?? 0)
  }, [focused, value, placeholder])

  useEffect(() => {
    if (useText && focused) {
      inputReference?.current?.focus?.()
    }
  }, [focused])

  return (
    <>
      <div
        className={clsx('flex items-center', {
          'gap-[8px]': hasIconEdit,
        })}
      >
        {useText && !focused && !!value?.length && (
          <Text
            className="overflow-hidden text-Secondary-Color"
            elementType="div"
            multipleLine={2}
            ellipsis
            onClick={() => {
              setFocused(true)
            }}
          >
            {value}
          </Text>
        )}
        <input
          ref={inputReference}
          className={twMerge(
            clsx(className, {
              'text-Error-Color placeholder:text-Error-Color': isError,
              'placeholder:text-transparent': focused,
              'invisible !w-0': useText && !focused && value?.length,
            })
          )}
          style={{
            width: renderInputWidth(),
          }}
          value={value}
          placeholder={placeholder}
          maxLength={maxLength}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          onChange={onChange}
        />
        {hasIconEdit && (
          <div
            className="flex cursor-pointer"
            onClick={() => inputReference?.current?.focus?.()}
          >
            <Icon
              name="Bold-MessagesConversation-Pen"
              size={20}
              color={iconColor}
            />
          </div>
        )}
      </div>
      <span
        ref={ref}
        className={clsx(
          'invisible fixed overflow-auto whitespace-pre',
          className
        )}
      >
        {focused || value ? value : placeholder}
      </span>
    </>
  )
}

export interface WorkflowBreadcrumbProps {
  isErrorName?: boolean
  urlPath?: string
  valueName?: string
  placeholderName?: string
  onChangeName?: (value: string) => void
  valueDescription?: string
  placeholderDescription?: string
  onChangeDescription?: (event: any) => void
}

const WorkflowBreadcrumb = ({
  isErrorName,
  urlPath,
  valueName,
  placeholderName = 'Type in workflow name',
  onChangeName,
  valueDescription,
  placeholderDescription = 'Type in description for workflow',
  onChangeDescription,
}: WorkflowBreadcrumbProps) => {
  const navigate = useNavigate()

  return (
    <PageHeader
      breadcrumbPaths={[
        {
          name: (
            <span
              className={clsx('flex', {
                'cursor-pointer duration-300 hover:text-Primary-Color': urlPath,
              })}
              onClick={() => {
                if (urlPath) {
                  navigate(urlPath)
                }
              }}
            >
              Workflows
            </span>
          ),
        },
        {
          name: (
            <InputFitContent
              className={twMerge(
                clsx(
                  'h-fit gap-[8px] rounded-none border-0 bg-transparent p-0 text-heading text-Primary-Color !shadow-none outline-none placeholder:text-Placeholder-Text'
                )
              )}
              maxLength={50}
              placeholder={placeholderName}
              value={valueName}
              onChange={(e) => onChangeName?.(e.target.value)}
              isError={isErrorName}
              hasIconEdit
              onBlur={() => onChangeName?.(valueName!.trim())}
            />
          ),
        },
      ]}
      title={
        <InputFitContent
          useText
          className="max-h-[42px] rounded-none border-0 bg-transparent p-0 text-body !shadow-none outline-none placeholder:text-Placeholder-Text"
          maxLength={255}
          placeholder={placeholderDescription}
          value={valueDescription}
          onChange={(e) => onChangeDescription?.(e.target.value)}
          onBlur={() => onChangeDescription?.(valueDescription!.trim())}
        />
      }
    />
  )
}

export default WorkflowBreadcrumb
