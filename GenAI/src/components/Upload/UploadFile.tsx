import Icon from '@/assets/icon/Icon'
import { colors } from '@/theme'
import clsx from 'clsx'
import { useEffect, useRef, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import Text from '../Text'
import { getDataFormat, getFileExt, getSizeIcon, getTypeText } from './helper'

interface UploadProps {
  className?: string
  multiple?: boolean
  dataTypes?: string
  disabled?: boolean
  file?: File
  size?: 'smaller' | 'small' | 'medium' | 'large'
  onChange?: (files: File[] | undefined) => void
  limit?: number
  isUploadFailed?: boolean
  uploadFailedMessage?: string
  isDisabledRemove?: boolean
}

const DEFAULT_GRADIENT = [colors['Base-Neutral'], colors['Base-Neutral']]
const LIMIT_SIZE = 200 * 1048576 // 200MB

const UploadFile = (props: UploadProps) => {
  const {
    limit = LIMIT_SIZE,
    size = 'medium',
    className,
    multiple = false,
    dataTypes = 'image/png',
    onChange,
    disabled = false,
    isUploadFailed = false,
    uploadFailedMessage,
  } = props

  const inputFile = useRef(null)
  const [hover, setHover] = useState(false)
  const [gradientIcon, setGradientIcon] = useState(DEFAULT_GRADIENT)

  const [alert, setAlert] = useState('')
  const [isDragging, setIsDragging] = useState(false)

  const dataFormat = getDataFormat(dataTypes)

  useEffect(() => {
    if (hover) {
      setGradientIcon(['#642B7333', '#C6426E33'])
    } else {
      setGradientIcon(DEFAULT_GRADIENT)
    }
  }, [hover])

  useEffect(() => {
    if (isUploadFailed && uploadFailedMessage) {
      setAlert(uploadFailedMessage ?? 'Failed')
    }
  }, [isUploadFailed, uploadFailedMessage])

  const validateFile = (file: File) => {
    const ext = getFileExt(file.name) ?? ''
    const acceptTypes = dataTypes.split(',')

    if (!acceptTypes.includes(`.${ext}`)) {
      return `Only accept ${dataFormat} format`
    }

    if (file.size > limit) {
      return `Maximum file size: ${limit / 1024 ** 2}MB`
    }

    return false
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files

    if (!files?.length) return

    const flattenFiles: File[] = [...files]
    const validatedFiles = flattenFiles.filter((file: File) => {
      const error = validateFile(file)
      if (error) {
        setAlert(error)
      }

      return !error
    })

    if (flattenFiles?.length !== validatedFiles?.length) {
      onChange?.(validatedFiles)

      return
    }

    setAlert('')
    onChange?.(validatedFiles)

    // @ts-expect-error: Unreachable code error
    inputFile.current.value = null
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)

    const files = e.dataTransfer.files

    if (!files?.length) return

    const flattenFiles: File[] = [...files]
    const validatedFiles = flattenFiles.filter((file: File) => {
      const error = validateFile(file)
      if (error) {
        setAlert(error)
      }

      return !error
    })

    if (flattenFiles?.length !== validatedFiles?.length) {
      onChange?.(validatedFiles)

      return
    }

    setAlert('')
    onChange?.(validatedFiles)

    // @ts-expect-error: Unreachable code error
    inputFile.current.value = null
  }

  const handleButtonClick = () => {
    // @ts-expect-error: Object is possibly 'null'.
    document.getElementById('fInput').click()
  }

  const handleDragOver = (e: { preventDefault: () => void }) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  return (
    <div className={clsx('flex flex-col items-end gap-[4px]', className)}>
      <div
        onDrop={handleDrop}
        onClick={handleButtonClick}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={twMerge(
          // eslint-disable-next-line max-len
          `relative flex h-full w-full cursor-pointer items-center justify-center rounded-[8px] border-[1px] border-dashed border-border-base-icon px-[12px] py-[20px] ${isDragging ? 'border-Base-Single-Color bg-Hover-Color' : ''} hover:border-Base-Single-Color hover:bg-Hover-Color`,
          clsx({
            '!border-[0.75px] border-dashed border-Error-Color':
              isUploadFailed || alert,
          }),
          className
        )}
      >
        <input
          ref={inputFile}
          id="fInput"
          type="file"
          name="file"
          multiple={multiple}
          accept={dataTypes}
          onChange={handleFileInput}
          style={{ display: 'none' }}
          disabled={disabled}
        />

        <div className="relative flex h-full w-full flex-col items-center justify-center gap-[8px]">
          <Icon
            name="Bold-Notes-Document"
            gradient={gradientIcon}
            size={getSizeIcon(size)}
          />
          <div className="flex flex-col items-center justify-center gap-[2px]">
            <Text
              type={getTypeText(size)}
              variant="regular"
              className={`text-center duration-300 ${hover ? 'text-Primary-Color' : 'text-Secondary-Color'}`}
            >
              Drag or click to upload
            </Text>

            <Text
              type={getTypeText(size)}
              variant="regular"
              className={`text-center duration-300 ${hover ? 'text-Primary-Color' : 'text-Secondary-Color'}`}
            >
              {size !== 'smaller'
                ? 'Maximum 200MB. Support types: '
                : 'Types: '}
              {dataFormat}
            </Text>
          </div>
        </div>
      </div>

      {alert && (
        <div className="flex w-full items-center gap-[2px] px-[4px] py-[2px]">
          {!isUploadFailed && (
            <Icon name="vuesax-bold-info-circle" color="#B91C1C" size={10} />
          )}

          <Text type="helperText" className="text-Error-Color">
            {alert}
          </Text>
        </div>
      )}
    </div>
  )
}

export default UploadFile
