import Icon from '@/assets/icon/Icon'
import { getUrlImage } from '@/helpers'
import clsx from 'clsx'
import { useEffect, useRef, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import IconButton from '../IconButton'
import Message from '../Message'
import Text from '../Text'

interface UploadProps {
  className?: string
  multiple?: boolean
  dataTypes?: string
  onChange: (image: any) => void
  disabled?: boolean
  image: string | undefined | null
  size?: 'smaller' | 'small' | 'medium' | 'large'
  onChangeFile?: (image: File | null) => void
  limit?: number
  type?: 'mini' | 'default'
}

function isBase64(str: string) {
  if (typeof str !== 'string') {
    return false
  }

  const base64PrefixPattern = /^data:image\/[a-zA-Z]+;base64,/
  if (base64PrefixPattern.test(str)) {
    str = str.replace(base64PrefixPattern, '')
  }

  const base64Pattern =
    /^(?:[A-Za-z0-9+/]{4})*?(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/
  return base64Pattern.test(str)
}
const Upload = (props: UploadProps) => {
  const {
    limit = 1048576,
    size = 'medium',
    className,
    multiple = false,
    dataTypes = 'image/png',
    onChange,
    onChangeFile,
    disabled = false,
    image,
    type,
  } = props

  const inputFile = useRef(null)
  const [hover, setHover] = useState(false)
  const [gradientIcon, setGradientIcon] = useState(['#D3C6CB', '#D3C6CB'])

  const [alert, setAlert] = useState('')
  const [isDragging, setIsDragging] = useState(false)

  const [dataFormat, setDataFormat] = useState('')

  const handleFileInput = (e: any) => {
    const file = e.target.files[0]

    if (file) {
      if (
        dataFormat
          .split(',')
          .find((n: any) => n?.trim() === file.name.split('.')[1])
      ) {
        if (file.size <= limit) {
          if (onChangeFile) onChangeFile(file)

          const reader = new FileReader()

          reader.onloadend = () => {
            onChange(reader.result)
          }
          reader.readAsDataURL(file)
        } else {
          onChange(null)
          if (onChangeFile) onChangeFile(null)

          if (type === 'mini') {
            Message.error({
              message: `Maximum file size: ${limit / 1024 ** 2}MB`,
            })
          } else {
            setAlert(
              `Maximum${size === 'smaller' ? '' : ' file'}  size: ${limit / 1024 ** 2}MB`
            )
          }
        }
      } else {
        onChange(null)
        if (type === 'mini') {
          Message.error({
            message: `Only accept ${dataFormat} format`,
          })
        } else {
          setAlert(
            `Only accept ${dataFormat}${size === 'smaller' ? '' : ' format'}`
          )
        }
      }
      // @ts-expect-error: Unreachable code error
      inputFile.current.value = null
    }
  }

  const handleDrop = (e: any) => {
    e.preventDefault()
    setIsDragging(false)

    const file = e.dataTransfer.files[0]

    if (
      file &&
      dataFormat
        .split(',')
        .find((n: any) => n?.trim() === file.name.split('.')[1])
    ) {
      if (file.size <= limit) {
        if (onChangeFile) onChangeFile(file)

        const reader = new FileReader()

        reader.onloadend = () => {
          onChange(reader.result)
        }
        reader.readAsDataURL(file)
      } else {
        onChange(null)
        if (onChangeFile) onChangeFile(null)

        if (type === 'mini') {
          Message.error({
            message: `Maximum file size: ${limit / 1024 ** 2}MB`,
          })
        } else {
          setAlert(
            `Maximum${size === 'smaller' ? '' : ' file'} size: ${limit / 1024 ** 2}MB`
          )
        }
      }
      inputFile.current = null
    } else if (
      !dataFormat
        .split(',')
        .find((n: any) => n?.trim() === file.name.split('.')[1])
    ) {
      onChange(null)
      if (type === 'mini') {
        Message.error({
          message: `Only accept ${dataFormat} format`,
        })
      } else {
        setAlert(
          `Only accept ${dataFormat}${size === 'smaller' ? '' : ' format'}`
        )
      }
    }
  }

  useEffect(() => {
    if (hover) {
      setGradientIcon(['#642B734D', '#C6426E4D'])
    } else {
      setGradientIcon(['#D3C6CB', '#D3C6CB'])
    }
  }, [hover])

  const handleButtonClick = () => {
    // @ts-expect-error: Object is possibly 'null'.
    document.getElementById('fileInput').click()
  }

  const handleRemoveImage = (e: any) => {
    e.stopPropagation()
    onChange(undefined)
    if (onChangeFile) onChangeFile(null)
    // @ts-expect-error: Object is possibly 'null'.
    document.getElementById('fileInput').value = ''
  }
  const handleDragOver = (e: { preventDefault: () => void }) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const sizeIcon = (size: string) => {
    switch (size) {
      case 'smaller':
        return 24
      case 'small':
        return 28
      case 'medium':
        return 40
      case 'large':
        return 64
      default:
        break
    }
  }

  const sizeImage = (size: string) => {
    switch (size) {
      case 'smaller':
        return 60
      case 'small':
        return 62
      case 'medium':
        return 80
      case 'large':
        return 110
      default:
        break
    }
  }

  const typeText = (size: string) => {
    switch (size) {
      case 'smaller':
        return 'helperText'
      case 'small':
        return 'helperText'
      case 'medium':
        return 'supportText'
      case 'large':
        return 'subBody'
      default:
        return 'subBody'
    }
  }

  useEffect(() => {
    if (dataTypes) {
      setDataFormat(
        dataTypes
          .split(',')
          .map((n: string) => {
            return n.split('/')[1] === 'jpeg'
              ? ' jpg, jpeg'
              : ' '.concat(n.split('/')[1])
          })
          .join(',')
      )
    }
  }, [dataTypes])

  useEffect(() => {
    if (image) {
      setAlert('')
    }
  }, [image])

  if (type === 'mini') {
    return (
      // <Tooltip text={image ? '' : 'Drag or click to upload logo'}>
      <div
        style={{
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'contain',
          backgroundPosition: 'center',
          backgroundImage: image
            ? `url(${isBase64(image) ? image : getUrlImage(image)})`
            : 'none',
        }}
        onDrop={handleDrop}
        onClick={handleButtonClick}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={clsx(
          'flex h-[56px] w-[56px] cursor-pointer items-center justify-center rounded-[8px] border-[1px] border-dashed border-border-base-icon hover:border-Base-Single-Color hover:bg-Hover-Color',
          isDragging ? 'border-Base-Single-Color bg-Hover-Color' : '',
          className
        )}
      >
        {!image && (
          <Icon name="Customize-Upload" gradient={gradientIcon} size={40} />
        )}
        <input
          ref={inputFile}
          id="fileInput"
          type="file"
          name="file"
          multiple={multiple}
          accept={dataTypes}
          onChange={handleFileInput}
          style={{ display: 'none' }}
          disabled={disabled}
        />
        {image && hover && (
          <div
            onClick={(e) => handleRemoveImage(e)}
            className="flex h-full w-full items-center justify-center bg-[#0000001A]"
          >
            <Icon name="Customize-DeleteFilled" size={24} color="#B91C1C" />
          </div>
        )}
      </div>
      // </Tooltip>
    )
  }

  return (
    <div className="flex flex-col items-end gap-[4px]">
      <div
        onDrop={handleDrop}
        onClick={handleButtonClick}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={twMerge(
          // eslint-disable-next-line max-len
          `relative flex h-full w-full cursor-pointer items-center justify-center rounded-[8px] border-[1px] border-dashed border-border-base-icon px-[12px] py-[20px] ${isDragging ? 'border-Base-Single-Color bg-Hover-Color' : ''} hover:border-Base-Single-Color hover:bg-Hover-Color`,
          className
        )}
      >
        <input
          ref={inputFile}
          id="fileInput"
          type="file"
          name="file"
          multiple={multiple}
          accept={dataTypes}
          onChange={handleFileInput}
          style={{ display: 'none' }}
          disabled={disabled}
        />

        <div className="relative flex flex-col items-center justify-center gap-[8px]">
          {!image ? (
            <>
              <Icon
                name="vuesax-bold-image"
                gradient={gradientIcon}
                size={sizeIcon(size)}
              />
              <div className="flex flex-col items-center justify-center gap-[2px]">
                <Text
                  type={typeText(size)}
                  variant="regular"
                  className={`text-center ${hover ? 'text-Primary-Color' : 'text-Secondary-Color'}`}
                >
                  Drag or click to upload
                </Text>

                <Text
                  type={typeText(size)}
                  variant="regular"
                  className={`text-center ${hover ? 'text-Primary-Color' : 'text-Secondary-Color'}`}
                >
                  {size !== 'smaller'
                    ? 'Maximum 1MB. Support types: '
                    : 'Types: '}
                  {dataFormat}
                </Text>
              </div>
            </>
          ) : (
            <img
              className="rounded-md bg-contain"
              style={{
                width: sizeImage(size),
                height: sizeImage(size),
              }}
              src={isBase64(image) ? image : getUrlImage(image)}
            />
          )}
        </div>
        {image && size === 'smaller' && (
          <IconButton
            className="absolute bottom-[4px] right-[7px] h-fit w-fit"
            nameIcon="Customize-Delete"
            colorIcon="#E5E5E5"
            hoverColor="#D3C6CB"
            sizeIcon={16}
            onClick={(e) => handleRemoveImage(e)}
          />
        )}
      </div>

      {image && size !== 'smaller' && (
        <div
          className="flex cursor-pointer items-center justify-center rounded px-[4px] py-[2px] hover:bg-Hover-Color"
          onClick={(e) => handleRemoveImage(e)}
        >
          <div className="cursor-pointer text-[10px] font-medium leading-[15px] text-red-800">
            Remove
          </div>
        </div>
      )}

      {alert && !image && (
        <div className="flex w-full items-center gap-[2px]">
          <Icon name="vuesax-bold-info-circle" color="#B91C1C" size={10} />

          <Text type="helperText" className="text-Error-Color">
            {alert}
          </Text>
        </div>
      )}
    </div>
  )
}

export default Upload
