import Icon from '@/assets/icon/Icon'
import { DialogMessageProps } from '.'
import ActionButton from '../ActionButton'
import Modal from '../Modal'

import './DialogMessage.scss'

const DialogMessageAsync = ({
  type,
  mainMessage,
  subMessage,
  open,
  onClick,
  onClose,
  isLoadingConfirm,
}: Partial<DialogMessageProps>) => {
  const handleActiveButton = () => {
    onClick?.()
  }

  const renderContent = () => {
    switch (type) {
      case 'confirmAsync':
        return (
          <div className="dialog-container">
            <div className="flex w-full flex-col gap-[12px]">
              <div className="flex w-full items-center justify-center gap-[11px]">
                <div className="wrap-icon min-h-[40px]] flex min-w-[40px] items-center justify-center bg-blue-50">
                  <Icon
                    name="Bold-EssentionalUI-QuestionCircle"
                    color="#3B82F6"
                    size={24}
                  />
                </div>

                <div className="title-dialog text-blue-500">Confirmation</div>
              </div>

              {mainMessage && subMessage && (
                <div className="flex w-full flex-col gap-[4px]">
                  <div className="main-message-dialog text-blue-500">
                    {mainMessage}
                  </div>
                  <div className="bug-message-dialog text-blue-500">
                    {subMessage}
                  </div>
                </div>
              )}
            </div>

            <div className="flex w-full gap-[12px]">
              <ActionButton
                className="w-full"
                type="secondary"
                action="confirm"
                onClick={() => {
                  onClose?.(false)
                }}
                text="Cancel"
              />
              <ActionButton
                className="w-full"
                type="primary"
                action="confirm"
                loading={isLoadingConfirm}
                onClick={() => {
                  handleActiveButton()
                }}
                text="Confirm"
              />
            </div>
          </div>
        )

      default:
        break
    }
  }

  return (
    <Modal
      open={open}
      showFooter={false}
      className="max-h-[240px] min-h-[167px] min-w-[368px] max-w-[420px] bg-white"
      classNameContent="mt-[0px]"
      classNameHeader="gap-[0px]"
      showCloseButton={false}
    >
      {renderContent()}
    </Modal>
  )
}

export default DialogMessageAsync
