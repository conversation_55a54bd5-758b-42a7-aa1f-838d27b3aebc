import Icon from '@/assets/icon/Icon'
import { useCallback, useEffect, useRef, useState } from 'react'
import { createRoot } from 'react-dom/client'
import ActionButton from '../ActionButton'
import Modal from '../Modal'
import './DialogMessage.scss'
import DialogMessageAsync from './DialogMessageAsync'

export interface DialogMessageProps {
  type: 'require' | 'confirm' | 'warning' | 'error' | 'confirmAsync'
  mainMessage: string
  subMessage: string
  duration?: number | undefined
  onClick?: () => void

  isLoadingConfirm?: boolean
  open?: boolean
  onClose?: (isOpen: boolean) => void
  onConfirm?: () => Promise<void>
}

const DialogMessage = ({
  type,
  mainMessage,
  subMessage,
  duration,
  onClick,
}: DialogMessageProps) => {
  const [isOpen, setIsOpen] = useState(true)
  const durationRef = useRef<any>()

  const handleActiveButton = useCallback(() => {
    if (duration) {
      clearTimeout(durationRef.current)
    }
    setIsOpen(false)
    onClick?.()
  }, [duration, onClick])

  useEffect(() => {
    if (duration) {
      durationRef.current = setTimeout(() => {
        setIsOpen(false)
        onClick?.()
      }, duration * 1000)
    }
  }, [duration])

  const renderContent = useCallback(() => {
    switch (type) {
      case 'require':
        return (
          <div className="dialog-container">
            <div className="flex w-full flex-col gap-[12px]">
              <div className="flex w-full items-center justify-center gap-[11px]">
                <div className="wrap-icon min-h-[40px]] flex min-w-[40px] items-center justify-center bg-slate-100">
                  <Icon
                    name="vuesax-bold-info-circle"
                    color="#1E293B"
                    size={24}
                  />
                </div>

                <div className="title-dialog text-slate-800">
                  Action required
                </div>
              </div>

              {mainMessage && subMessage && (
                <div className="flex w-full flex-col gap-[4px]">
                  <div className="main-message-dialog text-slate-800">
                    {mainMessage}
                  </div>
                  <div className="bug-message-dialog text-slate-800">
                    {subMessage}
                  </div>
                </div>
              )}
            </div>

            <ActionButton
              className="w-full"
              type="primary"
              action="required"
              onClick={() => {
                handleActiveButton()
              }}
              text="OK"
            />
          </div>
        )

      case 'confirm':
        return (
          <div className="dialog-container">
            <div className="flex w-full flex-col gap-[12px]">
              <div className="flex w-full items-center justify-center gap-[11px]">
                <div className="wrap-icon min-h-[40px]] flex min-w-[40px] items-center justify-center bg-blue-50">
                  <Icon
                    name="Bold-EssentionalUI-QuestionCircle"
                    color="#3B82F6"
                    size={24}
                  />
                </div>

                <div className="title-dialog text-blue-500">Confirmation</div>
              </div>

              {mainMessage && subMessage && (
                <div className="flex w-full flex-col gap-[4px]">
                  <div className="main-message-dialog text-blue-500">
                    {mainMessage}
                  </div>
                  <div className="bug-message-dialog text-blue-500">
                    {subMessage}
                  </div>
                </div>
              )}
            </div>

            <div className="flex w-full justify-center gap-[12px]">
              <ActionButton
                type="secondary"
                action="confirm"
                onClick={() => {
                  setIsOpen(false)
                }}
                text="Cancel"
              />
              <ActionButton
                type="primary"
                action="confirm"
                onClick={() => {
                  handleActiveButton()
                }}
                text="Confirm"
              />
            </div>
          </div>
        )

      case 'warning':
        return (
          <div className="dialog-container">
            <div className="flex w-full flex-col gap-[12px]">
              <div className="flex w-full items-center justify-center gap-[11px]">
                <div className="wrap-icon min-h-[40px]] flex min-w-[40px] items-center justify-center bg-amber-50">
                  <Icon name="vuesax-bold-danger" color="#B45309" size={24} />
                </div>

                <div className="title-dialog text-amber-700">Warning</div>
              </div>

              {mainMessage && subMessage && (
                <div className="flex w-full flex-col gap-[4px]">
                  <div className="main-message-dialog text-amber-700">
                    {mainMessage}
                  </div>
                  <div className="bug-message-dialog text-amber-700">
                    {subMessage}
                  </div>
                </div>
              )}
            </div>

            <div className="flex w-full justify-center gap-[12px]">
              <ActionButton
                type="secondary"
                action="warning"
                onClick={() => {
                  setIsOpen(false)
                }}
                text="Cancel"
              />
              <ActionButton
                type="primary"
                action="warning"
                onClick={() => {
                  handleActiveButton()
                }}
                text="Confirm"
              />
            </div>
          </div>
        )
      case 'error':
        return (
          <div className="dialog-container">
            <div className="flex w-full flex-col gap-[12px]">
              <div className="flex w-full items-center justify-center gap-[11px]">
                <div className="wrap-icon min-h-[40px]] flex min-w-[40px] items-center justify-center bg-red-50">
                  <Icon
                    name="vuesax-bold-close-circle"
                    color="#991B1B"
                    size={24}
                  />
                </div>

                <div className="title-dialog text-red-800">Error</div>
              </div>

              {mainMessage && subMessage && (
                <div className="flex w-full flex-col gap-[4px]">
                  <div className="main-message-dialog text-red-800">
                    {mainMessage}
                  </div>
                  <div className="bug-message-dialog text-red-800">
                    {subMessage}
                  </div>
                </div>
              )}
            </div>

            <div className="flex w-full justify-center gap-[12px]">
              <ActionButton
                type="secondary"
                action="error"
                onClick={() => {
                  setIsOpen(false)
                }}
                text="Cancel"
              />
              <ActionButton
                type="primary"
                action="error"
                onClick={() => {
                  handleActiveButton()
                }}
                text="Confirm"
              />
            </div>
          </div>
        )
      default:
        break
    }
  }, [handleActiveButton, mainMessage, subMessage, type])

  return (
    <Modal
      open={isOpen}
      showFooter={false}
      className="max-h-[240px] min-h-[167px] min-w-[368px] max-w-[420px] bg-white"
      classNameContent="mt-[0px]"
      classNameHeader="gap-[0px]"
      showCloseButton={false}
    >
      {renderContent()}
    </Modal>
  )
}

// Function to handle the creation of the modal dynamically
const createDialog = (props: DialogMessageProps) => {
  const modalContainer = document.createElement('div')
  document.body.appendChild(modalContainer)

  const root = createRoot(modalContainer)

  root.render(<DialogMessage {...props} />)
}

// Exporting different types of modals
export const MessageDialog = {
  confirm: (props: Omit<DialogMessageProps, 'type'>) =>
    createDialog({
      ...props,
      type: 'confirm',
    }),
  warning: (props: Omit<DialogMessageProps, 'type'>) =>
    createDialog({
      ...props,
      type: 'warning',
    }),
  error: (props: Omit<DialogMessageProps, 'type'>) =>
    createDialog({
      ...props,
      type: 'error',
    }),
  require: (props: Omit<DialogMessageProps, 'type'>) =>
    createDialog({
      ...props,
      type: 'require',
    }),
  ConfirmAsync: (props: Partial<DialogMessageProps>) => (
    <DialogMessageAsync {...props} type="confirmAsync" />
  ),
}
