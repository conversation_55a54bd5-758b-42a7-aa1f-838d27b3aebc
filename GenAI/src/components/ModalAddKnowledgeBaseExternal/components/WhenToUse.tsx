import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { twMerge } from 'tailwind-merge'

interface Props {
  text: string
  onChange: (value: string) => void
  className?: string
  classNameTextArea?: string
  placeholder?: string
}
const WhenToUse = ({
  text,
  onChange,
  placeholder = 'Describe when your worker needs to query data from knowledge base',
  className,
  classNameTextArea,
}: Props) => {
  const handleBlur = () => {
    if (text.trim() !== text) onChange(text.trim())
  }

  return (
    <div className={twMerge('flex w-full flex-col gap-1', className)}>
      <Text
        type="subBody"
        variant="medium"
        className="pl-1 text-Tertiary-Color"
      >
        When to use
      </Text>

      <TextArea
        value={text}
        onChange={(e) => onChange(e)}
        placeholder={placeholder}
        onBlur={handleBlur}
        className={twMerge('h-[89px]', classNameTextArea)}
        maxLength={255}
      />
    </div>
  )
}

export default WhenToUse
