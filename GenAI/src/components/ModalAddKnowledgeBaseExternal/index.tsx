import {
  KBExternalPublic,
  externalKnowledgeBaseFetchMyExternalKnowledgeBaseApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import { PAGE_SIZE } from '@/constants'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useEffect, useState } from 'react'
import BaseModal from '../BaseModal'
import Button from '../Button'
import EmptyData from '../EmptyData'
import Input from '../Input'
import NoDataFound from '../NoDataFound'
import Pagination from '../Pagination'
import Text from '../Text'
import ExternalKbItem from './components/ExternalKbItem'
import WhenToUse from './components/WhenToUse'
import IconButton from '../IconButton'

export interface CustomExternalKBPublic extends KBExternalPublic {
  is_external: boolean
  is_private: boolean
  file_extension: string
  file_display_name: string
  file_description: string
  directoryName: string
}

const ModalAddKnowledgeBaseExternal = ({
  isOpen,
  knowledgeBasePrompt,
  onClose,
  selected,
  onConfirm,
}: IModalAddKnowledgeBaseExternalProps) => {
  const [searchText, setSearchText] = useState('')
  const [searchTextDisplay, setSearchTextDisplay] = useState('')
  const [files, setFiles] = useState<CustomExternalKBPublic[]>([])
  const [selectedFiles, setSelectedFiles] = useState<CustomExternalKBPublic[]>(
    []
  )
  const [page, setPage] = useState(1)
  const [totalPage, setTotalPage] = useState(1)
  const [changed, setChanged] = useState(false)
  const [promptText, setPromptText] = useState(knowledgeBasePrompt ?? '')

  const fetchData = async () => {
    try {
      const res = await externalKnowledgeBaseFetchMyExternalKnowledgeBaseApi({
        query: {
          page_number: page,
          page_size: PAGE_SIZE.SMALL,
          name: searchText,
        },
      })

      if (res.status === 200) {
        const data = res.data?.data?.data || []
        setFiles(
          data.map((item) => ({
            ...item,
            is_external: true,
            is_private: !!item.is_sensitive,
            file_extension: '',
            file_display_name: item.name || '',
            file_description: item.description || '',
            directoryName: '',
            id: item.knowledge_base_id,
          }))
        )
        setTotalPage(res.data?.data?.total_pages || 1)
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  useEffect(() => {
    if (!isOpen) {
      setFiles([])
      setSelectedFiles([])
      setPage(1)
      setTotalPage(1)
      setSearchText('')
      setSearchTextDisplay('')
      setChanged(false)
      setPromptText('')
    } else {
      setPromptText(knowledgeBasePrompt ?? '')
    }
  }, [isOpen])

  useEffect(() => {
    if (isOpen) {
      fetchData()
    }
  }, [searchText, page, isOpen])

  useEffect(() => {
    if (isOpen) {
      setSelectedFiles(selected)
    }
  }, [selected, isOpen])

  const handleSelectTool = (tool: CustomExternalKBPublic) => {
    const index = selectedFiles.findIndex((item) => item.id === tool.id)
    if (index > -1) {
      setSelectedFiles([])
    } else {
      setSelectedFiles([tool])
    }
    setChanged(true)
  }

  useEffect(() => {
    if (searchTextDisplay.trim() !== searchText.trim()) {
      setSearchText(searchTextDisplay.trim())
      setPage(1)
      setTotalPage(1)
    }
  }, [searchTextDisplay])

  const showConfirm =
    changed && selectedFiles?.length > 0 && promptText.trim().length > 0

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div className="relative flex h-[534px] w-[965px] flex-col rounded-[20px] bg-white p-3 shadow-md">
        <div className="flex h-full w-full flex-row gap-3">
          <div className="flex h-full w-[calc(100%-300px)] flex-col">
            <Text
              type="body"
              variant="medium"
              className="px-[4px] text-Primary-Color"
              elementType="div"
            >
              Add external data
            </Text>
            <div className="mt-2 flex h-full w-full">
              <div className="flex w-full flex-col gap-3">
                <div className="flex w-full gap-3">
                  <div className="w-full">
                    <Input
                      placeholder="Search by display name"
                      className="text-Primary-Color"
                      value={searchTextDisplay}
                      onChange={(e) => setSearchTextDisplay(e.target.value)}
                      onBlur={() =>
                        setSearchTextDisplay(searchTextDisplay.trim())
                      }
                      suffix={
                        <Icon
                          name="Icon-Solid-Search"
                          size={16}
                          color={'#D4D4D4'}
                        />
                      }
                      activeSuffix={
                        <div
                          className="cursor-pointer select-none"
                          onClick={() =>
                            setSearchTextDisplay(searchTextDisplay.trim())
                          }
                        >
                          <Icon
                            name="Icon-Solid-Search"
                            size={16}
                            color={'#2D0136'}
                          />
                        </div>
                      }
                      onPressEnter={() =>
                        setSearchTextDisplay(searchTextDisplay.trim())
                      }
                    />
                  </div>
                </div>

                <div className="flex h-[400px] w-full flex-col gap-[4px] px-[4px]">
                  <div
                    className={clsx(
                      'genai-scrollbar flex w-full flex-col gap-3 overflow-y-auto',
                      {
                        'h-full': totalPage < 2,
                        'h-[calc(100%-24px)]': totalPage > 1,
                      }
                    )}
                  >
                    {files.map((file) => (
                      <ExternalKbItem
                        key={file.id}
                        selected={selectedFiles.some(
                          (item) => item.id === file.id
                        )}
                        title={file.name || ''}
                        description={file.description || ''}
                        onClick={() => handleSelectTool(file)}
                        isPrivate={!!file.is_sensitive}
                      />
                    ))}
                    {searchText.trim().length === 0 && files.length === 0 && (
                      <EmptyData
                        type="02"
                        className="mt-11 self-center"
                        size="small"
                      />
                    )}
                    {searchText.trim().length > 0 && files.length === 0 && (
                      <NoDataFound
                        className="mt-11"
                        textType="subheading"
                        textVariant="semibold"
                        subTextType="subBody"
                        subTextVariant="regular"
                        size={150}
                      />
                    )}
                  </div>
                  {totalPage > 1 && (
                    <Pagination
                      page={page}
                      totalPage={totalPage}
                      onChangePage={(page) => setPage(page)}
                      className="self-end"
                      type="mini"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex h-full w-[300px] flex-col gap-[8px]">
            <WhenToUse
              text={promptText}
              onChange={(value) => {
                setChanged(true)
                setPromptText(value)
              }}
              className="h-full w-[300px]"
              classNameTextArea="h-full"
            />
          </div>
        </div>

        <Button
          type="primary"
          size="small"
          disabled={!showConfirm}
          onClick={() => {
            onConfirm(selectedFiles, promptText.trim())
            onClose()
          }}
          text="Confirm"
          className="mt-[12px] w-[96px] self-end"
        />

        <IconButton
          className="absolute right-[10px] top-[10px] z-30 border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
      </div>
    </BaseModal>
  )
}

export default memo(ModalAddKnowledgeBaseExternal)

interface IModalAddKnowledgeBaseExternalProps {
  isOpen: boolean
  onClose: () => void
  selected: CustomExternalKBPublic[]
  knowledgeBasePrompt?: string | null
  onConfirm: (
    selected: CustomExternalKBPublic[],
    knowledgeBasePrompt: string
  ) => void
}
