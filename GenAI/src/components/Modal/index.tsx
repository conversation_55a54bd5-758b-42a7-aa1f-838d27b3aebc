import { colors } from '@/theme'
import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { PropsWithChildren, useCallback } from 'react'
import { twMerge } from 'tailwind-merge'
import But<PERSON> from '../Button'
import ModalHeader from './ModalHeader'
import IconButton from '../IconButton'

interface ModalProps {
  className?: string
  classNameContent?: string
  classNameHeader?: string
  classNameFooter?: string
  classNameOkButton?: string
  classNameCancelButton?: string
  mask?: boolean
  open?: boolean
  showHeader?: boolean
  showFooter?: boolean
  title?: string
  subTitle?: string
  okText?: string
  okDisable?: boolean
  okLoading?: boolean
  cancelText?: string
  onClickOk?: () => void
  onClickCancel?: () => void
  style?: React.CSSProperties
  showCloseButton?: boolean
  onClickClose?: () => void
}

const Modal = ({
  className,
  classNameContent,
  classNameFooter,
  classNameHeader,
  classNameOkButton,
  classNameCancelButton,
  open = false,
  showHeader = true,
  showFooter = true,
  title,
  subTitle,
  children,
  okText = 'Save',
  okDisable = false,
  okLoading = false,
  cancelText = 'Cancel',
  style,
  mask = true,
  showCloseButton = true,
  onClickCancel = () => {},
  onClickOk = () => {},
  onClickClose = () => {},
}: PropsWithChildren<ModalProps>) => {
  const handleRequestClose = useCallback(() => {
    onClickCancel?.()
    onClickClose?.()
  }, [onClickCancel, onClickClose])

  return (
    <Dialog open={open} onClose={handleRequestClose} className="relative z-50">
      {mask && (
        <DialogBackdrop className="pointer-events-none fixed inset-0 bg-black/5" />
      )}

      <div className="fixed inset-0 w-screen overflow-y-auto">
        <div className="flex min-h-full items-center justify-center">
          <DialogPanel
            className={twMerge(
              'relative rounded-[20px] border border-neutral-200 bg-Base-03 p-3 shadow-md',
              className
            )}
            style={style}
          >
            {showCloseButton && (
              <IconButton
                className="absolute right-[10px] top-[10px] flex h-[16px] w-[16px] cursor-pointer items-center justify-center duration-300 hover:bg-neutral-200"
                nameIcon="x-close"
                sizeIcon={16}
                colorIcon={colors.neutral[500]}
                onClick={handleRequestClose}
              />
            )}
            {showHeader && (
              <ModalHeader
                classNameHeader={classNameHeader}
                title={title}
                subTitle={subTitle}
              />
            )}

            <div className={twMerge('mt-[16px]', classNameContent)}>
              {children}
            </div>

            {showFooter && (
              <div
                className={twMerge(
                  'mt-[24px] flex h-[32px] gap-[12px]',
                  classNameFooter
                )}
              >
                <Button
                  type="secondary"
                  text={cancelText}
                  className={twMerge(
                    'w-[186px] min-w-[186px]',
                    classNameCancelButton
                  )}
                  onClick={onClickCancel}
                />
                <Button
                  disabled={okDisable}
                  type="primary"
                  loading={okLoading}
                  text={okText}
                  className={twMerge(
                    'w-[186px] min-w-[186px]',
                    classNameOkButton
                  )}
                  onClick={onClickOk}
                />
              </div>
            )}
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  )
}

export default Modal
