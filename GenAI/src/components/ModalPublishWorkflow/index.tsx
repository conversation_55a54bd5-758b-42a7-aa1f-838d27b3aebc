import { memo, useEffect, useState } from 'react'
import { colors } from '@/theme'
import BaseModal from '../BaseModal'
import Button from '../Button'
import Text from '../Text'
import Item from './Item'
import { WorkerType } from '@/pages/Workers/types'
import {
  WorkflowWorkerPublic,
  workflowGetListWorkersByWorkflowIdApi,
  workflowPublishWorkflowVerifyApi,
} from '@/apis/client'
import { HTTP_STATUS_CODE } from '@/constants'
import { MessageDialog } from '../DialogMessage'
import IconButton from '../IconButton'

const ModalPublishWorkflow = ({
  isOpen,
  id,
  onClose,
  onConfirm,
}: IModalPublishWorkflowProps) => {
  const [list, setList] = useState<WorkflowWorkerPublic[]>([])
  const [loading, setLoading] = useState<boolean>(false)

  const init = async () => {
    try {
      const res = await workflowGetListWorkersByWorkflowIdApi({
        path: {
          workflow_id: id,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const tList = res.data?.data?.data ?? []
        const removedDuplicate = tList.filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.id === item.id)
        )
        setList(removedDuplicate)
      } else {
        setList([])
      }
    } catch (error) {
      setList([])
    }
  }

  useEffect(() => {
    if (isOpen) {
      init()
    }
  }, [isOpen])

  const confirmHandler = async () => {
    setLoading(true)
    try {
      const res = await workflowPublishWorkflowVerifyApi({
        path: {
          workflow_id: id,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        onConfirm()
      } else {
        MessageDialog.require({
          mainMessage: 'Publish your tool to proceed',
          subMessage:
            'This workflow contains unpublished Tools. Please go to your Tool Collection and publish them all',
          onClick: onClose,
        })
      }
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div className="relative flex h-[568px] w-[996px] flex-col rounded-[20px] border border-neutral-200 bg-Base-03 p-3 shadow-md">
        <Text
          type="subheading"
          variant="medium"
          className="mb-1 text-Primary-Color"
          elementType="div"
        >
          Publish workflow
        </Text>
        <Text
          type="subBody"
          variant="regular"
          className="mb-4 text-Secondary-Color"
          elementType="div"
        >
          Please confirm to grant the purchaser authorization to utilize these
          private workers
        </Text>

        <div className="h-[-webkit-fill-available] w-full overflow-hidden rounded-lg border border-neutral-200 bg-white p-2">
          <Text
            type="body"
            variant="semibold"
            className="mgb-3 w-full bg-Main-Color bg-clip-text text-center text-transparent"
            elementType="div"
          >
            Workers
          </Text>
          <div className="genai-scrollbar flex max-h-[calc(100%-33px)] w-full flex-col gap-2 overflow-auto px-1">
            {list.map((item) => (
              <Item
                key={item.id}
                avatarUrl={item.avatar ?? ''}
                description={item.description}
                name={item.name}
                worker_type={WorkerType.AI}
              />
            ))}
          </div>
        </div>

        <Button
          type="primary"
          onClick={confirmHandler}
          text="Confirm"
          className="mt-6 w-[133px] self-end"
          loading={loading}
        />
        <IconButton
          className="absolute right-[10px] top-[10px] z-30 border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
      </div>
    </BaseModal>
  )
}

export default memo(ModalPublishWorkflow)

interface IModalPublishWorkflowProps {
  isOpen: boolean
  id: string
  onClose: () => void
  onConfirm: () => void
}
