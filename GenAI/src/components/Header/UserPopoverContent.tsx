import { UserPublicDetail } from '@/apis/client'
import { useAuth } from '@/hooks/useAuth'
import { rootUrls } from '@/routes/rootUrls'
import { memo } from 'react'
import { useNavigate } from 'react-router-dom'
import Avatar from '../Avatar'
import useSidebarStore from '../Sidebar/SidebarStore'
import Text from '../Text'
import './UserPopoverContent.scss'

interface UserPopoverContentProps {
  myProfile: UserPublicDetail | null | undefined
}
const UserPopoverContent = (props: UserPopoverContentProps) => {
  const { myProfile } = props

  const navigate = useNavigate()
  const { logout } = useAuth()
  const resetStore = useSidebarStore.use.resetStore()

  const handleLogout = () => {
    new Promise((resolve) => {
      logout()
      resetStore()
      resolve('')
    }).then(() => navigate(rootUrls.Login, { replace: true }))
  }

  return (
    <div className="min-h-[307px] min-w-[176px] max-w-[208px] bg-white">
      <div className="flex min-h-[140px] w-full flex-col items-center justify-center gap-[12px] px-[20px] py-[12px]">
        <Avatar
          className="min-h-[42px] min-w-[42px]"
          name={myProfile?.first_name ?? myProfile?.username ?? ''}
          avatarUrl={myProfile?.avatar || ''}
        />
        <div className="flex w-full flex-col items-center justify-center">
          <Text
            type="body"
            variant="semibold"
            className="break-all text-center text-Primary-Color"
          >
            {myProfile?.first_name}
          </Text>
          <div className="inline-block w-full overflow-hidden text-ellipsis whitespace-nowrap text-nowrap text-center text-supportText text-Secondary-Color">
            {myProfile?.email}
          </div>
        </div>
      </div>
      <div className="h-[1px] w-full bg-neutral-100"></div>
      <div className="flex flex-col items-center gap-[16px] px-[8px] py-[8px]">
        <div
          onClick={() => {
            navigate(rootUrls.Profile)
          }}
          className="settings-header flex w-full cursor-pointer items-center gap-[8px] rounded px-[8px] py-[4px] hover:bg-Hover-Color"
        >
          <div className="icon-settings-header"></div>
          <Text type="subBody" className="text-Primary-Color">
            Settings
          </Text>
        </div>
        <div
          className="pricing-header flex w-full cursor-pointer items-center gap-[8px] rounded px-[8px] py-[4px] hover:bg-Hover-Color"
          onClick={() => {
            navigate(rootUrls.PlansPricing)
          }}
        >
          <div className="icon-pricing-header"></div>
          <Text type="subBody" className="text-Primary-Color">
            Plans & Pricing
          </Text>
        </div>
      </div>

      <div className="h-[1px] w-full bg-neutral-100"></div>

      <div className="flex flex-col items-center gap-[16px] px-[8px] py-[8px]">
        <div
          onClick={() => handleLogout()}
          className="logout-header flex w-full cursor-pointer items-center gap-[8px] rounded px-[8px] py-[4px] hover:bg-Hover-Color"
        >
          <div className="icon-logout-header rotate-90"></div>
          <Text type="subBody" className="text-Primary-Color">
            Log out
          </Text>
        </div>

        <div className="flex w-full items-center justify-between p-[4px]">
          <Text
            type="supportText"
            variant="medium"
            className="text-Secondary-Color"
          >
            Privacy
          </Text>
          <Text
            type="supportText"
            variant="medium"
            className="text-Secondary-Color"
          >
            Terms
          </Text>
          <Text
            type="supportText"
            variant="medium"
            className="text-Secondary-Color"
          >
            Copyright
          </Text>
        </div>
      </div>
    </div>
  )
}

export default memo(UserPopoverContent)
