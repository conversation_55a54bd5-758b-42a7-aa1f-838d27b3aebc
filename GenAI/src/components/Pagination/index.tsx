import clsx from 'clsx'
import { trim } from 'lodash'
import { memo, useCallback, useEffect, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import { useDebouncedCallback } from 'use-debounce'
import Text from '../Text'
import ArrowLeft from './assets/ArrowLeft'

const DEBOUNCE_TIME = 100

interface PaginationProps {
  className?: string
  page: number
  totalPage?: number
  onChangePage: (page: number) => void
  type?: 'default' | 'mini'
}

const Pagination = ({
  onChangePage,
  page = 1,
  className,
  totalPage = 1000,
  type = 'default',
}: PaginationProps) => {
  const [isActiveInput, setIsActiveInput] = useState(false)
  const [inputValue, setInputValue] = useState<string>(page.toString())
  const [isFocus, setFocused] = useState(false)

  const handleChangePage = (value: number) => {
    if (isActiveInput) {
      setIsActiveInput(false)
    }
    onChangePage(value)
  }

  useEffect(() => {
    setInputValue(page.toString())
  }, [page])

  const handleNextPage = useDebouncedCallback(
    () => {
      const nextPage = page + 1
      if (nextPage > totalPage) {
        return
      }
      handleChangePage(nextPage)
    },
    DEBOUNCE_TIME,
    {
      leading: true,
      trailing: false,
    }
  )

  const handlePrevPage = useDebouncedCallback(
    () => {
      const prevPage = page - 1
      if (prevPage < 1) {
        return
      }
      handleChangePage(prevPage)
    },
    DEBOUNCE_TIME,
    {
      leading: true,
      trailing: false,
    }
  )

  const handleGoFirstPage = useDebouncedCallback(
    () => {
      handleChangePage(1)
    },
    DEBOUNCE_TIME,
    {
      leading: true,
      trailing: false,
    }
  )

  const handleGoLastPage = useDebouncedCallback(
    () => {
      handleChangePage(totalPage)
    },
    DEBOUNCE_TIME,
    {
      leading: true,
      trailing: false,
    }
  )

  const handleChangeInputNumber = (e: any) => {
    const value = e.target.value

    // check if value is not a number
    if (isNaN(value)) {
      return
    }

    if (value > totalPage) {
      return
    }

    setInputValue(trim(value))
  }

  const onBlur = () => {
    const targetPage = parseInt(inputValue)

    if (isNaN(targetPage) || targetPage < 1) {
      setIsActiveInput(false)
      setInputValue(page.toString())
      return
    }

    handleChangePage(targetPage)
  }

  const onPressEnter = () => {
    setIsActiveInput(false)

    const targetPage = parseInt(inputValue)

    if (isNaN(targetPage) || targetPage < 1) {
      setInputValue(page.toString())
      handleChangePage(page)
      return
    }

    handleChangePage(parseInt(inputValue))
  }

  const handleKeyDown = (event: any) => {
    if (event.key === 'Enter') {
      onPressEnter()
    }
  }

  const onFocus = useCallback(() => setFocused(true), [])

  const isDisablePrev = page - 1 < 1
  const isDisableGoFirst = page === 1
  const isDisableNext = page + 1 > totalPage
  const isDisableGoLast = page === totalPage

  return (
    <div
      className={twMerge(
        'flex h-[24px] w-auto items-center justify-center',
        className
      )}
    >
      <div
        className={twMerge(
          clsx(
            'flex flex-row gap-[24px]',
            type === 'default' ? 'gap-[24px]' : 'gap-[2px]'
          )
        )}
      >
        <div className="flex flex-row">
          {type === 'default' && (
            <ArrowLeft
              onClick={handleGoFirstPage}
              type="double"
              disable={isDisableGoFirst}
            />
          )}

          <ArrowLeft onClick={handlePrevPage} disable={isDisablePrev} />
        </div>

        <div className="flex items-center justify-center">
          {isActiveInput && (
            <div
              className={clsx(
                'box-border flex h-[22px] w-[44px] items-center rounded bg-Input-Field px-[8px]',
                isFocus && 'border border-Base-Neutral shadow-focus'
              )}
            >
              <input
                className={twMerge(
                  'h-[18px] w-full flex-grow bg-transparent !text-subBody font-regular text-Primary-Color outline-0 placeholder:text-subBody'
                )}
                value={inputValue.toString()}
                onChange={handleChangeInputNumber}
                onKeyDown={handleKeyDown}
                onFocus={onFocus}
                onBlur={onBlur}
                autoFocus
              />
            </div>
          )}

          {!isActiveInput && (
            <div
              className="flex h-[22px] cursor-pointer select-none items-center justify-center px-[4px] py-[2px] hover:bg-Hover-Color"
              onClick={() => {
                setIsActiveInput(true)
              }}
            >
              <Text
                type="subBody"
                variant="medium"
                className="!text-Primary-Color"
              >
                {page} of {totalPage}
              </Text>
            </div>
          )}
        </div>

        <div className="flex flex-row">
          <ArrowLeft
            className="rotate-180 transform"
            onClick={handleNextPage}
            disable={isDisableNext}
          />
          {type === 'default' && (
            <ArrowLeft
              className="rotate-180 transform"
              type="double"
              onClick={handleGoLastPage}
              disable={isDisableGoLast}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default memo(Pagination)
