import { KBFilePublic } from '@/apis/client'

export type KBFilePublicWDirectory = KBFilePublic & {
  knowledgeBaseFileStorageId: string
  directoryId?: string
  directoryName: string
}

export const ERR_NOT_ENOUGH_STORAGE =
  'Not enough storage available to complete, please recheck your Subscription Plan!'

export enum FileStatus {
  PREVIEW = 'preview',
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  ERROR = 'error',
}

export type FileUpload = {
  rawFile: File
  displayName: string
  description?: string
  status: FileStatus
  error?: boolean
  knowledgeFileId?: string
  respondedData?: KBFilePublicWDirectory
}
