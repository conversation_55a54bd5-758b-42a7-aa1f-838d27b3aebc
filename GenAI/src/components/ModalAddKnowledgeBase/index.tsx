import {
  KBFilesAllPublic,
  KBFilesPublic,
  KnowledgeBaseItem,
  knowledgeBaseDirectoryReadKnowledgeDirectoriesApi,
  knowledgeBaseFileFetchMyAllKbFilesApi,
  knowledgeBaseFileFetchMyKbFilesApi,
} from '@/apis/client'
import { addCategoryNameToFile } from '@/components/ModalAddKnowledgeBase/helpers'
import { PAGE_SIZE } from '@/constants'
import { colors } from '@/theme'
import { memo, useEffect, useRef, useState } from 'react'
import AddItem, { KnowledgeBaseMode } from '../AddItem'
import BaseModal from '../BaseModal'
import Button from '../Button'
import EmptyData from '../EmptyData'
import NoDataFound from '../NoDataFound'
import Pagination from '../Pagination'
import { ISelectBaseItem } from '@/pages/WorkflowDetail/components/SelectAssignee/components/Select'
import Text from '../Text'
import IconFile from '../Upload/IconFile'
import WhenToUse from './components/WhenToUse'
import IconButton from '../IconButton'
import ModalUploadKnowledgeFile from './components/ModalUploadKnowledgeFile'
import { useWs } from '@/pages/knowledgeBaseDirectory/hooks/use-Ws'
import { getAccessTokenLocalStorage, getWsKbFileUrl } from '@/helpers'
import { EEventKbFile } from '@/pages/knowledgeBaseDirectory/consts'
import { KBFilePublicWDirectory } from './consts'
import SearchBar from '../SearchBar'

const ALL_DIRECTORY: ISelectBaseItem = {
  id: 'all-directory',
  name: 'Directory',
}

const ModalAddKnowledgeBase = ({
  isOpen,
  knowledgeBasePrompt,
  onClose,
  selected,
  onConfirm,
}: IModalAddKnowledgeBaseProps) => {
  const [searchText, setSearchText] = useState('')
  const [searchTextDisplay, setSearchTextDisplay] = useState('')
  const [directoryLoading, setDirectoryLoading] = useState(false)
  const [directories, setDirectories] = useState<ISelectBaseItem[]>([])
  const [selectedCategory, setSelectedCategory] =
    useState<ISelectBaseItem>(ALL_DIRECTORY)
  const [files, setFiles] = useState<KnowledgeBaseItem[]>([])
  const [selectedFiles, setSelectedFiles] = useState<KnowledgeBaseItem[]>([])
  const [page, setPage] = useState(1)
  const [totalPage, setTotalPage] = useState(1)
  const [categoryPage, setCategoryPage] = useState(1)
  const [totalCategoryPage, setTotalCategoryPage] = useState(1)
  const [changed, setChanged] = useState(false)
  const [promptText, setPromptText] = useState(knowledgeBasePrompt ?? '')

  const [isOpenUploadKnowledgeFile, setIsOpenUploadKnowledgeFile] =
    useState(false)
  const uploadedKnowledgeFiles = useRef<
    Array<KBFilePublicWDirectory | undefined>
  >([])
  const countedEmbeddedKnowledgeFiles = useRef(0)
  const kbFileParams = useRef({ page: 1, selectedCategory: ALL_DIRECTORY })

  useWs({
    url: getWsKbFileUrl(getAccessTokenLocalStorage() || ''),

    onMessage: (message) => {
      const dataRes = JSON.parse(message.data)

      if (dataRes && dataRes.event === EEventKbFile.SendEmbeddingProgress) {
        const KBFileId = dataRes.data.knowledge_base_file_storage_id
        const embeddedKnowledgeFile = uploadedKnowledgeFiles.current.find(
          (file) => file?.knowledgeBaseFileStorageId === KBFileId
        )

        // Remove uploaded file from the list
        uploadedKnowledgeFiles.current = uploadedKnowledgeFiles.current.filter(
          (uploadedFile) =>
            uploadedFile?.knowledgeBaseFileStorageId !== KBFileId
        )

        // Auto select embedded file
        if (embeddedKnowledgeFile) {
          setChanged(true)
          countedEmbeddedKnowledgeFiles.current =
            countedEmbeddedKnowledgeFiles.current + 1
          setSelectedFiles((prev) => [embeddedKnowledgeFile, ...prev])

          if (
            (kbFileParams.current.selectedCategory?.id === ALL_DIRECTORY.id ||
              kbFileParams.current.selectedCategory?.id ===
                embeddedKnowledgeFile.directoryId) &&
            kbFileParams.current.page === 1
          ) {
            setFiles((prev) => [embeddedKnowledgeFile, ...prev])
          }
        }
      }
    },
  })

  const fetchAllFiles = async () => {
    try {
      const res = await knowledgeBaseFileFetchMyKbFilesApi({
        path: {
          knowledge_base_directory_id: selectedCategory?.id || '',
        },
        query: {
          file_display_name: searchText,
          page_size: PAGE_SIZE.SMALL * (totalPage + 1),
          page_number: 1,
          embedding_status: 'Completed', // Get file embedding completed
        },
      })
      if (res.status === 200) {
        return (
          res?.data?.data?.data?.map((file) =>
            addCategoryNameToFile(file, selectedCategory?.name ?? '')
          ) || []
        )
      } else {
        return fetchAllFiles()
      }
    } catch (error) {
      return fetchAllFiles()
    }
  }

  const fetchAllFilesInAllDirectories = async () => {
    try {
      const res = await knowledgeBaseFileFetchMyAllKbFilesApi({
        query: {
          file_display_name: searchText,
          page_size: PAGE_SIZE.SMALL * (totalPage + 1),
          page_number: 1,
          embedding_status: 'Completed', // Get file embedding completed
        },
      })
      if (res.status === 200) {
        return (
          res?.data?.data?.data?.map((file) =>
            addCategoryNameToFile(file, file.knowledge_directory_name || '')
          ) || []
        )
      } else {
        return fetchAllFilesInAllDirectories()
      }
    } catch (error) {
      return fetchAllFilesInAllDirectories()
    }
  }

  const selectAll = async () => {
    if (selectedCategory.id === ALL_DIRECTORY.id) {
      const allTools = await fetchAllFilesInAllDirectories()
      if (allTools.length > 0) {
        setChanged(true)
      }
      const filteredDuplicate = allTools.filter(
        (tool) => !selectedFiles.find((item) => item.id === tool.id)
      )
      setSelectedFiles([...filteredDuplicate, ...selectedFiles])
    } else {
      const allTools = await fetchAllFiles()
      if (allTools.length > 0) {
        setChanged(true)
      }
      const unselectedFiles = allTools.filter(
        (tool) => !selectedFiles.find((item) => item.id === tool.id)
      )
      setSelectedFiles([...unselectedFiles, ...selectedFiles])
    }
  }

  const unselectAll = () => {
    setSelectedFiles([])
    setChanged(true)
  }

  const fetchDirectories = async (currentCategoryPage = categoryPage) => {
    try {
      setDirectoryLoading(true)

      const res = await knowledgeBaseDirectoryReadKnowledgeDirectoriesApi({
        query: {
          page_size: PAGE_SIZE.LARGE,
          page_number: currentCategoryPage,
        },
      })
      if (res.status === 200) {
        const list = (res?.data?.data?.data || [])?.map((item) => ({
          id: item.id || '',
          name: item.name || '',
        }))
        const removedDuplicate = list.filter(
          (item) => !directories.find((category) => category.id === item.id)
        )
        if (currentCategoryPage === categoryPage) {
          setDirectories([...directories, ...removedDuplicate])
          setCategoryPage((current) => current + 1)
        } else {
          setDirectories(list)
          setCategoryPage(currentCategoryPage + 1)
        }
        setTotalCategoryPage(res?.data?.data?.total_pages || 1)
        if (!selectedCategory && removedDuplicate.length > 0) {
          setSelectedCategory(removedDuplicate[0])
          kbFileParams.current.selectedCategory = removedDuplicate[0]
        }
      }
    } catch (error) {
      fetchDirectories()
    } finally {
      setDirectoryLoading(false)
    }
  }

  useEffect(() => {
    if (categoryPage > 1 && categoryPage <= totalCategoryPage) {
      fetchDirectories()
    }
  }, [categoryPage, totalCategoryPage])

  const fetchToolsByCategory = async () => {
    try {
      if (!selectedCategory) return
      const res = await knowledgeBaseFileFetchMyKbFilesApi({
        path: {
          knowledge_base_directory_id: selectedCategory?.id || '',
        },
        query: {
          file_display_name: searchText,
          page_size: PAGE_SIZE.SMALL,
          page_number: page,
          embedding_status: 'Completed', // Get file embedding completed
        },
      })
      if (res.status === 200) {
        setTotalPage(res?.data?.data?.total_pages || 0)
        const data: KBFilesPublic = res?.data?.data?.data || []
        setFiles(
          data?.map((file) =>
            addCategoryNameToFile(file, selectedCategory?.name || '')
          ) || []
        )
      } else {
        setFiles([])
      }
    } catch (error) {
      setFiles([])
    }
  }

  const fetchAllCategoryFiles = async () => {
    try {
      const res = await knowledgeBaseFileFetchMyAllKbFilesApi({
        query: {
          file_display_name: searchText,
          page_size: PAGE_SIZE.SMALL,
          page_number: page,
          embedding_status: 'Completed', // Get file embedding completed
        },
      })
      if (res.status === 200) {
        setTotalPage(res?.data?.data?.total_pages || 0)
        const data: KBFilesAllPublic = res?.data?.data?.data || []
        setFiles(
          data?.map((file) =>
            addCategoryNameToFile(file, file.knowledge_directory_name || '')
          ) || []
        )
      } else {
        setFiles([])
      }
    } catch (error) {
      console.error('fetchAllCategoryFiles error', error)
      setFiles([])
    }
  }

  useEffect(() => {
    if (!isOpen) {
      setDirectories([])
      setSelectedCategory(ALL_DIRECTORY)
      setFiles([])
      setSelectedFiles([])
      setPage(1)
      setTotalPage(1)
      setCategoryPage(1)
      setTotalCategoryPage(1)
      setSearchText('')
      setSearchTextDisplay('')
      setChanged(false)
      setPromptText('')

      // update kb file params
      kbFileParams.current.selectedCategory = ALL_DIRECTORY
      kbFileParams.current.page = 1
    } else {
      fetchDirectories()
      setPromptText(knowledgeBasePrompt ?? '')
    }
  }, [isOpen])

  useEffect(() => {
    if (isOpen) {
      if (selectedCategory.id === ALL_DIRECTORY.id) {
        fetchAllCategoryFiles()
      } else {
        fetchToolsByCategory()
      }
    }
  }, [searchText, selectedCategory, page, isOpen])

  useEffect(() => {
    if (isOpen) {
      setSelectedFiles(selected)
    }
  }, [selected, isOpen])

  const handleSelectTool = (tool: KnowledgeBaseItem) => {
    const index = selectedFiles.findIndex((item) => item.id === tool.id)
    if (index > -1) {
      setSelectedFiles((prev) => prev.filter((item) => item.id !== tool.id))
    } else {
      setSelectedFiles((prev) => [tool, ...prev])
    }
    setChanged(true)
  }

  useEffect(() => {
    if (searchTextDisplay.trim() !== searchText.trim()) {
      setSearchText(searchTextDisplay.trim())
      setPage(1)
      setTotalPage(1)

      kbFileParams.current.page = 1
    }
  }, [searchTextDisplay])

  const showConfirm =
    changed && selectedFiles?.length > 0 && promptText.trim().length > 0

  return (
    <>
      <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
        <div className="relative flex h-[614px] w-[973px] flex-col rounded-[20px] bg-white p-3 shadow-md">
          <div className="flex flex-col">
            <Text
              type="body"
              variant="medium"
              className="px-[4px] text-Primary-Color"
              elementType="div"
            >
              Add internal data
            </Text>
            <div className="mt-[12px] flex flex-1 gap-3">
              <div className="flex h-[521px] w-[454px] flex-col gap-[12px]">
                <div className="flex gap-3">
                  <SearchBar
                    className="w-full"
                    placeholder="Search by file name"
                    onSearch={(value) => setSearchTextDisplay(value)}
                    filterOptions={[
                      {
                        contentClassName: '!min-w-[154px] !w-full  h-[148px]',
                        overlayClassName: '[--anchor-gap:12px]',
                        containerClassName: '!max-w-[178px]',
                        placement: 'bottom',
                        data: directories,
                        selected: selectedCategory,
                        loading: directoryLoading,
                        onChangeSelectedValue: (value) => {
                          if (value) {
                            setSelectedCategory(value)
                            kbFileParams.current.selectedCategory = value
                          } else {
                            setSelectedCategory(ALL_DIRECTORY)
                            kbFileParams.current.selectedCategory =
                              ALL_DIRECTORY
                          }
                          setPage(1)
                          setTotalPage(1)

                          kbFileParams.current.page = 1
                        },
                      },
                    ]}
                    hasFilter
                  />
                  <div className="relative flex items-center justify-center">
                    {uploadedKnowledgeFiles.current?.length ? (
                      <>
                        <IconButton
                          className="flex items-center rounded-lg border border-border-base-icon bg-Input-Field p-1 duration-300 hover:!bg-neutral-100"
                          classNameIcon="flex items-center justify-center h-6 w-6"
                          nameIcon="spinner"
                          sizeIcon={16}
                        />
                        <Text
                          value={countedEmbeddedKnowledgeFiles.current}
                          variant="medium"
                          type="supportText"
                          className="absolute -top-[3px] left-[25px] flex h-[15px] items-center justify-center rounded-full bg-neutral-200 px-1 py-[2px] text-Tertiary-Color"
                          elementType="div"
                        />
                      </>
                    ) : (
                      <IconButton
                        className="flex items-center rounded-lg border border-border-base-icon bg-Input-Field p-1 duration-300 hover:!bg-neutral-100"
                        nameIcon="attachment-02"
                        sizeIcon={24}
                        colorIcon={colors.neutral[400]}
                        onClick={() => setIsOpenUploadKnowledgeFile(true)}
                      />
                    )}
                  </div>
                </div>

                <div className="flex h-[475px] flex-col gap-[4px] px-[4px]">
                  {files.length > 0 && (
                    <button
                      className="self-end p-0 text-Primary-Color hover:text-Main-Color"
                      onClick={selectAll}
                    >
                      <Text type="subBody" variant="medium">
                        Select all
                      </Text>
                    </button>
                  )}
                  <div className="genai-scrollbar flex h-[425px] flex-col gap-3 overflow-y-auto">
                    {files.map((file) => (
                      <AddItem
                        key={file.id}
                        avatar={''}
                        kbIcon={
                          <IconFile
                            size={32}
                            fileExt={file.file_extension || ''}
                          />
                        }
                        title={file.file_display_name || ''}
                        content={file.file_description || ''}
                        selected={selectedFiles.some(
                          (item) => item.id === file.id
                        )}
                        onClick={() => handleSelectTool(file)}
                        directory={file.directoryName || ''}
                      />
                    ))}
                    {searchText.trim().length === 0 && files.length === 0 && (
                      <EmptyData
                        type="02"
                        className="mt-11 self-center"
                        size="small"
                      />
                    )}
                    {searchText.trim().length > 0 && files.length === 0 && (
                      <NoDataFound
                        className="mt-11"
                        textType="subheading"
                        textVariant="semibold"
                        subTextType="subBody"
                        subTextVariant="regular"
                        size={150}
                      />
                    )}
                  </div>
                  {totalPage > 1 && (
                    <Pagination
                      page={page}
                      totalPage={totalPage}
                      onChangePage={(page) => {
                        setPage(page)
                        kbFileParams.current.page = page
                      }}
                      className="self-end"
                      type="mini"
                    />
                  )}
                </div>
              </div>

              <div className="flex w-[483px] flex-col gap-[8px]">
                <div className="flex h-[395px] w-full flex-col rounded-lg border border-neutral-200 px-3 py-2">
                  <Text
                    type="body"
                    variant="medium"
                    className="text-Primary-Color"
                  >
                    Selected
                  </Text>
                  <button
                    className="self-end p-0 text-Primary-Color hover:text-Main-Color"
                    onClick={unselectAll}
                  >
                    <Text
                      type="subBody"
                      variant="medium"
                      className={
                        !selectedFiles?.length ? 'text-Disable-Text' : ''
                      }
                    >
                      Unselect all
                    </Text>
                  </button>
                  <div className="genai-scrollbar flex h-[354px] flex-col gap-3 overflow-y-auto">
                    {selectedFiles?.map((file) => (
                      <AddItem
                        key={file.id}
                        mode={KnowledgeBaseMode.SELECTED}
                        avatar={''}
                        kbIcon={
                          <IconFile
                            size={32}
                            fileExt={file.file_extension || ''}
                          />
                        }
                        title={file.file_display_name || ''}
                        content={file.file_description || ''}
                        selected={true}
                        onClick={() => handleSelectTool(file)}
                        directory={file.directoryName || ''}
                      />
                    ))}
                  </div>
                </div>

                <WhenToUse
                  text={promptText}
                  onChange={(value) => {
                    setChanged(true)
                    setPromptText(value)
                  }}
                />
              </div>
            </div>
          </div>

          <Button
            type="primary"
            size="small"
            disabled={!showConfirm}
            onClick={() => {
              onConfirm(selectedFiles, promptText.trim())
              onClose()
            }}
            text="Confirm"
            className="mt-[12px] w-[96px] self-end"
          />
          <IconButton
            className="absolute right-[10px] top-[10px] z-30 border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
            nameIcon="x-close"
            sizeIcon={16}
            colorIcon={colors.neutral[500]}
            onClick={onClose}
          />
        </div>
      </BaseModal>

      {isOpenUploadKnowledgeFile && (
        <ModalUploadKnowledgeFile
          isOpen={isOpenUploadKnowledgeFile}
          // onRefreshData={() => fetchDirectories()}
          onClose={() => {
            fetchDirectories(1)
            setIsOpenUploadKnowledgeFile(false)
          }}
          onUploadSuccess={(uploadedFiles) => {
            countedEmbeddedKnowledgeFiles.current = 0
            uploadedKnowledgeFiles.current = uploadedFiles ?? []
            fetchDirectories(1)
            setIsOpenUploadKnowledgeFile(false)
          }}
        />
      )}
    </>
  )
}

export default memo(ModalAddKnowledgeBase)

interface IModalAddKnowledgeBaseProps {
  isOpen: boolean
  onClose: () => void
  selected: KnowledgeBaseItem[]
  knowledgeBasePrompt?: string | null
  onConfirm: (
    selected: KnowledgeBaseItem[],
    knowledgeBasePrompt: string
  ) => void
}
