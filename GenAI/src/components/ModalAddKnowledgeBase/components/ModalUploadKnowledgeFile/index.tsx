import { useCallback, useEffect, useMemo, useState } from 'react'
import Button from '@/components/Button'
import Icon from '@/assets/icon/Icon'
import { useLoadDirectory } from '../../hooks/useLoadDirectory'
import Select from '@/pages/WorkflowDetail/components/SelectAssignee/components/Select'
import { ISelectBaseItem } from '@/components/Select'
import ModalAddNewKnowledgeBase from '@/pages/knowledgeBase/components/ModalAddNewKnowledgeBase'
import UploadFile from '@/components/Upload/UploadFile'
import clsx from 'clsx'
import { getFileNameWithoutExtension } from '@/components/Upload/helper'
import {
  ERR_NOT_ENOUGH_STORAGE,
  FileStatus,
  FileUpload,
  KBFilePublicWDirectory,
} from '../../consts'
import Item from './components/Item'
import {
  knowledgeBaseFileUpdateEmbeddingKbFileByIdApi,
  knowledgeBaseFileUploadKbFilesApi,
} from '@/apis/client'
import { GEN_AI_INTERNAL_PATH } from '@/constants'
import { MessageDialog } from '@/components/DialogMessage'
import Modal from '@/components/Modal'

interface IModalUploadKnowledgeFile {
  isOpen: boolean
  onUploadSuccess?: (
    uploadedFiles?: Array<KBFilePublicWDirectory | undefined>
  ) => void
  onClose: () => void
}

const ModalUploadKnowledgeFile = ({
  isOpen,
  onUploadSuccess,
  onClose,
}: IModalUploadKnowledgeFile) => {
  const [isDirty, setIsDirty] = useState(false)
  const [openModalCreateCategory, setOpenModalCreateCategory] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<ISelectBaseItem>()

  const {
    isLoading,
    data: directories,
    // inputValue,
    // handleReachEnd,
    // onChangeInputValue,
    reLoadDirectory,
  } = useLoadDirectory()

  const [errorFile, setErrorFile] = useState<string>()
  const [uploadingFiles, setUploadingFiles] = useState<FileUpload[]>([])

  const selectedKBDirectoryId = useMemo(
    () => selectedCategory?.id,
    [selectedCategory]
  )

  const formattedData: ISelectBaseItem[] = useMemo(
    () =>
      directories?.map((directory) => ({
        id: directory.id ?? '',
        value: directory.id ?? '',
        name: directory.name ?? '',
      })) ?? [],
    [directories]
  )

  const isDisabledSelectDirectory = useMemo(
    () =>
      uploadingFiles.some(
        (file) =>
          file.status === FileStatus.UPLOADING ||
          file.status === FileStatus.UPLOADED ||
          file.status === FileStatus.ERROR
      ),
    [uploadingFiles]
  )

  const isLoadingUploadBtn = useMemo(() => {
    if (uploadingFiles?.some((file) => file.status === FileStatus.PREVIEW)) {
      return false
    }

    return uploadingFiles.some((file) => file.status === FileStatus.UPLOADING)
  }, [uploadingFiles])

  const isDisabledUploadBtn = useMemo(() => {
    if (!uploadingFiles?.length) {
      return true
    }

    if (uploadingFiles.some((file) => file.status === FileStatus.PREVIEW)) {
      return false
    }

    return uploadingFiles.some((file) => file.status === FileStatus.UPLOADING)
  }, [uploadingFiles])

  const uploadButtonContent = useMemo(() => {
    if (!uploadingFiles?.length) {
      return 'Upload'
    }
    if (uploadingFiles.some((file) => file.status === FileStatus.PREVIEW)) {
      return 'Upload'
    }

    return 'Continue'
  }, [uploadingFiles])

  const handleChangeFiles = useCallback(
    (files?: File[]) => {
      if (errorFile) {
        setErrorFile(undefined)
      }

      if (!files?.length) {
        return
      }

      const addedFiles: FileUpload[] = [
        ...files.map((file) => ({
          rawFile: file,
          displayName: getFileNameWithoutExtension(file.name),
          description: '',
          status: FileStatus.PREVIEW,
          error: undefined,
          knowledgeFileId: undefined,
        })),
      ]
      setUploadingFiles((prevFiles) => [...addedFiles, ...prevFiles])
    },
    [errorFile]
  )

  const handleDeleteFiles = useCallback((files: FileUpload[]) => {
    setUploadingFiles((prevFiles) =>
      prevFiles.filter((file) => !files.includes(file))
    )
  }, [])

  const handleChangeFileName = useCallback(({ file, name }: any) => {
    file.displayName = name
    setUploadingFiles((prevFiles) => [...prevFiles])
  }, [])

  const handleChangeFileDesc = useCallback(({ file, description }: any) => {
    file.description = description
    setUploadingFiles((prevFiles) => [...prevFiles])
  }, [])

  const handleChangeFileStatus = useCallback(({ file, status }: any) => {
    file.status = status
    setUploadingFiles((prevFiles) => [...prevFiles])
  }, [])

  const handleUploadFile = useCallback(
    async (file: FileUpload) => {
      if (!selectedKBDirectoryId || !file) return

      const {
        rawFile,
        displayName,
        description,
        status: fileStatus,
        error: fileError,
      } = file

      try {
        if (fileError) {
          file.error = undefined
        }
        if (fileStatus !== FileStatus.UPLOADING) {
          handleChangeFileStatus({
            file,
            status: FileStatus.UPLOADING,
          })
        }

        const { data, error, status } = await knowledgeBaseFileUploadKbFilesApi(
          {
            baseURL: GEN_AI_INTERNAL_PATH,
            path: {
              knowledge_base_directory_id: selectedKBDirectoryId,
            },
            body: {
              file_display_name: displayName.trim(),
              file_description: description?.trim(),
              file_storage_upload: rawFile,
            },
          }
        )

        if (status !== 200) {
          if (
            error &&
            (error.detail as unknown as string) === ERR_NOT_ENOUGH_STORAGE
          ) {
            setErrorFile(ERR_NOT_ENOUGH_STORAGE)
          }

          handleChangeFileStatus({
            file,
            status: FileStatus.ERROR,
          })
        }

        if (status === 200 && data) {
          // Saving kb file id
          file.knowledgeFileId = data.data.id
          file.respondedData = {
            ...data.data,
            knowledgeBaseFileStorageId: data.data.id,
            id: data.data.knowledge_base_id,
            directoryId: selectedCategory?.id,
            directoryName: selectedCategory?.name ?? '',
          }
          handleChangeFileStatus({
            file,
            status: FileStatus.UPLOADED,
          })
        }
      } catch (error) {
        console.log('error:', error)
      }
    },
    [selectedKBDirectoryId]
  )

  const handleUploadFiles = useCallback(async () => {
    if (errorFile) {
      setErrorFile(undefined)
    }

    // Filter files by status 'Preview'
    const previewingFiles = uploadingFiles.filter(
      (file) => file.status === FileStatus.PREVIEW
    )

    // Change status into Uploading
    uploadingFiles.map((file) => {
      const { status, error } = file
      if (status !== FileStatus.PREVIEW) {
        return file
      }

      if (error) {
        file.error = undefined
      }
      file.status = FileStatus.UPLOADING

      return { ...file }
    })
    setUploadingFiles((prevFiles) => [...prevFiles])

    await new Promise((resolve) => {
      previewingFiles.forEach(async (file) => await handleUploadFile(file))
      resolve(true)
    }).then(() => {
      // setDirty = false after uploading kb files successfully
      setIsDirty(false)
    })
  }, [errorFile, uploadingFiles, handleUploadFile])

  const handleRetry = useCallback(
    async (file: FileUpload) => {
      if (!file?.knowledgeFileId || !selectedKBDirectoryId) return

      try {
        await knowledgeBaseFileUpdateEmbeddingKbFileByIdApi({
          path: {
            knowledge_base_directory_id: selectedKBDirectoryId,
            KB_file_id: file.knowledgeFileId,
          },
        })
      } catch (error) {
        console.log(error)
      }
    },
    [selectedKBDirectoryId]
  )

  const handleContinue = useCallback(async () => {
    if (errorFile) {
      setErrorFile(undefined)
    }

    const uploadedFiles = uploadingFiles.filter(
      (file) => file.knowledgeFileId && file.status === FileStatus.UPLOADED
    )

    if (uploadedFiles?.length) {
      uploadedFiles.forEach(async (file) => await handleRetry(file))
      onUploadSuccess?.(uploadedFiles?.map((file) => file.respondedData))
    } else if (
      uploadingFiles?.some((file) => file?.status === FileStatus.ERROR)
    ) {
      onClose?.()
    }
  }, [errorFile, uploadingFiles, onUploadSuccess, handleRetry, onClose])

  const handleClose = useCallback(() => {
    if (
      isDirty ||
      uploadingFiles?.some(
        (file) =>
          file.status === FileStatus.PREVIEW ||
          file.status === FileStatus.UPLOADING
      )
    ) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          handleContinue()
          onClose?.()
        },
      })

      return
    }

    handleContinue()
    onClose?.()
  }, [isDirty, handleContinue, onClose])

  useEffect(() => {
    if (formattedData?.length && !isDisabledSelectDirectory) {
      // Set default directory
      setSelectedCategory(formattedData[0])
    }
  }, [formattedData])

  return (
    <>
      <Modal
        open={isOpen}
        showCloseButton
        title="Upload knowledge file"
        subTitle="Add data source for your worker"
        okText={uploadButtonContent}
        classNameOkButton="w-[133px] min-w-[133px]"
        classNameCancelButton="invisible"
        classNameFooter="!mt-3 h-full justify-end"
        classNameContent="!mt-3 h-full"
        className="h-full w-[752px] !px-6 !py-3 shadow-md"
        classNameHeader="h-full"
        okDisable={isDisabledUploadBtn}
        okLoading={isLoadingUploadBtn}
        onClickClose={handleClose}
        onClickOk={() => {
          uploadButtonContent === 'Upload'
            ? handleUploadFiles()
            : handleContinue()
        }}
      >
        <div className="flex flex-col justify-center gap-3">
          <div className="flex items-center gap-3">
            <Select
              className="w-[578px]"
              overlayClassName="w-[var(--button-width)]"
              loading={isLoading}
              data={formattedData}
              selected={selectedCategory}
              disabled={isDisabledSelectDirectory}
              onChangeSelectedValue={(value) => {
                if (!isDisabledSelectDirectory) {
                  setIsDirty(true)
                  setSelectedCategory(value)
                }
              }}
            />
            <Button
              className="w-[114px] min-w-[114px]"
              type="secondary"
              text="Directory"
              leftIcon={
                <Icon name="plus" size={20} gradient={['#4D175B', '#A32952']} />
              }
              onClick={() => {
                setOpenModalCreateCategory(true)
              }}
            />
          </div>

          <div
            className={clsx(
              'box-border flex h-full flex-col',
              uploadingFiles?.length && 'gap-[8px]'
            )}
          >
            <UploadFile
              multiple
              size="large"
              dataTypes=".csv,.pdf,.doc,.docx,.ppt,.pptx,.txt"
              className={clsx(
                'min-h-[240px] duration-500',
                uploadingFiles?.length && '!min-h-[150px]'
              )}
              onChange={handleChangeFiles}
              isUploadFailed={!!errorFile}
              uploadFailedMessage={errorFile}
            />
            <div
              className={clsx(
                'genai-scrollbar flex flex-col gap-1 overflow-auto',
                uploadingFiles?.length &&
                  'max-h-[calc(100vh-400px)] min-h-[60px]'
              )}
            >
              {uploadingFiles.map((file) => {
                return (
                  <Item
                    key={file.knowledgeFileId}
                    file={file}
                    onChangeFileName={(value) =>
                      handleChangeFileName({
                        file,
                        name: value,
                      })
                    }
                    onChangeFileDescription={(value) =>
                      handleChangeFileDesc({
                        file,
                        description: value,
                      })
                    }
                    onDelete={(file) => handleDeleteFiles([file])}
                    onRetry={(file) => handleUploadFile(file)}
                  />
                )
              })}
            </div>
          </div>
        </div>
      </Modal>

      {openModalCreateCategory && (
        <ModalAddNewKnowledgeBase
          openCreateModal={openModalCreateCategory}
          setOpenCreateModal={setOpenModalCreateCategory}
          refresh={reLoadDirectory}
        />
      )}
    </>
  )
}

export default ModalUploadKnowledgeFile
