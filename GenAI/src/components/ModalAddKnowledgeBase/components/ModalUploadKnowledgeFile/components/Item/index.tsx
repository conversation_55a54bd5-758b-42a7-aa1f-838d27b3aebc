import { useMemo } from 'react'
import clsx from 'clsx'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import IconFile from '@/components/Upload/IconFile'
import { formatBytes } from '@/helpers'
import {
  getFileExt,
  getFileNameWithoutExtension,
} from '@/components/Upload/helper'
import { colors } from '@/theme'
import InputFitContent from '../InputFitContent'
import {
  FileStatus,
  FileUpload,
} from '@/components/ModalAddKnowledgeBase/consts'

interface Props {
  file: FileUpload
  onChangeFileName?: (fileName: string) => void
  onChangeFileDescription?: (fileDescription: string) => void
  onDelete?: (file: FileUpload) => void
  onRetry?: (file: FileUpload) => void
}
const Item = ({
  file,
  onChangeFileName,
  onChangeFileDescription,
  onDelete,
  onRetry,
}: Props) => {
  const { rawFile, displayName, description, status } = file

  const fileName = useMemo(() => {
    if (status === FileStatus.PREVIEW) {
      return (
        <InputFitContent
          className="h-fit rounded-none border-0 bg-transparent p-0 text-body font-medium text-Primary-Color !shadow-none outline-none placeholder:text-subBody placeholder:font-regular placeholder:text-Placeholder-Text"
          placeholderClassName="text-subBody font-regular"
          placeholder="Display name is required"
          value={displayName}
          textClassName="!w-fit max-w-full text-Primary-Color overflow-hidden"
          useTextAfterBlur
          onChange={(e) => onChangeFileName?.(e.target.value)}
          onBlur={() => {
            const value = displayName!.trim()
            onChangeFileName?.(
              value ? value : getFileNameWithoutExtension(rawFile.name)
            )
          }}
        />
      )
    }

    return (
      <Text
        type="body"
        variant="medium"
        className="overflow-hidden text-Primary-Color"
        elementType="div"
        hasTooltip={false}
        ellipsis
      >
        {displayName}
      </Text>
    )
  }, [rawFile, displayName, status, onChangeFileName])

  const fileDescription = useMemo(() => {
    if (status === FileStatus.PREVIEW) {
      return (
        <InputFitContent
          className="h-fit rounded-none border-0 bg-transparent p-0 text-subBody text-Secondary-Color !shadow-none outline-none placeholder:text-Placeholder-Text"
          placeholder="Type in supplementary info for knowledge file"
          value={description}
          textType="subBody"
          textVariant="regular"
          textClassName="!w-fit max-w-full text-Secondary-Color break-words overflow-hidden"
          textMultipleLine={2}
          useTextAfterBlur
          hidePlaceholder
          onChange={(e) => onChangeFileDescription?.(e.target.value)}
          onBlur={() => onChangeFileDescription?.(description!.trim())}
        />
      )
    }

    return (
      <Text
        type="subBody"
        className="overflow-hidden break-words text-Secondary-Color"
        elementType="div"
        multipleLine={2}
        hasTooltip={false}
        ellipsis
      >
        {description}
      </Text>
    )
  }, [description, status, onChangeFileDescription])

  const fileStatus = useMemo(() => {
    if (status === FileStatus.PREVIEW) {
      return (
        <IconButton
          sizeIcon={16}
          nameIcon="Customize-Delete"
          colorIcon="#D4D4D4"
          hoverColor={colors['Primary-Color']}
          className="h-[16px] w-[16px]"
          tooltipText="Delete"
          onClick={() => onDelete?.(file)}
        />
      )
    } else if (
      status === FileStatus.UPLOADING ||
      status === FileStatus.UPLOADED
    ) {
      return (
        <Text
          type="supportText"
          variant="medium"
          className="flex text-Secondary-Color"
        >
          {status === FileStatus.UPLOADING ? 'Uploading...' : 'Uploaded'}
        </Text>
      )
    } else if (status === FileStatus.ERROR) {
      return (
        <Text
          type="supportText"
          variant="medium"
          className="flex cursor-pointer text-Error-Color duration-300 hover:underline hover:decoration-solid hover:decoration-[1px] hover:underline-offset-[2.5px]"
          onClick={() => onRetry?.(file)}
        >
          Retry
        </Text>
      )
    }
  }, [file, onDelete])

  return (
    <div className="flex min-h-[51px] w-full flex-none items-center gap-3 rounded-md bg-white px-2 py-[6px] shadow-sm">
      <div className="flex min-w-[32px] flex-col justify-center gap-0.5">
        <IconFile
          size={32}
          fileExt={getFileExt(rawFile.name)}
          className="flex w-full justify-center"
        />
        {status === FileStatus.UPLOADED && (
          <Text
            type="helperText"
            variant="medium"
            className="flex justify-center text-Secondary-Color"
          >
            {formatBytes({ bytes: rawFile.size })}
          </Text>
        )}
      </div>
      <div
        className={clsx(
          'flex w-[calc(100%-125px)] flex-col',
          status === FileStatus.PREVIEW && 'w-[calc(100%-71px)]'
        )}
      >
        {fileName}
        {fileDescription}
      </div>
      <div
        className={clsx(
          'flex w-[69px] items-center justify-end',
          status !== FileStatus.PREVIEW && 'justify-center',
          status === FileStatus.PREVIEW && '!w-[15px]'
        )}
      >
        {fileStatus}
      </div>
    </div>
  )
}

export default Item
