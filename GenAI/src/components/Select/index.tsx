import Icon from '@/assets/icon/Icon'
import { colors } from '@/theme'
import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from '@headlessui/react'
import { debounce, size } from 'lodash'
import { useRef } from 'react'
import SimpleBar from 'simplebar-react'
import { twMerge } from 'tailwind-merge'
import EmptyData from '../EmptyData'
import NoDataFound from '../NoDataFound'
import Spin from '../Spin'
import Text from '../Text'
import IcNoDataFound from './assets/noDataFound.svg?react'

const OFFSET = 100

export interface ISelectBaseItem {
  id: string
  value?: any
  name?: string
}

interface SelectProps<T extends ISelectBaseItem> {
  data: T[]
  selected: T | undefined
  placeholder?: string
  isLoading?: boolean
  inputValue?: string
  displayValue?: (value: T | undefined) => string
  onChangeSelectedValue: (value: any) => void
  onChangeInputValue?: (newValue: string) => void
  onReachEnd?: () => void
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void
  className?: string
}

const Select = <T extends ISelectBaseItem>({
  data,
  selected,
  placeholder,
  isLoading,
  inputValue,
  onChangeSelectedValue,
  onChangeInputValue,
  displayValue,
  onReachEnd,
  onFocus,
  onBlur,
  className,
}: SelectProps<T>) => {
  const inputRef = useRef<HTMLInputElement>(null)

  const handleChangeSelected = (value: T) => {
    onChangeSelectedValue(value)

    if (value) {
      setTimeout(() => {
        inputRef.current?.blur()
      }, 100)
    }
  }

  const handleScroll = debounce(() => {
    onReachEnd?.()
  }, 500)

  const handleChangeInputValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    let v = e.target.value.trimStart()

    if (v.endsWith('  ')) {
      v = v.trimEnd()
    }

    onChangeInputValue?.(v)
  }

  const handleClose = () => {
    onChangeInputValue?.('')
  }

  return (
    <div className={className}>
      <Combobox
        immediate
        value={selected}
        onChange={handleChangeSelected}
        onClose={handleClose}
      >
        <div className="relative w-full">
          <ComboboxInput
            autoComplete="off"
            ref={inputRef}
            className={twMerge(
              '!text-subBody !font-regular text-Primary-Color placeholder:text-subBody placeholder:font-regular placeholder:text-Placeholder-Text',
              'h-[34px] w-full rounded-[8px] border border-border-base-icon bg-Input-Field py-[8px] pl-[12px] pr-[40px]',
              'hover:border-Main-03',
              'transition duration-75 ease-in focus:border-Focus-Border focus:shadow-focus focus:outline-none'
            )}
            displayValue={
              displayValue
                ? displayValue
                : (item: T) => {
                    return item?.value ?? item.name ?? ''
                  }
            }
            onChange={handleChangeInputValue}
            placeholder={placeholder}
            onFocus={onFocus}
            onBlur={onBlur}
          />
          <ComboboxButton className="data-[open]:genai_button_icon__active group absolute inset-y-0 right-0 px-2.5 data-[open]:rotate-180 data-[open]:transform">
            <Icon
              name="Outline-Chevron-Down"
              size={20}
              color={colors.neutral[400]}
            />
          </ComboboxButton>
        </div>

        <ComboboxOptions
          anchor="bottom"
          transition
          className={twMerge(
            'relative z-20 mt-[8px] min-h-[15px] w-[var(--input-width)] rounded-[12px] bg-white shadow-md [--anchor-gap:var(--spacing-1)]',
            'transition duration-100 ease-in'
          )}
        >
          <SimpleBar
            className="!max-h-[176px] w-[var(--input-width)] px-[12px] py-[8px]"
            onScroll={(e) => {
              e.preventDefault()
              e.stopPropagation()
              const { scrollTop, clientHeight, scrollHeight } =
                e.target as HTMLDivElement

              if (scrollHeight - scrollTop - clientHeight <= OFFSET) {
                handleScroll()
              }
            }}
          >
            {isLoading && (
              <div className="flex h-full w-full justify-center">
                <Spin size={'small'} />
              </div>
            )}
            {!isLoading && inputValue && size(data) === 0 && (
              <NoDataFound
                className="flex h-[160px] w-[372px] flex-col items-center justify-center"
                icNodata={
                  <div className="h-[80px ] w-[80px]">
                    <IcNoDataFound />
                  </div>
                }
                textType="body"
                textVariant="semibold"
                subTextType="supportText"
                subTextVariant="regular"
              />
            )}

            {!isLoading && !inputValue && size(data) === 0 && (
              <div className="flex h-[160px] w-[372px] flex-col items-center justify-center">
                <EmptyData
                  size="small"
                  type="01"
                  className=""
                  classNameText="font-semibold"
                  icSize={80}
                />
              </div>
            )}

            {size(data) > 0 && (
              <div className="flex flex-col gap-1">
                {data.map((item) => (
                  <ComboboxOption
                    key={item.id}
                    value={item}
                    className={twMerge(
                      'group flex cursor-pointer select-none items-center rounded px-[12px] py-[8px]',
                      'hover:bg-Background-Color data-[focus]:bg-Background-Color data-[selected]:bg-Background-Color'
                    )}
                  >
                    <Text
                      ellipsis
                      elementType="div"
                      type="body"
                      variant="medium"
                      className={twMerge(
                        'overflow-hidden text-ellipsis whitespace-nowrap !text-Primary-Color',
                        'group-data-[selected]:bg-Main-Color group-data-[selected]:bg-clip-text group-data-[selected]:text-transparent',
                        'group-hover:!text-Primary-Color'
                      )}
                    >
                      {item.value ?? item.name}
                    </Text>
                  </ComboboxOption>
                ))}
              </div>
            )}
          </SimpleBar>
        </ComboboxOptions>
      </Combobox>
    </div>
  )
}

export default Select
