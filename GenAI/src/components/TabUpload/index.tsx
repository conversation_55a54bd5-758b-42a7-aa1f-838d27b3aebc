import Icon from '@/assets/icon/Icon'
import { getUrlImage } from '@/helpers'
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import React, { useState } from 'react'
import { twMerge } from 'tailwind-merge'
import Avatar from '../Avatar'
import Tabs from '../Tabs'
import Text from '../Text'
import Tooltip from '../Tooltip'
import Upload from '../Upload'
import './TabUpload.scss'

function isBase64(str: string | undefined) {
  if (typeof str !== 'string') {
    return false
  }

  const base64PrefixPattern = /^data:image\/[a-zA-Z]+;base64,/
  if (base64PrefixPattern.test(str)) {
    str = str.replace(base64PrefixPattern, '')
  }

  const base64Pattern =
    /^(?:[A-Za-z0-9+/]{4})*?(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/
  return base64Pattern.test(str)
}

type TABS = 'Collection' | 'Custom'

interface TabUploadProps {
  className?: string
  image: string | undefined
  onChange: (image: any, type?: TABS) => void
  changeFile: (image: File | null) => void
  imageCollection: string[]
}

const TabUpload: React.FC<TabUploadProps> = ({
  className,
  image,
  onChange,
  changeFile,
  imageCollection,
}) => {
  const [activeTab, setActiveTab] = useState<TABS>('Collection')

  const collection = () => {
    return (
      <div className="grid grid-cols-4 gap-[16px]">
        {imageCollection?.map((item, i) => {
          return (
            <div
              key={i}
              className={`flex max-h-[32px] min-h-[32px] min-w-[32px] max-w-[32px] items-center justify-center rounded-full ${item === image ? 'border-gradient-Main-Color-avatar' : 'border-red border-[2px] hover:border-Base-Neutral'}`}
              onClick={() => onChange(item, 'Collection')}
            >
              <Avatar
                className="max-h-[32px] min-h-[32px] min-w-[32px] max-w-[32px] border-[2px] border-white hover:border-Base-Neutral"
                avatarUrl={item}
              />
            </div>
          )
        })}
      </div>
    )
  }
  const custom = () => {
    return (
      <div className="flex h-full w-full flex-col items-center justify-center gap-[8px]">
        <Upload
          dataTypes="image/png, image/jpeg"
          className="h-[100px] w-[117px]"
          image={imageCollection?.find((n) => n === image) ? undefined : image}
          onChange={(e) => onChange(e)}
          onChangeFile={(e) => changeFile(e)}
          size="smaller"
        />
        <div className="flex flex-col">
          <Text
            className="text-center text-Secondary-Color"
            type="helperText"
            variant="regular"
          >
            Recommended size is 100 x 100 pixcels
          </Text>
          <Text
            className="text-center text-Secondary-Color"
            type="helperText"
            variant="regular"
          >
            Maximum size per file 1MB
          </Text>
        </div>
      </div>
    )
  }
  const contentTab = () => {
    return (
      <div className="flex h-[214px] w-[220px] flex-col items-center">
        <Tabs
          size="small"
          tabs={[
            {
              label: 'Collection',
              value: 'Collection',
            },
            {
              label: 'Custom',
              value: 'Custom',
            },
          ]}
          className="w-auto px-8"
          value={activeTab}
          onChange={setActiveTab}
        />
        <div className="genai-scrollbar h-full w-full overflow-y-auto px-[20px] py-[16px]">
          {activeTab === 'Collection' ? collection() : custom()}
        </div>
      </div>
    )
  }
  return (
    <Popover className="group">
      {({ open }) => (
        <>
          <PopoverButton className="flex items-center gap-2 focus-visible:outline-none">
            <Tooltip text={image ? '' : 'Drag or click to upload logo'}>
              <div
                style={{
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: 'contain',
                  backgroundPosition: 'center',
                  backgroundImage: image
                    ? `url(${isBase64(image) ? image : getUrlImage(image)})`
                    : 'none',
                }}
                className={twMerge(
                  'flex h-[56px] w-[56px] cursor-pointer items-center justify-center rounded-full',
                  image
                    ? open
                      ? 'border-[2px] border-Base-Neutral'
                      : 'border-[2px] border-white'
                    : open
                      ? 'border-[1px] border-dashed border-Base-Single-Color bg-Hover-Color'
                      : 'border-[1px] border-dashed border-border-base-icon hover:border-Base-Single-Color hover:bg-Hover-Color',
                  className
                )}
              >
                {!image && (
                  <Icon
                    name="Customize-Upload"
                    gradient={['#D3C6CB', '#D3C6CB']}
                    size={40}
                  />
                )}
              </div>
            </Tooltip>
          </PopoverButton>
          {open && (
            <PopoverPanel
              anchor="bottom"
              className="z-20 mt-[5px] flex flex-col !overflow-hidden rounded-[8px] border-[0.5px] border-border-base-icon bg-white shadow-md"
            >
              {contentTab()}
            </PopoverPanel>
          )}
        </>
      )}
    </Popover>
  )
}

export default TabUpload
