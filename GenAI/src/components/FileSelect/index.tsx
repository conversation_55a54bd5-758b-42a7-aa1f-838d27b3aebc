import { useEffect, useMemo } from 'react'
import { isEmpty, size } from 'lodash'
import Popover from '../Popover'
import clsx from 'clsx'
import { twMerge } from 'tailwind-merge'
import Text from '../Text'
import { colors } from '@/theme'
import Icon from '@/assets/icon/Icon'
import FileUpload, { FormattedFileUploadProps } from './components/FileUpload'
import FileItem from './components/FileItem'

interface FileSelectProps {
  open?: boolean
  setOpen?: (value: boolean) => void
  inputId?: string
  selectedFiles?: FormattedFileUploadProps[]
  dataTypes?: string
  className?: string
  overlayClassName?: string
  contentClassName?: string
  placement?:
    | 'top'
    | 'top start'
    | 'top end'
    | 'bottom'
    | 'bottom start'
    | 'bottom end'
    | 'left'
    | 'left start'
    | 'left end'
    | 'right'
    | 'right start'
    | 'right end'
  multiple?: boolean
  isError?: boolean
  getPopupContainer?: (
    triggerNode?: HTMLElement
  ) => Element | DocumentFragment | HTMLElement | ParentNode | null | undefined
  onChangeSelectedValue: (files: FormattedFileUploadProps[]) => void
  onRemoveSelectedValue: (file: FormattedFileUploadProps) => void
  onOpen?: () => void
}

const FileSelect = ({
  open,
  setOpen,
  inputId = 'file-select-finput',
  selectedFiles,
  dataTypes,
  className,
  overlayClassName,
  contentClassName,
  placement,
  multiple = false,
  isError,
  getPopupContainer,
  onChangeSelectedValue,
  onRemoveSelectedValue,
  onOpen,
}: FileSelectProps) => {
  const fileLength = useMemo(() => selectedFiles?.length ?? 0, [selectedFiles])

  const buttonContent = useMemo(() => {
    if (fileLength > 1) {
      return `${fileLength} files selected`
    }
    if (fileLength) {
      return `${fileLength} file selected`
    }

    return 'File'
  }, [fileLength])

  const handleButtonClick = () => {
    // @ts-expect-error: Object is possibly 'null'.
    document.getElementById(inputId).click()
  }

  const handleChangeSelected = (files?: FormattedFileUploadProps[]) => {
    onChangeSelectedValue(files ?? [])
  }

  const handleRemoveSelected = (file?: FormattedFileUploadProps) => {
    if (!isEmpty(file) && file) {
      onRemoveSelectedValue?.(file)
    }
  }

  useEffect(() => {
    if (open && onOpen) {
      onOpen()
    }

    // Automatically remove failed files when close is called
    if (!open) {
      selectedFiles?.forEach((selectedFile) => {
        if (selectedFile.error) {
          handleRemoveSelected(selectedFile)
        }
      })
    }
  }, [open])

  return (
    <div className={className}>
      <FileUpload
        id={inputId}
        multiple={multiple}
        dataTypes={dataTypes}
        onChange={(files) => handleChangeSelected(files)}
      />
      <Popover
        isPure
        overlayClassName={clsx('z-50 !p-0', overlayClassName)}
        open={open}
        title="Description"
        getPopupContainer={getPopupContainer}
        onOpenChange={(value) => {
          if (fileLength) {
            setOpen?.(value)
          } else {
            handleButtonClick()
          }
        }}
        placement={placement}
        content={
          <div
            className={clsx(
              'flex flex-col gap-1 rounded-lg px-1 py-2 shadow-md',
              contentClassName
            )}
          >
            {multiple && (
              <div
                className="flex w-full cursor-pointer items-center justify-center gap-2 px-3 py-1"
                onClick={handleButtonClick}
              >
                <Icon
                  name="Outline-EssentionalUI-AddSquare"
                  size={16}
                  color={colors['Primary-Color']}
                />
                <Text
                  value="Upload files"
                  className="text-Primary-Color"
                  type="subBody"
                  variant="regular"
                />
              </div>
            )}

            {!!size(selectedFiles) && multiple && (
              <div className="flex h-[1px] w-full bg-neutral-200" />
            )}
            <div className="genai-scrollbar flex !max-h-[201px] w-full flex-col overflow-auto overflow-x-hidden">
              {selectedFiles?.map((item) => (
                <FileItem
                  key={item.name}
                  name={item.name}
                  size={item.size}
                  error={item.error}
                  onRemove={() => {
                    if (!multiple) {
                      setOpen?.(false)
                    }

                    handleRemoveSelected(item)
                  }}
                />
              ))}
            </div>
          </div>
        }
        allowCloseWhenClickButton
      >
        <div className="relative flex w-full cursor-pointer items-center">
          <div
            className={twMerge(
              clsx(
                'flex h-[34px] w-full items-center justify-center gap-1 rounded-[8px] border border-border-base-icon bg-Input-Field px-3 py-2 duration-300',
                'hover:bg-Hover-Color',
                'transition duration-75 ease-in',
                fileLength && 'bg-Input-Field',
                isError && '!border-Error-Color',
                { 'border-Focus-Border shadow-focus': open }
              )
            )}
          >
            <Icon
              name="Outline-EssentionalUI-AddSquare"
              size={16}
              color={
                open || fileLength
                  ? colors['Primary-Color']
                  : colors.Placeholder
              }
            />
            <Text
              value={buttonContent}
              className={`${open || fileLength ? 'text-Primary-Color' : 'text-Placeholder'}`}
              type="subBody"
              variant="regular"
            />
          </div>
        </div>
      </Popover>
    </div>
  )
}

export default FileSelect
