import { getFileExt } from '@/components/Upload/helper'
import { useRef } from 'react'

const LIMIT_SIZE = 10 * 1048576 // 10MB

export interface FormattedFileUploadProps {
  rawFile: File
  name: string
  size: number
  type: string
  error?: string
  fileUrl?: string | null
}

interface FileUploadProps {
  dataTypes?: string
  onChange?: (files: FormattedFileUploadProps[] | undefined) => void
  limit?: number
  multiple?: boolean
  id?: string
}

const FileUpload = ({
  dataTypes = '.csv,.pdf,.doc,.docx,.txt,.png,.jpeg,.jpg',
  onChange,
  limit = LIMIT_SIZE,
  multiple = false,
  id,
}: FileUploadProps) => {
  const inputFile = useRef(null)

  const validateFile = (file: File) => {
    const ext = getFileExt(file.name) ?? ''
    const acceptTypes = dataTypes.split(',')

    if (!acceptTypes.includes(`.${ext}`)) {
      return 'Format is not supported'
    }

    if (file.size > limit) {
      return `Max size ${limit / 1024 ** 2}MB`
    }

    return ''
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files

    if (!files?.length) return

    const flattenFiles: File[] = [...files]
    const validatedFiles = flattenFiles.map((file: File) => ({
      rawFile: file,
      name: file.name,
      size: file.size,
      type: file.type,
      error: validateFile(file),
    }))

    onChange?.(validatedFiles)

    // @ts-expect-error: Unreachable code error
    inputFile.current.value = null
  }

  return (
    <input
      ref={inputFile}
      id={id || 'file-select-finput'}
      type="file"
      name="file"
      accept={dataTypes}
      onChange={handleFileInput}
      style={{ display: 'none' }}
      multiple={multiple}
    />
  )
}

export default FileUpload
