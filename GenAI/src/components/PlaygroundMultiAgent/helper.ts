import { NodeItem_Output } from '@/apis/client'
import { DATA_TYPES } from '@/pages/WorkflowDetail/components/PlaygroundAutomationProcess/AddFileProcess/helper'
import { CustomNode } from '@/pages/WorkflowDetail/types'

export interface ILocalFile {
  id: string
  name: string
  size: number
  type: string
  file: File
  error?: string
}

export interface ILocalMessageParams {
  text?: string
  files?: ILocalFile[]
  intent?: string
}

export const ArrAIOperation = [
  { id: 'Summarize content', name: 'Summarize content' },
  { id: 'Extract information', name: 'Extract information' },
  { id: 'Query and search', name: 'Query and search' },
]

export const getErrorValidateFileWithMax = (
  size: number,
  type: string,
  maxMB: number
) => {
  if (!DATA_TYPES?.includes(type)) {
    return 'Not supported'
  }
  const maxSize = maxMB * 1048576

  if (size > maxSize) return `Max ${maxMB}MB`
  return ''
}

export const findNodeById = (nodes: CustomNode[], id: string) => {
  return nodes.find((node) => node.id === id)
}

export const findNodeItemById = (
  nodes: NodeItem_Output[] | undefined,
  id: string
) => {
  if (!nodes) return undefined
  return nodes.find((node) => node.id === id)
}
