import BotMessage from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/BotMessage'
import HelloMessage from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/HelloMessage'
import Responding from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/Responding'
import UserMessage from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/UserMessage'
import {
  EMessageType,
  IMessage,
  MAX_LENGTH,
  VALID_DATA_TYPES_FILE_UPLOAD,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { colors } from '@/theme'
import { nanoid } from 'nanoid'
import { memo, useCallback, useMemo, useRef, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import IconButton from '../IconButton'
import AvatarBotWaitingDefault from './assets/avatar_bot_waiting.svg?react'
import AttachFile from './AttachFile'
import CitationMessage from './CitationMessage'
import FileDownload from './FileDownload'
import FileProcessStatus from './FileProcessStatus'
import FileUploadMessage from './FileUploadMessage'
import {
  ILocalFile,
  ILocalMessageParams,
  getErrorValidateFileWithMax,
} from './helper'
import PlaygroundHeader from './PlaygroundHeader'
import TextAreaMessage from './TextAreaMessage'

interface Props {
  className?: string
  workerType: string
  message: IMessage[]

  communicationStyle?: string
  language?: string

  responding: boolean
  text: string

  helloMessage?: string
  headerTitle?: string
  headerSubTitle?: React.ReactNode
  headerAvatar?: string | React.ReactNode
  placeholder?: string
  allowStart?: boolean
  email?: string

  userMessageAvatar?: React.ReactNode

  setText: (text: string) => void

  // resetHandler
  onClickNewChat: () => void
  // chatHandler
  onSend: (message: string) => void

  // attach files
  handleFiles: (type: EMessageType, params: ILocalMessageParams) => void
  isAttachingFile?: boolean
}

const Playground = ({
  className,
  workerType,
  message,

  communicationStyle,
  language,
  responding,
  helloMessage,

  headerTitle,
  headerSubTitle,
  headerAvatar,
  text,
  placeholder = 'Send message to start',
  allowStart = true,
  userMessageAvatar,
  email,

  setText,
  onClickNewChat,
  onSend,

  handleFiles,
  isAttachingFile = false,
}: Props) => {
  const textAreaRef = useRef<any>()
  const [files, setFiles] = useState<ILocalFile[]>([])

  const IconProps = useMemo(() => {
    if (text.trimStart().trimEnd().length === 0 || responding) {
      return { color: colors['Base-Neutral'] }
    }
    return { gradient: ['#642B73', '#C6426E'] }
  }, [text, responding])

  const classNameBtn = useMemo(() => {
    if (text.length === 0 || responding) {
      return 'h-[20px] w-[20px] rounded border-0 bg-transparent flex items-center justify-center'
    }
    return 'flex items-center justify-center h-[20px] w-[20px] rounded border-0 hover:bg-Hover-2'
  }, [text, responding])

  const renderChatDetail = useCallback(() => {
    return (
      <>
        {message.map((item) => {
          const messageAvatar = item.worker?.workerAvatar ?? ''

          if (item.type === EMessageType.loading) {
            return (
              <Responding
                avatar={messageAvatar}
                key={item.id}
                workerType={workerType}
              />
            )
          }
          if (item.type === EMessageType.user) {
            return (
              <UserMessage
                message={item.text!}
                time={item.time}
                key={item.id}
                userMessageAvatar={userMessageAvatar}
              />
            )
          }

          if (item.type === EMessageType.fileUploadMessage) {
            return (
              <FileUploadMessage
                key={item.id}
                files={item.files!}
                text={item.text}
                intent={item.intent}
                time={item.time}
              />
            )
          }

          if (item.type === EMessageType.fakeMessageFileProcessStatus) {
            return (
              <FileProcessStatus
                key={item.id}
                text={item.text!}
                className="p-[4px]"
              />
            )
          }

          if (item.type === EMessageType.citationMessage) {
            return (
              <CitationMessage
                avatar={messageAvatar}
                item={item}
                key={item.id}
                workerType={workerType}
                className="p-2"
                responding={responding}
                message={message}
                setText={(value) => {
                  setText(value)
                  textAreaRef.current?.focus()
                }}
              />
            )
          }

          if (item.type === EMessageType.fileUrlCitation) {
            return (
              <FileDownload
                key={item.id}
                avatar={messageAvatar}
                workerType={workerType}
                time={item.time}
                file={item.file_url!}
                className="p-[4px]"
              />
            )
          }

          return (
            <BotMessage
              avatar={messageAvatar}
              message={item.text!}
              time={item.time}
              key={item.id}
              id={item.id}
              responding={responding}
              related_question={item.related_question}
              loading={item.loading}
              hideCopy={item.hideCopy}
              workerType={workerType}
              setText={(value) => {
                setText(value)
                textAreaRef.current?.focus()
              }}
            />
          )
        })}
      </>
    )
  }, [message, responding])

  const _handleFiles = (files: FileList) => {
    const localFiles = Array.from(files).map((file) => {
      return {
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        id: nanoid(),
        error: getErrorValidateFileWithMax(file.size, file.type, 200),
      }
    })

    setFiles((prev) => [...prev, ...localFiles])
  }

  const _handleSend = (type: EMessageType, params: ILocalMessageParams) => {
    switch (type) {
      case EMessageType.user:
        onSend(params.text ?? '')
        break
      case EMessageType.fileUploadMessage: {
        const { files, intent, text } = params

        const filesFilteredError = files?.filter((file) => !file.error)

        if (filesFilteredError?.length === 0) {
          if (!intent && text?.trim()) {
            onSend(text)
            setFiles([])
            return
          } else if (intent) {
            setText('')
            setFiles([])
          }
          return
        }
        setFiles([])
        handleFiles(type, {
          ...params,
          files: filesFilteredError,
        })
        break
      }

      default:
        break
    }
  }

  return (
    <div
      className={twMerge(
        'flex w-full flex-1 flex-col overflow-hidden rounded-[12px] bg-white p-[16px]',
        className
      )}
    >
      <PlaygroundHeader
        avatarDefault={<AvatarBotWaitingDefault />}
        avatar={headerAvatar}
        workerType={workerType}
        isStart={allowStart && message.length === 0}
        headerTitle={headerTitle}
        headerSubTitle={headerSubTitle}
        email={email}
      />

      <div className="genai-scrollbar flex flex-1 flex-col-reverse gap-1 overflow-y-auto">
        {responding && (
          <Responding
            avatar="/assets/images/avatar_bot_waiting.svg?react"
            workerType={workerType}
          />
        )}
        {renderChatDetail()}
      </div>
      {message.length === 0 && (
        <HelloMessage
          helloMessage={helloMessage}
          className="items-center"
          name={''}
          personality={''}
          communicationStyle={communicationStyle}
          language={language}
        />
      )}
      <div className="mb-2 mt-[12px] flex justify-end gap-1 pr-[12px]">
        <AttachFile
          sendFiles={_handleFiles}
          dataTypes={VALID_DATA_TYPES_FILE_UPLOAD}
          loading={isAttachingFile || responding}
        />
        <IconButton
          disabled={responding || isAttachingFile}
          nameIcon="vuesax-outline-message-add"
          sizeIcon={16}
          onClick={onClickNewChat}
          tooltipText="New chat"
          tooltipAlign={{
            offset: [-15, -4],
            overflow: { shiftX: true },
          }}
        />
      </div>

      <TextAreaMessage
        ref={textAreaRef}
        // isAutofocus={true}
        placeholder={placeholder}
        className="h-[unset] max-h-[128px] min-h-[80px] text-Primary-Color"
        classNameTextAreaWrapper="bg-transparent"
        maxLength={MAX_LENGTH}
        maxHeight={128}
        autoResize
        onChange={setText}
        value={text}
        onSend={_handleSend}
        onBlur={() => setText(text.trim())}
        classNameBtn={classNameBtn}
        iconsProps={IconProps}
        iconsBtnDisabled={text.trimStart().trimEnd().length === 0 || responding}
        files={files}
        handleDeleteFile={(file) => {
          setFiles((prev) => prev.filter((item) => item.id !== file.id))
        }}
      />
    </div>
  )
}

export default memo(Playground)
