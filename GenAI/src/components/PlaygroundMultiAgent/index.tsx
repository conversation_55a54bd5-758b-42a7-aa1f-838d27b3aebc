import {
  WorkflowResponseMessage,
  usersReadCurrentUser,
  workflowUploadFileWorkflow,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import { GEN_AI_INTERNAL_PATH, HTTP_STATUS_CODE } from '@/constants'
import { getAccessTokenLocalStorage, getWsWorkflowUrl } from '@/helpers'
import {
  EMessageType,
  IMessage,
  LETS_START_MESSAGE,
  LETS_START_RESPONSE,
  getTimeChat,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { CustomNode } from '@/pages/WorkflowDetail/types'
import { nanoid } from 'nanoid'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { v4 as uuid } from 'uuid'
import { ILocalMessageParams, findNodeById } from './helper'
import Playground from './Playground'
import PlaygroundHeaderSubTitle from './PlaygroundHeaderSubTitle'

interface IPlaygroundProps {
  className?: string
  workerType: string
  language?: string
  communicationStyle?: string

  nodes: CustomNode[]
  workflowId: string
  helloMessage?: string
  headerTitle?: string
}

const DEFAULT_WORKER_AVATAR = (
  <Icon
    name={'face-id-square'}
    size={32}
    gradient={['#642B734D', '#C6426E4D']}
  />
)

const DEFAULT_WORKER_NODE: Partial<CustomNode> | undefined = {
  data: { workerName: '', workerAvatar: null },
}

const PlaygroundMultiAgent = ({
  workerType,
  communicationStyle,
  language,
  workflowId,
  helloMessage,
  headerTitle,
  nodes,
}: IPlaygroundProps) => {
  const [message, setMessage] = useState<IMessage[]>([])
  const [text, setText] = useState('')
  const [responding, setResponding] = useState(false)

  const [responseMessage, setResponseMessage] = useState('')
  const [responseTime, setResponseTime] = useState('')
  const [hasRelatedQuestion, setHasRelatedQuestion] = useState(false)

  const socket = useRef<WebSocket | null>(null)
  const [isSocketReady, setSocketReady] = useState(false)

  const mounted = useRef(true)
  const [isAttachingFile, setAttachingFile] = useState(false)

  const [sessionId, setSessionId] = useState<string>(uuid())

  const [wsMessage, setWsMessage] = useState<WorkflowResponseMessage | null>(
    null
  )

  const currentWorkerNode = useRef<Partial<CustomNode> | undefined>(
    DEFAULT_WORKER_NODE
  )

  const messageQueue = useRef<WorkflowResponseMessage[]>([])
  const isProcessing = useRef(false)

  const processQueue = () => {
    if (messageQueue.current.length > 0 && !isProcessing.current) {
      isProcessing.current = true
      const message = messageQueue.current.shift()
      setWsMessage(message!)
      setTimeout(() => {
        isProcessing.current = false
        processQueue()
      }, 10)
    }
  }

  const [currentWorkflowNode, setCurrentWorkflowNode] =
    useState<CustomNode | null>(null)

  const connectToPlayground = async () => {
    const token = getAccessTokenLocalStorage()
    if (socket.current) {
      socket.current.close()
      socket.current = null
    }
    setResponding(false)
    setResponseMessage('')
    if (!workflowId) return
    const tSocket = new WebSocket(
      getWsWorkflowUrl(token ?? '', workflowId, sessionId)
    )
    tSocket.onopen = () => {
      console.log('Connected to playground')
      setSocketReady(true)
    }
    socket.current = tSocket
  }

  useEffect(() => {
    if (!socket.current) return

    socket.current.onmessage = (event) => {
      const res = JSON.parse(event.data) as WorkflowResponseMessage

      switch (res.type) {
        case EMessageType.worker:
          if (!responseTime) {
            setResponseTime(getTimeChat())
          }
          if (res.related_question) setHasRelatedQuestion(true)
          setResponseMessage((prev) => prev + res.text)
          currentWorkerNode.current = findNodeById(nodes, res.speaker_id ?? '')

          break

        case EMessageType.end:
          setResponding(false)

          if (responseMessage) {
            setMessage((pre) => [
              {
                id: nanoid(),
                text: responseMessage,
                type: EMessageType.worker,
                time: responseTime,
                worker: {
                  workerName: currentWorkerNode.current?.data?.workerName ?? '',
                  workerAvatar: currentWorkerNode.current?.data?.workerAvatar,
                },
                related_question: hasRelatedQuestion,
              },
              ...pre,
            ])
            setResponseMessage('')
            setResponseTime('')
            setHasRelatedQuestion(false)
          }
          break
        default:
          messageQueue.current.push(res)
          processQueue()
          break
      }
    }
    socket.current.onclose = (e) => {
      console.log('Onclose to playground', e)
      if (e.code === 3401) {
        usersReadCurrentUser()
      }
      setSocketReady(false)
      socket.current = null
      if (mounted.current) {
        setTimeout(() => {
          console.log('Reconnect to playground')
          connectToPlayground()
        }, 1000)
      }
    }
  }, [
    message,
    responseMessage,
    sessionId,
    hasRelatedQuestion,
    responseTime,
    nodes,
  ])

  useEffect(() => {
    if (!wsMessage) return

    const node = findNodeById(nodes, wsMessage.speaker_id ?? '')

    switch (wsMessage.type) {
      case EMessageType.workflowProgressMessage:
        if (node) {
          setCurrentWorkflowNode(node)
        }
        break

      // case EMessageType.worker:
      //   if (wsMessage.related_question) setHasRelatedQuestion(false)
      //   setResponseMessage((prev) => prev + wsMessage.text)

      //   break

      // case EMessageType.end:
      //   setResponding(false)

      //   if (responseMessage) {
      //     setMessage((pre) => [
      //       {
      //         id: nanoid(),
      //         text: responseMessage,
      //         type: EMessageType.worker,
      //         time: responseTime,
      //         worker: {
      //           workerName: node?.data.workerName ?? '',
      //           workerAvatar: node?.data.workerAvatar,
      //         },
      //         related_question: hasRelatedQuestion,
      //       },
      //       ...pre,
      //     ])
      //   }
      //   break

      case EMessageType.noTokenMessage:
        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.noTokenMessage,
            time: getTimeChat(),
          },
          ...pre,
        ])
        setResponding(false)
        break

      case EMessageType.citationMessage:
        if (wsMessage.related_question) setResponding(false)

        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.citationMessage,
            time: getTimeChat(),
            citation: wsMessage.citation as any,
            speakerId: wsMessage.speaker_id!,
            worker: {
              workerName: node?.data.workerName ?? '',
              workerAvatar: node?.data.workerAvatar,
            },
            related_question: wsMessage.related_question,
          },
          ...pre,
        ])
        break

      case EMessageType.fileUrlCitation:
        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.fileUrlCitation,
            time: getTimeChat(),
            file_url: wsMessage.file_url!,
            worker: {
              workerName: node?.data.workerName ?? '',
              workerAvatar: node?.data.workerAvatar,
            },
          },
          ...pre,
        ])
        break

      default:
        break
    }
  }, [wsMessage])

  useEffect(() => {
    connectToPlayground()
    setMessage([])
  }, [workflowId, sessionId])

  useEffect(() => {
    return () => {
      socket.current?.close()
      mounted.current = false
    }
  }, [])

  const resetHandler = () => {
    setMessage([])
    setText('')
    setSessionId(uuid())
  }

  const chatHandler = (tVal: string) => {
    const val = tVal.trimStart().trimEnd()

    if (val.length === 0 || responding || !isSocketReady) return
    setResponding(true)
    setText('')
    setMessage((pre) => [
      {
        id: nanoid(),
        text: val,
        type: EMessageType.user,
        time: getTimeChat(),
      },
      ...pre,
    ])
    if (val.toLocaleLowerCase() === LETS_START_MESSAGE) {
      setTimeout(() => {
        setMessage((pre) => [
          {
            id: nanoid(),
            text: LETS_START_RESPONSE,
            type: EMessageType.worker,
            time: getTimeChat(),
            hideCopy: true,
          },
          ...pre,
        ])
        setResponding(false)
      }, 1000)
    } else {
      socket.current?.send(
        JSON.stringify({
          text: val,
          type: EMessageType.user,
          chat_history: message
            .filter(
              ({ text, type }) =>
                (type === EMessageType.user &&
                  text!.toLocaleLowerCase() !== LETS_START_MESSAGE) ||
                (type === EMessageType.worker && text !== LETS_START_RESPONSE)
            )
            .map(({ text, type }) => ({
              text,
              type,
            }))
            .reverse(),
        })
      )
    }
  }

  const handleFiles = async (
    __type: EMessageType,
    params: ILocalMessageParams
  ) => {
    try {
      if (isAttachingFile || !params.files) return

      const { text, intent } = params
      if (text) {
        setText('')
      }
      setResponding(true)

      const files = [...params.files]

      if (files?.length) {
        // Fake file message
        let listFakeMessageFiles = files

        const idMessage = nanoid()
        const time = getTimeChat()

        setAttachingFile(true)

        setMessage((pre) => [
          {
            id: idMessage,
            files: files,
            text,
            intent,
            type: EMessageType.fileUploadMessage,
            time,
          },
          ...pre,
        ])

        const listFilePromises = files.map((file) => {
          return workflowUploadFileWorkflow({
            baseURL: GEN_AI_INTERNAL_PATH,
            body: {
              file_display_name: file?.name,
              file_storage_upload: file.file,
              file_intent: (intent as any) || null,
            },
            path: {
              workflow_id: workflowId,
              session_id: sessionId,
            },
          })
        })
        const listFileResponses = await Promise.all(listFilePromises)

        const fileSuccess: any = []

        for (let i = 0; i < listFileResponses?.length; i++) {
          if (listFileResponses[i].status === HTTP_STATUS_CODE.SUCCESS) {
            const dataRes = listFileResponses[i].data

            if (dataRes) fileSuccess.push(dataRes.data.id)
          } else {
            listFakeMessageFiles = listFakeMessageFiles?.map(
              (item: any, index: number) => {
                if (i === index && !item.error) {
                  return {
                    ...item,
                    error: 'Failed',
                  }
                }
                return item
              }
            )
          }
        }

        const newMessage = {
          id: idMessage,
          files: listFakeMessageFiles,
          text,
          intent,
          type: EMessageType.fileUploadMessage,
          time,
        }

        setMessage((prev) => {
          const newMessages = JSON.parse(JSON.stringify(prev)) as IMessage[]

          // update message with idMessage
          const index = newMessages.findIndex((item) => item.id === idMessage)
          if (index !== -1) {
            newMessages[index] = newMessage
          }

          return newMessages
        })

        if (fileSuccess?.length) {
          socket?.current?.send(
            JSON.stringify({
              type: EMessageType.fileUploadMessage,
              text: fileSuccess?.join(', '),
            })
          )

          setTimeout(() => {
            socket?.current?.send(
              JSON.stringify({
                type: EMessageType.user,
                text: intent ?? text,
              })
            )
          }, 100)
        } else {
          setResponding(false)
        }
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setAttachingFile(false)
    }
  }

  const displayMessage = useMemo(() => {
    if (responding && responseMessage) {
      return [
        {
          text: responseMessage,
          type: EMessageType.worker,
          time: getTimeChat(),
          loading: true,
        },
        ...message,
      ]
    }

    return message
  }, [message, responding, responseMessage])

  return (
    <Playground
      workerType={workerType}
      communicationStyle={communicationStyle}
      language={language}
      message={displayMessage}
      text={text}
      setText={setText}
      headerTitle={headerTitle}
      headerSubTitle={
        <PlaygroundHeaderSubTitle
          name={currentWorkflowNode?.data.workerName ?? ''}
          task={currentWorkflowNode?.data.name ?? ''}
        />
      }
      headerAvatar={
        currentWorkflowNode &&
        (currentWorkflowNode.data?.workerAvatar?.trim() ||
          DEFAULT_WORKER_AVATAR)
      }
      responding={responding}
      helloMessage={helloMessage}
      onClickNewChat={resetHandler}
      onSend={chatHandler}
      handleFiles={handleFiles}
      isAttachingFile={isAttachingFile}
    />
  )
}

export default memo(PlaygroundMultiAgent)
