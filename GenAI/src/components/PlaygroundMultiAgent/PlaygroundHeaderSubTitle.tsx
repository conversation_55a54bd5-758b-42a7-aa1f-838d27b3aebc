import Text from '../Text'

const PlaygroundHeaderSubTitle = ({
  name,
  task,
}: {
  name: string
  task: string
}) => {
  if (!name && !task) return null

  return (
    <div className="flex w-[calc(100%)] items-center justify-center px-[44px]">
      <Text
        type="supportText"
        variant="medium"
        className="text-center text-Secondary-Color"
      >
        {name} is handling{' '}
        <Text
          type="supportText"
          variant="medium"
          className="text-center text-Primary-Color"
        >
          {task}
        </Text>{' '}
        task...
      </Text>
    </div>
  )
}

export default PlaygroundHeaderSubTitle
