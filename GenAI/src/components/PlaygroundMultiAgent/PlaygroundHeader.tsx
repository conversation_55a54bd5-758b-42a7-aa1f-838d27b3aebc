import Icon from '@/assets/icon/Icon'
import Avatar from '../Avatar'
import Text from '../Text'

interface Props {
  avatar?: string | React.ReactNode
  workerType: string
  isStart?: boolean
  headerTitle?: string
  headerSubTitle?: React.ReactNode
  avatarDefault?: React.ReactNode
  email?: string
}
const PlaygroundHeader = ({
  avatar,
  workerType,
  isStart,
  headerTitle,
  headerSubTitle,
  avatarDefault,
  email,
}: Props) => {
  const element = () => {
    if (isStart) {
      return (
        <>
          <div className="flex h-[40px] w-[40px] items-center justify-center rounded-full bg-Main-Disable-2">
            <Icon
              name="Custom-Activate"
              size={32}
              gradient={['#996F93', '#B26987']}
            />
          </div>

          <Text
            type="subBody"
            variant="medium"
            className="text-center text-Secondary-Color"
          >
            Start
          </Text>
        </>
      )
    }

    if (!email) {
      return (
        <>
          {typeof avatar === 'string' ||
          avatar === null ||
          avatar === undefined ? (
            <Avatar
              avatarUrl={avatar || undefined}
              className="self-center"
              avatarDefault={
                avatarDefault ? (
                  avatarDefault
                ) : (
                  <div className="flex size-10 items-center justify-center rounded-full bg-Background-Color">
                    <Icon
                      name={
                        workerType === 'Human Worker'
                          ? 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
                          : 'face-id-square'
                      }
                      size={32}
                      gradient={['#642B734D', '#C6426E4D']}
                    />
                  </div>
                )
              }
            />
          ) : (
            avatar
          )}

          {headerSubTitle && headerSubTitle}
        </>
      )
    }

    return (
      <div className="flex w-full items-center gap-2">
        <div className="flex !size-6 items-center justify-center rounded-full bg-Background-Color">
          <Icon
            name={'vuesax-bold-user'}
            size={16}
            gradient={['#642B73', '#C6426E']}
          />
        </div>
        <div className="!w-[calc(100%-32px)]">
          <Text
            value={email}
            className="text-Secondary-Color"
            elementType="div"
            type="subBody"
            variant="medium"
            ellipsis
            tooltipBreakWord
            tooltipPosition="bottom"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="flex w-full flex-col items-center gap-[8px] overflow-hidden">
      {headerTitle && (
        <div className="flex w-[calc(100%)] items-center justify-center px-[44px]">
          <Text
            type="body"
            variant="semibold"
            elementType="div"
            className="text-center text-Primary-Color"
          >
            {headerTitle}
          </Text>
        </div>
      )}

      <div className="flex w-full flex-col items-center gap-1">{element()}</div>
    </div>
  )
}

export default PlaygroundHeader
