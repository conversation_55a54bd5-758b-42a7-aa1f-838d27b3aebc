import Text from '@/components/Text'
import { memo } from 'react'

/* eslint-disable max-len */
const FormatNotSupported = ({ page }: { page: number | undefined }) => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-[20px]">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="99"
        height="120"
        viewBox="0 0 99 120"
        fill="none"
      >
        <path
          d="M57.8145 49.0287C59.0359 47.8073 60.643 47.143 62.3573 47.143H91.2858V6.4285C91.2858 2.89286 88.393 0 84.8573 0H33.4294V19.2848C33.4294 22.8204 30.5365 25.7133 27.0009 25.7133H7.71606V55.713H51.1295L57.8145 49.0287Z"
          fill="url(#paint0_linear_13057_146239)"
        />
        <path
          d="M29.1433 19.2972V1.25488L8.95752 21.4407H26.9998C28.1837 21.4407 29.1433 20.4811 29.1433 19.2972Z"
          fill="url(#paint1_linear_13057_146239)"
        />
        <path
          d="M96.643 51.4297H62.3577C61.7898 51.4297 61.2434 51.6547 60.8416 52.0565L52.897 60.0011H2.35768C1.17376 60.0011 0.214844 60.96 0.214844 62.1439V109.286C0.214844 115.195 5.02014 120 10.929 120H88.071C93.9799 120 98.7851 115.195 98.7851 109.286V53.5726C98.7851 52.3886 97.8269 51.4297 96.643 51.4297ZM30.2145 86.7861C29.6681 86.7861 29.1163 86.5772 28.6984 86.1593C27.8627 85.3236 27.8627 83.9682 28.6984 83.1272L32.5395 79.2862L28.6984 75.4451C27.8627 74.6094 27.8627 73.254 28.6984 72.4131C29.5341 71.5774 30.8895 71.5774 31.7305 72.4131L35.5715 76.2541L39.4126 72.4131C40.2483 71.5774 41.6037 71.5774 42.4446 72.4131C43.2803 73.2488 43.2803 74.6042 42.4446 75.4451L38.6036 79.2862L42.4446 83.1272C43.2803 83.963 43.2803 85.3183 42.4446 86.1593C42.0268 86.5772 41.4804 86.7861 40.9285 86.7861C40.3821 86.7861 39.8303 86.5772 39.4124 86.1593L35.5714 82.3183L31.7303 86.1593C31.3125 86.5772 30.7663 86.7861 30.2145 86.7861ZM62.3577 108.214C61.1738 108.214 60.2148 107.255 60.2148 106.072C60.2148 100.163 55.4095 95.3574 49.5007 95.3574C43.5918 95.3574 38.7865 100.163 38.7865 106.072C38.7865 107.255 37.8276 108.214 36.6437 108.214C35.4598 108.214 34.5009 107.255 34.5009 106.072C34.5009 97.8003 41.2293 91.0711 49.5014 91.0711C57.7735 91.0711 64.5019 97.7995 64.5019 106.072C64.5019 107.255 63.5416 108.214 62.3577 108.214ZM70.3023 83.1271C71.138 83.9629 71.138 85.3183 70.3023 86.1592C69.8844 86.5771 69.338 86.786 68.7862 86.786C68.2397 86.786 67.688 86.5771 67.2701 86.1592L63.429 82.3182L59.588 86.1592C59.1701 86.5771 58.6237 86.786 58.0719 86.786C57.5254 86.786 56.9737 86.5771 56.5558 86.1592C55.7201 85.3235 55.7201 83.9681 56.5558 83.1271L60.3968 79.2861L56.5558 75.445C55.7201 74.6093 55.7201 73.2539 56.5558 72.413C57.3915 71.5773 58.7469 71.5773 59.5878 72.413L63.4289 76.254L67.2699 72.413C68.1057 71.5773 69.461 71.5773 70.302 72.413C71.1377 73.2487 71.1377 74.6041 70.302 75.445L66.4609 79.2861L70.3023 83.1271Z"
          fill="url(#paint2_linear_13057_146239)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_13057_146239"
            x1="91.2858"
            y1="27.8565"
            x2="35.9238"
            y2="68.4883"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#DBCEDD" />
            <stop offset="1" stopColor="#EFD5DF" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_13057_146239"
            x1="29.1433"
            y1="11.3478"
            x2="12.5422"
            y2="19.4705"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#DBCEDD" />
            <stop offset="1" stopColor="#EFD5DF" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_13057_146239"
            x1="98.7851"
            y1="85.715"
            x2="31.5655"
            y2="132.994"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.2" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
          </linearGradient>
        </defs>
      </svg>

      <div className="flex flex-col items-center">
        <Text type="subBody" variant="medium" className="text-Primary-Color">
          Format is currently not supported!
        </Text>

        <div className="flex items-center gap-[3px]">
          <Text type="subBody" variant="light" className="text-Secondary-Color">
            Could you review this citation on
          </Text>

          <Text type="subBody" variant="light" className="text-Primary-Color">
            page {page}
          </Text>

          <Text type="subBody" variant="light" className="text-Secondary-Color">
            of your document file
          </Text>
        </div>
      </div>
    </div>
  )
}

export default memo(FormatNotSupported)
