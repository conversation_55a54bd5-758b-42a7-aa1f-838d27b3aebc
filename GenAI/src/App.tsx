import { MessageContainer } from '@/components/Message/Message'
import { StyleProvider } from '@ant-design/cssinjs'
import { ConfigProvider } from 'antd'
import AuthProvider from './providers/AuthProvider'
import ClientProvider from './providers/ClientProvider'
import GenerateWorkflowProvider from './providers/GenerateWorkflowProvider'
import GroupToolProvider from './providers/GroupToolProvider'
import MyProfileProvider from './providers/MyProfileProvider'
import SidebarProvider from './providers/SidebarProvider'
import Routes from './routes/routes'
import './styles/App.css'
import './styles/index.scss'

function App() {
  return (
    <StyleProvider layer>
      <ConfigProvider theme={{ hashed: false }}>
        <AuthProvider>
          <ClientProvider>
            <MyProfileProvider>
              <SidebarProvider>
                <GenerateWorkflowProvider>
                  <MessageContainer />
                  <GroupToolProvider>
                    <Routes />
                  </GroupToolProvider>
                </GenerateWorkflowProvider>
              </SidebarProvider>
            </MyProfileProvider>
          </ClientProvider>
        </AuthProvider>
      </ConfigProvider>
    </StyleProvider>
  )
}

export default App
