/* eslint-disable max-len */
import Button from '@/components/Button'
import Text from '@/components/Text'
import { rootUrls } from '@/routes/rootUrls'
import { useNavigate } from 'react-router-dom'

const Welcome = () => {
  const navigate = useNavigate()

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative flex h-[271px] w-[483px] flex-col items-center justify-center gap-[24px] rounded-[16px]">
        <div className="flex flex-col items-center gap-[8px]">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="121"
            height="121"
            viewBox="0 0 121 121"
            fill="none"
          >
            <path
              d="M65.6954 92.432L67.0296 91.9873C78.5199 88.1572 84.265 86.2422 85.3347 81.7109C86.4044 77.1796 82.1222 72.8975 73.5579 64.3332L65.1116 55.8868C65.104 55.9105 65.0962 55.9351 65.0881 55.9608C64.99 56.2698 64.848 56.7251 64.6768 57.2971C64.3338 58.4429 63.8762 60.0475 63.4195 61.8766C62.4845 65.6206 61.6358 69.9863 61.6358 73.276C61.6358 76.5657 62.4845 80.9313 63.4195 84.6753C63.8762 86.5044 64.3338 88.109 64.6768 89.2548C64.848 89.8268 64.99 90.2821 65.0881 90.5911C65.1371 90.7456 65.1751 90.8633 65.2002 90.9407L65.2281 91.0259L65.2343 91.0448L65.6954 92.432Z"
              fill="url(#paint0_linear_4766_19349)"
            />
            <path
              d="M37.8585 101.707C26.5507 105.435 20.5929 106.995 17.2972 103.699C13.6478 100.049 15.9523 93.136 20.5613 79.3089L29.0088 53.9664C32.1689 44.4863 34.0253 38.917 37.1231 36.6578L37.097 36.7868C37.0609 36.9664 37.0088 37.2287 36.9431 37.5663C36.8117 38.2412 36.6262 39.2173 36.4071 40.4331C35.9691 42.8632 35.3957 46.2592 34.8527 50.1286C33.777 57.7947 32.7807 67.5896 33.294 75.4168C33.6046 80.153 34.5899 86.0283 35.4693 90.6192C35.9132 92.9362 36.338 94.9667 36.6521 96.419C36.8093 97.1456 36.939 97.7287 37.0298 98.1318L37.1355 98.5974L37.8585 101.707Z"
              fill="url(#paint1_linear_4766_19349)"
            />
            <path
              d="M44.7527 36.7948L44.4993 38.0212L44.4968 38.0333L44.4877 38.0781L44.4501 38.2638C44.4169 38.4293 44.3676 38.677 44.305 38.999C44.1796 39.6433 44.0005 40.585 43.7882 41.7633C43.3632 44.1213 42.8064 47.4188 42.28 51.1708C41.2168 58.7475 40.3177 67.9069 40.7779 74.926C41.0566 79.1756 41.9667 84.6729 42.8354 89.2082C43.2656 91.4543 43.678 93.4252 43.9826 94.8336C44.1348 95.5373 44.2599 96.0994 44.3465 96.4838L44.446 96.9224L44.4715 97.033L45.0056 99.3286L58.5802 94.8038L58.0679 93.2601C58.0375 93.1667 57.9941 93.032 57.9394 92.8598C57.8302 92.5156 57.676 92.0209 57.4918 91.4056C57.124 90.177 56.6339 88.4584 56.1429 86.4925C55.1825 82.6465 54.1358 77.535 54.1358 73.276C54.1358 69.0169 55.1825 63.9054 56.1429 60.0594C56.6339 58.0936 57.124 56.375 57.4918 55.1463C57.676 54.531 57.8302 54.0363 57.9394 53.6921C57.9941 53.5199 58.0375 53.3853 58.0679 53.2918L58.1134 53.1527L59.1771 49.9524L56.6629 47.4382C51.4448 42.2201 47.8164 38.5916 44.7527 36.7948Z"
              fill="url(#paint2_linear_4766_19349)"
            />
            <path
              d="M55.1331 12.301C56.9001 13.3813 57.4569 15.6895 56.3767 17.4565C55.5928 18.7387 55.7892 20.3911 56.8519 21.4538L57.3413 21.9431C60.2846 24.8864 61.3699 29.2103 60.1656 33.1947C59.5663 35.1772 57.4734 36.2985 55.4909 35.6992C53.5084 35.1 52.3871 33.0071 52.9864 31.0246C53.3908 29.6867 53.0264 28.2348 52.038 27.2464L51.5486 26.7571C48.036 23.2444 47.3866 17.7829 49.9777 13.5446C51.0579 11.7776 53.3661 11.2208 55.1331 12.301Z"
              fill="url(#paint3_linear_4766_19349)"
            />
            <path
              d="M68.3061 22.4829C69.3114 21.4776 69.814 20.975 70.3952 20.7908C70.8868 20.6349 71.4146 20.6349 71.9062 20.7908C72.4873 20.975 72.99 21.4776 73.9953 22.4829C75.0005 23.4882 75.5032 23.9908 75.6874 24.572C75.8433 25.0636 75.8433 25.5914 75.6874 26.083C75.5032 26.6641 75.0006 27.1668 73.9953 28.172C72.99 29.1773 72.4873 29.68 71.9062 29.8642C71.4146 30.0201 70.8868 30.0201 70.3952 29.8642C69.814 29.68 69.3114 29.1774 68.3061 28.1721C67.3008 27.1668 66.7982 26.6641 66.614 26.083C66.4581 25.5914 66.4581 25.0636 66.614 24.572C66.7982 23.9908 67.3008 23.4882 68.3061 22.4829Z"
              fill="url(#paint4_linear_4766_19349)"
            />
            <path
              d="M35.1366 20.2045C36.1839 19.1572 37.882 19.1572 38.9294 20.2045C39.9767 21.2518 39.9767 22.9499 38.9294 23.9973C37.882 25.0446 36.1839 25.0446 35.1366 23.9973C34.0892 22.9499 34.0892 21.2518 35.1366 20.2045Z"
              fill="url(#paint5_linear_4766_19349)"
            />
            <path
              d="M100.375 35.734C99.6825 36.0007 99.0691 36.6142 97.8421 37.8411C96.6152 39.0681 96.0017 39.6816 95.735 40.3736C95.423 41.1835 95.423 42.0805 95.735 42.8904C96.0017 43.5824 96.6152 44.1959 97.8421 45.4229C99.0691 46.6498 99.6825 47.2633 100.375 47.5299C101.185 47.842 102.081 47.842 102.891 47.5299C103.583 47.2633 104.197 46.6498 105.424 45.4229C106.651 44.1959 107.264 43.5824 107.531 42.8904C107.843 42.0805 107.843 41.1835 107.531 40.3736C107.264 39.6816 106.651 39.0681 105.424 37.8411C104.197 36.6142 103.583 36.0007 102.891 35.734C102.081 35.422 101.185 35.422 100.375 35.734Z"
              fill="url(#paint6_linear_4766_19349)"
            />
            <path
              d="M95.7909 77.0667C96.8382 76.0194 98.5363 76.0194 99.5837 77.0667C100.631 78.1141 100.631 79.8122 99.5837 80.8595C98.5363 81.9069 96.8382 81.9069 95.7909 80.8595C94.7435 79.8122 94.7435 78.1141 95.7909 77.0667Z"
              fill="url(#paint7_linear_4766_19349)"
            />
            <path
              d="M88.947 24.1087C90.9779 24.5149 92.2949 26.4905 91.8887 28.5213L91.1688 32.121C90.178 37.0751 86.6086 41.1184 81.8157 42.7161C79.5761 43.4626 77.9082 45.3519 77.4452 47.6669L76.7253 51.2665C76.3191 53.2974 74.3435 54.6144 72.3127 54.2083C70.2818 53.8021 68.9647 51.8265 69.3709 49.7957L70.0909 46.196C71.0817 41.2419 74.6511 37.1986 79.444 35.6009C81.6836 34.8544 83.3515 32.965 83.8145 30.6501L84.5344 27.0504C84.9406 25.0196 86.9162 23.7025 88.947 24.1087Z"
              fill="url(#paint8_linear_4766_19349)"
            />
            <path
              d="M107.542 63.2981C105.733 62.5073 103.63 62.8394 102.153 64.1491C98.1069 67.7371 92.2186 68.3334 87.5352 65.6294L86.4709 65.015C84.6774 63.9794 84.0628 61.686 85.0984 59.8924C86.1339 58.0988 88.4274 57.4842 90.2209 58.5198L91.2852 59.1342C93.1729 60.2241 95.5463 59.9837 97.1772 58.5375C100.841 55.2884 106.059 54.4643 110.546 56.4262L112.004 57.0634C113.902 57.8932 114.767 60.1041 113.938 62.0017C113.108 63.8993 110.897 64.765 108.999 63.9353L107.542 63.2981Z"
              fill="url(#paint9_linear_4766_19349)"
            />
            <path
              d="M88.0017 49.208C89.0491 48.1606 90.7471 48.1606 91.7945 49.208C92.8418 50.2553 92.8418 51.9534 91.7945 53.0007C90.7471 54.0481 89.0491 54.0481 88.0017 53.0007C86.9544 51.9534 86.9544 50.2553 88.0017 49.208Z"
              fill="url(#paint10_linear_4766_19349)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.3" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.3" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient
                id="paint2_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.3" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient
                id="paint3_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint4_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint5_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint6_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint7_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint8_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint9_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient
                id="paint10_linear_4766_19349"
                x1="114.253"
                y1="58.625"
                x2="34.7182"
                y2="99.618"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#642B73" stopOpacity="0.2" />
                <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
              </linearGradient>
            </defs>
          </svg>

          <div className="flex flex-col items-center gap-[4px]">
            <Text
              variant="semibold"
              type="title"
              className="text-Primary-Color"
            >
              Welcome to AI Agency!
            </Text>
            <Text
              variant="regular"
              type="subBody"
              className="text-Secondary-Color"
            >
              Ready to optimize your business process with our AI agency?
            </Text>
          </div>
        </div>
      </div>

      <Button
        text={'Go to Login'}
        type="secondary"
        onClick={() => {
          navigate(rootUrls.Login, { replace: true })
        }}
      />
    </div>
  )
}

export default Welcome
