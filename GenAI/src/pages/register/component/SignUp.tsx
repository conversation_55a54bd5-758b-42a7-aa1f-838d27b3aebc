import { ValidationError, usersRegister<PERSON>ser<PERSON><PERSON> } from '@/apis/client'
import Button from '@/components/Button'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { useState } from 'react'

interface SignUpProps {
  email: string
  setEmail: any
  nextStep: () => void
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,6}$/
  const domainPart = email.split('@')[1]

  // Kiểm tra phần miền chỉ có một dấu chấm
  if (domainPart && domainPart.split('.').length === 2) {
    return emailRegex.test(email)
  }
  return false
}

const SignUp = (props: SignUpProps) => {
  const { email, setEmail, nextStep } = props
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<ValidationError[] | string>('')

  const submitEmail = async () => {
    setLoading(true)
    try {
      const data = await usersRegisterUserApi({
        body: {
          email: email?.trim(),
        },
      })

      if (data.status === 200) {
        nextStep()
      } else if (data.status === 400 && data.error) {
        const message =
          data.error.detail ===
          'The user with this email already exists in the system'
            ? 'This email is already registered'
            : data.error.detail
        setError(message ?? '')
      } else {
        setError('Invalid email address')
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }
  const handleBlurEmail = () => {
    setEmail(email?.trim())
    if (!isValidEmail(email.trim()) && email.trim() !== '')
      setError('Invalid email address')
  }

  const handleChangeEmail = (event: any) => {
    setEmail(event.target.value)
    setError('')
  }

  const handleContinue = async () => {
    if (email.trim() === '' || !isValidEmail(email.trim())) {
      setError('Invalid email address')
    } else {
      submitEmail()
      setError('')
    }
  }

  return (
    <div className="flex flex-col items-center gap-[32px] p-[32px]">
      <Text type="title" variant="semibold" className="text-Primary-Color">
        Sign Up
      </Text>

      <div className="flex flex-col items-center gap-[12px]">
        <div className="flex w-[287px] flex-col items-center gap-[28px]">
          <div className="flex w-full flex-col items-center gap-[24px]">
            <div className="flex w-full flex-col items-center gap-[12px]">
              <Input
                label={'Email'}
                placeholder="Type in your email address"
                value={email}
                maxLength={255}
                onBlur={handleBlurEmail}
                onChange={handleChangeEmail}
                onPressEnter={handleContinue}
                isFullWidth
                errorText={error.toString()}
                isError={!!error.toString()}
                type="text"
              />
            </div>
          </div>
          <Button
            onClick={() => {
              handleContinue()
            }}
            text={'Continue'}
            type="primary"
            disabled={email.trim() === '' || !isValidEmail(email.trim())}
            className="w-full"
            loading={loading}
          />
        </div>

        <Text
          type="supportText"
          variant="medium"
          className="text-Secondary-Color"
        >
          By creating an account, you agree to our Terms of Services and Privacy
          Policy
        </Text>
      </div>
    </div>
  )
}

export default SignUp
