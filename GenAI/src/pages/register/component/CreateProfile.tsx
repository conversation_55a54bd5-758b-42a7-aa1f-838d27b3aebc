import {
  Val<PERSON><PERSON><PERSON>rror,
  users<PERSON>on<PERSON>rm<PERSON>ser<PERSON><PERSON><PERSON><PERSON>,
  usersReg<PERSON><PERSON>ser<PERSON><PERSON>,
} from '@/apis/client'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { useCallback, useEffect, useState } from 'react'

interface CreateProfileProps {
  email: string
  nextStep: () => void
}

function isValidOTP(otp: string) {
  const otpPattern = /^\d{6}$/

  return otpPattern.test(otp)
}
function validatePassword(password: string) {
  const errors = []

  // Check if password is at least 8 characters long
  if (password.length < 8) {
    errors.push(1)
  }

  // Check if password contains at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    errors.push(2)
  }

  // Check if password contains at least one digit
  if (!/\d/.test(password)) {
    errors.push(3)
  }

  // Check if password contains at least one special character
  if (!/[@$!%*?&]/.test(password)) {
    errors.push(4)
  }

  // If there are no errors, the password is valid
  if (errors.length === 0) {
    return {
      isValid: true,
      errors: [],
    }
  }

  // Return errors if the password is not valid
  return {
    isValid: false,
    errors: errors,
  }
}
const CreateProfile = (props: CreateProfileProps) => {
  const { email, nextStep } = props
  const [typePassword, setTypePassword] = useState('text')
  const [loading, setLoading] = useState(false)
  const [resend, setResend] = useState(false)
  const [isCountDown, setIsCountDown] = useState(false)
  const [countDown, setCountDown] = useState(30)

  const [errorOtp, setErrorOtp] = useState<ValidationError[] | string>('')
  const [errorPassword, setErrorPassword] = useState<number[]>([])

  const [otp, setOtp] = useState('')
  const [name, setName] = useState('')
  const [password, setPassword] = useState('')

  const handleViewPassword = useCallback(() => {
    switch (typePassword) {
      case 'password':
        setTypePassword('text')
        break
      case 'text':
        setTypePassword('password')
        break
      default:
        break
    }
  }, [typePassword])

  const submitProfile = useCallback(async () => {
    setLoading(true)
    try {
      const data = await usersConfirmUserEmailApi({
        body: {
          email,
          otp_code: otp,
          name: name?.trim(),
          password,
        },
      })

      if (data.status === 200) {
        nextStep()
      } else if (data.status === 400 && data.error) {
        setErrorOtp('Token is invalid or expired')
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }, [email, otp, name, password])

  const resendEmail = useCallback(async () => {
    setResend(true)
    try {
      const data = await usersRegisterUserApi({
        body: {
          email: email,
        },
      })

      if (data.status === 200) {
        setResend(false)
        setIsCountDown(true)
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }, [email])

  useEffect(() => {
    if (isCountDown) {
      if (countDown > 0) {
        const timerId = setInterval(() => {
          setCountDown((prevSeconds) => prevSeconds - 1)
        }, 1000)

        return () => clearInterval(timerId)
      } else {
        setIsCountDown(false)
        setCountDown(30)
      }
    }
  }, [countDown, isCountDown])

  const handleContinue = useCallback(() => {
    setPassword(password?.trim())

    const validatePass = validatePassword(password?.trim())
    const validateOtp = isValidOTP(otp)

    if (!validatePass.isValid) {
      setErrorPassword(validatePass.errors)
    }
    if (!validateOtp) {
      setErrorOtp('6 digit one-time-password is required')
    }
    if (validatePass.isValid && validateOtp) {
      submitProfile()
    }
  }, [password, otp])

  const validatePasswords = useCallback(() => {
    const validatePass = validatePassword(password)
    if (!validatePass.isValid && password?.trim()) {
      setErrorPassword(validatePass.errors)
    } else {
      setErrorPassword([])
    }
  }, [password])

  const validateOtp = useCallback(() => {
    const validateOtp = isValidOTP(otp)
    if (!validateOtp && otp) {
      setErrorOtp('6 digit one-time-password is required')
    } else {
      setErrorOtp('')
    }
  }, [otp])

  useEffect(() => {
    const handleKeyDown = (event: { key: string }) => {
      if (event.key === 'Enter') {
        if (
          !(
            !name?.trim() ||
            !password ||
            !otp ||
            !validatePassword(password).isValid ||
            !isValidOTP(otp)
          )
        ) {
          submitProfile()
        }
      }
    }
    window.addEventListener('keydown', handleKeyDown)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [name, password, otp])

  return (
    <div className="flex flex-col items-center gap-[32px] p-[32px]">
      <div className="flex flex-col items-center gap-[4px]">
        <Text type="title" variant="semibold" className="text-Primary-Color">
          Create your profile
        </Text>
        <Text
          type="subheading"
          variant="medium"
          className="text-Secondary-Color"
        >
          Tell us about yourself
        </Text>
      </div>

      <div className="flex flex-col items-center gap-[12px]">
        <div className="flex w-[287px] flex-col items-center gap-[28px]">
          <div className="flex w-full flex-col items-center gap-[24px]">
            <div className="flex w-full flex-col items-center gap-[12px]">
              <Input
                label={'Your name'}
                placeholder="Type in your preferred name"
                isFullWidth
                onChange={(e) => setName(e.target.value)}
                onBlur={() => setName(name?.trim())}
                value={name}
                maxLength={50}
              />
              <Input
                label={'Set password'}
                placeholder="Password need to be in required format"
                type={typePassword}
                onChange={(e) => {
                  setPassword(e.target.value)
                }}
                onBlur={() => {
                  setPassword(password?.trim())
                  validatePasswords()
                }}
                value={password}
                className="bg-white"
                maxLength={255}
                helperText={
                  <div className="flex flex-col">
                    <div className="flex flex-1 items-center gap-[0.1em]">
                      <Text
                        type="supportText"
                        className={
                          errorPassword.find((n) => n === 1)
                            ? 'ml-[0.1em] text-Error-Color'
                            : 'ml-[0.1em] text-Secondary-Color'
                        }
                      >
                        At least 8 characters long
                      </Text>
                      <Text type="supportText" className="text-Secondary-Color">
                        ,
                      </Text>
                      <Text
                        type="supportText"
                        className={
                          errorPassword.find((n) => n === 2)
                            ? 'ml-[0.1em] text-Error-Color'
                            : 'ml-[0.1em] text-Secondary-Color'
                        }
                      >
                        1 uppercase letter
                      </Text>
                      <Text type="supportText" className="text-Secondary-Color">
                        ,
                      </Text>
                      <Text
                        type="supportText"
                        className={
                          errorPassword.find((n) => n === 3)
                            ? 'ml-[0.1em] text-Error-Color'
                            : 'ml-[0.1em] text-Secondary-Color'
                        }
                      >
                        1 number
                      </Text>
                    </div>

                    <div className="flex flex-1 items-center gap-[0.1em]">
                      <Text type="supportText" className="text-Secondary-Color">
                        and
                      </Text>
                      <Text
                        type="supportText"
                        className={
                          errorPassword.find((n) => n === 4)
                            ? 'ml-[0.1em] text-Error-Color'
                            : 'ml-[0.1em] text-Secondary-Color'
                        }
                      >
                        1 special character
                      </Text>
                    </div>
                  </div>
                }
                isFullWidth
                suffix={
                  <IconButton
                    onClick={() => handleViewPassword()}
                    nameIcon={
                      typePassword === 'password'
                        ? 'vuesax-bold-eye-slash'
                        : 'vuesax-bold-eye'
                    }
                    sizeIcon={16}
                    colorIcon="#D4D4D4"
                    hoverColor={'#2D0136'}
                  />
                }
              />
              <div className="flex w-full flex-col items-center justify-center gap-[8px]">
                <Input
                  label={'Verification code'}
                  placeholder="Type in OTP you got via email"
                  isFullWidth
                  onChange={(e) => {
                    if (!isNaN(e.target.value) && e.target.value.length < 7) {
                      setOtp(e.target.value.trim())
                      if (errorOtp) {
                        setErrorOtp('')
                      }
                    }
                  }}
                  onBlur={() => {
                    validateOtp()
                  }}
                  value={otp}
                  maxLength={6}
                  errorText={errorOtp.toString()}
                  isError={!!errorOtp.toString()}
                />

                <div className="flex items-center justify-center gap-[0.1em]">
                  <Text type="subBody" className="text-Secondary-Color">
                    Didn’t get the code?
                  </Text>
                  {isCountDown ? (
                    <Text
                      type="subBody"
                      variant="medium"
                      className="cursor-pointer rounded px-[4px] text-Secondary-Color"
                      elementType="div"
                    >
                      {`Resend in ${countDown}s`}
                    </Text>
                  ) : (
                    <Text
                      type="subBody"
                      variant="medium"
                      className="cursor-pointer rounded px-[4px] text-Primary-Color hover:bg-Hover-2"
                      elementType="div"
                      onClick={() => {
                        if (!resend) resendEmail()
                      }}
                    >
                      {resend ? 'Resending...' : 'Resend'}
                    </Text>
                  )}
                </div>
              </div>
            </div>
          </div>
          <Button
            onClick={() => {
              handleContinue()
            }}
            loading={loading}
            text={'Continue'}
            type="primary"
            disabled={
              !name?.trim() ||
              !password ||
              !otp ||
              !validatePassword(password).isValid ||
              !isValidOTP(otp)
            }
            className="w-full"
          />
        </div>

        <Text
          type="supportText"
          variant="medium"
          className="text-Secondary-Color"
        >
          By creating an account, you agree to our Terms of Services and Privacy
          Policy
        </Text>
      </div>
    </div>
  )
}

export default CreateProfile
