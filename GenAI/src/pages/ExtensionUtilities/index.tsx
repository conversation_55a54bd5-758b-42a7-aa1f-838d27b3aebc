import Icon from '@/assets/icon/Icon'
import Layout from '@/components/Layout'
import PageHeader from '@/components/PageHeader'
import Text from '@/components/Text'
import { UC_PATH } from '@/constants'
import { colors } from '@/theme'
import { memo, useMemo } from 'react'
import ApplicationItem from './ApplicationItem'
import IconDocumentCitation from './IconDocumentCitation'
import IconLinkedin from './IconLinkedin'
import IconOutlook from './IconOutlook'

function ExtensionUtilities() {
  const listApplication = useMemo(() => {
    return [
      {
        thumbnail: (
          <div className="flex size-[52px] items-center justify-center rounded-full bg-sky-100 p-3">
            <IconOutlook />
          </div>
        ),
        title: 'Outlook Email Assistant',
        description:
          'An extension application designed to streamline and enhance your email writing process.',
        footer: (
          <div className="flex w-full items-center justify-end gap-1">
            <div className="flex w-[18px] items-center justify-center rounded-full bg-teal-100 p-0.5">
              <Icon name="Extension_add" size={14} color={colors.teal[400]} />
            </div>
            <Text type="supportText">Extension</Text>
          </div>
        ),
      },
      {
        thumbnail: (
          <div className="flex size-[52px] items-center justify-center rounded-full bg-[#E6F1F8] p-3">
            <IconLinkedin />
          </div>
        ),
        title: 'Linkedin Content Creator',
        description:
          'Provides personalized writing suggestions and optimizes your posts for greater engagement',
        footer: (
          <div className="flex w-full items-center justify-end gap-1">
            <div className="flex w-[18px] items-center justify-center rounded-full bg-teal-100 p-0.5">
              <Icon name="Extension_add" size={14} color={colors.teal[400]} />
            </div>
            <Text type="supportText">Extension</Text>
          </div>
        ),
      },
      {
        thumbnail: (
          <div className="flex size-[52px] items-center justify-center rounded-full bg-indigo-100 p-3">
            <IconDocumentCitation />
          </div>
        ),
        title: 'Universal Chat',
        description:
          'Get quick, accurate answers with relevant references to enhance your research efficiency',
        footer: (
          <div className="flex w-full items-center justify-end gap-1">
            <div className="flex items-center justify-center rounded-full bg-pink-100 p-0.5">
              <Icon name="layout-alt-01" size={14} color={colors.pink[300]} />
            </div>
            <Text type="supportText">Web application</Text>
          </div>
        ),
        onClick: () => window.open(UC_PATH, '_blank'),
      },
    ]
  }, [])

  return (
    <Layout>
      <div className="flex w-full flex-col items-start gap-5">
        <PageHeader
          breadcrumbPaths={[
            {
              name: 'Applications',
            },
          ]}
          title={''}
        />

        <div className="mx-1 flex w-full gap-3">
          {listApplication?.map((item) => (
            <ApplicationItem {...item} key={item.title} />
          ))}
        </div>
      </div>
    </Layout>
  )
}

export default memo(ExtensionUtilities)
