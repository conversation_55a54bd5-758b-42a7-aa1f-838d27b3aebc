import Text from '@/components/Text'
import { memo } from 'react'

interface IProps {
  thumbnail: JSX.Element
  title: string
  description: string
  footer: JSX.Element
  onClick?: () => void
}

const ApplicationItem = ({
  thumbnail,
  title,
  description,
  footer,
  onClick,
}: IProps) => {
  return (
    <div
      className="flex w-[320px] cursor-pointer flex-col gap-1 rounded-xl bg-white px-3 pb-2 pt-3 shadow-base"
      onClick={onClick}
    >
      {thumbnail}
      <Text variant="medium">{title}</Text>
      <Text type="subBody" className="text-Secondary-Color">
        {description}
      </Text>
      {footer}
    </div>
  )
}

export default memo(ApplicationItem)
