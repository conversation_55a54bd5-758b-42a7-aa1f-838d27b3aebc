import { loginVerifyUsername } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import Input from '@/components/Input'
import Text from '@/components/Text'
import { GEN_AI_PATH } from '@/constants'
import { useAuth } from '@/hooks/useAuth'
import { useMyProfile } from '@/hooks/useMyProfile'
import { rootUrls } from '@/routes/rootUrls'
import { CodeResponse, useGoogleLogin } from '@react-oauth/google'
import { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const { Email } = Input

const ERROR_MESSAGE = 'Incorrect email or password'
const ERROR_DISPLAY = 'Incorrect password'
const Login = () => {
  const navigate = useNavigate()
  const { login, loginGoogleSSO } = useAuth()
  const { fetchMyProfile, fetchMySubscriptionPlan } = useMyProfile()

  const [loading, setLoading] = useState<boolean>(false)
  const [isEmailVerified, setIsEmailVerified] = useState(false)

  const [validateEmail, setValidateEmail] = useState(false)
  const [errorTextEmail, setErrorTextEmail] = useState<any>('')
  const [errorTextPassword, setErrorTextPassword] = useState<any>('')

  const [email, setEmail] = useState<string>('')
  const [password, setPassword] = useState<string>('')

  const handleLoginGoogleSSO = useCallback(async (google_code: string) => {
    try {
      const response = await loginGoogleSSO({
        google_code,
      })

      if (response?.data?.access_token) {
        setTimeout(() => {
          fetchMyProfile()
          fetchMySubscriptionPlan()
        }, 100)
      }
    } catch (error) {
      console.error('error', error)
    }
  }, [])

  const onSignInSuccess = useCallback(
    (res: Pick<CodeResponse, 'code'>) => {
      handleLoginGoogleSSO(res.code)
    },
    [history]
  )

  const onSignInFailure = useCallback((res: Pick<CodeResponse, 'error'>) => {
    console.log('Google login failed: ', res)
  }, [])

  const onGooglesignIn = useGoogleLogin({
    onSuccess: onSignInSuccess,
    onError: onSignInFailure,
    flow: 'auth-code',
    redirect_uri: `${GEN_AI_PATH}/login`,
    scope:
      'email profile openid https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
  })
  const fakeLoadingContinue = useCallback((action: any) => {
    setLoading(true)
    setTimeout(() => {
      action?.()
      setLoading(false)
    }, 300)
  }, [])

  const handleBlurEmail = useCallback(() => {
    setEmail(email?.trim())
  }, [email])

  const handleValidateEmail = useCallback((value: boolean) => {
    setValidateEmail(value)
  }, [])

  const handleChangeEmail = useCallback(
    (event: any) => {
      setEmail(event.target.value)

      if (isEmailVerified) {
        setIsEmailVerified(false)
        setPassword('')
        setErrorTextPassword('')
      }
      // Remove error text of email when changing email
      setErrorTextEmail('')
    },
    [isEmailVerified, validateEmail]
  )

  const handleChangePassword = useCallback((event: any) => {
    setPassword(event.target.value)

    // Remove error text of password when changing password
    setErrorTextPassword('')
  }, [])

  const handleVerifyEmail = useCallback(async () => {
    if (!validateEmail || !email) {
      fakeLoadingContinue(() => {
        if (!email) {
          setErrorTextEmail('Please enter email')
        }
      })

      return
    }

    try {
      setLoading(true)
      const response: any = await loginVerifyUsername({
        query: {
          username: email,
        },
      })
      setLoading(false)

      if (response?.data?.data) {
        setIsEmailVerified(true)
        setErrorTextEmail('')
      } else {
        setErrorTextEmail('Email doesn’t exist')
      }
    } catch (error) {
      console.error('error', error)
      setLoading(false)
      setErrorTextEmail('Email doesn’t exist')
    }
  }, [email, validateEmail])

  const handleLogin = useCallback(async () => {
    if (!validateEmail || !email || !password || password?.length < 8) {
      fakeLoadingContinue(() => {
        if (!email) {
          setErrorTextEmail('Please enter email')
        }
        if (!password) {
          setErrorTextPassword('Please enter password')
        } else if (password?.length < 8) {
          setErrorTextPassword('Invalid password')
        }
      })

      return
    }

    try {
      setLoading(true)
      const response = await login({
        email,
        password,
      })
      setLoading(false)

      if (response?.data?.access_token) {
        setErrorTextEmail('')
        setErrorTextPassword('')
        setTimeout(() => {
          fetchMyProfile()
          fetchMySubscriptionPlan()
        }, 100)
      } else {
        if (response?.error?.detail === ERROR_MESSAGE) {
          setErrorTextPassword(ERROR_DISPLAY)
        } else {
          setErrorTextPassword(
            'Your account is currently inactive. Please contact Admin for support'
          )
        }
      }
    } catch (error) {
      console.error('error', error)
      setLoading(false)
      setErrorTextPassword('Incorrect password')
    }
  }, [validateEmail, email, password])

  const handleContinue = useCallback(async () => {
    if (isEmailVerified) {
      handleLogin()
    } else {
      handleVerifyEmail()
    }
  }, [isEmailVerified, handleLogin, handleVerifyEmail])

  const handlePressContinue = useCallback(() => {
    handleBlurEmail()
    handleContinue()
  }, [handleBlurEmail, handleContinue])

  const handleCWGG = useCallback(async () => {
    onGooglesignIn()
  }, [])

  return (
    <div
      className="flex h-full w-full justify-start"
      style={{
        backgroundImage: 'url(/icons/background-login.svg)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'right',
      }}
    >
      <div className="flex h-full w-[521px] flex-col items-center justify-start gap-[36px] bg-white px-[44px] pt-[144px]">
        <div className="flex w-[300px] flex-col gap-[20px]">
          <div className="flex justify-center">
            <img src="/assets/images/genai-logo.svg" width={80.56} />
          </div>
          <div className="flex flex-col items-center">
            <Text
              type="title"
              variant="semibold"
              className="text-Primary-Color"
            >
              Welcome back
            </Text>
            <Text type="subheading" className="text-Secondary-Color">
              Login or register with your email
            </Text>
          </div>
        </div>
        <div className="flex w-[300px] flex-col gap-[24px]">
          <div className="flex flex-col gap-[16px]">
            <Button
              id="login-btn-continue-w-google"
              type="secondary"
              text="Continue with Google"
              leftIcon={<Icon name="Google" size={20} />}
              onClick={handleCWGG}
            />
            <div className="flex w-full items-center justify-center gap-[8px]">
              <div className="flex h-[1px] w-full bg-Base-Neutral" />
              <Text type="subBody" className="text-Secondary-Color">
                or
              </Text>
              <div className="flex h-[1px] w-full bg-Base-Neutral" />
            </div>
            <Email
              id="login-input-email"
              placeholder="Enter email address"
              value={email}
              maxLength={255}
              errorId="login-input-email-error-inline-mess"
              errorText={errorTextEmail}
              isError={errorTextEmail}
              onBlur={handleBlurEmail}
              onChange={handleChangeEmail}
              onPressEnter={handlePressContinue}
              onValidate={handleValidateEmail}
              showErrorText={!!errorTextEmail}
            />
            {isEmailVerified && (
              <div className="flex flex-col gap-[8px]">
                <Input
                  id="login-input-password"
                  placeholder="Enter password"
                  value={password}
                  minLength={8}
                  maxLength={255}
                  errorId="login-input-password-error-inline-mess"
                  errorText={errorTextPassword}
                  isError={!!errorTextPassword}
                  onChange={handleChangePassword}
                  onPressEnter={handlePressContinue}
                  secure
                />
                <Text
                  id="login-btn-forgot"
                  type="supportText"
                  variant="medium"
                  className="text-gradient-hover cursor-pointer px-[4px] text-Primary-Color"
                  onClick={() =>
                    navigate(rootUrls.ResetPassword, {
                      state: { email: email },
                    })
                  }
                >
                  Forgot password?
                </Text>
              </div>
            )}
          </div>
          <div className="flex flex-col gap-[8px]">
            <Button
              id="login-btn-continue"
              type="primary"
              text="Continue"
              loading={loading}
              onClick={handleContinue}
            />

            <div className="flex gap-[0.1em]">
              <Text type="helperText" className="text-Secondary-Color">
                By continuing, you agree to AI Agency’s
              </Text>
              <Text
                type="helperText"
                className="text-Secondary-Color underline"
              >
                Terms of Service
              </Text>
              <Text type="helperText" className="text-Secondary-Color">
                and
              </Text>
              <Text
                type="helperText"
                className="text-Secondary-Color underline"
              >
                Privacy Policy.
              </Text>
            </div>
          </div>

          <div className="flex w-full items-center justify-center gap-[0.1em]">
            <Text type="subBody" className="text-Secondary-Color">
              Haven’t got an account?
            </Text>
            <Text
              type="subBody"
              variant="medium"
              className="text-gradient-hover cursor-pointer text-Primary-Color"
              elementType="div"
              onClick={() => navigate(rootUrls.Register, { replace: true })}
            >
              Register
            </Text>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
