import { ModelsPublic, modelsGetListModelApi } from '@/apis/client'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import React, { PropsWithChildren, useEffect, useState } from 'react'

interface IModelsContext {
  models: ModelsPublic
}

export const ModelsContext = React.createContext<IModelsContext>({
  models: [],
})

export const ModelsProvider = ({ children }: PropsWithChildren) => {
  const [models, setModels] = useState<ModelsPublic>([])

  const fetchModels = async () => {
    try {
      const res = await modelsGetListModelApi({
        query: {
          page_number: 1,
          page_size: PAGE_SIZE.FULL,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setModels(res.data?.data?.data || [])
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  useEffect(() => {
    fetchModels()
  }, [])

  return (
    <ModelsContext.Provider value={{ models }}>
      {children}
    </ModelsContext.Provider>
  )
}
