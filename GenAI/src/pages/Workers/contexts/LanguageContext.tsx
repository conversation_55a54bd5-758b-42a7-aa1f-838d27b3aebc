import { WorkerLanguagesPublic, workersGetListLanguageApi } from '@/apis/client'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import React, { PropsWithChildren, useEffect, useState } from 'react'

interface ILanguageContext {
  languages: WorkerLanguagesPublic
}

export const LanguageContext = React.createContext<ILanguageContext>({
  languages: [],
})

export const LanguageProvider = ({ children }: PropsWithChildren) => {
  const [languages, setLanguages] = useState<WorkerLanguagesPublic>([])

  const fetchLanguages = async () => {
    try {
      const res = await workersGetListLanguageApi({
        query: {
          page_number: 1,
          page_size: PAGE_SIZE.FULL,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setLanguages(res.data?.data?.data || [])
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  useEffect(() => {
    fetchLanguages()
  }, [])

  return (
    <LanguageContext.Provider value={{ languages }}>
      {children}
    </LanguageContext.Provider>
  )
}
