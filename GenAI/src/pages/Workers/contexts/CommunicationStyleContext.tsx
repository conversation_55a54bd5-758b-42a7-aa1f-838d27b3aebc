import {
  WorkerCommunicationStylesPublic,
  workersGetListCommunicationStyleApi,
} from '@/apis/client'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import React, { PropsWithChildren, useEffect, useState } from 'react'

interface ICommunicationStyleContext {
  communicationStyles: WorkerCommunicationStylesPublic
}

export const CommunicationStyleContext =
  React.createContext<ICommunicationStyleContext>({ communicationStyles: [] })

export const CommunicationStyleProvider = ({ children }: PropsWithChildren) => {
  const [communicationStyles, setCommunicationStyles] =
    useState<WorkerCommunicationStylesPublic>([])

  const fetchListCommunicationStyle = async () => {
    try {
      const res = await workersGetListCommunicationStyleApi({
        query: {
          page_number: 1,
          page_size: PAGE_SIZE.FULL,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setCommunicationStyles(res.data?.data?.data || [])
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  useEffect(() => {
    fetchListCommunicationStyle()
  }, [])

  return (
    <CommunicationStyleContext.Provider value={{ communicationStyles }}>
      {children}
    </CommunicationStyleContext.Provider>
  )
}
