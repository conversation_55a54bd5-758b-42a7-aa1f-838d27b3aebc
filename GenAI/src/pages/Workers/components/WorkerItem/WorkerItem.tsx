import { memo, useCallback, useContext, useMemo, useState } from 'react'

import { WorkerLanguagePublic, WorkerPublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import { MessageDialog } from '@/components/DialogMessage'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { CustomAI } from '@/pages/Workers/assets/icons'
import { LanguageContext } from '@/pages/Workers/contexts'
import clsx from 'clsx'
import { WorkerActions, WorkerType, workerActionItems } from '../../types'
import styles from './styles.module.scss'

export interface WorkerActionProps {
  workerId?: string
}

const WorkerItem = ({
  workerId,
  name,
  background,
  avatar,
  language,
  onClick,
  onDeleteWorker,
  isShowAction = true,
  worker_type = 'AI Worker',
  onPlayground,
  onDuplicateWorker,
}: Partial<WorkerPublic> & {
  onClick: () => void
  onDeleteWorker?: ({ workerId }: WorkerActionProps) => void
  onDuplicateWorker?: ({ workerId }: WorkerActionProps) => void
  workerId: string
  isShowAction?: boolean
  onPlayground: () => void
}) => {
  const { languages } = useContext(LanguageContext)
  const [openWorkerAction, setOpenWorkerAction] = useState(false)

  const languageFlag = useMemo(() => {
    if (!language) return ''
    return (
      languages.find((lang: WorkerLanguagePublic) => lang.name === language)
        ?.national_flag ?? ''
    )
  }, [languages, language])

  const handleDeleteWorker = useCallback(() => {
    MessageDialog.warning({
      mainMessage: 'Delete worker?',
      subMessage:
        'If continue, please note that all workflow steps associated with this worker will be impacted. You will need to reconfigure them.',
      onClick: () => onDeleteWorker?.({ workerId }),
    })
  }, [workerId, onDeleteWorker])

  const handleSelectWorkerAction = useCallback(
    ({ key }: SelectedItemProps) => {
      switch (key) {
        case WorkerActions.PLAYGROUND:
          onPlayground()
          break
        case WorkerActions.DELETE:
          handleDeleteWorker()
          break
        case WorkerActions.DUPLICATE:
          onDuplicateWorker?.({ workerId })
          break
      }
    },
    [handleDeleteWorker, onDuplicateWorker, workerId]
  )

  return (
    <div className={styles['worker-item']} onClick={onClick}>
      {isShowAction && (
        <div className="absolute right-2 top-3 flex flex-row-reverse gap-1">
          <Dropdown
            // overlayClassName="w-[113px]"
            open={openWorkerAction}
            items={workerActionItems}
            onSelect={handleSelectWorkerAction}
            onOpenChange={(newOpen) => {
              setOpenWorkerAction(newOpen)
            }}
          >
            <More active={openWorkerAction} />
          </Dropdown>
        </div>
      )}
      <Avatar
        name={name}
        avatarUrl={avatar!}
        size="large"
        avatarDefault={
          <div className="flex size-[42px] items-center justify-center rounded-full bg-Background-Color">
            <Icon
              name={
                worker_type === WorkerType.HUMAN
                  ? 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
                  : 'face-id-square'
              }
              size={28}
              gradient={['#642B734D', '#C6426E4D']}
            />
          </div>
        }
      />
      <Text
        variant="medium"
        className="my-1 w-full text-center text-Primary-Color"
        value={name}
        elementType="div"
        ellipsis
      />
      <Text
        className={clsx(styles['worker-description'], 'h-9')}
        value={background}
        type="subBody"
        elementType="div"
        ellipsis
        multipleLine={2}
      />
      <div className="mt-2 flex w-full items-center justify-between gap-1">
        <div className="flex items-center justify-center gap-1">
          <CustomAI size={20} />
          <Text
            value={worker_type}
            type="subBody"
            className="text-Primary-Color"
            ellipsis
            elementType="div"
          />
        </div>

        {language && (
          <div className="flex items-center justify-center gap-1">
            <Avatar avatarUrl={languageFlag} className="!size-4" />
            <Text
              value={language}
              type="subBody"
              variant="medium"
              className="text-Primary-Color"
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default memo(WorkerItem)
