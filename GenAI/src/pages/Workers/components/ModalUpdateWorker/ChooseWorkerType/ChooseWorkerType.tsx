import Icon from '@/assets/icon/Icon'
import { CustomAI } from '@/pages/Workers/assets/icons'
import { StepCreateWorker, WorkerType } from '@/pages/Workers/types'
import { memo } from 'react'
import WorkerTypeItem from './WorkerTypeItem'

interface IProps {
  onClickWorkerType: (step: StepCreateWorker) => void
  setWorkerStore: (workerType: WorkerType) => void
}

const WorkerTypes = [
  {
    icon: <CustomAI size={44} />,
    name: 'AI Worker',
    description:
      'Worker could understand language, learn from data, and take actions to achieve goals automatically',
    value: WorkerType.AI,
  },
  {
    icon: (
      <Icon
        name="vuesax-bold-user"
        size={44}
        gradient={['#642B73', '#C6426E']}
      />
    ),
    name: 'Human Worker',
    description: 'Description for the use of human worker in the workflow',
    value: WorkerType.HUMAN,
    disabled: true,
  },
]

const ChooseWorkerType = ({ onClickWorkerType, setWorkerStore }: IProps) => {
  return (
    <div className="flex w-full items-center justify-center px-3 pb-6 pt-0">
      <div className="flex h-[315px] w-[664px] items-center justify-center gap-11 rounded-xl border border-neutral-200 bg-white">
        {WorkerTypes.map((type) => (
          <WorkerTypeItem
            key={type.value}
            {...type}
            onClick={() => {
              onClickWorkerType(StepCreateWorker.UPDATE)
              setWorkerStore(type.value)
            }}
          />
        ))}
      </div>
    </div>
  )
}

export default memo(ChooseWorkerType)
