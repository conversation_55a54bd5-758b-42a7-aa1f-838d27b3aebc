import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import Text from '@/components/Text'
import { colors } from '@/theme'
import { memo } from 'react'

interface IProps {
  hasBack?: boolean
  back?: () => void
  isOpenPlayground?: boolean
  hasPlayground?: boolean
  togglePlayground?: () => void
  onSave?: () => void
  isValid?: boolean
}

const Footer = ({
  hasBack = true,
  back = () => {},
  hasPlayground = false,
  isOpenPlayground = false,
  togglePlayground = () => {},
  onSave,
  isValid = true,
}: IProps) => {
  return (
    <div className="mb-4 mt-3 flex items-center justify-between px-5">
      {hasBack ? (
        <div
          className="flex cursor-pointer items-center justify-center gap-1"
          onClick={back}
        >
          <Icon
            name="vuesax-outline-arrow-left"
            size={20}
            color={colors['Primary-Color']}
          />
          <Text value="Back" type="subBody" className="text-Primary-Color" />
        </div>
      ) : (
        <div />
      )}

      <div className="flex items-center justify-center gap-3">
        {hasPlayground ? (
          <Button onClick={togglePlayground} type="secondary">
            {isOpenPlayground ? 'Exit playground' : 'Playground'}
          </Button>
        ) : (
          <div />
        )}
        <Button onClick={onSave!} disabled={!isValid} type="primary">
          Save
        </Button>
      </div>
    </div>
  )
}

export default memo(Footer)
