import { useState } from 'react'
import { copyToClipboard } from '@/helpers'
import Tooltip from '@/components/Tooltip'
import CopyIcon from '../BotMessage/CopyIcon'

interface CopiedButtonProps {
  message: string
}

const CopiedButton = ({ message }: CopiedButtonProps) => {
  const [copied, setCopied] = useState(false)

  return (
    <div>
      <Tooltip text={copied ? 'Copied!' : 'Copy'} position="top">
        <button
          className="group mt-[4px] block text-Secondary-Color hover:text-Primary-Color"
          onClick={() => {
            setCopied(true)
            copyToClipboard(message)
            setTimeout(() => {
              setCopied(false)
            }, 1000)
          }}
        >
          <CopyIcon />
        </button>
      </Tooltip>
    </div>
  )
}

export default CopiedButton
