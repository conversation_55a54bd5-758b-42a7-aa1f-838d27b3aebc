import { DATE_TIME_FORMAT } from '@/helpers/dateTime'
import moment from 'moment'

export const MAX_LENGTH = 10000
// eslint-disable-next-line quotes
export const LETS_START_MESSAGE = `let's start`
export const LETS_START_RESPONSE = 'I’m ready to assist you!'

export enum EMessageType {
  user = 'user_message',
  worker = 'worker_message',
  noTokenMessage = 'no_token_message',
  end = 'end_message',
  loading = 'loading',
  workflowProgressMessage = 'workflow_progress_message',
  fileUploadMessage = 'file_upload_message',
  fakeMessageFileProcessStatus = 'fake_message_file_process_status',
  fileUrlCitation = 'file_url_message',
  citationMessage = 'citation_message',
  perplexityMessage = 'perplexity_message',
}

export interface IMessage {
  id?: string
  text?: string
  type: EMessageType
  name?: string
  time: string
  loading?: boolean
  hideCopy?: boolean
  worker?: {
    workerName: string
    workerAvatar?: string | null
  }
  // file with intent
  files?: {
    id: string
    name: string
    size: number
    type: string
    error?: string
  }[]
  intent?: string

  citation?: {
    chunks: Array<{
      chunk_id: string
      content: string
      file_name: string
      file_type: string
      file_url: string
    }>
    chunks_to_page: {
      [key: string]: number
    }
  }

  speakerId?: string
  related_question?: boolean

  file_url?: {
    file_url: string
    file_size: number
    file_name: string
    file_type: string
  }

  citations?: string[] // Perplexity
}

export const getTimeChat = () => {
  return moment().locale('en').format(DATE_TIME_FORMAT.HMMADDMMM)
}

export const trimText = (text: string) => {
  while (text[0] === ' ') {
    text = text.slice(1)
  }
  while (text[text.length - 1] === ' ') {
    text = text.slice(0, -1)
  }
  return text
}

export const VALID_DATA_TYPES_FILE_UPLOAD = [
  'application/pdf',
  'text/plain',
  'text/csv',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/png',
  'image/jpeg',
  'image/jpg',
]

export const getErrorValidateFile = (size: number, type: string) => {
  if (!VALID_DATA_TYPES_FILE_UPLOAD?.includes(type)) {
    return 'File type is not supported'
  }
  if (size > 10485760) return 'Maximum size 10MB'
  return ''
}

export const getFileName = (disposition: any) => {
  let filename = ''

  if (disposition && disposition.indexOf('attachment') !== -1) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(disposition)

    if (matches !== null && matches[1]) {
      filename = matches[1].replace(/['"]/g, '')
    }
  }
  return filename
}

export const downloadFile = (blob: any, filename: string) => {
  const link = document.createElement('a')

  link.href = URL.createObjectURL(blob)
  link.download = filename
  link.click()
}

export const downloadFiles = async (fileUrl: string, fileName: string) => {
  const response: Response = await fetch(fileUrl)

  const blob = await response.blob()
  downloadFile(blob, fileName)
}
