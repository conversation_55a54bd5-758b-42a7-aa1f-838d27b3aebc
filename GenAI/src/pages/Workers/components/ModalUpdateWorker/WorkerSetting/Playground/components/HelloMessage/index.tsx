import Text from '@/components/Text'
import { memo, useMemo } from 'react'
import { twMerge } from 'tailwind-merge'
import WavingIcon from './WavingIcon'

const HelloMessage = ({
  communicationStyle,
  language,
  name,
  personality,
  helloMessage,
  className,
}: IHelloMessageProps) => {
  const text = useMemo(() => {
    if (communicationStyle && language) {
      return `I am ${personality}, I could speak ${language} and I could assist you to handle task or communicate with you in ${communicationStyle}`
    }
    if (!communicationStyle && !language) {
      return `I am ${personality}`
    }
    if (!communicationStyle) {
      return `I am ${personality}, I could speak ${language} and I could assist you to handle task`
    }
    return `I am ${personality}, I could assist you to handle task or communicate with you in ${communicationStyle}`
  }, [communicationStyle, language, personality])

  return (
    <div
      className={twMerge(
        'mt-[16px] flex items-start justify-start gap-[8px]',
        className
      )}
    >
      <WavingIcon />

      {helloMessage ? (
        <Text
          variant="regular"
          type="supportText"
          className="text-Secondary-Color"
        >
          {helloMessage}
        </Text>
      ) : (
        <div className="flex flex-col">
          <Text
            variant="regular"
            type="supportText"
            className="text-Secondary-Color"
          >
            Hi, I am {name}
            <br />
            {text}
          </Text>
          <Text
            variant="regular"
            type="supportText"
            elementType="div"
            className="mt-[4px] text-Secondary-Color"
          >
            Just ask me anything, tell me that you're ready by sending: “Let's
            start”
          </Text>
        </div>
      )}
    </div>
  )
}

export default memo(HelloMessage)

interface IHelloMessageProps {
  personality: string
  language?: string
  communicationStyle?: string
  name: string
  helloMessage?: string
  className?: string
}
