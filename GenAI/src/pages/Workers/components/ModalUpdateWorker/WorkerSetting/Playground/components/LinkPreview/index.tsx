import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { LoadImage } from '@/pages/knowledgeBase/constants'
import { getLinkPreview } from 'link-preview-js'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import './style.scss'

const initialMetaData = {
  url: '',
  images: [],
  title: '',
  description: '',
}

interface LinkPreviewProps {
  rawUrl: string
}

const LinkPreview = ({ rawUrl }: LinkPreviewProps) => {
  const [metaData, setMetaData] = useState<any>(initialMetaData)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )

  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const getMeteData = useCallback(async (urlValue: string) => {
    try {
      const response = await getLinkPreview(urlValue)
      setMetaData(response)
    } catch (error) {
      console.log('error', error)
    }
  }, [])

  useEffect(() => {
    getMeteData(rawUrl)
  }, [rawUrl])

  const getImagePreview = () => {
    if (loadImageState !== LoadImage.error && !isEmpty(metaData.images?.[0])) {
      return (
        <div className="flex h-[114px] items-center justify-center">
          <img
            src={metaData.images?.[0]}
            alt="img"
            className="h-full"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
      )
    }

    return (
      <div className="flex h-[114px] items-center justify-center">
        <div className="flex flex-col items-center justify-center gap-[7px]">
          <Icon name="vuesax-bold-image" size={80} color="#E5E5E5" />
          <Text
            className="text-neutral-300"
            type="supportText"
            variant="medium"
          >
            Preview is not available
          </Text>
        </div>
      </div>
    )
  }

  return (
    <div className="flex w-full max-w-[263px] flex-col gap-[7px] py-1">
      {getImagePreview()}
      <div className="flex flex-col text-start">
        <Link to={rawUrl} target="_blank">
          <Text
            type="supportText"
            variant="medium"
            className="overflow-hidden break-words text-Primary-Color"
            elementType="div"
            multipleLine={2}
            hasTooltip={false}
            ellipsis
          >
            {metaData.title}
          </Text>
        </Link>

        <Text
          type="supportText"
          className="overflow-hidden break-words text-Secondary-Color"
          elementType="div"
          multipleLine={3}
          hasTooltip={false}
          ellipsis
        >
          {metaData.description}
        </Text>
      </div>
    </div>
  )
}

export default LinkPreview
