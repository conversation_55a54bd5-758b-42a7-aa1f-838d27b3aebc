import { ModelPublic, ModelsPublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { cn, getUrlImage } from '@/helpers'
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react'
import clsx from 'clsx'
import { groupBy } from 'lodash'
import { useEffect, useMemo, useState } from 'react'
import { ModalUpdateWorkerStore } from '../..'

interface Props {
  models: ModelsPublic
  workerStore: ModalUpdateWorkerStore | undefined
  onClickModel: (model: ModelPublic) => void
}

const SelectAI = ({ models, workerStore, onClickModel }: Props) => {
  const [selected, setSelected] = useState<ModelPublic>()

  useEffect(() => {
    if (workerStore) {
      // find the selected model
      const selectedModel = models.find(
        (model) => model.id === workerStore.llm_model_id
      )
      if (selectedModel) {
        setSelected(selectedModel)
      }
    }
  }, [workerStore])

  const transformModalByProvider = useMemo(() => {
    return groupBy(models, 'provider')
  }, [])

  return (
    <Listbox value={selected} onChange={setSelected}>
      <ListboxButton className="group flex h-[68px] w-full items-center justify-center gap-2 rounded-xl bg-white p-[12px] shadow-base focus-visible:outline-none">
        <div className="flex flex-1 items-center justify-between gap-2 overflow-hidden">
          <div className="flex gap-2 overflow-hidden">
            <div className="padding-[2px] flex size-[44px] min-w-[44px] items-center justify-center overflow-hidden rounded-[8px] border-[0.5px] border-solid border-border-base-icon">
              <img
                className="object-contain"
                src={getUrlImage(selected?.image_url)}
              />
            </div>
            <div className="flex flex-col gap-1 overflow-hidden">
              <Text
                variant="medium"
                type="body"
                className="text-left text-Primary-Color"
                value={selected?.display_name}
                elementType="div"
                ellipsis
              />
              <Text
                variant="regular"
                type="subBody"
                className="text-left text-Secondary-Color"
                value={selected?.provider}
                elementType="div"
                ellipsis
              />
            </div>
          </div>

          <div
            className={cn('h-[22px] rounded-full px-2 py-[2px]', {
              'bg-teal-100': selected?.hosting_mode === 'Cloud-Based',
              'bg-orange-100': selected?.hosting_mode === 'Self-Hosted',
            })}
          >
            <Text
              variant="regular"
              type="subBody"
              className={cn('text-center text-teal-500', {
                'text-orange-500': selected?.hosting_mode === 'Self-Hosted',
              })}
              value={selected?.hosting_mode}
              elementType="div"
              ellipsis
            />
          </div>
        </div>

        <div className="rotate-180 transform transition-transform duration-100 ease-out group-data-[open]:rotate-0">
          <Icon name="chevron-up" size={20} />
        </div>
      </ListboxButton>

      <ListboxOptions
        anchor="bottom"
        transition
        className={cn(
          'z-[99] w-[var(--button-width)] origin-top-right rounded-[12px] border border-border-base-icon bg-white p-[12px] shadow-base transition duration-100 ease-out [--anchor-gap:5px] focus:outline-none data-[closed]:opacity-0',
          'genai-scrollbar !max-h-[320px] overflow-y-auto'
        )}
      >
        <div className="flex w-full flex-col gap-2">
          {Object.entries(transformModalByProvider).map(
            ([provider, models]) => (
              <div key={provider}>
                <Text
                  variant="medium"
                  type="subBody"
                  className="text-left text-Secondary-Color"
                  value={provider}
                  elementType="div"
                  ellipsis
                />
                {models.map((model) => {
                  const isDisabled = [
                    'Deepseek-R1',
                    'Qwen-2.5-14b',
                    'Gemini 2.0 Flash',
                  ]?.includes(model.display_name!)
                  return (
                    <ListboxOption
                      disabled={isDisabled}
                      key={model.id}
                      value={model}
                      className={clsx('rounded-md', isDisabled && 'opacity-65')}
                      onClick={() => {
                        onClickModel(model)
                      }}
                    >
                      <div className="flex flex-1 cursor-pointer items-center justify-between gap-2 overflow-hidden rounded-[8px] p-[4px] hover:bg-Hover-Color">
                        <div className="flex gap-2 overflow-hidden">
                          <div className="padding-[2px] flex size-[36px] min-w-[36px] items-center justify-center overflow-hidden rounded-[8px] border-[0.5px] border-solid border-border-base-icon">
                            <img
                              className="object-contain"
                              src={getUrlImage(model.image_url)}
                            />
                          </div>
                          <div className="flex flex-col justify-center gap-1 overflow-hidden">
                            <Text
                              variant="medium"
                              type="body"
                              className="text-center text-Primary-Color"
                              value={model.display_name}
                              elementType="div"
                              ellipsis
                            />
                          </div>
                        </div>
                        <div
                          className={cn(
                            'h-[22px] rounded-full bg-teal-100 px-2 py-[2px]',
                            {
                              'bg-orange-100':
                                model.hosting_mode === 'Self-Hosted',
                            }
                          )}
                        >
                          <Text
                            variant="regular"
                            type="subBody"
                            className={cn('text-center text-teal-500', {
                              'text-orange-500':
                                model.hosting_mode === 'Self-Hosted',
                            })}
                            value={model.hosting_mode}
                            elementType="div"
                            ellipsis
                          />
                        </div>
                      </div>
                    </ListboxOption>
                  )
                })}
              </div>
            )
          )}
        </div>
      </ListboxOptions>
    </Listbox>
  )
}

export default SelectAI
