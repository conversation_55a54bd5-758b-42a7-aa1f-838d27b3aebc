import {
  WorkerCommunicationStylePublic,
  WorkerCommunicationStylesPublic,
} from '@/apis/client'
import Input from '@/components/Input'
import TabUpload from '@/components/TabUpload'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { WORKER_AVATARS } from '@/constants'
import { ModalUpdateWorkerStore } from '@/pages/Workers/components/ModalUpdateWorker'
import {
  CommunicationStyleContext,
  LanguageContext,
} from '@/pages/Workers/contexts'
import { isEmpty } from 'lodash'
import { nanoid } from 'nanoid'
import { memo, useCallback, useContext, useEffect, useState } from 'react'
import AddNewCommunicationStyleButton from './AddNewCommunicationStyleButton'
import CommunicationStyle from './CommunicationStyle'
import Language from './Language'

const MAX_STYLE_SELECTED = 3

interface IProps {
  workerStore?: ModalUpdateWorkerStore
  setWorkerStore?: (data: Partial<ModalUpdateWorkerStore>) => void
  isCreatingByAI?: boolean
  setDirtyData: (data: boolean) => void
}

type TypeSelectedStyle = string | number

const GeneralSettings = ({
  workerStore,
  setWorkerStore,
  isCreatingByAI = false,
  setDirtyData,
}: IProps) => {
  const { communicationStyles } = useContext(CommunicationStyleContext)
  const { languages } = useContext(LanguageContext)
  const [customCommunicationStyles, setCustomCommunicationStyles] =
    useState<WorkerCommunicationStylesPublic>([])
  const [selectedCommunicationStyle, setSelectedCommunicationStyle] = useState<
    TypeSelectedStyle[]
  >([])

  useEffect(() => {
    // Reset custom style
    if (isCreatingByAI) {
      setCustomCommunicationStyles([])
      setSelectedCommunicationStyle([])
    }
  }, [isCreatingByAI])

  useEffect(() => {
    // Init custom communication styles when received data from server
    if (!customCommunicationStyles?.length && !isCreatingByAI) {
      const selectedStyle: TypeSelectedStyle[] = []
      const customStyle: WorkerCommunicationStylesPublic = []

      for (let i = 0; i < MAX_STYLE_SELECTED; i++) {
        const style = workerStore?.[i === 0 ? 'style' : `style_${i + 1}`]
        const styleDescription =
          workerStore?.[
            i === 0 ? 'style_description' : `style_description_${i + 1}`
          ]
        if (style) {
          const communicationStyleSetting = communicationStyles?.find(
            (communicationStyle) =>
              communicationStyle.name === style &&
              communicationStyle.description === styleDescription
          )

          // Not a default communication style
          if (isEmpty(communicationStyleSetting)) {
            const id = nanoid() as unknown as number
            customStyle.push({
              id,
              name: style || '',
              description: styleDescription || '',
            })

            selectedStyle.push(id)
          } else {
            if (!selectedStyle.includes(communicationStyleSetting?.id)) {
              selectedStyle.push(communicationStyleSetting?.id)
            } else {
              selectedStyle.push('')
            }
          }
        } else {
          selectedStyle.push('')
        }
      }

      setCustomCommunicationStyles(customStyle)
      setSelectedCommunicationStyle(selectedStyle)
    }
  }, [
    customCommunicationStyles?.length,
    communicationStyles,
    workerStore?.style,
    workerStore?.style_description,
    workerStore?.style_2,
    workerStore?.style_description_2,
    workerStore?.style_3,
    workerStore?.style_description_3,
    isCreatingByAI,
  ])

  const createCustomCommunicationStyle = useCallback(
    (communicationStyle: WorkerCommunicationStylePublic) => {
      setCustomCommunicationStyles((prev) => [communicationStyle, ...prev])
    },
    [customCommunicationStyles]
  )

  const deleteCustomCommunicationStyle = useCallback(
    (itemId: number, index: number) => {
      const isActive = index !== -1

      // If delete selected communication style, remove style in store
      if (isActive) {
        const styleFieldName = index === 0 ? 'style' : `style_${index + 1}`
        const styleDescriptionFieldName =
          index === 0 ? 'style_description' : `style_description_${index + 1}`

        setWorkerStore?.({
          [styleFieldName]: null,
          [styleDescriptionFieldName]: null,
        })
        selectedCommunicationStyle[index] = ''

        setDirtyData(true)

        setSelectedCommunicationStyle(
          JSON.parse(JSON.stringify(selectedCommunicationStyle))
        )
      }
      setCustomCommunicationStyles((prev) => {
        return prev.filter((item) => item.id !== itemId)
      })
    },
    [customCommunicationStyles, selectedCommunicationStyle]
  )

  const onChangeDescription = (id: string, description: string) => {
    const newCustomCommunicationStyles = customCommunicationStyles?.map(
      (item: WorkerCommunicationStylePublic) => {
        if (item.id === (id as unknown as number)) {
          return {
            ...item,
            description,
          }
        }
        return item
      }
    )

    setCustomCommunicationStyles(newCustomCommunicationStyles)
  }

  const handleSelectStyle = (
    style: WorkerCommunicationStylePublic,
    index: number
  ) => {
    const isActive = index !== -1

    if (!isActive) {
      const availableIndex = selectedCommunicationStyle.findIndex(
        (item) => item === ''
      )

      if (availableIndex === -1) return

      selectedCommunicationStyle[availableIndex] = style.id

      setSelectedCommunicationStyle(
        JSON.parse(JSON.stringify(selectedCommunicationStyle))
      )

      const styleFieldName =
        availableIndex === 0 ? 'style' : `style_${availableIndex + 1}`
      const styleDescriptionFieldName =
        availableIndex === 0
          ? 'style_description'
          : `style_description_${availableIndex + 1}`

      setWorkerStore?.({
        [styleFieldName]: style.name,
        [styleDescriptionFieldName]: style.description,
      })
      setDirtyData(true)
    } else {
      selectedCommunicationStyle[index] = ''

      setSelectedCommunicationStyle(
        JSON.parse(JSON.stringify(selectedCommunicationStyle))
      )

      const styleFieldName = index === 0 ? 'style' : `style_${index + 1}`
      const styleDescriptionFieldName =
        index === 0 ? 'style_description' : `style_description_${index + 1}`

      setWorkerStore?.({
        [styleFieldName]: null,
        [styleDescriptionFieldName]: null,
      })
      setDirtyData(true)
    }
  }

  return (
    <div className="flex w-full flex-col gap-3">
      <div className="flex items-end gap-3 px-2">
        <TabUpload
          image={workerStore?.avatar ?? ''}
          changeFile={(file: File | null) => {
            setWorkerStore?.({
              avatar_file: file,
            })
            setDirtyData(true)
          }}
          imageCollection={WORKER_AVATARS}
          onChange={(
            avatar: string | undefined,
            type?: 'Collection' | 'Custom'
          ) => {
            const params: Partial<ModalUpdateWorkerStore> = { avatar }
            if (type === 'Collection') {
              params['avatar_file'] = null
            }

            setWorkerStore?.(params)
            setDirtyData(true)
          }}
        />
        <Input
          label="Worker name"
          placeholder="Type in name for your worker"
          isFullWidth
          value={workerStore?.name}
          onChange={(e) => {
            setWorkerStore?.({
              name: e.target.value,
            })
            setDirtyData(true)
          }}
          onBlur={() =>
            setWorkerStore?.({
              name: workerStore?.name?.trim(),
            })
          }
          maxLength={30}
        />
      </div>
      <TextArea
        maxLength={512}
        placeholder="Type in the background of worker, what his role is and what he could do"
        label="Background"
        className="h-[100px]"
        value={workerStore?.background}
        onChange={(background: string | undefined) => {
          setWorkerStore?.({
            background,
          })
          setDirtyData(true)
        }}
        onBlur={() =>
          setWorkerStore?.({
            background: workerStore?.background?.trim(),
          })
        }
      />
      <TextArea
        maxLength={255}
        placeholder="Describes how your worker tends to think, feel, and behave on an ongoing basis"
        label="Personality Traits"
        className="h-[100px]"
        value={workerStore?.traits}
        onChange={(traits: string | undefined) => {
          setWorkerStore?.({
            traits,
          })
          setDirtyData(true)
        }}
        onBlur={() =>
          setWorkerStore?.({
            traits: workerStore?.traits?.trim(),
          })
        }
      />
      <div className="flex flex-col gap-1">
        <Text
          className="pl-1 text-Tertiary-Color"
          variant="medium"
          type="subBody"
        >
          Communication Styles
        </Text>
        <div className="inline-flex w-full flex-wrap gap-3">
          <AddNewCommunicationStyleButton
            createCustomCommunicationStyle={createCustomCommunicationStyle}
          />
          {customCommunicationStyles?.map((customCommunicationStyle) => {
            const index = selectedCommunicationStyle.findIndex(
              (item) => item === customCommunicationStyle.id
            )

            const isActive = index !== -1

            const styleDescriptionFieldName =
              index === 0
                ? 'style_description'
                : `style_description_${index + 1}`

            return (
              <CommunicationStyle
                key={customCommunicationStyle.id}
                id={customCommunicationStyle.id}
                name={customCommunicationStyle.name}
                description={customCommunicationStyle.description!}
                isCustom
                deleteCustomCommunicationStyle={(itemId) =>
                  deleteCustomCommunicationStyle(itemId, index)
                }
                onChooseCommunicationStyle={() => {
                  handleSelectStyle(customCommunicationStyle, index)
                }}
                onChangeDescription={(description?: string) => {
                  if (isActive) {
                    setWorkerStore?.({
                      [styleDescriptionFieldName]: description,
                    })
                    setDirtyData(true)
                  }
                  onChangeDescription(
                    customCommunicationStyle?.id as unknown as string,
                    description!
                  )
                }}
                isActive={isActive}
              />
            )
          })}
          {communicationStyles?.map((communicationStyle) => {
            const index = selectedCommunicationStyle.findIndex(
              (item) => item === communicationStyle.id
            )

            const isActive = index !== -1

            return (
              <CommunicationStyle
                key={communicationStyle.id}
                id={communicationStyle.id}
                name={communicationStyle.name}
                description={communicationStyle.description!}
                onChooseCommunicationStyle={() => {
                  handleSelectStyle(communicationStyle, index)
                }}
                isActive={isActive}
              />
            )
          })}
        </div>
      </div>

      <div className="flex flex-col gap-1">
        <Text
          className="pl-1 text-Tertiary-Color"
          variant="medium"
          type="subBody"
        >
          Language
        </Text>
        <div className="inline-flex w-full flex-wrap gap-3">
          {languages?.map((language) => {
            const isActive = workerStore?.language === language.name
            return (
              <Language
                key={language.id}
                name={language.name}
                national_flag={language.national_flag}
                onChooseLanguage={() => {
                  setWorkerStore?.({
                    language: isActive ? undefined : language.name,
                  })
                  setDirtyData(true)
                }}
                isActive={isActive}
              />
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default memo(GeneralSettings)
