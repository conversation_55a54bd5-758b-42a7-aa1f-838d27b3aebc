import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import TextArea from '@/components/TextArea'
import { ModalUpdateWorkerStore } from '@/pages/Workers/components/ModalUpdateWorker'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useState } from 'react'
import OutsideClickHandler from 'react-outside-click-handler'
interface IProps {
  isCreatingByAI?: boolean
  createWorkerByAI: () => Promise<boolean>
  setWorkerStore?: (data: Partial<ModalUpdateWorkerStore>) => void
  prompt?: string
}

const CreateWorkerWithAIBtn = ({
  isCreatingByAI = false,
  createWorkerByAI,
  setWorkerStore,
  prompt,
}: IProps) => {
  const [isOpenPromptPopup, setOpenPromptPopup] = useState(false)

  const handleCreateWorker = async () => {
    // if (await createWorkerByAI()) {
    //   setOpenPromptPopup(false)
    // }
    await createWorkerByAI()
  }

  return (
    <OutsideClickHandler
      onOutsideClick={() => {
        setOpenPromptPopup(false)
      }}
    >
      <div className="relative">
        <div
          className={clsx(
            'cursor-pointer rounded-full p-1 shadow-lg hover:bg-Background-Color',
            { 'bg-Main-Color': isOpenPromptPopup }
          )}
          onClick={() => setOpenPromptPopup(!isOpenPromptPopup)}
        >
          <Icon
            name="Bold-EssentionalUI-MagicStick3"
            gradient={isOpenPromptPopup ? undefined : ['#4D175B', '#A32952']}
            size={24}
            className="rotate-90"
            color={colors.white}
          />
        </div>
        {isOpenPromptPopup && (
          <div className="absolute bottom-[34px] right-0 flex flex-col gap-2 rounded-2xl bg-white px-3 pb-2 pt-3 shadow-md">
            <TextArea
              onChange={(prompt) => {
                setWorkerStore?.({
                  ai_gen_prompt: prompt,
                })
              }}
              isAutofocus
              maxLength={1000}
              className="h-[185px] w-[282px]"
              placeholder="Describe your worker here..."
              onBlur={() => {
                setWorkerStore?.({
                  ai_gen_prompt: prompt?.trim(),
                })
              }}
              value={prompt}
            />
            <div className="flex w-full justify-end">
              <Button
                onClick={handleCreateWorker}
                text="Generate"
                loading={isCreatingByAI}
                type="secondary"
                size="small"
                disabled={isCreatingByAI || !prompt?.trim()}
              />
            </div>
          </div>
        )}
      </div>
    </OutsideClickHandler>
  )
}

export default memo(CreateWorkerWithAIBtn)
