import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { ModalUpdateWorkerStore } from '@/pages/Workers/components/ModalUpdateWorker'
import { ModelsContext } from '@/pages/Workers/contexts'
import { colors } from '@/theme'
import { memo, useContext } from 'react'
import SelectAI from './SelectAI'

interface IProps {
  workerStore?: ModalUpdateWorkerStore
  setWorkerStore?: (data: Partial<ModalUpdateWorkerStore>) => void
  setChangePrompt?: (data: boolean) => void
  setDirtyData: (data: boolean) => void
}

const AdvancedSettings = ({
  workerStore,
  setWorkerStore,
  setChangePrompt,
  setDirtyData,
}: IProps) => {
  const { models } = useContext(ModelsContext)

  const renderAIFoundation = () => {
    return (
      <div className="mb-2 flex w-full flex-col gap-1">
        <Text
          variant="medium"
          className="px-1 text-Tertiary-Color"
          type="subBody"
        >
          AI Foundation
        </Text>

        <div className="flex w-full flex-wrap p-1">
          <SelectAI
            models={models}
            workerStore={workerStore}
            onClickModel={(model) => {
              setWorkerStore?.({
                llm_model_id: model.id,
              })
              setDirtyData(true)
            }}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full w-full flex-col px-2">
      {renderAIFoundation()}
      <TextArea
        placeholder="Type in the prompt for this worker"
        label="Prompting"
        className="mb-2 h-[296px]"
        onChange={(system_message: string) => {
          setWorkerStore?.({ system_message })
          setChangePrompt?.(true)
          setDirtyData(true)
        }}
        value={workerStore?.system_message}
        maxLength={2500}
      />
      <div className="flex gap-3">
        <Icon
          name="Bold-EssentionalUI-InfoCircle"
          size={20}
          color={colors['Primary-Color']}
          className="min-w-5"
        />
        <div className="flex flex-col text-supportText text-Primary-Color">
          <Text
            variant="medium"
            type="supportText"
            elementType="div"
            className="mb-1"
          >
            Prompting will be automatically generated as the following format:
          </Text>
          <div className="mb-1 whitespace-pre-wrap text-supportText italic">
            <Text type="supportText">Your name is </Text>
            <Text type="supportText" variant="medium">
              Worker Name,{' '}
            </Text>
            <Text type="supportText">
              you're an employee designed to assist users by leveraging your
              background:
            </Text>
            <br />
            <Text type="supportText">*********Background*********</Text>
            <br />
            <Text type="supportText" variant="medium">
              Background
            </Text>
            <br />
            <Text type="supportText">***************************</Text>
            <br />
            <Text type="supportText">You exhibit in ways of </Text>
            <Text type="supportText" variant="medium">
              Personality,{' '}
            </Text>
            <Text type="supportText">
              which help you interact effectively with users. You are capable of
              performing and resolving task by using your special abilities and
              communicating in a{' '}
            </Text>
            <Text type="supportText" variant="medium">
              Communication Styles 1{' '}
            </Text>
            <Text type="supportText">and </Text>
            <Text type="supportText" variant="medium">
              Communication Styles 2{' '}
            </Text>
            <Text type="supportText">and </Text>
            <Text type="supportText" variant="medium">
              Communication Styles 3{' '}
            </Text>
            <Text type="supportText">
              manner, ensuring that interactions are{' '}
            </Text>
            <Text type="supportText" variant="medium">
              Style Description 1{' '}
            </Text>
            <Text type="supportText">and </Text>
            <Text type="supportText" variant="medium">
              Style Description 2{' '}
            </Text>
            <Text type="supportText">and </Text>
            <Text type="supportText" variant="medium">
              Style Description 3.{' '}
            </Text>
            <Text type="supportText">
              You can speak in other language if user asked you to, otherwise,
              you should answer in{' '}
            </Text>
            <Text type="supportText" variant="medium">
              Language,{' '}
            </Text>
            <Text type="supportText">
              you can effectively assist users who speak this language.
            </Text>
          </div>
          <div className="text-supportText">
            <Text type="supportText" variant="medium">
              Notes:{' '}
            </Text>
            <Text type="supportText">
              You totally could customize this. In case you make changes on
              worker profile, the prompting will be re-generated.
            </Text>
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(AdvancedSettings)
