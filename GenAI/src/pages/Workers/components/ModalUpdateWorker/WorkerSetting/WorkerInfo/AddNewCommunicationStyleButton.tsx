import { WorkerCommunicationStylePublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useState } from 'react'
import CreateCommunicationChip from './CreateCommunicationChip'

const AddNewCommunicationStyleButton = ({
  createCustomCommunicationStyle,
}: {
  createCustomCommunicationStyle: (
    communicationStyle: WorkerCommunicationStylePublic
  ) => void
}) => {
  const [isHover, setIsHover] = useState(false)

  const [isTyping, setTyping] = useState(false)

  if (isTyping) {
    return (
      <CreateCommunicationChip
        createCustomCommunicationStyle={createCustomCommunicationStyle}
        endTyping={() => setTyping(false)}
      />
    )
  }

  return (
    <div
      className="flex h-[22px] w-24 cursor-pointer items-center justify-center gap-1 rounded-full bg-Main-Disable-2 py-0.5 pl-2 pr-3 hover:bg-Main-03"
      onClick={() => {
        setTyping(true)
      }}
      onMouseOver={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      <Icon
        name="vuesax-outline-add"
        size={20}
        color={colors[isHover ? 'Primary-Color' : 'Secondary-Color']}
      />
      <Text
        value="Add new"
        type="subBody"
        className={clsx(
          isHover ? 'text-Primary-Color' : 'text-Secondary-Color'
        )}
      />
    </div>
  )
}

export default memo(AddNewCommunicationStyleButton)
