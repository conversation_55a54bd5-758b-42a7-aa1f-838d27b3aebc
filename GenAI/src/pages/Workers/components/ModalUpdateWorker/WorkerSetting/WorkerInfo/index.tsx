import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Spin from '@/components/Spin'
import Tabs from '@/components/Tabs'
import Text from '@/components/Text'
import Tooltip from '@/components/Tooltip'
import { TABS } from '@/constants'
import { ModalUpdateWorkerStore } from '@/pages/Workers/components/ModalUpdateWorker'
import AdvancedSettings from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/WorkerInfo/AdvancedSettings'
import clsx from 'clsx'
import { memo, useEffect, useState } from 'react'
import CreateWorkerWithAIBtn from './CreateWorkerWithAIBtn'
import GeneralSettings from './GeneralSettings'

interface IProps {
  workerStore?: ModalUpdateWorkerStore
  setWorkerStore?: (data: Partial<ModalUpdateWorkerStore>) => void
  isOpenPlayground?: boolean
  setChangePrompt?: (data: boolean) => void
  isCreatingByAI?: boolean
  createWorkerByAI: () => Promise<boolean>
  setDirtyData: (data: boolean) => void
}

const WorkerInfo = ({
  workerStore,
  setWorkerStore,
  isOpenPlayground = false,
  setChangePrompt,
  isCreatingByAI = false,
  createWorkerByAI,
  setDirtyData,
}: IProps) => {
  const [currentTab, setCurrentTab] = useState<TABS>(TABS.GENERAL)

  const [isCollapse, setCollapse] = useState(false)

  useEffect(() => {
    if (!isOpenPlayground && isCollapse) {
      setCollapse(false)
    }
  }, [isOpenPlayground, isCollapse])

  const renderContent = () => {
    return (
      <div className="genai-scrollbar h-[531px] w-full overflow-auto p-4">
        {currentTab === TABS.GENERAL ? (
          <GeneralSettings
            workerStore={workerStore}
            setWorkerStore={setWorkerStore}
            isCreatingByAI={isCreatingByAI}
            setDirtyData={setDirtyData}
          />
        ) : (
          <AdvancedSettings
            workerStore={workerStore}
            setWorkerStore={setWorkerStore}
            setChangePrompt={setChangePrompt}
            setDirtyData={setDirtyData}
          />
        )}
      </div>
    )
  }

  if (isCollapse) {
    return (
      <div className="relative flex h-full w-[37px] items-center justify-center rounded-lg bg-Main-05">
        <Text
          variant="medium"
          type="subheading"
          className="rotate-[-90deg] whitespace-nowrap text-Primary-Color"
        >
          Worker profile
        </Text>
        <IconButton
          nameIcon="Custom-expand"
          sizeIcon={24}
          className="absolute bottom-2 right-2 hover:bg-Hover-2"
          onClick={() => setCollapse(false)}
        />
      </div>
    )
  }

  return (
    <div
      className={clsx(
        'relative flex h-full flex-col overflow-hidden rounded-xl bg-white',
        isOpenPlayground ? 'w-[688px] min-w-[688px]' : 'w-full'
      )}
    >
      <div className="relative flex w-full items-center">
        <Tabs
          tabs={[
            {
              value: TABS.GENERAL,
              label: TABS.GENERAL,
            },
          ]}
          value={currentTab}
          onChange={setCurrentTab}
        />
        <Tooltip text="Configure advanced options">
          <div
            className="absolute right-4 flex cursor-pointer items-center justify-center gap-1"
            onClick={() =>
              setCurrentTab(
                currentTab === TABS.ADVANCED ? TABS.GENERAL : TABS.ADVANCED
              )
            }
          >
            <Icon
              name="vuesax-bold-setting-2"
              size={20}
              gradient={
                currentTab === TABS.ADVANCED
                  ? ['#642B73', '#C6426E']
                  : ['#DE8389', '#B488BF']
              }
            />
            <Text
              value={TABS.ADVANCED}
              variant="medium"
              type="subBody"
              className={clsx(
                currentTab === TABS.ADVANCED
                  ? 'text-Primary-Color'
                  : 'text-Secondary-Color'
              )}
            />
          </div>
        </Tooltip>
      </div>

      {renderContent()}

      {isOpenPlayground && (
        <IconButton
          nameIcon="Custom-expand"
          sizeIcon={24}
          className="absolute bottom-2 right-2 z-10 hover:bg-Hover-2"
          onClick={() => setCollapse(true)}
        />
      )}

      {/* Create with AI  */}
      {currentTab === TABS.GENERAL && (
        <div
          className={clsx(
            'absolute',
            isOpenPlayground
              ? 'bottom-[40px] right-2'
              : 'bottom-[14px] right-[14px]'
          )}
        >
          <CreateWorkerWithAIBtn
            isCreatingByAI={isCreatingByAI}
            createWorkerByAI={createWorkerByAI}
            setWorkerStore={setWorkerStore}
            prompt={workerStore?.ai_gen_prompt ?? ''}
          />
        </div>
      )}

      {isCreatingByAI && (
        <div className="absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center">
          <Spin size="larger" />
        </div>
      )}
    </div>
  )
}

export default memo(WorkerInfo)
