import Text from '@/components/Text'
import clsx from 'clsx'
import { memo, useState } from 'react'

import IconButton from '@/components/IconButton'
import Popover from '@/components/Popover'
import TextArea from '@/components/TextArea'
import Tooltip from '@/components/Tooltip'
import { colors } from '@/theme'

const CommunicationStyle = ({
  id,
  name,
  isActive = false,
  isCustom = false,
  description = '',
  deleteCustomCommunicationStyle,
  onChooseCommunicationStyle,
  onChangeDescription,
}: {
  id: number
  name: string
  isActive?: boolean
  isCustom?: boolean
  description?: string
  deleteCustomCommunicationStyle?: (itemId: number) => void
  onChooseCommunicationStyle: () => void
  onChangeDescription?: (value: string) => void
}) => {
  const [isOpenPopoverDescription, setOpenPopoverDescription] = useState(false)

  if (isCustom) {
    return (
      <div
        className={clsx(
          'flex h-[22px] cursor-pointer items-center justify-center gap-1 rounded-full px-2 py-0.5 hover:bg-Background-Color',
          isActive
            ? 'border-gradient-Main-Color-full !bg-Main-03'
            : 'bg-Base-01'
        )}
        onClick={onChooseCommunicationStyle}
      >
        <Text type="subBody" className="text-Primary-Color">
          {name}
        </Text>

        <Popover
          isPure
          overlayClassName=" z-20 genai-scrollbar h-[101px] w-[344px]"
          open={isOpenPopoverDescription}
          content={
            <TextArea
              className="h-[80px] w-full"
              label="Description"
              value={description}
              placeholder="Define particularly the style and tone your worker would use in a conversation"
              onChange={onChangeDescription!}
              onBlur={() => onChangeDescription?.(description?.trim())}
            />
          }
          onOpenChange={(open) => setOpenPopoverDescription(open)}
        >
          <IconButton
            nameIcon="icon-fill-caret-small-left"
            sizeIcon={20}
            activeColor={colors['Primary-Color']}
            colorIcon={colors.neutral[300]}
            hoverColor={colors['Primary-Color']}
            onClick={() => {
              setOpenPopoverDescription((prev) => !prev)
            }}
            active={isOpenPopoverDescription}
            className={clsx(
              isOpenPopoverDescription ? 'rotate-90' : 'rotate-[-90deg]'
            )}
          />
        </Popover>
        <IconButton
          nameIcon="Customize-Close"
          sizeIcon={12}
          activeColor={colors['Primary-Color']}
          colorIcon={colors.neutral[300]}
          hoverColor={colors['Primary-Color']}
          onClick={() => deleteCustomCommunicationStyle?.(id)}
        />
      </div>
    )
  }

  return (
    <Tooltip theme="light" text={description} trigger={'hover'}>
      <div
        className={clsx(
          'flex h-[22px] cursor-pointer items-center justify-center gap-1 rounded-full px-2 py-0.5 hover:bg-Background-Color',
          isActive
            ? 'border-gradient-Main-Color-full !bg-Main-03'
            : 'bg-Base-01'
        )}
        onClick={onChooseCommunicationStyle}
      >
        <Text type="subBody" className="text-Primary-Color">
          {name}
        </Text>
      </div>
    </Tooltip>
  )
}

export default memo(CommunicationStyle)
