import {
  Body_workers_create_worker_api,
  workers<PERSON>reate<PERSON>orker<PERSON><PERSON>,
  workersGenerateWorkerApi,
  workersUpdateWorkerApi,
} from '@/apis/client'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE } from '@/constants'
import { ModalUpdateWorkerStore } from '@/pages/Workers/components/ModalUpdateWorker'
import clsx from 'clsx'
import { isEqual, omit, unionWith } from 'lodash'
import { memo, useMemo, useRef, useState } from 'react'
import { DEFAULT_LANGUAGE } from '../constant'
import Footer from './Footer'
import Playground from './Playground'
import WorkerInfo from './WorkerInfo'
import { cn } from '@/helpers'

interface IProps {
  workerStore: ModalUpdateWorkerStore
  setWorkerStore: (data: Partial<ModalUpdateWorkerStore>) => void
  isDirtyData: boolean
  setDirtyData: (data: boolean) => void
  onClickBack: () => void
  setDirtyModal: (data: boolean) => void
  hasBack: boolean
  onComplete?: () => void
}

const WorkerSetting = ({
  workerStore,
  setWorkerStore,
  isDirtyData,
  setDirtyData,
  onClickBack,
  setDirtyModal,
  hasBack,
  onComplete,
}: IProps) => {
  const [isOpenPlayground, setOpenPlayground] = useState(false)
  const [isLoading, setLoading] = useState(false)

  const [isChangePrompt, setChangePrompt] = useState(false)

  const [isCreatingByAI, setCreatingByAI] = useState(false)

  const createWorkerByAI = async () => {
    try {
      if (isCreatingByAI) return false

      setCreatingByAI(true)
      setChangePrompt?.(false)

      const res = await workersGenerateWorkerApi({
        body: {
          prompt: workerStore?.ai_gen_prompt ?? '',
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setWorkerStore?.(
          omit({ ...data?.data, avatar_file: null }, ['id', 'llm_model_id']) ||
            {}
        )
        setDirtyData(true)
        return true
      } else {
        Message.error({ message: 'Something went wrong!' })

        return false
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })

      return false
    } finally {
      setCreatingByAI(false)
    }
  }

  const workerRef = useRef<ModalUpdateWorkerStore>(workerStore)

  const handleCallApiWorker = async () => {
    try {
      if (isLoading || !isDirtyData) return

      setLoading(true)

      const service = workerStore?.id
        ? workersUpdateWorkerApi
        : workersCreateWorkerApi

      const params: {
        body: Body_workers_create_worker_api
        path: { worker_id: string }
      } = {
        body: {
          worker_type: workerStore.worker_type,
          name: workerStore.name,
          background: workerStore.background?.trim(),
          traits: workerStore.traits?.trim(),

          avatar_file: workerStore.avatar_file,
          avatar: workerStore.avatar_file ? '' : workerStore.avatar || '',
          language: workerStore.language || DEFAULT_LANGUAGE,
          system_message: isChangePrompt
            ? workerStore.system_message?.trim()
            : '', // If user don't change prompt, system auto generate prompt
          llm_model_id: workerStore.llm_model_id,

          style: workerStore.style,
          style_description: workerStore.style_description?.trim(),
          style_2: workerStore.style_2,
          style_description_2: workerStore.style_description_2?.trim(),
          style_3: workerStore.style_3,
          style_description_3: workerStore.style_description_3?.trim(),

          ai_gen_prompt: workerStore.ai_gen_prompt?.trim(),
        },
        path: {
          worker_id: workerStore.id!,
        },
      }

      const res = await service(params)

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({
          message: workerStore.id
            ? 'Successfully updated worker, run it now!'
            : 'Successfully created worker, run it now!',
        })
        setDirtyData(false)
        setChangePrompt(false)
        // Set modal dirty to refetch data when close modal
        setDirtyModal(true)
        if (!workerStore.id) {
          setWorkerStore({
            id: res.data?.data.id,
            system_message: res.data?.data.system_message,
            avatar_file: null,
            language: res.data?.data.language,
          })
        } else {
          setWorkerStore({
            system_message: res.data?.data.system_message,
            avatar_file: null,
            language: res.data?.data.language,
          })
        }

        onComplete?.()
        workerRef.current = res.data?.data as unknown as ModalUpdateWorkerStore
      } else {
        if (res.status === HTTP_STATUS_CODE.BAD_REQUEST)
          Message.error({ message: res.error!.detail ?? '' })
        else Message.error({ message: 'Something wrong, please retry!' })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const isValid = useMemo(
    () =>
      Boolean(
        workerStore.name?.trim() &&
          workerStore.background?.trim() &&
          workerStore.traits?.trim()
      ),
    [workerStore.name, workerStore.background, workerStore.traits]
  )

  const hasPlayground = useMemo(() => !!workerStore.id, [workerStore.id])

  const communicationStyleText = useMemo(() => {
    if (
      !workerRef.current?.style &&
      !workerRef.current?.style_2 &&
      !workerRef.current?.style_3
    )
      return ''

    const communicationStyles = unionWith(
      [
        {
          style: workerRef.current?.style,
          styleDescription: workerRef.current?.style_description,
        },
        {
          style: workerRef.current?.style_2,
          styleDescription: workerRef.current?.style_description_2,
        },
        {
          style: workerRef.current?.style_3,
          styleDescription: workerRef.current?.style_description_3,
        },
      ],
      isEqual
    )
      .filter((item) => item.style)
      ?.map((item) => item.style)

    return communicationStyles.join(', ')
  }, [
    workerRef.current?.style,
    workerRef.current?.style_description,

    workerRef.current?.style_2,
    workerRef.current?.style_description_2,

    workerRef.current?.style_3,
    workerRef.current?.style_description_3,
  ])

  return (
    <>
      <div
        className={cn(
          'flex h-[70dvh] max-h-[568px] w-[1200px] overflow-hidden px-3',
          {
            'gap-2': isOpenPlayground,
          }
        )}
      >
        <WorkerInfo
          setWorkerStore={(data: Partial<ModalUpdateWorkerStore>) => {
            setWorkerStore(data)
          }}
          workerStore={workerStore}
          isOpenPlayground={isOpenPlayground}
          setChangePrompt={setChangePrompt}
          isCreatingByAI={isCreatingByAI}
          createWorkerByAI={createWorkerByAI}
          setDirtyData={setDirtyData}
        />
        {hasPlayground && (
          <div
            className={clsx(
              isOpenPlayground ? 'flex w-[1px] flex-1' : 'w-0 overflow-hidden'
            )}
          >
            <Playground
              name={workerRef.current?.name ?? ''}
              avatar={workerRef.current?.avatar ?? ''}
              playgroundId={isLoading ? '' : (workerStore?.id ?? '')}
              workerType={workerRef.current?.worker_type ?? ''}
              personality={workerRef.current?.traits ?? ''}
              communicationStyle={communicationStyleText}
              language={workerRef.current?.language ?? ''}
            />
          </div>
        )}
      </div>
      <Footer
        hasBack={hasBack}
        onSave={handleCallApiWorker}
        hasPlayground={hasPlayground}
        isOpenPlayground={isOpenPlayground}
        togglePlayground={() => setOpenPlayground(!isOpenPlayground)}
        isValid={isValid && isDirtyData && !isCreatingByAI}
        back={onClickBack}
      />
    </>
  )
}

export default memo(WorkerSetting)
