import { Body_workers_create_worker_api, WorkerPublic } from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import { MessageDialog } from '@/components/DialogMessage'
import Text from '@/components/Text'
import WorkerSetting from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting'
import { StepCreateWorker, WorkerType } from '@/pages/Workers/types'
import { colors } from '@/theme'
import { isEmpty, random } from 'lodash'
import { memo, useCallback, useEffect, useReducer, useState } from 'react'
import ChooseWorkerType from './ChooseWorkerType'
import { DEFAULT_LANGUAGE, LLM_MODEL_ID, WORKER_NAME } from './constant'
import IconButton from '@/components/IconButton'

export type ModalUpdateWorkerStore = Body_workers_create_worker_api & {
  id?: string
  [key: string]: any
}

const initialState: ModalUpdateWorkerStore = {
  worker_type: '',
  name: '',
  background: '',
  traits: '',
  system_message: '',
  language: null,
  style: null,
  style_description: null,
  style_2: null,
  style_description_2: null,
  style_3: null,
  style_description_3: null,
  avatar: null,
  avatar_file: null,
  id: undefined,
  llm_model_id: undefined,
  ai_gen_prompt: '',
}

interface IModalUpdateWorkerProps {
  isOpen?: boolean
  onClose: () => void
  defaultStep?: StepCreateWorker
  workerType?: WorkerType
  worker?: WorkerPublic
  refreshData: () => void
  onComplete?: () => void
}

const ModalUpdateWorker = ({
  isOpen = false,
  onClose,
  defaultStep = StepCreateWorker.CHOOSE_TYPE,
  workerType,
  worker,
  refreshData,
  onComplete,
}: IModalUpdateWorkerProps) => {
  const [step, setStep] = useState<StepCreateWorker>(defaultStep)

  const [workerStore, setWorkerStore] = useReducer(
    (
      state: ModalUpdateWorkerStore,
      newState: Partial<ModalUpdateWorkerStore>
    ) => ({
      ...state,
      ...newState,
    }),
    initialState
  )

  const [isDirtyData, setDirtyData] = useState(false)

  const [isDirtyModal, setDirtyModal] = useState(false)

  const handleCloseModal = useCallback(() => {
    if (isDirtyData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          onClose()
          if (isDirtyModal) {
            refreshData()
          }
        },
      })
    } else {
      onClose()
      if (isDirtyModal) {
        refreshData()
      }
    }
  }, [isDirtyData, isDirtyModal])

  useEffect(() => {
    if (!isEmpty(worker)) {
      setWorkerStore({ ...worker })
      setStep(StepCreateWorker.UPDATE)
    } else {
      setWorkerStore({
        name: WORKER_NAME[random(0, WORKER_NAME.length - 1)],
        llm_model_id: LLM_MODEL_ID,
        language: DEFAULT_LANGUAGE,
      })
    }
  }, [worker])

  useEffect(() => {
    if (workerType) {
      setStep(StepCreateWorker.UPDATE)
      setWorkerStore({ worker_type: workerType })
    }
  }, [workerType])

  const renderContent = () => {
    if (step === StepCreateWorker.UPDATE)
      return (
        <WorkerSetting
          hasBack={!workerType}
          workerStore={workerStore}
          setWorkerStore={setWorkerStore}
          isDirtyData={isDirtyData}
          setDirtyData={setDirtyData}
          setDirtyModal={setDirtyModal}
          onClickBack={() => setStep(StepCreateWorker.CHOOSE_TYPE)}
          onComplete={onComplete}
        />
      )
    return (
      <ChooseWorkerType
        onClickWorkerType={setStep}
        setWorkerStore={(workerType: WorkerType) =>
          setWorkerStore({ worker_type: workerType })
        }
      />
    )
  }

  return (
    <BaseModal isPureModal isOpen={isOpen} onClose={handleCloseModal}>
      <div className="relative flex min-h-[413px] min-w-[712px] flex-col overflow-hidden rounded-[20px] bg-Base-03 shadow-md">
        <IconButton
          className="absolute right-[10px] top-[10px] cursor-pointer duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={handleCloseModal}
        />

        <div className="flex flex-col gap-1 bg-Base-03 px-3 pb-4 pt-3">
          <Text
            value={workerStore?.id ? 'Update Worker' : 'Create Worker'}
            type="subheading"
            variant="medium"
            className="text-Primary-Color"
          />
          <Text
            value={
              step === StepCreateWorker.CHOOSE_TYPE
                ? 'Choose type of worker you want to build'
                : 'The worker represents the employee in your business process. He can perform all assigned tasks using support tools or AI Power.'
            }
            type="subBody"
            className="text-Secondary-Color"
          />
        </div>

        {renderContent()}
      </div>
    </BaseModal>
  )
}

export default memo(ModalUpdateWorker)
