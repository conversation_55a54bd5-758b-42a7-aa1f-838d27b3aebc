export enum StepCreateWorker {
  CHOOSE_TYPE = 'CHOOSE_TYPE',
  UPDATE = 'UPDATE',
}

export enum WorkerType {
  AI = 'AI',
  HUMAN = 'HUMAN',
}

export enum WorkerActions {
  PLAYGROUND = 'playground',
  DELETE = 'delete',
  DUPLICATE = 'duplicate',
}

export const workerActionItems = [
  {
    key: WorkerActions.DUPLICATE,
    label: 'Duplicate',
  },
  {
    key: WorkerActions.PLAYGROUND,
    label: 'Playground',
  },
  {
    key: WorkerActions.DELETE,
    label: 'Delete',
  },
]
