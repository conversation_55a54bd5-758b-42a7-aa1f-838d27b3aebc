import {
  WorkerPublic,
  WorkersPublic,
  workersDeleteWorkerApi,
  workersReadWorkersApi,
  workersUpdateWorkerDuplicateApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import PageHeader from '@/components/PageHeader'
import Pagination from '@/components/Pagination'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import ModalUpdateWorker from '@/pages/Workers/components/ModalUpdateWorker'
import {
  ItemAddWorker,
  WorkerItem,
  WorkerItemSkeleton,
} from '@/pages/Workers/components/WorkerItem'
import {
  CommunicationStyleProvider,
  LanguageProvider,
  ModelsProvider,
} from '@/pages/Workers/contexts'

import ModalPlayground from '@/components/ModalPlayground'
import SearchBar from '@/components/SearchBar'
import { isEqual, unionWith } from 'lodash'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { WorkerActionProps } from './components/WorkerItem/WorkerItem'

const Workers = () => {
  const [searchKey, setSearchKey] = useState('')
  const [isLoading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [pageSize] = useState(PAGE_SIZE.LARGE)
  const [totalPage, setTotalPage] = useState<number>()
  const [workers, setWorkers] = useState<WorkersPublic>([])
  const [modalPlaygroundVisible, setModalPlaygroundVisible] = useState(false)
  const [playgroundInfo, setPlaygroundInfo] = useState<
    WorkerPublic | undefined
  >(undefined)

  const [isOpenModalUpdateWorker, setOpenModalUpdateWorker] =
    useState<boolean>(false)

  const [selectedWorker, setSelectedWorker] = useState<WorkerPublic>()

  const handleOpenModalUpdateWorker = useCallback((data?: any) => {
    setSelectedWorker(data)
    setOpenModalUpdateWorker(true)
  }, [])

  const fetchWorkers = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await workersReadWorkersApi({
        query: {
          page_number: page,
          page_size: pageSize,
          name: searchKey || undefined,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setWorkers(data?.data?.data ?? [])
        setTotalPage(data?.data?.total_pages)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteWorker = useCallback(
    async ({ workerId }: WorkerActionProps) => {
      if (!workerId) {
        return
      }

      const response = await workersDeleteWorkerApi({
        path: {
          worker_id: workerId,
        },
      })

      if (response.status === HTTP_STATUS_CODE.NO_CONTENT) {
        Message.success({ message: 'Successfully deleted worker' })

        if (workers?.length <= 1 && page > 1) {
          setPage(page - 1)
        } else {
          fetchWorkers()
        }
      } else if (response.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        Message.error({
          message: response?.error?.detail ?? 'Something went wrong!',
        })
      }
    },
    [workers, page, fetchWorkers]
  )

  const handleDuplicateWorker = async ({ workerId }: any) => {
    if (!workerId) return

    const res = await workersUpdateWorkerDuplicateApi({
      path: {
        worker_id: workerId,
      },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS) {
      Message.success({ message: 'Successfully duplicated worker' })
      fetchWorkers()
    } else if (res.status === HTTP_STATUS_CODE.NOT_FOUND) {
      Message.error({
        message: 'Something went wrong!',
      })
    } else {
      Message.error({
        message: res?.error?.detail ?? 'Something went wrong!',
      })
    }
  }

  const handlePlayground = useCallback(
    (worker: WorkerPublic) => {
      setModalPlaygroundVisible(true)
      setPlaygroundInfo(worker)
    },
    [setModalPlaygroundVisible, setPlaygroundInfo]
  )

  useEffect(() => {
    fetchWorkers()
  }, [searchKey, page])

  const renderContent = () => {
    if (isLoading)
      return (
        <div className="genai-scrollbar mt-3 h-full overflow-auto">
          <div className="flex flex-wrap gap-3 px-1 pb-2">
            {Array.from({ length: pageSize }).map((_, index) => (
              <WorkerItemSkeleton key={index} />
            ))}
          </div>
        </div>
      )

    if (!workers?.length) {
      if (searchKey) return <NoDataFound className="mt-[84px]" />
      return (
        <div className="mt-[100px] flex flex-col items-center">
          <EmptyData size="medium" type="01" className="mb-6" />

          <Button
            type="primary"
            size="medium"
            onClick={() => handleOpenModalUpdateWorker()}
            text="New Worker"
            leftIcon={
              <Icon name="vuesax-outline-add" color="#ffffff" size={20} />
            }
          />
        </div>
      )
    }

    return (
      <>
        <div className="genai-scrollbar mt-3 h-full overflow-auto">
          <div className="flex flex-wrap gap-3 px-1 pb-2">
            {!searchKey && (
              <ItemAddWorker
                addNewWorker={() => handleOpenModalUpdateWorker()}
              />
            )}
            {workers.map((worker: WorkerPublic) => (
              <WorkerItem
                key={worker.id}
                workerId={worker.id}
                name={worker.name}
                avatar={worker.avatar}
                background={worker.background}
                onClick={() => handleOpenModalUpdateWorker(worker)}
                onDeleteWorker={handleDeleteWorker}
                onDuplicateWorker={handleDuplicateWorker}
                language={worker.language}
                onPlayground={() => handlePlayground(worker)}
              />
            ))}
          </div>
        </div>
      </>
    )
  }

  const communicationStyleText = useMemo(() => {
    if (
      !playgroundInfo?.style &&
      !playgroundInfo?.style_2 &&
      !playgroundInfo?.style_3
    )
      return ''

    const communicationStyles = unionWith(
      [
        {
          style: playgroundInfo?.style,
          styleDescription: playgroundInfo?.style_description,
        },
        {
          style: playgroundInfo?.style_2,
          styleDescription: playgroundInfo?.style_description_2,
        },
        {
          style: playgroundInfo?.style_3,
          styleDescription: playgroundInfo?.style_description_3,
        },
      ],
      isEqual
    )
      .filter((item) => item.style)
      ?.map((item) => item.style)

    return communicationStyles.join(', ')
  }, [
    playgroundInfo?.style,
    playgroundInfo?.style_description,

    playgroundInfo?.style_2,
    playgroundInfo?.style_description_2,

    playgroundInfo?.style_3,
    playgroundInfo?.style_description_3,
  ])

  return (
    <LanguageProvider>
      <CommunicationStyleProvider>
        <ModelsProvider>
          <Layout>
            <div className="workers flex h-full flex-col">
              <div className="flex items-center justify-between gap-5">
                <PageHeader
                  breadcrumbPaths={[
                    {
                      name: 'Workers',
                    },
                  ]}
                  title={
                    'Build your worker to automate tasks, analyze data, manage customer interactions,\npersonalize experiences, monitor performance, and support human.'
                  }
                />
                <SearchBar
                  onSearch={(value) => {
                    setSearchKey(value)
                    setPage(1)
                  }}
                />
              </div>

              {renderContent()}
              {totalPage! > 1 && (
                <Pagination
                  page={page}
                  totalPage={totalPage}
                  onChangePage={setPage}
                  className="mt-3 flex w-full items-center justify-end"
                />
              )}
              {isOpenModalUpdateWorker && (
                <ModalUpdateWorker
                  isOpen={isOpenModalUpdateWorker}
                  onClose={() => {
                    setOpenModalUpdateWorker(false)
                    setSelectedWorker(undefined)
                  }}
                  worker={selectedWorker}
                  refreshData={fetchWorkers}
                />
              )}
              <ModalPlayground
                avatar={playgroundInfo?.avatar || ''}
                name={playgroundInfo?.name || ''}
                isOpen={modalPlaygroundVisible}
                onClose={() => setModalPlaygroundVisible(false)}
                playgroundId={playgroundInfo?.id || ''}
                workerType={playgroundInfo?.worker_type || ''}
                personality={playgroundInfo?.traits || ''}
                communicationStyle={communicationStyleText}
                language={playgroundInfo?.language || ''}
              />
            </div>
          </Layout>
        </ModelsProvider>
      </CommunicationStyleProvider>
    </LanguageProvider>
  )
}

export default memo(Workers)
