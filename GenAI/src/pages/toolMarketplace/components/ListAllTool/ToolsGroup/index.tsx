import {
  MarketToolPublic,
  MarketToolsPublic,
  toolsGetListToolByCategoryApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import { MessageDialog } from '@/components/DialogMessage'
import Text from '@/components/Text'
import { PAGE_SIZE } from '@/constants'
import { useBuyTool } from '@/pages/toolMarketplace/hooks/useBuyTool'
import ModalToolDetail from '@/pages/Tools/common/ModalToolDetail'
import useMarketToolsCategory from '@/store/marketTools'
import clsx from 'clsx'
import { useState } from 'react'
import { twMerge } from 'tailwind-merge'
import MarketplaceTool from '../../MarketplaceTool'
import LoadMoreButton from './LoadMoreButton'

interface Props {
  tools: MarketToolsPublic
  categoryId: string
}

export const ToolsGroup = ({ tools, categoryId }: Props) => {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const [isOpenModalToolDetail, setOpenModalToolDetail] = useState(false)

  const [selectedTool, setSelectedTool] = useState<MarketToolPublic>()

  const search = useMarketToolsCategory((state) => state.search)
  const addToolsByCategoryId = useMarketToolsCategory(
    (state) => state.addToolsByCategoryId
  )

  const [page, setPage] = useState(1)

  const isHaveMore = tools.length < (tools[0].sub_total ?? 0)
  const categoryName = tools[0].category_name ?? ''

  const toggleExpand = () => {
    setIsExpanded((prev) => !prev)
  }

  const handleSelectTool = (tool: MarketToolPublic) => {
    setSelectedTool(tool)
    setOpenModalToolDetail(true)
  }

  const onCloseModal = () => {
    setSelectedTool(undefined)
    setOpenModalToolDetail(false)
  }

  const handleLoadMoreItem = async () => {
    setIsLoading(true)

    const nextPage = page + 1
    setPage(nextPage)

    try {
      const { data } = await toolsGetListToolByCategoryApi({
        query: {
          name: search,
          category_id: categoryId,
          page_number: nextPage,
          page_size: PAGE_SIZE.SMALL,
          exclude_built_in: true,
        },
      })

      if (
        data &&
        data.data &&
        data.data.data &&
        Array.isArray(data.data.data)
      ) {
        addToolsByCategoryId(
          categoryId,
          data.data.data.map(
            (item) =>
              ({
                ...item,
                category_name: categoryName,
                sub_total: data.data.total_count,
                tool_id: item.tool_id,
                tool_name: item.tool_name,
                row_id: 10,
              }) as MarketToolPublic
          )
        )
      }
    } catch (error: any) {
      console.log('error', error)
    } finally {
      setIsLoading(false)
    }
  }

  const {
    isOpenModalBuyTool,
    isLoadingConfirm,
    handleBuyToolVerify,
    setOpenModalBuyTool,
    handleConfirmBuy,
  } = useBuyTool()

  return (
    <div className="flex flex-col gap-[12px]">
      <div className="flex h-[21px] w-full flex-row items-center justify-between gap-[12px]">
        <div className="flex flex-1 items-center overflow-hidden">
          <Text
            type="body"
            variant="semibold"
            className="overflow-hidden text-ellipsis text-nowrap !text-[#2D0136]"
          >
            {categoryName.toUpperCase()}
          </Text>
        </div>

        <div
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className={twMerge(
            'h-[20px] w-[20px] cursor-pointer',
            clsx('transition-transform', !isExpanded && 'rotate-180')
          )}
          onClick={toggleExpand}
        >
          <Icon
            name="Outline-Arrows-DoubleAltArrowDown"
            size={20}
            color={isHovered ? '#D3C6CB' : !isExpanded ? '#2D0136' : '#E5E5E5'}
          />
        </div>
      </div>

      {isExpanded && (
        <div className="flex flex-col gap-[12px]">
          <div className="flex flex-row flex-wrap gap-x-[12px] gap-y-[8px]">
            {tools.map((tool) => (
              <MarketplaceTool
                key={tool.tool_id}
                tool={tool}
                onClick={() => handleSelectTool(tool)}
                onClickBuy={handleBuyToolVerify}
              />
            ))}
          </div>

          {isHaveMore && (
            <div className="flex justify-end px-[12px]">
              <LoadMoreButton
                isLoading={isLoading}
                onClick={handleLoadMoreItem}
              />
            </div>
          )}
        </div>
      )}

      {isOpenModalToolDetail && (
        <ModalToolDetail
          isOpen={isOpenModalToolDetail}
          toolId={selectedTool?.tool_id}
          onClose={onCloseModal}
        />
      )}

      {isOpenModalBuyTool && (
        <MessageDialog.ConfirmAsync
          open={isOpenModalBuyTool}
          isLoadingConfirm={isLoadingConfirm}
          mainMessage="Buy tool?"
          subMessage="If continue, this tool will be successfully acquired and added to your Tool Collection."
          onClick={handleConfirmBuy}
          onClose={() => setOpenModalBuyTool(false)}
        />
      )}
    </div>
  )
}
