import {
  MarketToolPublic,
  MarketToolsPublic,
  ToolCategoryPublic,
  toolsGetListToolByCategoryApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import Pagination from '@/components/Pagination'
import { PAGE_SIZE } from '@/constants'
import ModalToolDetail from '@/pages/Tools/common/ModalToolDetail'
import useMarketToolsCategory from '@/store/marketTools'
import { size } from 'lodash'
import { memo, useEffect, useState } from 'react'
import { useBuyTool } from '../../hooks/useBuyTool'
import MarketplaceTool from '../MarketplaceTool'
import EmptyListToolByCategory from './EmptyListToolByCategory'

const ListToolByCategory = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<MarketToolsPublic | undefined>()

  const [isOpenModalToolDetail, setOpenModalToolDetail] = useState(false)
  const [selectedTool, setSelectedTool] = useState<MarketToolPublic>()

  const [
    search,
    activeCategory,
    currentPageToolByCategory,
    setCurrentPageToolByCategory,
    paginationToolByCategory,
    setPaginationToolByCategory,
  ] = useMarketToolsCategory((state) => [
    state.search,
    state.activeCategory,
    state.currentPageToolByCategory,
    state.setCurrentPageToolByCategory,
    state.paginationToolByCategory,
    state.setPaginationToolByCategory,
  ])

  const handleSelectTool = (tool: MarketToolPublic) => {
    setSelectedTool(tool)
    setOpenModalToolDetail(true)
  }

  const onCloseModal = () => {
    setSelectedTool(undefined)
    setOpenModalToolDetail(false)
  }

  const updateCountOfCategoryById = useMarketToolsCategory(
    (state) => state.updateCountOfCategoryById
  )

  const updateCategoryPagingByCache = useMarketToolsCategory(
    (state) => state.updateCategoryPagingByCache
  )

  useEffect(() => {
    const fetchToolsByCategory = async (
      category: ToolCategoryPublic | undefined,
      page: number,
      search: string
    ) => {
      try {
        if (loading || !category?.id) return

        setLoading(true)

        const { data } = await toolsGetListToolByCategoryApi({
          query: {
            name: search,
            category_id: category.id,
            page_number: page,
            page_size: PAGE_SIZE.LARGE,
            exclude_built_in: true,
          },
        })

        if (data && data.data) {
          setPaginationToolByCategory({
            total_count: data.data.total_count,
            total_page: data.data.total_pages,
            next_page: data.data.next_page,
            prev_page: data.data.prev_page,
          })

          setData(data.data.data)

          if (search) {
            // update number data of category by id
            updateCountOfCategoryById(category.id, data.data.total_count)
          } else {
            updateCategoryPagingByCache()
          }
        }
      } catch (error) {
        console.log('🚀 ~ fetchToolsByCategory ~ error:', error)
        Message.error({ message: 'Something wrong, please retry!' })
      } finally {
        setLoading(false)
      }
    }

    fetchToolsByCategory(activeCategory, currentPageToolByCategory, search)
  }, [activeCategory, currentPageToolByCategory, search])

  const {
    isOpenModalBuyTool,
    isLoadingConfirm,
    handleBuyToolVerify,
    setOpenModalBuyTool,
    handleConfirmBuy,
  } = useBuyTool()

  return (
    <div className="flex h-full flex-col">
      <div className="genai-scrollbar h-full overflow-y-auto">
        {!loading && search && size(data) === 0 && (
          <NoDataFound className="mt-[64px]" />
        )}

        {!loading && !search && size(data) === 0 && <EmptyListToolByCategory />}

        {!loading && size(data) > 0 && (
          <div className="flex w-full flex-row flex-wrap gap-x-[12px] gap-y-[8px] px-[4px] pb-[1px]">
            {data!.map((tool) => (
              <MarketplaceTool
                key={tool.tool_id}
                tool={tool}
                onClick={() => handleSelectTool(tool)}
                onClickBuy={handleBuyToolVerify}
              />
            ))}
          </div>
        )}
      </div>

      {paginationToolByCategory.total_page > 1 && (
        <div className="mt-[12px] flex w-full flex-row justify-end">
          <Pagination
            onChangePage={setCurrentPageToolByCategory}
            page={currentPageToolByCategory}
            totalPage={paginationToolByCategory.total_page ?? 0}
            className="w-[220px]"
          />
        </div>
      )}

      {isOpenModalToolDetail && (
        <ModalToolDetail
          isOpen={isOpenModalToolDetail}
          toolId={selectedTool?.tool_id}
          onClose={onCloseModal}
        />
      )}

      {isOpenModalBuyTool && (
        <MessageDialog.ConfirmAsync
          open={isOpenModalBuyTool}
          isLoadingConfirm={isLoadingConfirm}
          mainMessage="Buy tool?"
          subMessage="If continue, this tool will be successfully acquired and added to your Tool Collection."
          onClick={handleConfirmBuy}
          onClose={() => setOpenModalBuyTool(false)}
        />
      )}
    </div>
  )
}

export default memo(ListToolByCategory)
