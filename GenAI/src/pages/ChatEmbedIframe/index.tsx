import Playground from '@/components/PlaygroundMultiAgent/Playground'
import { memo, useEffect, useMemo, useState } from 'react'
import {
  EMessageType,
  IMessage,
  LETS_START_MESSAGE,
  LETS_START_RESPONSE,
  getTimeChat,
} from '../Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'

import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import reduce from 'lodash/reduce'
import IconLogoSmall from './components/IconLogoSmall'
import { ChatMessageType, getWidgetParams, postMessageWidget } from './helper'

import { nanoid } from 'nanoid'
import { v4 as uuid } from 'uuid'
import IconUserAvatarDefault from './components/IconUserAvatarDefault'
import { jwtDecode } from 'jwt-decode'

const ChatEmbedIframe = () => {
  const [message, setMessage] = useState<IMessage[]>([])
  const [text, setText] = useState('')
  const [responding, setResponding] = useState(false)

  const [sessionId, setSessionId] = useState<string>()

  const {
    tokenValue: accessToken,
    widgetId: workflowId,
    eToken,
    email,
    session,
  } = getWidgetParams(window.location.href)

  const emailFromEToken = useMemo(() => {
    try {
      if (eToken) {
        const jwtDecoded: any = jwtDecode(eToken)
        return jwtDecoded?.Email
      }
    } catch (error) {
      console.log('error:', error)
    }
  }, [eToken])

  useEffect(() => {
    if (!session) {
      setSessionId(uuid())
    } else {
      setSessionId(session)
    }
  }, [session])

  const iconUserAvatar = useMemo(() => <IconUserAvatarDefault />, [])

  useEffect(() => {
    setMessage([])
  }, [workflowId])

  const resetHandler = () => {
    setMessage([])
    setText('')
    if (!session) {
      setSessionId(uuid())
    }
  }

  const chatHandler = async (tVal: string) => {
    const val = tVal.trim()
    if (val.length === 0 || responding || !accessToken || !workflowId) return

    setResponding(true)
    setText('')
    setMessage((pre) => [
      {
        id: nanoid(),
        text: val,
        type: EMessageType.user,
        time: getTimeChat(),
      },
      ...pre,
    ])

    try {
      const his = reduce(
        message,
        (acc, m, index) => {
          if (m.type === EMessageType.end) {
            return acc // skip end message
          } else if (
            index > 0 &&
            message[index - 1].type === EMessageType.end
          ) {
            return acc //skip
          } else {
            if (
              (m.type === EMessageType.user &&
                text.toLocaleLowerCase() !== LETS_START_MESSAGE) ||
              (m.type === EMessageType.worker && text !== LETS_START_RESPONSE)
            ) {
              acc.push(m)
            }
          }

          return acc
        },
        [] as IMessage[]
      )

      const res = await postMessageWidget({
        token: accessToken,
        eToken: eToken ?? '',
        workflowId,
        message: {
          text: val,
          type: EMessageType.user,
          chat_history: his
            .map(({ text, type, name }) => ({
              text,
              type: type as unknown as ChatMessageType,
              name,
            }))
            .reverse(),
          session_id: sessionId || '',
        },
      })

      if (res.status !== HTTP_STATUS_CODE.SUCCESS) return

      const { data }: any = await res.json()

      if (!data) return

      setResponding(false)

      if (data.name === 'End' && data.type !== EMessageType.noTokenMessage) {
        return
      }

      setMessage((pre) => [
        {
          id: nanoid(),
          text: data.text,
          name: data.name,
          type: data.type,
          time: getTimeChat(),
          worker: {
            workerName: '',
            workerAvatar: '/assets/images/avatar_bot_waiting.svg?react',
          },
          related_question: data.related_question,
        },
        ...pre,
      ])
    } catch (error) {
      console.log('error', error)
    }
  }

  const displayMessage = useMemo(() => {
    if (responding) {
      return [
        {
          text: '',
          type: EMessageType.loading,
          time: getTimeChat(),
          worker: {
            workerName: '',
            workerAvatar: '/assets/images/avatar_bot_waiting.svg?react',
          },
        },
        ...message,
      ]
    }
    return message
  }, [message, responding])

  return (
    <div className="flex h-full w-full flex-col gap-[8px] px-3 pb-3 pt-[16px]">
      <Playground
        workerType={'AI'}
        displayMessage={displayMessage}
        message={message}
        text={text}
        setText={setText}
        responding={responding}
        onClickNewChat={resetHandler}
        onSend={chatHandler}
        className="h-[calc(100%-34px)] !overflow-visible px-0 py-0 pb-[5px]"
        placeholder="Send message..."
        helloMessage="Hi, I am your omnipotent virtual assistant!"
        headerSubTitle={
          <Text
            type="supportText"
            variant="medium"
            className="!text-Secondary-Color"
          >
            Tell me what you need. I am here to help!
          </Text>
        }
        allowStart={false}
        userMessageAvatar={iconUserAvatar}
        email={email || emailFromEToken || ''}
      />

      <Sign />
    </div>
  )
}

export const Sign = memo(() => {
  return (
    <div className="flex items-center justify-center gap-[4px]">
      <Text type="subBody" variant="medium" className="text-Primary-Color">
        Powered by
      </Text>

      <IconLogoSmall />
    </div>
  )
})

export default ChatEmbedIframe
