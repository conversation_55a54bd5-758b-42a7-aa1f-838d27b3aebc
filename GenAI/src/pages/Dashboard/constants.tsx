import Icon from '@/assets/icon/Icon'
import { CHANNEL_TYPE } from '@/pages/WorkflowIntegration/constants'
import { colors } from '@/theme'
import { nanoid } from 'nanoid'
import IconTelegramWithPadding from './assets/IconTelegramWithPadding'
import IconWhatsAppWithPadding from './assets/IconWhatsAppWithPadding'
import IconZaloWithPadding from './assets/IconZaloWithPadding'
import IconMessenger from './assets/IconMessenger'

export const PLATFORMS = [
  {
    id: nanoid(),
    icon24: <IconWhatsAppWithPadding size={24} />,
    icon20: <IconWhatsAppWithPadding size={20} />,
    label: 'WhatsApp',
    value: CHANNEL_TYPE.WhatsApp,
  },
  {
    id: nanoid(),
    icon24: <IconTelegramWithPadding size={24} />,

    icon20: <IconTelegramWithPadding size={20} />,
    label: 'Telegram',
    value: CHANNEL_TYPE.Telegram,
  },
  {
    id: nanoid(),
    icon24: <IconZaloWithPadding size={24} />,
    icon20: <IconZaloWithPadding size={20} />,
    label: 'Zalo',
    value: CHANNEL_TYPE.Zalo,
  },
  {
    id: nanoid(),
    icon24: <IconMessenger size={24} />,
    icon20: <IconMessenger size={20} />,
    label: 'Messenger',
    value: CHANNEL_TYPE.Messenger,
  },
  {
    id: nanoid(),
    icon24: (
      <Icon name="vuesax-bold-code-circle" color={colors.lime[400]} size={24} />
    ),
    icon20: (
      <Icon name="vuesax-bold-code-circle" color={colors.lime[400]} size={20} />
    ),
    label: 'Chat Widget',
    value: CHANNEL_TYPE.EmbeddedWidget,
  },
]
