import IconButton from '@/components/IconButton'
import Layout from '@/components/Layout'
import PageHeader from '@/components/PageHeader'
import { colors } from '@/theme'
import { memo } from 'react'
import { useNavigate } from 'react-router-dom'
import Overview from './components/Overview'
import SessionLog from './components/SessionLog'
import TokenCard from './components/TokenCard'

const Dashboard = () => {
  const navigate = useNavigate()

  return (
    <Layout>
      <div className="flex h-full flex-col overflow-hidden">
        <div className="flex w-full items-center justify-between pr-2">
          <PageHeader
            breadcrumbPaths={[
              {
                name: 'Dashboard',
              },
            ]}
          />

          <IconButton
            nameIcon="Bold-Arrows-RefreshCircle"
            hoverColor={colors['Primary-Color']}
            colorIcon={colors.neutral[300]}
            tooltipText="Refresh data"
            tooltipPosition="left"
            onClick={() => navigate(0)}
            sizeIcon={24}
          />
        </div>
        <div className="mt-3 flex h-full gap-[18px] overflow-hidden">
          <div className="flex flex-col gap-3 p-1">
            <Overview />
            <TokenCard />
          </div>
          <div className="flex grow overflow-hidden">
            <SessionLog />
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default memo(Dashboard)
