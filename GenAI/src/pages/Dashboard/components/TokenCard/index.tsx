import { TokenStatistic, dashboardGetTokenStatistics } from '@/apis/client'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { memo, useEffect, useState } from 'react'
import IconToken from './IconToken'
import TokenChart from './TokenChart'

const TokenCard = () => {
  const [isLoading, setLoading] = useState(false)

  const [token, setToken] = useState<TokenStatistic>()

  const fetchTokenStatistic = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await dashboardGetTokenStatistics()

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setToken(res.data?.data)
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTokenStatistic()
  }, [])

  return (
    <div className="flex w-[347px] flex-col rounded-3xl bg-white px-5 pt-5 shadow-lg">
      <div className="mb-4 flex items-center gap-2">
        <div className="flex items-center justify-center rounded-full bg-pink-50 p-2">
          <IconToken />
        </div>
        <Text variant="semibold" value="Token" className="text-pink-400" />
      </div>

      <div className="mb-3 flex flex-col items-center">
        {isLoading ? (
          <>
            <div className="mb-1 h-[21px] w-[54px] animate-pulse rounded bg-Skeleton-Loader-Content" />
            <div className="h-[21px] w-[150px] animate-pulse rounded bg-Skeleton-Loader-Content" />
          </>
        ) : (
          <>
            <Text
              type="subBody"
              variant="semibold"
              className="text-Secondary-Color"
              value="Total"
            />
            <Text
              type="heading"
              variant="semibold"
              className="text-Primary-Color"
              value={token?.total_tokens?.toLocaleString('ja')}
            />
          </>
        )}
      </div>

      <div className="flex w-full items-center justify-center pt-[14px]">
        <TokenChart
          totalToken={token?.total_tokens ?? 0}
          usedToken={token?.used_tokens ?? 0}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}

export default memo(TokenCard)
