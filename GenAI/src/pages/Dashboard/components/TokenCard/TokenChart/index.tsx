import Text from '@/components/Text'
import { backgroundImage, colors } from '@/theme'
import { memo, useMemo } from 'react'
import './styles.scss'

interface IProps {
  totalToken: number
  usedToken: number
  isLoading?: boolean
}

const TokenChart = ({ totalToken, usedToken, isLoading = false }: IProps) => {
  const percentage = useMemo(() => {
    if (!totalToken) return 0
    return (usedToken / totalToken) * 100
  }, [usedToken, totalToken])

  return (
    <div className="relative w-fit">
      <div
        className="token-chart"
        style={
          {
            '--percentage': isLoading ? 45 : percentage,
            '--bg': isLoading
              ? colors['Skeleton-Loader-Content']
              : backgroundImage['Main-Color'],
            '--bgTray': isLoading
              ? colors['Skeleton-Loader']
              : backgroundImage['Main-Disable-2'],
          } as any
        }
      />
      <div className="absolute bottom-0 left-1/2 z-10 translate-x-[-50%] translate-y-0">
        <div className="flex flex-col items-center pb-[11px]">
          {isLoading ? (
            <>
              <div className="mb-1 h-[21px] w-[118px] animate-pulse rounded bg-Skeleton-Loader-Content" />
              <div className="h-[21px] w-[62px] animate-pulse rounded bg-Skeleton-Loader-Content" />
            </>
          ) : (
            <>
              <Text
                type="heading"
                variant="semibold"
                className="text-Primary-Color"
              >
                {usedToken?.toLocaleString('ja')}
              </Text>

              <Text
                type="subBody"
                variant="semibold"
                className="text-Secondary-Color"
              >
                Token used
              </Text>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default memo(TokenChart)
