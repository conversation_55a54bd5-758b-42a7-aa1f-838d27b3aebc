import IconButton from '@/components/IconButton'
import { DEBOUNCE_TIME } from '@/constants'
import { colors } from '@/theme'
import clsx from 'clsx'
import { debounce } from 'lodash'
import { memo, useCallback, useRef, useState } from 'react'

interface IProps {
  onPressEnter?: (e: any) => void
  onSearch?: (query: string) => void
}

export interface SearchBarInterface {
  resetSearchBar: () => void
}

const SearchBar = ({ onPressEnter, onSearch }: IProps) => {
  const inpRef = useRef<HTMLInputElement>(null)

  const [search, setSearch] = useState('')

  const handleKeyDown = useCallback(
    (event: any) => {
      if (event.key === 'Enter') {
        onPressEnter?.(event)
        setSearch(search?.trim())
      }
    },
    [onPressEnter, search]
  )

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onSearch?.(value.trim())
    }, DEBOUNCE_TIME),
    []
  )

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = event.target.value
    setSearch(newQuery)
    debouncedSearch(newQuery)
  }

  const onBlur = useCallback(() => {
    setSearch(search?.trim())
  }, [search])

  return (
    <div
      className={clsx(
        'flex w-full items-center gap-3 rounded-t-lg border-b border-border-base-icon px-3 py-2'
      )}
      onClick={() => inpRef?.current?.focus()}
    >
      <IconButton
        nameIcon="Icon-Solid-Search"
        sizeIcon={18}
        colorIcon={colors.neutral[300]}
        onClick={onPressEnter}
      />
      <input
        className="flex-grow bg-transparent text-body font-regular text-Primary-Color outline-0 placeholder:text-body placeholder:text-Placeholder-Text"
        ref={inpRef}
        value={search}
        placeholder="Search"
        onChange={(e) => handleSearch(e)}
        onKeyDown={handleKeyDown}
        onBlur={onBlur}
      />
    </div>
  )
}

export default memo(SearchBar)
