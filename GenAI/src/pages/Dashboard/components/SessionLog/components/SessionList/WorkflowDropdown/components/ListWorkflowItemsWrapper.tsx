import {
  WorkflowPublicList,
  WorkflowsPublic,
  workflowReadWorkflowsApi,
} from '@/apis/client'
import EmptyData from '@/components/EmptyData'
import Message from '@/components/Message'
import IconNoData from '@/components/NoDataFound/IconNoData'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { debounce } from '@/helpers/schedulers'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import ListWorkflowItems from './ListWorkflowItems'
import SearchBar from './SearchBar'
import WorkflowItemSkeleton from './WorkflowItemSkeleton'

interface IProps {
  selectedWorkflows?: string[]
  onClickWorkflow: (workflow: WorkflowPublicList, isActive: boolean) => void
}

const ListWorkflowItemsWrapper = ({
  selectedWorkflows,
  onClickWorkflow,
}: IProps) => {
  const [listWorkflow, setListWorkflow] = useState<WorkflowsPublic>([])

  const [isInitial, setInitial] = useState(true)
  const [isLoading, setLoading] = useState(false)

  const [currentPage, setCurrentPage] = useState(1)
  const [totalPage, setTotalPage] = useState<number>(0)
  const [searchKey, setSearchKey] = useState('')

  const fetchListWorkflow = async (page = currentPage, search = searchKey) => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await workflowReadWorkflowsApi({
        query: {
          page_number: page,
          page_size: PAGE_SIZE.SMALL,
          name: search || undefined,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        if (page !== 1) {
          setListWorkflow([...listWorkflow, ...(data?.data?.data ?? [])])
        } else {
          setListWorkflow(data?.data?.data ?? [])
        }

        if (isInitial) {
          setTotalPage(data?.data?.total_pages ?? 0)
        }

        setCurrentPage(page)
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoading(false)

      if (isInitial) {
        setInitial(false)
      }
    }
  }

  const handleSearch = useCallback((text: string) => {
    setInitial(true)
    setSearchKey(text)
    fetchListWorkflow(1, text)
  }, [])

  const onLoadMore = () => {
    if (currentPage < totalPage) {
      fetchListWorkflow(currentPage + 1)
    }
  }

  const loadMoreForwards = useMemo(
    () => debounce(() => onLoadMore(), 1000, true, false),
    [onLoadMore]
  )

  useEffect(() => {
    fetchListWorkflow()
  }, [])

  const renderContent = () => {
    if (isInitial) {
      return (
        <div className="flex flex-col gap-1 px-3 py-2">
          {Array.from({ length: PAGE_SIZE.EXTRA_SMALL }).map((_, index) => (
            <WorkflowItemSkeleton key={index} />
          ))}
        </div>
      )
    }

    if (!listWorkflow.length) {
      if (searchKey)
        return (
          <div className="flex w-full flex-col items-center justify-center">
            <IconNoData size={120} />
            <Text variant="semibold" className="mb-1 mt-2">
              No data found
            </Text>
            <Text
              type="supportText"
              className="mb-6 w-[243px] whitespace-pre-wrap text-center text-Secondary-Color"
            >
              Oops, we couldn’t find what you are looking for Please try again
              and remember to check spelling!
            </Text>
          </div>
        )

      return (
        <div className="flex flex-col items-center">
          <EmptyData size="small" type="03" className="my-4" />
        </div>
      )
    }

    return (
      <ListWorkflowItems
        listWorkflow={listWorkflow}
        loadMoreForwards={loadMoreForwards}
        isLoading={isLoading}
        selectedWorkflows={selectedWorkflows}
        onClickWorkflow={onClickWorkflow}
      />
    )
  }

  return (
    <div
      className="absolute -right-[14px] top-[35px] z-10 flex w-[328px] flex-col rounded-xl border border-border-base-icon bg-white shadow-base"
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
      }}
    >
      <SearchBar onPressEnter={handleSearch} onSearch={handleSearch} />
      {renderContent()}
    </div>
  )
}

export default memo(ListWorkflowItemsWrapper)
