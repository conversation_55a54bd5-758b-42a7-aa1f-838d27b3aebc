import {
  ChatBaseHistorySessionsManagementPublic,
  ChatBaseHistorySessionsManagementsPublic,
  WorkflowConversationChannelTypes,
  chatBaseHistoryFetchListSessionsApi,
} from '@/apis/client'
import Message from '@/components/Message'
import IconNoData from '@/components/NoDataFound/IconNoData'
import Text from '@/components/Text'
import { DEBOUNCE_TIME, HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import clsx from 'clsx'
import { debounce, isEmpty } from 'lodash'
import { memo, useCallback, useEffect, useState } from 'react'
import SessionItem from '../SessionItem'
import SessionItemSkeleton from '../SessionItem/SessionItemSkeleton'
import IconEmptyData from './assets/IconEmptyData'
import DashboardSearchBar from '../DashboardSearchBar'

interface IProps {
  pinSession: ChatBaseHistorySessionsManagementPublic
  setPinSession: React.Dispatch<
    React.SetStateAction<ChatBaseHistorySessionsManagementPublic | undefined>
  >
}

const SessionList = ({ pinSession, setPinSession }: IProps) => {
  const [searchKey, setSearchKey] = useState('')
  const [selectedPlatform, setSelectedPlatform] = useState<string[]>([])
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([])

  const [_searchKey, _setSearchKey] = useState('')

  const [listSession, setListSession] =
    useState<ChatBaseHistorySessionsManagementsPublic>([])
  const [isLoading, setLoading] = useState<boolean>(false)

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchKey?.(value.trim())
    }, DEBOUNCE_TIME),
    []
  )

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = event.target.value
    _setSearchKey(newQuery)
    debouncedSearch(newQuery)
  }

  const onBlur = useCallback(() => {
    _setSearchKey(_searchKey?.trim())
  }, [_searchKey])

  const fetchListSession = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseHistoryFetchListSessionsApi({
        query: {
          created_display_name: searchKey || undefined,
          workflow_id: selectedWorkflows,
          channel_type:
            selectedPlatform as unknown as WorkflowConversationChannelTypes[],
        },
        paramsSerializer: {
          indexes: null,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setListSession(data?.data?.data || [])
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchListSession()
  }, [searchKey, selectedWorkflows, selectedPlatform])

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col gap-3 pt-4">
          {Array.from({ length: PAGE_SIZE.SMALL }).map((_, index) => (
            <SessionItemSkeleton key={index} />
          ))}
        </div>
      )
    }

    if (!listSession.length) {
      if (searchKey || selectedWorkflows.length || selectedPlatform.length)
        return (
          <>
            <div className="mt-11 flex w-full flex-col items-center justify-center">
              <IconNoData size={120} />
              <Text variant="semibold" className="mb-1 mt-2">
                No data found
              </Text>
              <Text
                type="supportText"
                className="mb-6 w-[243px] whitespace-pre-wrap text-center text-Secondary-Color"
              >
                Oops, we couldn’t find what you are looking for Please try again
                and remember to check spelling!
              </Text>
            </div>
          </>
        )

      return (
        <div className="mt-11 flex flex-col items-center">
          <IconEmptyData />

          <Text variant="medium" type="subheading" className="mb-1 mt-2">
            Empty!
          </Text>
          <Text className="w-[330px]text-center text-Secondary-Color">
            None of your customers have reached out to you
          </Text>
        </div>
      )
    }

    return (
      <div
        className={clsx(
          'genai-scrollbar flex h-full w-full flex-col gap-3 overflow-y-auto overflow-x-hidden pb-1 pl-1 pr-2 pt-4'
        )}
      >
        {listSession?.map((item: ChatBaseHistorySessionsManagementPublic) => {
          const isActive = pinSession?.id === item.id
          return (
            <SessionItem
              key={item.id}
              data={item}
              onClick={() => {
                setPinSession(isActive ? undefined : item)
              }}
              isActive={isActive}
            />
          )
        })}
      </div>
    )
  }

  return (
    <div
      className={clsx(
        'flex h-full flex-col py-1',
        !isEmpty(pinSession) ? 'w-[calc(100%-369px)]' : 'w-full'
      )}
    >
      <DashboardSearchBar
        searchKey={searchKey}
        onChange={handleSearch}
        onBlur={onBlur}
        onPressEnter={() => {
          setSearchKey(_searchKey?.trim())
        }}
        selectedWorkflows={selectedWorkflows}
        setSelectedWorkflows={setSelectedWorkflows}
        selectedPlatform={selectedPlatform}
        setSelectedPlatform={setSelectedPlatform}
      />

      {!isEmpty(pinSession) &&
      (searchKey || selectedWorkflows.length || selectedPlatform.length) ? (
        <div className="flex w-full pb-1 pl-1 pr-2 pt-2">
          <SessionItem
            key={pinSession.id}
            data={pinSession}
            onClick={() => {
              setPinSession(undefined)
            }}
            isActive
            isPinned
          />
        </div>
      ) : null}

      {renderContent()}
    </div>
  )
}

export default memo(SessionList)
