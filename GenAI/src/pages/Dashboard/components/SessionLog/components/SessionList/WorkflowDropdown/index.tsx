import { WorkflowPublicList } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { DEBOUNCE_TIME, DEFAULT_OVERFLOW_COUNT } from '@/constants'
import { colors } from '@/theme'
import clsx from 'clsx'
import { produce } from 'immer'
import { debounce, isEmpty } from 'lodash'
import { memo, useEffect, useRef, useState } from 'react'
import ListWorkflowItemsWrapper from './components/ListWorkflowItemsWrapper'
import { cn } from '@/helpers'

const WorkflowDropdown = ({
  selectedWorkflows = [],
  setSelectedWorkflows,
}: {
  selectedWorkflows?: string[]
  setSelectedWorkflows: React.Dispatch<React.SetStateAction<string[]>>
}) => {
  const [isOpen, setOpen] = useState(false)
  const divRef = useRef<HTMLDivElement>(null)

  const [_selectedWorkflows, _setSelectedWorkflows] =
    useState(selectedWorkflows)

  const debounceClick = useRef(
    debounce((workflows) => {
      setSelectedWorkflows(workflows)
    }, DEBOUNCE_TIME)
  ).current

  const _onClickWorkflow = (
    workflow: WorkflowPublicList,
    isActive: boolean
  ) => {
    const workflowFilter = produce(_selectedWorkflows ?? [], (draft) => {
      if (isActive && draft) {
        draft.splice(
          draft.findIndex((ite) => ite === workflow.id),
          1
        )
      } else {
        draft.push(workflow.id)
      }
    })

    _setSelectedWorkflows(workflowFilter)
    debounceClick(workflowFilter)
  }

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (divRef.current && !divRef.current.contains(event.target)) {
        setOpen?.(false)
      }
    }
    document.addEventListener('click', handleClickOutside, true)
    return () => {
      document.removeEventListener('click', handleClickOutside, true)
    }
  }, [])

  return (
    <div
      className={cn(
        'relative flex h-[20px] w-max cursor-pointer items-center justify-between gap-1 px-1'
      )}
      ref={divRef}
      onClick={() => setOpen(!isOpen)}
    >
      <div className="flex select-none items-center gap-1">
        {isEmpty(_selectedWorkflows) ? (
          <>
            <Text
              type="subBody"
              variant="regular"
              className="text-Tertiary-Color"
            >
              Workflow
            </Text>
          </>
        ) : (
          <>
            <Text
              type="subBody"
              variant="regular"
              className="text-Primary-Color"
            >
              {_selectedWorkflows?.length > DEFAULT_OVERFLOW_COUNT
                ? `${DEFAULT_OVERFLOW_COUNT}+`
                : _selectedWorkflows?.length + ' selected'}
            </Text>

            <IconButton
              sizeIcon={14}
              nameIcon="vuesax-bold-close-circle"
              onClick={() => {
                _setSelectedWorkflows([])
                debounceClick([])
              }}
              colorIcon={colors['border-base-icon']}
              hoverColor={colors['Primary-Color']}
            />
          </>
        )}
      </div>
      <Icon
        name="Outline-Chevron-Down"
        size={14}
        color={colors['Primary-Color']}
        className={clsx(isOpen && 'rotate-180')}
      />

      {isOpen && (
        <ListWorkflowItemsWrapper
          selectedWorkflows={_selectedWorkflows}
          onClickWorkflow={_onClickWorkflow}
        />
      )}
    </div>
  )
}

export default memo(WorkflowDropdown)
