import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { DEBOUNCE_TIME } from '@/constants'
import { PLATFORMS } from '@/pages/Dashboard/constants'
import { IPlatform } from '@/pages/Dashboard/types'
import { colors } from '@/theme'
import { produce } from 'immer'
import { debounce, isEmpty } from 'lodash'
import { memo, useEffect, useRef, useState } from 'react'
import { cn } from '@/helpers'

const PlatformDropdown = ({
  selectedPlatform,
  setSelectedPlatform,
}: {
  selectedPlatform?: string[]
  setSelectedPlatform: React.Dispatch<React.SetStateAction<string[]>>
}) => {
  const [isOpen, setOpen] = useState(false)
  const divRef = useRef<HTMLDivElement>(null)

  const [_selectedPlatform, _setSelectedPlatform] = useState(selectedPlatform)

  const debounceClick = useRef(
    debounce((platforms) => {
      setSelectedPlatform(platforms)
    }, DEBOUNCE_TIME)
  ).current

  const _onClickPlatform = (platform: IPlatform, isActive: boolean) => {
    const platformFilter = produce(_selectedPlatform ?? [], (draft) => {
      if (isActive && draft) {
        draft.splice(
          draft.findIndex((ite) => ite === platform.value),
          1
        )
      } else {
        draft.push(platform.value)
      }
    })

    _setSelectedPlatform(platformFilter)
    debounceClick(platformFilter)
  }

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (divRef.current && !divRef.current.contains(event.target)) {
        setOpen?.(false)
      }
    }
    document.addEventListener('click', handleClickOutside, true)
    return () => {
      document.removeEventListener('click', handleClickOutside, true)
    }
  }, [])

  return (
    <div
      className={cn(
        'relative flex h-[20px] w-max max-w-[167px] cursor-pointer items-center justify-between gap-1 rounded-lg bg-white px-1'
      )}
      onClick={() => setOpen(!isOpen)}
      ref={divRef}
    >
      <div className="flex select-none items-center gap-1">
        {isEmpty(_selectedPlatform) ? (
          <Text type="subBody" className="text-Tertiary-Color">
            Platform
          </Text>
        ) : (
          <>
            <Text type="subBody" className="">
              {_selectedPlatform?.length + ' selected'}
            </Text>
            <IconButton
              sizeIcon={14}
              nameIcon="vuesax-bold-close-circle"
              onClick={() => {
                _setSelectedPlatform([])
                debounceClick([])
              }}
              colorIcon={colors['border-base-icon']}
              hoverColor={colors['Primary-Color']}
            />
          </>
        )}
      </div>
      <Icon
        name="Outline-Chevron-Down"
        size={14}
        color={colors['Primary-Color']}
        className={cn(isOpen && 'rotate-180')}
      />

      {isOpen && (
        <div
          className="absolute -right-3 top-[35px] z-10 flex w-[172px] flex-col gap-1 rounded-xl border border-border-base-icon bg-white p-2 shadow-base"
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
          }}
        >
          {PLATFORMS?.map((platform) => {
            const isActive = _selectedPlatform?.includes(platform?.value)
            return (
              <div
                key={platform.id}
                className={cn(
                  'flex items-center gap-2 rounded-md px-2 py-1.5 hover:bg-Hover-Color',
                  isActive && 'bg-Background-Color'
                )}
                onClick={() => {
                  _onClickPlatform(platform, isActive!)
                }}
              >
                {platform.icon24}
                <Text
                  value={platform.label}
                  variant="medium"
                  className={cn(
                    isActive && 'bg-Main-Color bg-clip-text text-transparent'
                  )}
                  type="subBody"
                />
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default memo(PlatformDropdown)
