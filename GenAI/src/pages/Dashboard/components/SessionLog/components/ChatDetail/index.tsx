import {
  ChatBaseHistorySessionsManagementPublic,
  WorkflowConversationsHistory,
  chatBaseHistoryGetChatBaseHistoryApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { memo, useEffect, useMemo, useState } from 'react'
import { BotMessageSkeleton, UserMessageSkeleton } from './ChatSkeleton'
import MessageList from './MessageList'
import { handleMessage } from './helpers'

interface IProps {
  pinSession: ChatBaseHistorySessionsManagementPublic
}

const ChatDetail = ({ pinSession }: IProps) => {
  const {
    session_id,
    workflow_id,
    workflow_description,
    workflow_name,
    chat_base_id,
    created_display_name,
    id,
  } = pinSession
  const [isInitial, setInitial] = useState(true)
  const [isLoading, setLoading] = useState(false)

  const [messageList, setMessageList] = useState<
    WorkflowConversationsHistory[]
  >([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPage, setTotalPage] = useState(0)

  const isAnonymous = useMemo(
    () => created_display_name === 'Annonyomous',
    [created_display_name]
  )

  const fetchChatDetail = async (page = currentPage) => {
    try {
      if (isLoading) return
      setLoading(true)

      if (session_id) {
        const res = await chatBaseHistoryGetChatBaseHistoryApi({
          query: {
            workflow_id,
            session_id,
            chat_base_id,
            page_number: page,
            page_size: PAGE_SIZE.LARGE,
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          const { data } = res

          if (isInitial) {
            setMessageList(data?.data?.data?.reverse() ?? []) // reverse list message because the last message must be in the bottom
            setTotalPage(data?.data?.total_pages ?? 0)
          } else {
            setMessageList(
              handleMessage(messageList, data?.data?.data?.reverse() ?? [])
            )
            setCurrentPage(page)
          }
        }
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoading(false)
      setInitial(false)
    }
  }

  const onLoadMore = ({ type }: { type: 'top' | 'bottom' }) => {
    if (type === 'top' && currentPage < totalPage) {
      fetchChatDetail(currentPage + 1)
    }
  }

  useEffect(() => {
    setInitial(true)
    setCurrentPage(1)
  }, [id, session_id, chat_base_id, workflow_id])

  useEffect(() => {
    if (isInitial) fetchChatDetail()
  }, [isInitial])

  return (
    <div className="flex h-full w-[361px] flex-col gap-2 rounded-2xl bg-white px-2 py-3 shadow-md transition-all">
      <div className="flex w-full items-center gap-2 border-b border-Base-Neutral-02 px-2 pb-2 pt-1">
        <div className="flex items-center justify-center rounded-full bg-Main-Disable-2 p-2">
          <Icon
            name="vuesax-bold-hierarchy"
            gradient={['#996F93', '#B26987']}
            size={20}
          />
        </div>
        <div className="flex max-w-[calc(100%-42px)] flex-col">
          <Text
            variant="semibold"
            type="subBody"
            value={workflow_name}
            elementType="div"
            ellipsis
          />
          <Text
            type="supportText"
            value={workflow_description}
            elementType="div"
            ellipsis
            multipleLine={2}
            className="text-Secondary-Color"
            tooltipPosition="bottom"
          />
        </div>
      </div>

      {isInitial ? (
        <div className="flex w-full flex-col">
          <UserMessageSkeleton />
          <BotMessageSkeleton />
          <UserMessageSkeleton />
          <BotMessageSkeleton />
          <BotMessageSkeleton />
          <BotMessageSkeleton />
          <UserMessageSkeleton />
        </div>
      ) : (
        <MessageList
          isLoading={isLoading}
          messageList={messageList!}
          onLoadMore={onLoadMore}
          isAnonymous={isAnonymous}
        />
      )}
    </div>
  )
}

export default memo(ChatDetail)
