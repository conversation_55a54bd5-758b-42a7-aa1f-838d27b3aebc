import { ChatBaseHistorySessionsManagementPublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import Text from '@/components/Text'
import { convertTimeFromNow } from '@/helpers/dateTime'
import IconTelegramWithPadding from '@/pages/Dashboard/assets/IconTelegramWithPadding'
import IconWhatsAppWithPadding from '@/pages/Dashboard/assets/IconWhatsAppWithPadding'
import IconZaloWithPadding from '@/pages/Dashboard/assets/IconZaloWithPadding'
import { APP_NAME } from '@/pages/WorkflowIntegration/constants'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useCallback, useMemo } from 'react'
import IconToken from '../../../TokenCard/IconToken'
import IconMessenger from '@/pages/Dashboard/assets/IconMessenger'

interface IProps {
  data: ChatBaseHistorySessionsManagementPublic
  isPinned?: boolean
  onClick: () => void
  isActive?: boolean
}

const SessionItem = ({
  data,
  onClick,
  isPinned = false,
  isActive = false,
}: IProps) => {
  const {
    created_avatar,
    created_display_name,
    updated_at,
    total_tokens,
    channel_type,
  } = data

  const isAnonymous = useMemo(
    () => created_display_name === 'Annonyomous',
    [created_display_name]
  )

  const renderAppTag = useCallback(() => {
    switch (channel_type) {
      case APP_NAME.WhatsApp:
        return (
          <div className="flex items-center justify-center rounded-full bg-emerald-100 p-1">
            <IconWhatsAppWithPadding size={20} />
          </div>
        )

      case APP_NAME.Telegram:
        return (
          <div className="flex items-center justify-center rounded-full bg-sky-100 p-1">
            <IconTelegramWithPadding size={20} />
          </div>
        )

      case APP_NAME.Zalo:
        return (
          <div className="flex items-center justify-center rounded-full bg-[#DAE4FF] p-1">
            <IconZaloWithPadding size={20} />
          </div>
        )

      case APP_NAME.EmbededWidget:
        return (
          <div className="flex items-center justify-center rounded-full bg-lime-100 p-1">
            <Icon
              name="vuesax-bold-code-circle"
              size={20}
              color={colors.lime[400]}
            />
          </div>
        )

      case APP_NAME.Messenger:
        return (
          <div className="bg-Messenger-gradient flex size-[26px] items-center justify-center rounded-full p-1">
            <IconMessenger size={20} />
          </div>
        )

      default:
        return <></>
    }
  }, [channel_type])

  const renderPinTag = useCallback(() => {
    return (
      <div className="absolute -left-1 -top-1 flex items-center justify-center rounded-full bg-Main-06 p-1">
        <Icon
          name="Bold-EssentionalUI-Pin"
          size={12}
          color={colors.white}
          className="-rotate-90"
        />
      </div>
    )
  }, [isPinned])

  return (
    <div
      className={clsx(
        'relative flex w-full cursor-pointer items-center justify-between gap-3 rounded-xl px-3 py-2 shadow-base',
        isActive
          ? 'border border-Base-Single-Color bg-Background-Color'
          : 'bg-white hover:bg-Background-Color'
      )}
      onClick={onClick}
    >
      <div className="flex w-full items-center gap-3 overflow-hidden">
        <Avatar
          avatarUrl={created_avatar!}
          avatarDefault={
            <div className="flex size-8 min-w-[32px] items-center justify-center rounded-full bg-Main-Disable-2">
              <Icon
                name={
                  isAnonymous ? 'Bold-Security-Incognito' : 'vuesax-bold-user'
                }
                gradient={['#642B73', '#C6426E']}
                size={20}
              />
            </div>
          }
        />
        <div className="flex w-full max-w-[calc(100%-42px)] flex-col">
          <Text variant="medium" elementType="div" ellipsis>
            {isAnonymous ? 'Anonymous user' : created_display_name}
          </Text>
          <Text type="subBody" className="text-Secondary-Color">
            {`Last updated ${convertTimeFromNow(updated_at!, true)}`}
          </Text>
        </div>
      </div>
      <div className="flex gap-1 rounded-md bg-pink-50 px-1 py-0.5">
        <IconToken size={16} />
        <Text type="subBody" variant="medium" className="text-pink-400">
          {total_tokens?.toLocaleString('ja')}
        </Text>
      </div>
      <div className="absolute -right-2 -top-2">{renderAppTag()}</div>
      {isPinned && renderPinTag()}
    </div>
  )
}

export default memo(SessionItem)
