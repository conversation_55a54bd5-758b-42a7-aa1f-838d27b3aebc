import Icon from '@/assets/icon/Icon'
import { cn } from '@/helpers'
import { colors } from '@/theme'
import WorkflowDropdown from '../SessionList/WorkflowDropdown'
import PlatformDropdown from '../SessionList/PlatformDropdown'
import React from 'react'

interface IProps {
  searchKey: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onBlur: () => void
  selectedWorkflows: string[]
  setSelectedWorkflows: React.Dispatch<React.SetStateAction<string[]>>
  selectedPlatform: string[]
  setSelectedPlatform: React.Dispatch<React.SetStateAction<string[]>>
  onPressEnter?: () => void
}
const DashboardSearchBar = ({
  searchKey,
  onChange,
  onBlur,
  onPressEnter,
  selectedWorkflows,
  setSelectedWorkflows,
  selectedPlatform,
  setSelectedPlatform,
}: IProps) => {
  const inpRef = React.useRef<HTMLInputElement>(null)
  const [isFocus, setFocused] = React.useState(false)

  const onFocus = () => {
    setFocused(true)
  }

  const handleKeyDown = (event: any) => {
    if (event.key === 'Enter') {
      onPressEnter?.()
    }
  }

  const onBlurInput = () => {
    setFocused(false)
    onBlur?.()
  }

  return (
    <div
      className={cn(
        'flex h-[36px] w-full items-center gap-5 rounded-full border border-border-base-icon bg-white px-3 py-2',
        'hover-bg-clip-padding-border box-border hover:border-transparent hover:bg-Input-Main-03 hover:bg-origin-border',
        isFocus && '!border-Base-Neutral shadow-focus'
      )}
    >
      <div className="flex w-full min-w-[187px]">
        <input
          className="flex-grow bg-transparent text-subBody font-regular text-Primary-Color outline-0 placeholder:text-subBody placeholder:text-Placeholder-Text"
          ref={inpRef}
          value={searchKey}
          placeholder={'Search by name or ID'}
          onChange={onChange}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          onBlur={onBlurInput}
        />
        <div className="cursor-pointer" onClick={onPressEnter}>
          <Icon
            name="Icon-Solid-Search"
            size={20}
            color={isFocus ? colors['Base-Neutral'] : colors.neutral[300]}
          />
        </div>
      </div>

      <div className="flex items-center gap-1">
        <div className="h-[14px] w-[1px] bg-neutral-300" />
        <WorkflowDropdown
          selectedWorkflows={selectedWorkflows}
          setSelectedWorkflows={setSelectedWorkflows}
        />
        <PlatformDropdown
          selectedPlatform={selectedPlatform}
          setSelectedPlatform={setSelectedPlatform}
        />
      </div>
    </div>
  )
}

export default DashboardSearchBar
