import { ChatBaseHistorySessionsManagementPublic } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { colors } from '@/theme'
import { isEmpty } from 'lodash'
import { memo, useState } from 'react'
import ChatDetail from './components/ChatDetail'
import SessionList from './components/SessionList'

const SessionLog = () => {
  const [pinSession, setPinSession] =
    useState<ChatBaseHistorySessionsManagementPublic>()

  return (
    <div className="flex h-full w-full flex-col overflow-hidden">
      <div className="mb-1 flex items-center justify-between rounded-xl bg-white px-3 py-2 shadow-md">
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center rounded-full bg-orange-50 p-2">
            <Icon
              name="Bold-Notes-Documents"
              size={20}
              color={colors.orange[400]}
            />
          </div>
          <Text
            variant="semibold"
            value="Session Log"
            className="text-orange-400"
          />
        </div>

        <div className="flex select-none items-center rounded-md bg-orange-50 px-2 py-1">
          <Text
            type="subBody"
            variant="semibold"
            value="100 latest"
            className="text-orange-400"
          />
        </div>
      </div>

      <div className="flex h-full w-full gap-2 overflow-hidden p-2">
        <SessionList pinSession={pinSession!} setPinSession={setPinSession} />
        {!isEmpty(pinSession) ? <ChatDetail pinSession={pinSession} /> : null}
      </div>
    </div>
  )
}

export default memo(SessionLog)
