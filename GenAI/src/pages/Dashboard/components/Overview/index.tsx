import {
  OverviewStatistic,
  dashboardGetOverviewStatistics,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { colors } from '@/theme'
import { memo, useEffect, useState } from 'react'
import IconMessenger from '../../assets/IconMessenger'
import IconTelegramWithPadding from '../../assets/IconTelegramWithPadding'
import IconWhatsAppWithPadding from '../../assets/IconWhatsAppWithPadding'
import IconZaloWithPadding from '../../assets/IconZaloWithPadding'
import OverviewSkeleton from './OverviewSkeleton'

const Overview = () => {
  const [isLoading, setLoading] = useState(false)
  const [overview, setOverview] = useState<OverviewStatistic>()

  const fetchOverviewStatistic = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await dashboardGetOverviewStatistics()

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setOverview(res.data?.data)
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOverviewStatistic()
  }, [])

  return (
    <div className="flex w-[347px] flex-col rounded-3xl bg-white px-5 pb-6 pt-5 shadow-lg">
      <div className="mb-4 flex items-center gap-2">
        <div className="flex items-center justify-center rounded-full bg-purple-50 p-2">
          <Icon
            name="Bold-BusinessStatistic-ChatSquare2"
            size={20}
            color={colors.purple[400]}
          />
        </div>
        <Text variant="semibold" value="Overview" className="text-purple-400" />
      </div>

      {isLoading ? (
        <OverviewSkeleton />
      ) : (
        <div className="flex gap-3">
          <div className="flex w-[128px] min-w-[128px] flex-col items-center justify-center gap-3">
            <Text
              variant="semibold"
              type="subBody"
              value="Total"
              className="text-center text-Secondary-Color"
              elementType="div"
            />

            <div className="flex w-full flex-col items-center justify-center gap-3 overflow-hidden">
              <div className="flex items-center justify-center rounded-xl bg-indigo-50">
                <Icon
                  name="Bold-MessagesConversation-ChatRoundCall"
                  size={40}
                  color={colors.indigo[400]}
                />
              </div>

              <div className="flex w-full flex-col items-center overflow-hidden">
                <Text
                  variant="semibold"
                  type="heading"
                  value={overview?.total_sessions?.toLocaleString('ja')}
                  className="text-center text-Primary-Color"
                  elementType="div"
                  ellipsis
                />
                <Text
                  type="subBody"
                  variant="medium"
                  value="Sessions"
                  className="text-Secondary-Color"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-1">
            <div className="grid grid-cols-[44px_55.5px_55.5px]">
              <Text
                value="Platform"
                variant="medium"
                type="supportText"
                className="py-0.5 text-center text-Secondary-Color"
                elementType="div"
              />
              <Text
                value="Session"
                variant="medium"
                type="supportText"
                className="py-0.5 text-center text-Secondary-Color"
                elementType="div"
              />
              <Text
                value="Workflow"
                variant="medium"
                type="supportText"
                className="py-0.5 text-center text-Secondary-Color"
                elementType="div"
              />
            </div>

            <div className="grid grid-cols-[44px_55.5px_55.5px] gap-x-1 gap-y-2">
              <div className="flex items-center justify-center">
                <div className="flex w-8 items-center justify-center rounded-lg bg-emerald-100 p-1">
                  <IconWhatsAppWithPadding size={24} />
                </div>
              </div>
              <Text
                value={overview?.whatsapp?.session_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />
              <Text
                value={overview?.whatsapp?.workflow_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />

              <div className="flex items-center justify-center">
                <div className="flex w-8 items-center justify-center rounded-lg bg-lime-100 p-1">
                  <Icon
                    name="vuesax-bold-code-circle"
                    size={24}
                    color={colors.lime[400]}
                  />
                </div>
              </div>
              <Text
                value={overview?.widget?.session_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />
              <Text
                value={overview?.widget?.workflow_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />

              <div className="flex items-center justify-center">
                <div className="flex items-center justify-center rounded-lg bg-sky-100 p-1">
                  <IconTelegramWithPadding size={24} />
                </div>
              </div>
              <Text
                value={overview?.telegram?.session_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />
              <Text
                value={overview?.telegram?.workflow_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />

              <div className="flex items-center justify-center">
                <div className="flex w-8 items-center justify-center rounded-lg bg-[#DAE4FF] p-1">
                  <IconZaloWithPadding size={24} />
                </div>
              </div>
              <Text
                value={overview?.zalo?.session_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />
              <Text
                value={overview?.zalo?.workflow_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />

              <div className="flex items-center justify-center">
                <div
                  className="flex w-8 items-center justify-center rounded-lg p-1"
                  style={{
                    background:
                      'linear-gradient(220deg, #F8DDEC 21.85%, #EFD0F3 42.98%, #D3DAFC 80.67%)',
                  }}
                >
                  <IconMessenger size={24} />
                </div>
              </div>
              <Text
                value={overview?.messenger?.session_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />
              <Text
                value={overview?.messenger?.workflow_count ?? 0}
                variant="medium"
                className="self-center text-center"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default memo(Overview)
