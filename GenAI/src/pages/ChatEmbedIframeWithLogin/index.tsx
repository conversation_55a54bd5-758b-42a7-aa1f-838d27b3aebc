import { useMemo, useRef, useState } from 'react'
import Assistant<PERSON><PERSON> from './AssistantIcon'
import Text from '@/components/Text'
import Input, { InputWithRef } from '@/components/Input'
import Button from '@/components/Button'
import { isValidEmail } from '../register/component/SignUp'
import SendDisabled from './SendDisabled'
import Send from './Send'
import { Sign } from '../ChatEmbedIframe'
import {
  chatBaseWidgetWorkflowUserEmailConfirmApi,
  chatBaseWidgetWorkflowUserEmailRegisterApi,
} from '@/apis/client'
import { HTTP_STATUS_CODE } from '@/constants'
import { getWidgetParams } from '../ChatEmbedIframe/helper'

enum STEPS {
  ENTER_EMAIL = 'ENTER_EMAIL',
  ENTER_PASSWORD = 'ENTER_PASSWORD',
}

const ChatEmbedIframeWithLogin = () => {
  const [step, setStep] = useState(STEPS.ENTER_EMAIL)
  const [email, setEmail] = useState('')
  const [code, setCode] = useState('')
  const [emailError, setEmailError] = useState<string>('')
  const [codeError, setCodeError] = useState<string>('')
  const [id, setId] = useState('')
  const [loading, setLoading] = useState(false)
  const codeRef = useRef<HTMLInputElement>(null)

  const { widgetId: workflowId } = getWidgetParams(window.location.href)

  const verifyCodeHandler = async (email: string, code: string) => {
    try {
      setLoading(true)
      const res = await chatBaseWidgetWorkflowUserEmailConfirmApi({
        path: {
          workflow_id: workflowId || '',
        },
        body: {
          email,
          otp_code: code,
          id,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const destination = window.location.href.replace(
          '/application-widget',
          '/application'
        )
        const hasQuery = destination.includes('?')
        const session = res.data?.data?.session_id || ''

        const url = `${destination}${hasQuery ? '&' : '?'}email=${email}&session=${session}`
        window.location.href = url
      } else {
        setCodeError(res.error?.detail?.toString() ?? 'Something went wrong')
      }
    } catch (error) {
      setCodeError('Something went wrong')
    } finally {
      setLoading(false)
    }
  }

  const sendCodeHandler = async (email: string) => {
    try {
      setLoading(true)
      const res = await chatBaseWidgetWorkflowUserEmailRegisterApi({
        path: {
          workflow_id: workflowId || '',
        },
        body: {
          email,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setStep(STEPS.ENTER_PASSWORD)
        setEmailError('')
        setId(res.data?.data?.id || '')
      } else {
        if (typeof res.error?.detail === 'string') {
          setEmailError(res.error?.detail)
        } else {
          setEmailError('Invalid email address')
        }
      }
    } catch (error) {
      setEmailError('Something went wrong')
    } finally {
      setLoading(false)
      setTimeout(() => {
        codeRef.current?.focus()
      }, 200)
    }
  }

  const pressHandler = () => {
    if (step === STEPS.ENTER_EMAIL) {
      if (isValidEmail(email)) {
        sendCodeHandler(email)
      } else {
        setEmailError('Invalid email address')
      }
    } else {
      verifyCodeHandler(email, code)
    }
  }

  const rightIcon = useMemo(() => {
    if (step === STEPS.ENTER_EMAIL) {
      return email.length ? <Send /> : <SendDisabled />
    }
    return null
  }, [step, email])

  const buttonText = useMemo(() => {
    if (step === STEPS.ENTER_EMAIL) {
      return 'Start'
    }
    return 'Continue'
  }, [step])

  const skipHandler = () => {
    const destination = window.location.href.replace(
      '/application-widget',
      '/application'
    )
    // navigate(destination)
    window.location.href = destination
  }

  const enterOnEmailHandler = () => {
    if (step === STEPS.ENTER_EMAIL) {
      pressHandler()
    } else {
      codeRef.current?.focus()
    }
  }

  const enterOnCodeHandler = () => {
    pressHandler()
  }

  return (
    <div className="m-auto flex h-[568px] w-[350px] flex-col items-center justify-center gap-2 p-3 pt-4">
      <div className="flex h-[508px] w-[264px] flex-col items-center justify-center gap-8">
        <div className="flex w-full flex-col items-center gap-2">
          <AssistantIcon />
          <Text
            type="subBody"
            variant="regular"
            className="text-center text-Secondary-Color"
          >
            {step === STEPS.ENTER_EMAIL
              ? 'Hi, I am your omnipotent virtual assistant!\nEnter your email to sign in'
              : '6-digit code sent to your email, type in verification code here to continue'}
          </Text>
        </div>
        <div className="flex w-full flex-col gap-3">
          <Input
            placeholder="Email address"
            value={email}
            onChange={(e) => {
              const text = e.target.value
              setEmail(text)
              setStep(STEPS.ENTER_EMAIL)
              setEmailError('')
              setCode('')
              setCodeError('')
            }}
            classNameInputWrapper="w-full"
            maxLength={255}
            errorText={emailError}
            isError={emailError.length > 0}
            onBlur={() => {
              setEmail(email.trim())
            }}
            onPressEnter={enterOnEmailHandler}
          />
          {step === STEPS.ENTER_PASSWORD && (
            <InputWithRef
              placeholder="Code"
              value={code}
              onChange={(e) => {
                const text = e.target.value.replace(/[^0-9]/g, '')
                setCode(text)
                setCodeError('')
              }}
              classNameInputWrapper="w-full"
              maxLength={6}
              errorText={codeError}
              isError={codeError.length > 0}
              ref={codeRef}
              onPressEnter={enterOnCodeHandler}
            />
          )}
        </div>
        <Button
          type="secondary"
          text={buttonText}
          disabled={
            (step === STEPS.ENTER_EMAIL && email.length === 0) ||
            (step === STEPS.ENTER_PASSWORD && code.length === 0)
          }
          rightIcon={rightIcon}
          onClick={pressHandler}
          loading={loading}
        />
        <button
          onClick={skipHandler}
          className="p-0 text-Primary-Color hover:bg-Main-Color hover:bg-clip-text hover:text-transparent"
        >
          <Text
            type="subBody"
            variant="regular"
            elementType="div"
            value="Continue incognito"
          />
        </button>
      </div>
      <Sign />
    </div>
  )
}

export default ChatEmbedIframeWithLogin
