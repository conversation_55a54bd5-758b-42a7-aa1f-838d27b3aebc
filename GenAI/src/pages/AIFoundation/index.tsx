import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { ModelPublic, ModelsPublic, modelsGetListModelApi } from '@/apis/client'
import EmptyData from '@/components/EmptyData'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import PageHeader from '@/components/PageHeader'
import Pagination from '@/components/Pagination'
import ModelItemSkeleton from './components/ModelItemSkeleton'
import ModelItem from './components/ModelItem'
import SearchBar from '@/components/SearchBar'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'

const MODEL_PARAMS_INIT = {
  page: 1,
  page_size: PAGE_SIZE.LARGE,
  name: '',
}

type ModelParam = {
  key: number | string
  value: any
}

function AIFoundation() {
  const [modelParams, setModelParams_] = useState(MODEL_PARAMS_INIT)
  const [loading, setLoading] = useState(false)
  const [totalPage, setTotalPage] = useState<number>()

  const [models, setModels] = useState<ModelsPublic>([])

  const setModelParams = useCallback(
    ({ key, value }: ModelParam) =>
      setModelParams_({
        ...modelParams,
        [key]: value,
      }),
    [modelParams]
  )

  const handleSearchName = useCallback(
    (value: string) =>
      setModelParams({
        key: 'name',
        value,
      }),
    [setModelParams]
  )

  const handleChangePage = useCallback(
    (value: number) =>
      setModelParams({
        key: 'page',
        value,
      }),
    [setModelParams]
  )

  const fetchModels = async () => {
    try {
      if (loading) return

      setLoading(true)

      const res = await modelsGetListModelApi({
        query: modelParams,
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setModels(data?.data?.data ?? [])
        setTotalPage(data?.data?.total_pages)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const renderContent = useMemo(() => {
    if (loading)
      return (
        <div className="genai-scrollbar mt-5 h-full overflow-auto">
          <div className="flex flex-wrap gap-3 px-1 pb-2">
            {Array.from({ length: modelParams.page_size }).map((_, index) => (
              <ModelItemSkeleton key={index} />
            ))}
          </div>
        </div>
      )

    if (!models?.length) {
      if (modelParams.name) {
        return (
          <div className="flex w-full justify-center">
            <NoDataFound className="mt-[84px]" />
          </div>
        )
      }
      return (
        <div className="mt-[100px] flex w-full flex-col items-center justify-center">
          <EmptyData size="medium" type="01" className="mb-6" />
        </div>
      )
    }

    return (
      <div className="genai-scrollbar mt-5 h-full flex-1 overflow-auto">
        <div className="flex flex-wrap gap-6 px-1 pb-2">
          {models.map((model: ModelPublic) => (
            <ModelItem
              key={model.id}
              display_name={model.display_name}
              description={model.description}
              apiType={model.api_type}
              provider={model.provider}
              image_url={model.image_url}
              hosting_mode={model.hosting_mode}
              onClick={() => {}}
            />
          ))}
        </div>
      </div>
    )
  }, [loading, modelParams, models])

  useEffect(() => {
    fetchModels()
  }, [modelParams])

  return (
    <Layout>
      <div className="flex h-full w-full flex-col items-start gap-5 overflow-hidden">
        <div className="flex w-full items-center justify-between">
          <PageHeader
            breadcrumbPaths={[
              {
                name: 'AI Foundation',
              },
            ]}
            title={
              'Empower worker to be more intelligent, responsive, and capable in a wide range of applications.'
            }
          />
          <SearchBar onSearch={handleSearchName} />
        </div>
        {renderContent}
        {totalPage! > 1 && (
          <Pagination
            page={modelParams.page}
            totalPage={totalPage}
            onChangePage={handleChangePage}
            className="mt-3 flex w-full items-center justify-end"
          />
        )}
      </div>
    </Layout>
  )
}

export default memo(AIFoundation)
