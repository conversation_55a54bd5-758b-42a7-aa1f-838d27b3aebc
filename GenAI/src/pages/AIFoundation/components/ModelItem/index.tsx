import { ModelPublic } from '@/apis/client'
import Text from '@/components/Text'
import { cn, getUrlImage } from '@/helpers'
import clsx from 'clsx'
import { memo, useMemo } from 'react'

const tooltipProps = {
  mouseEnterDelay: 0.3,
}

function ModelItem({
  display_name,
  description,
  provider,
  image_url,
  hosting_mode,
  onClick,
}: Partial<ModelPublic> & {
  apiType?: string | null
  onClick: () => void
}) {
  const isDisabled = useMemo(
    () =>
      ['Deepseek-R1', 'Qwen-2.5-14b', 'Gemini 2.0 Flash']?.includes(
        display_name
      ),
    [display_name]
  )

  return (
    <div
      className={clsx(
        'flex h-[140px] w-[374px] min-w-[374px] cursor-pointer flex-col items-center justify-center gap-2 rounded-xl bg-white p-[12px] shadow-base duration-100',
        isDisabled && 'opacity-65'
      )}
      onClick={onClick}
    >
      <div className="flex h-[44px] w-full justify-between gap-2">
        <div className="flex gap-2 overflow-hidden">
          <div className="padding-[2px] flex size-[44px] min-w-[44px] items-center justify-center overflow-hidden rounded-[8px] border-[0.5px] border-solid border-border-base-icon">
            <img className="object-contain" src={getUrlImage(image_url)} />
          </div>
          <div className="flex flex-col gap-1 overflow-hidden">
            <Text
              variant="medium"
              type="body"
              className="text-center text-Primary-Color"
              value={display_name}
              elementType="div"
              ellipsis
              tooltipProps={tooltipProps}
            />
            <Text
              variant="regular"
              type="subBody"
              className="text-Secondary-Color"
              value={provider}
              elementType="div"
              ellipsis
              tooltipProps={tooltipProps}
            />
          </div>
        </div>

        <div
          className={cn('h-[22px] rounded-full px-2 py-[2px]', {
            'bg-teal-100': hosting_mode === 'Cloud-Based',
            'bg-orange-100': hosting_mode === 'Self-Hosted',
          })}
        >
          <Text
            variant="regular"
            type="subBody"
            className={cn('text-center text-teal-500', {
              'text-orange-500': hosting_mode === 'Self-Hosted',
            })}
            value={hosting_mode}
            elementType="div"
          />
        </div>
      </div>

      <div className="flex-1">
        <Text
          variant="regular"
          type="subBody"
          className="text-Secondary-Color"
          value={description}
          elementType="div"
          ellipsis
          multipleLine={3}
        />
      </div>
    </div>
  )
}

export default memo(ModelItem)
