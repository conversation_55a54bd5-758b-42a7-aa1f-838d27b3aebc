import { ValidationError, usersResetPasswordApi } from '@/apis/client'
import Button from '@/components/Button'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { useState } from 'react'

interface SignUpProps {
  email: string
  setEmail: any
  nextStep: () => void
}
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,6}$/
  const domainPart = email.split('@')[1]

  // Kiểm tra phần miền chỉ có một dấu chấm
  if (domainPart && domainPart.split('.').length === 2) {
    return emailRegex.test(email)
  }
  return false
}
const Recover = (props: SignUpProps) => {
  const { email, setEmail, nextStep } = props
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<ValidationError[] | string>('')

  const submitEmail = async () => {
    setLoading(true)
    try {
      const data = await usersResetPasswordApi({
        body: {
          email: email?.trim(),
        },
      })

      if (data.status === 200) {
        nextStep()
      } else if (data.status === 400 && data.error) {
        const message = data.error.detail
        setError(message ?? '')
      } else {
        setError('Invalid email address')
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }
  const handleBlurEmail = () => {
    setEmail(email?.trim())
    if (!isValidEmail(email.trim()) && email.trim() !== '')
      setError('Invalid email address')
  }

  const handleChangeEmail = (event: any) => {
    setEmail(event.target.value)
    setError('')
  }

  const handleContinue = async () => {
    if (email.trim() === '' || !isValidEmail(email.trim())) {
      setError('Invalid email address')
    } else {
      submitEmail()
      setError('')
    }
  }

  return (
    <div className="flex flex-col items-center gap-[20px] p-[32px]">
      <div className="flex flex-col items-center justify-center gap-[4px]">
        <Text type="heading" variant="semibold" className="text-Primary-Color">
          Recover your account
        </Text>
        <Text type="subBody" variant="regular" className="text-Secondary-Color">
          Enter your registered email, we’ll help you to reset your password
        </Text>
      </div>
      <div className="flex w-[287px] flex-col items-center justify-center gap-[28px]">
        <Input
          placeholder="Type in your email address"
          value={email}
          maxLength={255}
          onBlur={handleBlurEmail}
          onChange={handleChangeEmail}
          onPressEnter={handleContinue}
          isFullWidth
          errorText={error.toString()}
          isError={!!error.toString()}
          type="text"
        />
        <Button
          onClick={() => {
            handleContinue()
          }}
          text={'Continue'}
          type="primary"
          disabled={email.trim() === '' || !isValidEmail(email.trim())}
          className="w-full"
          loading={loading}
        />
      </div>
    </div>
  )
}

export default Recover
