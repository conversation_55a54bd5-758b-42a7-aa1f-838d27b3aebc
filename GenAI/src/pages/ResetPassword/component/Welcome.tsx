/* eslint-disable max-len */
import Button from '@/components/Button'
import Text from '@/components/Text'
import { rootUrls } from '@/routes/rootUrls'
import { useNavigate } from 'react-router-dom'

const Welcome = () => {
  const navigate = useNavigate()

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative flex flex-col items-center justify-center gap-[8px] p-[32px]">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="100"
          height="100"
          viewBox="0 0 100 100"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M39.968 13.3351C38.9533 14.1998 38.4459 14.6322 37.9041 14.9954C36.662 15.8279 35.2671 16.4057 33.8001 16.6953C33.1601 16.8217 32.4956 16.8747 31.1667 16.9808C27.8276 17.2472 26.158 17.3804 24.7651 17.8724C21.5435 19.0104 19.0094 21.5444 17.8715 24.7661C17.3795 26.159 17.2462 27.8285 16.9798 31.1677C16.8737 32.4966 16.8207 33.1611 16.6944 33.8011C16.4047 35.268 15.8269 36.663 14.9944 37.9051C14.6312 38.4469 14.1989 38.9543 13.3341 39.969C11.1615 42.5185 10.0751 43.7933 9.43807 45.1261C7.96465 48.2088 7.96465 51.7925 9.43807 54.8752C10.0751 56.208 11.1615 57.4828 13.3341 60.0323C14.1988 61.0469 14.6313 61.5544 14.9944 62.0962C15.8269 63.3383 16.4047 64.7333 16.6944 66.2002C16.8207 66.8402 16.8737 67.5047 16.9798 68.8336C17.2462 72.1727 17.3795 73.8423 17.8715 75.2352C19.0094 78.4568 21.5435 80.9909 24.7651 82.1289C26.158 82.6208 27.8276 82.7541 31.1667 83.0205C32.4956 83.1266 33.1601 83.1796 33.8001 83.306C35.2671 83.5956 36.662 84.1734 37.9041 85.0059C38.4459 85.3691 38.9533 85.8014 39.968 86.6662C42.5175 88.8389 43.7923 89.9252 45.1251 90.5622C48.2078 92.0357 51.7915 92.0357 54.8742 90.5622C56.207 89.9252 57.4818 88.8389 60.0313 86.6662C61.046 85.8014 61.5534 85.3691 62.0952 85.0059C63.3373 84.1734 64.7323 83.5956 66.1992 83.306C66.8392 83.1796 67.5037 83.1266 68.8327 83.0205C72.1718 82.7541 73.8413 82.6208 75.2342 82.1289C78.4559 80.9909 80.99 78.4569 82.1279 75.2352C82.6199 73.8423 82.7531 72.1727 83.0196 68.8336C83.1256 67.5047 83.1786 66.8402 83.305 66.2002C83.5946 64.7333 84.1724 63.3383 85.0049 62.0962C85.3681 61.5544 85.8005 61.047 86.6652 60.0323C88.8379 57.4828 89.9242 56.208 90.5613 54.8752C92.0347 51.7925 92.0347 48.2088 90.5613 45.1261C89.9242 43.7933 88.8379 42.5185 86.6652 39.969C85.8005 38.9543 85.3681 38.4469 85.0049 37.9051C84.1724 36.663 83.5946 35.268 83.305 33.8011C83.1786 33.1611 83.1256 32.4966 83.0196 31.1677C82.7531 27.8285 82.6199 26.159 82.1279 24.7661C80.99 21.5444 78.4559 19.0104 75.2342 17.8724C73.8413 17.3804 72.1718 17.2472 68.8327 16.9808C67.5037 16.8747 66.8392 16.8217 66.1992 16.6953C64.7323 16.4057 63.3373 15.8279 62.0952 14.9954C61.5534 14.6322 61.046 14.1999 60.0313 13.3351C57.4818 11.1624 56.207 10.0761 54.8742 9.43905C51.7915 7.96563 48.2078 7.96563 45.1251 9.43905C43.7923 10.0761 42.5175 11.1625 39.968 13.3351ZM68.2225 41.097C69.5468 39.7727 69.5468 37.6256 68.2225 36.3013C66.8982 34.977 64.7511 34.977 63.4268 36.3013L43.2174 56.5107L36.5724 49.8657C35.2481 48.5414 33.101 48.5414 31.7767 49.8657C30.4524 51.19 30.4524 53.3371 31.7767 54.6614L40.8196 63.7043C42.1439 65.0286 44.291 65.0286 45.6153 63.7043L68.2225 41.097Z"
            fill="url(#paint0_linear_6466_151668)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_6466_151668"
              x1="91.6663"
              y1="50.0006"
              x2="23.1316"
              y2="83.5336"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#DBCEDD" />
              <stop offset="1" stopColor="#EFD5DF" />
            </linearGradient>
          </defs>
        </svg>

        <div className="flex flex-col items-center gap-[4px]">
          <Text variant="semibold" type="title" className="text-Primary-Color">
            Done!
          </Text>
          <Text
            variant="regular"
            type="subBody"
            className="text-Secondary-Color"
          >
            New password has been set, login now!
          </Text>
        </div>
      </div>

      <Button
        text={'Go to Login'}
        type="secondary"
        onClick={() => {
          navigate(rootUrls.Login)
        }}
      />
    </div>
  )
}

export default Welcome
