import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { rootUrls } from '@/routes/rootUrls'
import { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import CreateProfile from './component/CreateProfile'
import Recover from './component/Recover'
import Welcome from './component/Welcome'

const ResetPassword = () => {
  const navigate = useNavigate()

  const [step, setStep] = useState(0)
  const [email, setEmail] = useState(useLocation()?.state?.email ?? '')
  const [hover, setHover] = useState(false)

  const nextStep = () => {
    setStep(step + 1)
  }
  return (
    <div className="flex h-full w-full items-center justify-center bg-Base-01">
      {step < 2 && (
        <div
          onMouseEnter={() => setHover(true)}
          onMouseLeave={() => setHover(false)}
          onClick={() => navigate(rootUrls.Login, { replace: true })}
          className="absolute left-[32px] top-[24px] flex cursor-pointer items-center justify-center gap-[4px]"
        >
          <div
            className={`h-[24px] w-[24px] cursor-pointer rounded-md ${hover ? 'bg-Hover-2' : ''}`}
          >
            <Icon name="icon-fill-caret-small-left" size={24} color="#2D0136" />
          </div>
          <div className="flex w-full items-center justify-center gap-[0.1em]">
            <Text type="subBody" className="text-Secondary-Color">
              Mistake?
            </Text>
            <Text
              type="subBody"
              variant="medium"
              className="cursor-pointer text-Primary-Color"
              elementType="div"
            >
              Back to login
            </Text>
          </div>
        </div>
      )}

      {step === 0 && (
        <Recover email={email} setEmail={setEmail} nextStep={nextStep} />
      )}
      {step === 1 && <CreateProfile email={email} nextStep={nextStep} />}
      {step === 2 && <Welcome />}
    </div>
  )
}

export default ResetPassword
