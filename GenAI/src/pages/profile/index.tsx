import { Body_users_update_user_api, usersUpdateUserApi } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import Button from '@/components/Button'
import UserPopoverContent from '@/components/Header/UserPopoverContent'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Text from '@/components/Text'
import UploadAvatar from '@/components/UploadAvatar'
import { useMyProfile } from '@/hooks/useMyProfile'
import { rootUrls } from '@/routes/rootUrls'
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const Profile = () => {
  const navigate = useNavigate()
  const { myProfile, fetchMyProfile } = useMyProfile()

  const [name, setName] = useState('')
  const [initName, setInitName] = useState('')
  const [loading, setLoading] = useState(false)
  const [avatar, setAvatar] = useState<string | undefined | null>()
  const [initAvatar, setInitAvatar] = useState<string | undefined | null>()
  const [fileAvatar, setFileAvatar] = useState<File | null>(null)
  const [nameAvatar, setNameAvatar] = useState('')

  const updateProfile = async () => {
    setLoading(true)
    try {
      const params: Body_users_update_user_api = {}

      if (name?.trim() !== '' || name?.trim() !== initName) {
        params.first_name = name?.trim()
      }

      if (avatar !== initAvatar) {
        if (fileAvatar) {
          params.avatar_file = fileAvatar
        } else {
          params.avatar = ''
        }
      } else {
        params.avatar = myProfile?.avatar
      }
      const data = await usersUpdateUserApi({
        body: params,
        path: {
          user_id: myProfile?.id || 1,
        },
      })

      if (data.status === 200) {
        fetchMyProfile()
        setInitName(name?.trim())
        setInitAvatar(avatar)
        Message.success({ message: 'Successfully updated profile' })
      } else if (data.status === 400 && data.error) {
        Message.error({ message: data?.error?.detail || '' })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const handleChangeName = (e: any) => {
    setName(e.target.value)
  }

  const handleBlurName = () => {
    if (!name?.trim()) {
      setName(initName)
      setNameAvatar(initName)
    } else {
      setName(name?.trim())
      setNameAvatar(name?.trim())
    }
  }

  useEffect(() => {
    if (myProfile?.first_name) {
      setName(myProfile?.first_name)
      setInitName(myProfile?.first_name)
      setNameAvatar(myProfile?.first_name)
      if (myProfile?.avatar) {
        setAvatar(myProfile?.avatar)
        setInitAvatar(myProfile?.avatar)
      }
    }
  }, [myProfile?.first_name])

  return (
    <div className="flex h-full w-full flex-col items-center gap-[20px] bg-Base-01 px-[20px] py-[8px]">
      <div className="flex w-full items-center justify-between pl-[12px]">
        <img
          onClick={() => navigate(rootUrls.Home, { replace: true })}
          className="cursor-pointer"
          src="/assets/images/genai-logo.svg"
          width={35.8}
        />

        <Popover className="group">
          {({ open }) => (
            <>
              <PopoverButton className="flex items-center gap-2 focus-visible:outline-none">
                <Avatar
                  className={
                    open
                      ? 'border-[2px] border-Base-Neutral'
                      : 'border-[2px] border-Base-01'
                  }
                  avatarUrl={myProfile?.avatar || ''}
                  name={myProfile?.first_name ?? myProfile?.username ?? ''}
                />
              </PopoverButton>
              {open && (
                <PopoverPanel
                  anchor="bottom end"
                  className="z-20 mt-[5px] flex flex-col !overflow-hidden rounded-[8px] border-[0.5px] border-border-base-icon bg-white shadow-md"
                >
                  <UserPopoverContent myProfile={myProfile} />
                </PopoverPanel>
              )}
            </>
          )}
        </Popover>
      </div>

      <div className="flex h-full w-full flex-col items-center gap-[12px]">
        <div className="flex h-full flex-col items-center gap-[32px]">
          <Text type="title" variant="semibold" className="text-Primary-Color">
            Your profile
          </Text>

          <div className="flex flex-col items-end justify-center gap-[8px]">
            <div className="flex flex-col items-center gap-[24px]">
              <UploadAvatar
                image={avatar}
                onChange={(e) => {
                  setAvatar(e)
                }}
                onChangeFile={(e) => {
                  setFileAvatar(e)
                }}
                nameUser={nameAvatar}
              />

              <div className="flex w-[350px] flex-col items-end gap-[24px] rounded-[8px] bg-Base-03 px-[20px] py-[12px]">
                <div className="flex w-full flex-col gap-[16px]">
                  <Text
                    type="subheading"
                    variant="semibold"
                    className="text-Primary-Color"
                  >
                    Personal details
                  </Text>

                  <div className="flex flex-col gap-[20px]">
                    <Input
                      onChange={handleChangeName}
                      onBlur={handleBlurName}
                      label="Name"
                      value={name || ''}
                      maxLength={50}
                    />
                    <Input
                      disabled
                      label="Email address"
                      value={myProfile?.email || ''}
                    />
                  </div>
                </div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => updateProfile()}
                  text="Save"
                  loading={loading}
                  disabled={
                    name?.trim() === '' ||
                    (name?.trim() === initName && initAvatar === avatar)
                  }
                />
              </div>

              <div className="flex w-[350px] flex-col items-end gap-[16px] rounded-[8px] bg-Base-03 px-[20px] py-[12px]">
                <Text
                  type="subheading"
                  variant="semibold"
                  className="w-full text-Primary-Color"
                >
                  Manage account
                </Text>

                <div className="flex w-full flex-col gap-[20px]">
                  <div className="flex w-full items-center px-[4px]">
                    <div className="flex w-full flex-col">
                      <Text
                        type="body"
                        variant="medium"
                        className="text-Primary-Color"
                      >
                        Delete account
                      </Text>
                      <Text
                        type="supportText"
                        variant="regular"
                        className="text-Secondary-Color"
                      >
                        Permanently delete the account
                      </Text>
                    </div>
                    <Icon
                      className="rotate-180"
                      name="icon-fill-caret-small-left"
                      size={24}
                      color="#2D0136"
                    />
                  </div>

                  <div className="flex w-full items-center px-[4px]">
                    <div className="flex w-full flex-col">
                      <Text
                        type="body"
                        variant="medium"
                        className="text-Primary-Color"
                      >
                        Password
                      </Text>
                      <Text
                        type="supportText"
                        variant="regular"
                        className="text-Secondary-Color"
                      >
                        Change password
                      </Text>
                    </div>
                    <Icon
                      className="rotate-180"
                      name="icon-fill-caret-small-left"
                      size={24}
                      color="#2D0136"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              onClick={() => navigate(-1)}
              className="flex cursor-pointer items-center gap-[4px] rounded pr-[8px] hover:bg-Hover-2"
            >
              <Icon
                name="icon-fill-caret-small-left"
                size={24}
                color="#2D0136"
              />
              <Text
                type="subBody"
                variant="medium"
                className="leading-[24px] text-Primary-Color"
              >
                Back
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Profile
