export enum EWorkflowType {
  conversation = 'Conversation',
  autoProcessing = 'Processing',
  all = 'All',
}

export enum EWorkflowMoreOption {
  duplicate = 'Duplicate',
  delete = 'Delete',
  grantGlobalAccess = 'Grant global access',
  revokeGlobalAccess = 'Revoke global access',
}

export const WorkflowMoreOptions = [
  {
    key: EWorkflowMoreOption.duplicate,
    label: 'Duplicate',
  },
  {
    key: EWorkflowMoreOption.delete,
    label: 'Delete',
  },
]
