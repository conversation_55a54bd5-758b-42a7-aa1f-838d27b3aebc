import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import Text from '@/components/Text'
import { memo, useContext, useEffect, useState } from 'react'
import { EWorkflowType } from '../../const'
import TextAreaTag from './component/TextAreaTag'
import Button from '@/components/Button'
import { IconGenerate } from './assets/IconGenerate'
import { colors } from '@/theme'
import clsx from 'clsx'
import { GenerateWorkflowContext } from '@/providers/GenerateWorkflowProvider'
import Message from '@/components/Message'
import {
  WorkflowPromptTemplatePublic,
  WorkflowPubicResponse,
  processingWorkflowProcessingWorkflowAiGenerateApi,
  workflowGenerateConversationWorkflowApi,
  workflowGetListWorkflowPromptTemplateApi,
} from '@/apis/client'
import { HTTP_STATUS_CODE } from '@/constants'
import { MessageDialog } from '@/components/DialogMessage'
import IconButton from '@/components/IconButton'

const ModalSelectWorkflowType = ({
  onClose,
  onSelect,
  open,
}: ModalSelectWorkflowTypeProps) => {
  const [active, setActive] = useState(EWorkflowType.conversation)
  const { setGenerateText, setModel } = useContext(GenerateWorkflowContext)
  const [itemPrompt, setItemPrompt] = useState<WorkflowPromptTemplatePublic[]>(
    []
  )
  const [generateModel, setGenerateModel] = useState<
    WorkflowPubicResponse | undefined
  >()

  const [text, setText] = useState('')
  const [loadingGenerate, setLoadingGenerate] = useState(false)
  const [isGenerate, setIsGenerate] = useState(false)
  const generateHandler = async () => {
    try {
      setLoadingGenerate(true)
      const service =
        active === EWorkflowType.conversation
          ? workflowGenerateConversationWorkflowApi
          : processingWorkflowProcessingWorkflowAiGenerateApi

      const res = await service({
        body: {
          prompt: text.trim(),
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        if (active === EWorkflowType.conversation) {
          setGenerateModel(res.data as any)
        } else {
          const tData = res?.data || ({} as any)
          const data = {
            ...tData,
            data: {
              ...tData.data,
              config: {
                ...tData?.data?.config,
                nodes: [
                  tData?.data?.config?.startNode,
                  tData?.data?.config?.endNode,
                  ...(tData?.data?.config?.nodes || []),
                ],
              },
            },
          }
          setGenerateModel(data as any)
        }

        setIsGenerate(true)
      } else {
        Message.error({
          message: res?.error?.detail || 'Something went wrong!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoadingGenerate(false)
    }
  }

  const getListPrompt = async () => {
    const res = await workflowGetListWorkflowPromptTemplateApi({
      query: {
        template_workflow_type: active,
        page_number: 1,
        page_size: 99999,
      },
    })

    if (res.data?.data.data.length) {
      setItemPrompt(res.data?.data.data)
    }
  }
  useEffect(() => {
    if (open) {
      getListPrompt()
    }
  }, [active, open])

  useEffect(() => {
    if (isGenerate && open) {
      onSelect(active)
      setGenerateText(text.trim())
      setModel(generateModel)
    } else if (isGenerate && !open) {
      setGenerateText('')
      setModel(undefined)

      setIsGenerate(false)
    }
  }, [isGenerate])
  return (
    <BaseModal
      isOpen={open}
      onClose={() => {
        if (loadingGenerate || text) {
          MessageDialog.warning({
            mainMessage: 'Wanna leave?',
            subMessage: 'If continue, your changes may not be saved',
            onClick: () => {
              onClose()
              setItemPrompt([])
              setText('')
            },
          })
        } else {
          onClose()
        }
      }}
      isPureModal
    >
      <div className="relative flex h-fit w-[724px] flex-col gap-[16px] rounded-[20px] bg-Base-03 p-[12px] shadow-md">
        <Text
          type="subheading"
          variant="medium"
          className="px-[4px] text-Primary-Color"
          elementType="div"
        >
          New workflow
        </Text>

        <IconButton
          className="absolute right-[10px] top-[10px] border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={() => {
            if (loadingGenerate || text.trim()) {
              MessageDialog.warning({
                mainMessage: 'Wanna leave?',
                subMessage: 'If continue, your changes may not be saved',
                onClick: () => {
                  onClose()
                  setItemPrompt([])
                  setText('')
                },
              })
            } else {
              onClose()
            }
          }}
        />

        <div className="flex w-full flex-col items-center gap-[12px] px-[4px]">
          <div className="flex w-full flex-col gap-[12px]">
            <div
              onClick={() => {
                if (!loadingGenerate && active !== EWorkflowType.conversation) {
                  setActive(EWorkflowType.conversation)
                  setItemPrompt([])
                  setText('')
                }
              }}
              className={clsx(
                active === EWorkflowType.conversation
                  ? 'border-Base-Single-Color'
                  : 'border-border-base-icon',
                'flex w-full cursor-pointer items-center gap-[8px] rounded-[16px] border-[1px] bg-white px-[16px] py-[12px] hover:bg-Hover-Color'
              )}
            >
              <Icon
                name="message-chat-square-01"
                color={colors['Primary-Color']}
                size={32}
              />
              <div className="flex w-full flex-col gap-[4px]">
                <Text
                  type="body"
                  variant="medium"
                  className="text-Primary-Color"
                  elementType="div"
                >
                  Conversational workflow
                </Text>
                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                  elementType="div"
                >
                  Process that involves dynamic interaction between AI and
                  humans to complete tasks
                </Text>
              </div>
            </div>

            <div
              onClick={() => {
                if (
                  !loadingGenerate &&
                  active !== EWorkflowType.autoProcessing
                ) {
                  setActive(EWorkflowType.autoProcessing)
                  setItemPrompt([])
                  setText('')
                }
              }}
              className={clsx(
                active === EWorkflowType.autoProcessing
                  ? 'border-Base-Single-Color'
                  : 'border-border-base-icon',
                'flex w-full cursor-pointer items-center gap-[8px] rounded-[16px] border-[1px] bg-white px-[16px] py-[12px] hover:bg-Hover-Color'
              )}
            >
              <Icon
                name="code-square-03"
                color={colors['Primary-Color']}
                size={32}
              />
              <div className="flex w-full flex-col gap-[4px]">
                <Text
                  type="body"
                  variant="medium"
                  className="text-Primary-Color"
                  elementType="div"
                >
                  Automated workflow
                </Text>
                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                  elementType="div"
                >
                  Process that relies entirely on AI to handle tasks from start
                  to finish with minimal human involvement
                </Text>
              </div>
            </div>
          </div>

          <div className="flex w-full flex-col gap-[12px]">
            <TextAreaTag
              placeholder="Describe the workflow that you want to create"
              value={text}
              onChange={(e) => setText(e)}
              maxLength={1000}
              onBlur={() => {
                if (text) {
                  setText(text.trim())
                }
              }}
              itemPrompt={itemPrompt}
              loading={loadingGenerate}
            ></TextAreaTag>

            <div className="flex items-center gap-[12px] px-[12px]">
              <Button
                className="w-full"
                type="secondary"
                onClick={() => onSelect(active)}
                disabled={loadingGenerate}
              >
                Start from scratch
              </Button>

              <Button
                className="w-full"
                type="secondary"
                onClick={() => {
                  generateHandler()
                }}
                loading={loadingGenerate}
                disabled={!text.trim()}
                rightIcon={<IconGenerate disable={!text} />}
              >
                Generate
              </Button>
            </div>
          </div>
        </div>
      </div>
    </BaseModal>
  )
}

export default memo(ModalSelectWorkflowType)

interface ModalSelectWorkflowTypeProps {
  open: boolean
  onClose: () => void
  onSelect: (type: EWorkflowType) => void
}
