import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { memo } from 'react'

const ButtonAddWorkflow = ({ onClick }: ButtonAddWorkflowProps) => {
  return (
    <button
      onClick={onClick}
      className="flex h-[227px] w-[360px] flex-col items-center justify-center gap-[12px] rounded-[16px] border-[1px] border-dashed border-[#C6426E] p-[16px] hover:bg-Hover-Color"
    >
      <div className="h-[40px] w-[40px] rounded-[8px] bg-Hover-Color p-[4px]">
        <Icon
          name="Bold-MessagesConversation-PenNewRound"
          gradient={['#C6426E', '#642B73']}
          size={32}
        />
      </div>
      <div className="flex flex-col items-center justify-center gap-[4px]">
        <Text
          variant="semibold"
          className="text-Primary-Color"
          elementType="div"
        >
          Add new workflow
        </Text>
        <Text
          type="subBody"
          variant="regular"
          className="text-Secondary-Color"
          elementType="div"
        >
          Click here to create your own workflow
        </Text>
      </div>
    </button>
  )
}

interface ButtonAddWorkflowProps {
  onClick?: () => void
}

export default memo(ButtonAddWorkflow)
