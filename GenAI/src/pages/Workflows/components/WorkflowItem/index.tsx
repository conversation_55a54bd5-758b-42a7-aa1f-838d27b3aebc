import { WorkflowAppIntegrationsList } from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import { MessageDialog } from '@/components/DialogMessage'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import IconButton from '@/components/IconButton'
import More from '@/components/More'
import Text from '@/components/Text'
import { cn, getUrlImage } from '@/helpers'
import { useMyProfile } from '@/hooks/useMyProfile'
import { APP_NAME } from '@/pages/WorkflowIntegration/constants'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  EWorkflowMoreOption,
  EWorkflowType,
  WorkflowMoreOptions,
} from '../../const'
import WorkflowSkeleton from '../WorkflowSkeleton'

enum LoadImage {
  none = 'none',
  success = 'success',
  error = 'error',
}

const WorkflowItem = ({
  id,
  image,
  description,
  title,
  workflow_type,
  is_global_access,
  onClick,
  onDeleteWorkflow,
  appIntegrations,
  onPublish,
  published,
  marketView,
  handleCloneWorkflow,
  addedToExtension,
  onAddToExtension,
  handleDuplicateWorkflow,
  handleUpdateGlobalAccess,
}: WorkflowItemProps) => {
  const { isSuperAdmin } = useMyProfile()

  const [preview, setPreview] = useState(false)
  const [focused, setFocused] = useState(false)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )
  const [openMoreAction, setOpenMoreAction] = useState(false)

  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const handleDeleteWorkflow = useCallback(() => {
    MessageDialog.warning({
      mainMessage: 'Delete workflow?',
      subMessage:
        'If continue, this workflow will be permanently removed. This action cannot be reverted!',
      onClick: () => onDeleteWorkflow?.(),
    })
  }, [onDeleteWorkflow])

  const handlePublishWorkflow = useCallback(
    (e: any) => {
      setFocused(false)
      e.stopPropagation()

      if (published) {
        MessageDialog.confirm({
          mainMessage: 'Cancel publishing workflow?',
          subMessage:
            'If continue, this workflow template would be unlisted and hide from the template marketplace',
          onClick: () => onPublish?.(),
        })
      } else {
        onPublish?.()
      }
    },
    [onPublish, published]
  )

  const handlePreview = () => {
    setPreview(true)
    setFocused(false)
  }

  const onClickCloneWorkflow = () => {
    setFocused(false)
    handleCloneWorkflow?.()
  }

  useEffect(() => {
    if (!image) {
      setLoadImageState(LoadImage.error)
    } else {
      setLoadImageState(LoadImage.success)
    }
  }, [image])

  const publishedRightPos = useMemo(() => {
    if (addedToExtension && !published) {
      return 'right-[32px]'
    }
    return 'right-[6px]'
  }, [addedToExtension, published])

  const addedToExtensionRightPos = useMemo(() => {
    if (addedToExtension && !published) {
      return 'right-[6px]'
    }
    return 'right-[32px]'
  }, [published, addedToExtension])

  const handleSelectMoreAction = useCallback(
    ({ key }: SelectedItemProps) => {
      switch (key) {
        case EWorkflowMoreOption.duplicate:
          handleDuplicateWorkflow?.()
          break
        case EWorkflowMoreOption.delete:
          handleDeleteWorkflow()
          break
        case EWorkflowMoreOption.grantGlobalAccess:
          handleUpdateGlobalAccess?.(id!, true)
          break
        case EWorkflowMoreOption.revokeGlobalAccess:
          handleUpdateGlobalAccess?.(id!, false)
          break
      }
    },
    [handleDeleteWorkflow]
  )

  const _WorkflowMoreOptions = useMemo(() => {
    const options = [...WorkflowMoreOptions]

    if (isSuperAdmin) {
      if (is_global_access) {
        options.push({
          key: EWorkflowMoreOption.revokeGlobalAccess,
          label: EWorkflowMoreOption.revokeGlobalAccess,
        })
      } else {
        options.push({
          key: EWorkflowMoreOption.grantGlobalAccess,
          label: EWorkflowMoreOption.grantGlobalAccess,
        })
      }
    }

    return options
  }, [is_global_access])

  return (
    <>
      {loadImageState === LoadImage.none ? (
        <WorkflowSkeleton />
      ) : (
        <div
          onClick={onClick}
          className={clsx(
            'relative flex w-[360px] cursor-pointer flex-col items-center overflow-hidden rounded-[12px] bg-white shadow-base',
            {
              'h-[232px]': !marketView,
              'h-[217px]': marketView,
            }
          )}
          onMouseEnter={() => setFocused(true)}
          onMouseLeave={() => {
            setFocused(false)
          }}
        >
          {!marketView && (focused || openMoreAction) && (
            <div
              className="absolute right-2 top-3 z-10 flex flex-row-reverse gap-1"
              onClick={(e) => {
                e.stopPropagation()
              }}
            >
              <Dropdown
                overlayClassName={cn({
                  // 'w-[179px]': !is_global_access,
                  // 'w-[190px]': is_global_access,
                })}
                open={openMoreAction}
                items={_WorkflowMoreOptions}
                onSelect={handleSelectMoreAction}
                onOpenChange={(newOpen) => {
                  setOpenMoreAction(newOpen)
                }}
                classNameMenuItem="px-[12px] py-[8px]"
                // textType="body"
                // textVariant="medium"
              >
                <More
                  active={openMoreAction}
                  className={openMoreAction ? '!bg-Base-Button' : ''}
                />
              </Dropdown>
            </div>
          )}

          {marketView && focused && (
            <IconButton
              className={
                'absolute right-[8px] top-[8px] z-10 cursor-pointer duration-300'
              }
              nameIcon="Bold-ArrowsAction-MaximizeSquareMinimalistic"
              sizeIcon={24}
              hoverColor="#2D0136"
              activeColor="#2D0136"
              onClick={handlePreview}
              tooltipText="Preview"
            />
          )}
          {marketView && focused && (
            <IconButton
              className="absolute left-[162px] top-[52px] z-10 cursor-pointer duration-300"
              nameIcon="vuesax-bold-copy"
              sizeIcon={36}
              colorIcon={colors['Primary-Color']}
              hoverColor={['#642B73', '#C6426E']}
              onClick={onClickCloneWorkflow}
              tooltipText="Clone template"
            />
          )}

          {!marketView && (
            <div className="absolute left-[8px] top-[8px] z-10 flex h-[22px] items-center gap-[4px]">
              {published && (
                <IconButton
                  className={clsx(
                    'flex size-[22px] min-w-[22px] cursor-pointer items-center justify-center rounded-full bg-purple-100 p-[4px]'
                  )}
                  nameIcon="vuesax-bold-shop-add"
                  sizeIcon={14}
                  colorIcon={colors['purple']['400']}
                />
              )}

              {addedToExtension && (
                <IconButton
                  className={clsx(
                    'flex size-[22px] min-w-[22px] cursor-pointer items-center justify-center rounded-full bg-teal-100 p-[4px]'
                  )}
                  nameIcon="Extension_add"
                  sizeIcon={14}
                  colorIcon={colors['teal']['400']}
                />
              )}

              {workflow_type && (
                <IconButton
                  className={clsx(
                    'flex size-[22px] min-w-[22px] cursor-pointer items-center justify-center rounded-full bg-sky-100 p-[4px]'
                  )}
                  nameIcon={
                    workflow_type === EWorkflowType.conversation
                      ? 'vuesax-bold-message'
                      : 'vuesax-bold-document-code'
                  }
                  sizeIcon={14}
                  colorIcon={colors['sky']['400']}
                />
              )}

              {is_global_access && (
                <IconButton
                  className={clsx(
                    'flex size-[22px] min-w-[22px] cursor-pointer items-center justify-center rounded-full bg-orange-100 p-[4px]'
                  )}
                  nameIcon="vuesax-bold-lock-slash"
                  sizeIcon={14}
                  colorIcon={colors['orange']['400']}
                />
              )}
            </div>
          )}

          <div
            className={clsx(
              'relative flex min-h-[140px] w-full items-center justify-center bg-Base-03 duration-300',
              { 'bg-black/5': focused || openMoreAction }
            )}
          >
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
              <div className="flex items-center justify-center gap-[8px]">
                {!marketView && !published && focused && (
                  <IconButton
                    className={clsx(
                      'flex size-[28px] min-w-[28px] cursor-pointer items-center justify-center rounded-full bg-neutral-300 p-[4px] shadow-sm duration-300 hover:bg-white',
                      publishedRightPos
                    )}
                    nameIcon="vuesax-bold-shop-add"
                    sizeIcon={22}
                    hoverColor={colors['Primary-Color']}
                    colorIcon={colors['neutral']['400']}
                    onClick={handlePublishWorkflow}
                    tooltipText={'Publish to marketplace'}
                  />
                )}
                {!marketView && published && focused && (
                  <IconButton
                    className={clsx(
                      'flex size-[28px] min-w-[28px] cursor-pointer items-center justify-center rounded-full bg-neutral-300 p-[4px] shadow-sm duration-300 hover:bg-white',
                      publishedRightPos
                    )}
                    nameIcon="vuesax-bold-shop-remove"
                    sizeIcon={22}
                    hoverColor={colors['Primary-Color']}
                    colorIcon={colors['neutral']['400']}
                    onClick={handlePublishWorkflow}
                    tooltipText={'Cancel publishing'}
                  />
                )}
                {!marketView && !addedToExtension && focused && (
                  <IconButton
                    className={clsx(
                      'flex size-[28px] min-w-[28px] cursor-pointer items-center justify-center rounded-full bg-neutral-300 p-[4px] shadow-sm duration-300 hover:bg-white',
                      addedToExtensionRightPos
                    )}
                    nameIcon="Extension_add"
                    sizeIcon={22}
                    hoverColor={colors['Primary-Color']}
                    colorIcon={colors['neutral']['400']}
                    onClick={onAddToExtension}
                    tooltipText={'Add to extension'}
                  />
                )}
                {!marketView && addedToExtension && focused && (
                  <IconButton
                    className={clsx(
                      'flex size-[28px] min-w-[28px] cursor-pointer items-center justify-center rounded-full bg-neutral-300 p-[4px] shadow-sm duration-300 hover:bg-white',
                      addedToExtensionRightPos
                    )}
                    nameIcon="Extension_remove"
                    sizeIcon={22}
                    hoverColor={colors['Primary-Color']}
                    colorIcon={colors['neutral']['400']}
                    onClick={onAddToExtension}
                    tooltipText={'Remove from extension'}
                  />
                )}
              </div>
            </div>

            <div className="flex h-[140px] w-[292px] items-center justify-center overflow-hidden">
              {loadImageState !== LoadImage.error && !isEmpty(image) ? (
                <img
                  src={getUrlImage(image)}
                  alt="img"
                  className="h-full w-full object-contain"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              ) : (
                <img
                  src={'../assets/images/alternative-workflow-thumbnail.svg'}
                  alt="img"
                  className="min-h-[140px] w-[292px]"
                />
              )}
            </div>
          </div>
          <div className="flex h-full w-full flex-col items-center justify-center px-[12px] pb-2 pt-1">
            <Text
              type="body"
              variant="medium"
              className="mb-1 text-center text-Primary-Color"
              elementType="div"
              tooltipPosition="top"
              ellipsis
            >
              {title}
            </Text>

            <Text
              type="subBody"
              className="min-h-[36px] w-full text-center text-Secondary-Color"
              elementType="div"
              tooltipPosition="bottom"
              ellipsis
              multipleLine={2}
            >
              {description}
            </Text>
            {!marketView && (
              <div className="flex h-[15px] w-full items-center justify-center gap-4">
                {appIntegrations?.map((app, index) => {
                  return (
                    <Text
                      value={
                        app.channel_type === APP_NAME.EmbededWidget
                          ? 'Widget'
                          : APP_NAME[app.channel_type! as keyof typeof APP_NAME]
                      }
                      type="supportText"
                      variant="medium"
                      className="bg-Main-Color bg-clip-text text-transparent"
                      key={index}
                    />
                  )
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {preview && (
        <BaseModal isOpen onClose={() => {}} isPureModal>
          <div
            className="flex h-[calc(100vh-3.5rem)] w-full items-center justify-center"
            onClick={() => {
              setPreview(false)
            }}
          >
            <div className="genai-scrollbar max-h-[70%] max-w-[90%] overflow-auto rounded-[20px] bg-Base-03 shadow-md">
              <img
                src={
                  loadImageState !== LoadImage.error
                    ? getUrlImage(image)
                    : '../assets/images/alternative-workflow-thumbnail.svg'
                }
                onClick={(e) => e.stopPropagation()}
                alt="img"
              />
            </div>
          </div>
        </BaseModal>
      )}
    </>
  )
}

export default WorkflowItem

export interface WorkflowItemProps {
  id?: string
  image: string | null
  title: string
  description: string
  workflow_type?: string
  is_global_access?: boolean
  onClick: () => void
  onDeleteWorkflow?: () => Promise<void>
  appIntegrations?: WorkflowAppIntegrationsList
  published?: boolean
  onPublish?: () => void
  marketView?: boolean
  handleCloneWorkflow?: () => void
  handleDuplicateWorkflow?: () => void
  addedToExtension?: boolean
  onAddToExtension?: () => void
  handleUpdateGlobalAccess?: (id: string, is_global_access: boolean) => void
}
