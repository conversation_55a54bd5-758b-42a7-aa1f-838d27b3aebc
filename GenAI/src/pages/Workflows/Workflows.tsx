import {
  WorkflowPublicList,
  businessCategoriesReadBusinessCategoriesApi,
  processingWorkflowDuplicateWorkflow,
  workflowAddWorkflowToExtensionApi,
  workflowDeleteWorkflowApi,
  workflowPublishWorkflowApi,
  workflowReadAllStaredWorkflowsApi,
  workflowReadWorkflowsApi,
  workflowUpdateDuplicateWorkflowApi,
  workflowUpdateGlobalAccessWorkflowApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import ModalPublishWorkflow from '@/components/ModalPublishWorkflow'
import ModalPublishWorkflowWithCategory from '@/components/ModalPublishWorkflowWithCategory'
import NoDataFound from '@/components/NoDataFound'
import PageHeader from '@/components/PageHeader'
import Pagination from '@/components/Pagination'
import SearchBar from '@/components/SearchBar'
import { ISelectBaseItem } from '@/components/Select'
import { HTTP_STATUS_CODE, ID_EXTENSION, PAGE_SIZE } from '@/constants'
import { isEmpty } from 'lodash'
import { memo, useCallback, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import ButtonAddWorkflow from './components/ButtonAddWorkflow'
import ListSkeleton from './components/ListSkeleton'
import ModalSelectWorkflowType from './components/ModalSelectWorkflowType'
import WorkflowItem from './components/WorkflowItem'
import { EWorkflowType } from './const'

interface IFetchData {
  tQuery?: string
  tPage?: number
  tTotalPage?: number
}
interface IPagination {
  query: string
  page: number
  totalPage: number
}

interface IWorkflowType {
  id: string
  name: string
  value: EWorkflowType
}

const workflowButtonAddNew = 'workflowAddNew'
const CREATE_ITEM = {
  id: workflowButtonAddNew,
  name: '',
  description: '',
  thumbnail: '',
  workflow_type: EWorkflowType.conversation,
}

const LIST_TYPES: IWorkflowType[] = [
  {
    id: EWorkflowType.all as string,
    name: 'Type',
    value: EWorkflowType.all,
  },
  {
    id: EWorkflowType.conversation as string,
    name: 'Conversational',
    value: EWorkflowType.conversation,
  },
  {
    id: EWorkflowType.autoProcessing as string,
    name: 'Automated',
    value: EWorkflowType.autoProcessing,
  },
]

const OptionAllCategories = {
  id: 'all',
  name: 'Category',
}

const UNCATEGORIZED_ID = '00000000-0000-0000-0000-000000000000'
const OptionUncategorized = {
  id: UNCATEGORIZED_ID,
  name: 'Uncategorized',
}

const Workflows = () => {
  const navigate = useNavigate()
  const [modalSelectWorkflowTypeVisible, setModalSelectWorkflowTypeVisible] =
    useState(false)
  const [initializing, setInitializing] = useState(true)
  const [firstTimeLoadData, setFirstTimeLoadData] = useState(true)
  const [pagination, setPagination] = useState<IPagination>({
    query: '',
    page: 1,
    totalPage: 1,
  })
  const [searchKey, setSearchKey] = useState('')
  const [isFirstLoad, setIsFirstLoad] = useState(true)
  const [data, setData] = useState<WorkflowPublicList[]>([])
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('')
  const [isSelectedProcessingWorkflow, setIsSelectedProcessingWorkflow] =
    useState(false)
  const [selectedWorkflowWithCategory, setSelectedWorkflowWithCategory] =
    useState<string>('')
  const [type, setType] = useState(LIST_TYPES[0])
  const [categories, setCategories] = useState<ISelectBaseItem[]>([
    OptionUncategorized,
  ])
  const [selectedCategory, setSelectedCategory] = useState<ISelectBaseItem[]>([
    OptionAllCategories,
  ])

  const { query, page, totalPage } = pagination

  const fetchData = useCallback(
    async ({ tQuery = '', tPage = 1 }: IFetchData) => {
      try {
        setFirstTimeLoadData(true)

        const categoryId = selectedCategory
          .filter((item) => item.id !== OptionAllCategories.id)
          .map((item) => item.id)
          .join(',')

        const workflowType = type.id === EWorkflowType.all ? undefined : type.id

        const res = await workflowReadWorkflowsApi({
          query: {
            page_number: tPage,
            page_size: PAGE_SIZE.LARGE,
            name: tQuery || undefined,
            workflow_type: workflowType as any,
            category_ids: categoryId.length ? categoryId : undefined,
          },
        })
        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          const arr: WorkflowPublicList[] = res?.data?.data?.data || []
          if (
            tQuery.length > 0 ||
            type.id !== EWorkflowType.all ||
            selectedCategory[0].id !== OptionAllCategories.id
          ) {
            setData(arr)
          } else {
            if (arr.length > 0) {
              setData([CREATE_ITEM, ...arr])
            } else {
              setData(arr)
            }
          }
          setPagination({
            query: tQuery,
            page: tPage,
            totalPage: res?.data?.data?.total_pages || 1,
          })
        }
      } catch (error) {
        Message.error({ message: 'Something wrong, please retry!' })
        setData([CREATE_ITEM])
      } finally {
        setFirstTimeLoadData(false)
        if (isFirstLoad) {
          setIsFirstLoad(false)
        }
      }
    },
    [type, selectedCategory, isFirstLoad]
  )

  const handleDeleteWorkflow = useCallback(
    async (workflowId: string) => {
      if (!workflowId) {
        return
      }

      const response = await workflowDeleteWorkflowApi({
        path: {
          workflow_id: workflowId,
        },
      })

      if (response.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully deleted workflow' })

        const filterDataByWFId = data?.filter(
          (item) => item.id !== workflowButtonAddNew
        )

        if (filterDataByWFId?.length <= 1 && page > 1) {
          fetchData({
            tQuery: searchKey,
            tPage: page - 1,
          })
        } else {
          fetchData({
            tQuery: searchKey,
            tPage: page,
          })
        }
      } else {
        Message.error({
          message: 'Something went wrong!',
        })
      }
    },
    [searchKey, data, page, fetchData]
  )

  useEffect(() => {
    if (!isFirstLoad) {
      fetchData({
        tQuery: searchKey,
        tPage: 1,
      })
    }
  }, [searchKey, type, selectedCategory])

  useEffect(() => {
    setInitializing(true)
    setTimeout(() => {
      setInitializing(false)
    }, 750)
    fetchData({})
  }, [])

  const cancelPublishWorkflow = async (id: string) => {
    try {
      const res = await workflowPublishWorkflowApi({
        path: {
          workflow_id: id,
        },
        body: {
          is_published: false,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully cancelled publishing' })
        setData((prev) => {
          return prev.map((item) => {
            if (item.id === id) {
              return {
                ...item,
                is_public: false,
              }
            }
            return item
          })
        })
      } else if (res.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        Message.error({
          message: res?.error?.detail ?? 'Something went wrong!',
        })
      }
    } catch (error) {
      Message.error({
        message: 'Something went wrong!',
      })
    }
  }

  const publishWorkflowHandler = (id: string, isProcessing: boolean) => {
    setSelectedWorkflow(id)
    if (isProcessing) {
      setSelectedWorkflowWithCategory(id)
    }
    setIsSelectedProcessingWorkflow(isProcessing)
  }

  const handlePublishingSuccess = (id: string) => {
    setData((prev) => {
      return prev.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            is_public: true,
          }
        }
        return item
      })
    })
    setSelectedWorkflowWithCategory('')
    setSelectedWorkflow('')
  }

  const handleAddToExtension = async (id: string) => {
    try {
      const res = await workflowAddWorkflowToExtensionApi({
        path: {
          workflow_id: id,
        },
        body: {
          is_extension_added: true,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully added workflow to extension' })
        setData((prev) => {
          return prev.map((item) => {
            if (item.id === id) {
              return {
                ...item,
                is_extension_added: true,
              }
            }
            return item
          })
        })
      } else if (res.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        Message.error({
          message: res?.error?.detail ?? 'Something went wrong!',
        })
      }
    } catch (error) {
      Message.error({
        message: 'Something went wrong!',
      })
    }
  }

  const handleRemoveFromExtension = async (id: string) => {
    try {
      const res = await workflowAddWorkflowToExtensionApi({
        path: {
          workflow_id: id,
        },
        body: {
          is_extension_added: false,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({
          message: 'Successfully remove workflow from extension',
        })
        setData((prev) => {
          return prev.map((item) => {
            if (item.id === id) {
              return {
                ...item,
                is_extension_added: false,
              }
            }
            return item
          })
        })
      } else if (res.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        Message.error({
          message: res?.error?.detail ?? 'Something went wrong!',
        })
      }
    } catch (error) {
      Message.error({
        message: 'Something went wrong!',
      })
    }
  }

  const handleDuplicateWorkflow = useCallback(
    async (id: string, type: EWorkflowType) => {
      try {
        const service =
          type === EWorkflowType.conversation
            ? workflowUpdateDuplicateWorkflowApi
            : processingWorkflowDuplicateWorkflow
        const res = await service({
          path: {
            workflow_id: id,
          },
        })
        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          Message.success({ message: 'Successfully duplicated workflow' })
          fetchData({
            tQuery: searchKey,
            tPage: 1,
          })
        } else {
          Message.error({
            message: 'Something went wrong!',
          })
        }
      } catch (error) {
        Message.error({
          message: 'Something went wrong!',
        })
      }
    },
    [searchKey, fetchData]
  )

  const fetAllCategories = async () => {
    try {
      const res = await businessCategoriesReadBusinessCategoriesApi()
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const arr: ISelectBaseItem[] = res?.data?.data?.data || []
        setCategories([OptionAllCategories, OptionUncategorized, ...arr])
      } else {
        setCategories([OptionAllCategories, OptionUncategorized])
      }
    } catch (error) {
      setCategories([OptionAllCategories, OptionUncategorized])
    }
  }

  useEffect(() => {
    fetAllCategories()
  }, [])

  const handleUpdateGlobalAccess = async (
    id: string,
    isGlobalAccess: boolean
  ) => {
    const res = await workflowUpdateGlobalAccessWorkflowApi({
      path: {
        workflow_id: id,
      },
      body: {
        is_global_access: isGlobalAccess,
      },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS) {
      fetchData({
        tQuery: searchKey,
        tPage: page,
      })

      Message.success({ message: res.data?.message ?? '' })
    } else {
      Message.error({ message: res.error?.detail ?? 'Something went wrong!' })
    }
  }

  return (
    <Layout>
      <div className="flex h-full flex-col overflow-hidden">
        <div className="flex w-full items-center justify-between">
          <PageHeader
            breadcrumbPaths={[{ name: 'Workflows' }]}
            title={
              'Configure your own workflow using AI-Powered worker to enhance business\noperations and user experiences'
            }
          />

          <div className="flex flex-row justify-end gap-2">
            <SearchBar
              onSearch={(value) => {
                setSearchKey(value)
              }}
              hasFilter
              filterOptions={[
                {
                  contentClassName: '!min-w-[123px]',
                  overlayClassName: '[--anchor-gap:12px]',
                  selected: type,
                  data: LIST_TYPES.filter(
                    (item) => item.id !== EWorkflowType.all
                  ),
                  onChangeSelectedValue: (value) => {
                    if (value) {
                      setType(value as IWorkflowType)
                    } else {
                      setType(LIST_TYPES[0])
                    }
                  },
                },
                {
                  contentClassName: '!w-full',
                  overlayClassName: '[--anchor-gap:12px] ml-3',
                  selected: selectedCategory[0],
                  selectedMultiple: selectedCategory,
                  multipleChoice: true,
                  allowClear: selectedCategory[0].id !== OptionAllCategories.id,
                  data: categories.filter(
                    (item) => item.id !== OptionAllCategories.id
                  ),
                  onClear: () => {
                    if (selectedCategory[0].id !== OptionAllCategories.id) {
                      setSelectedCategory([OptionAllCategories])
                    }
                  },
                  onChangeSelectedValue: (value) => {
                    const isSelected = selectedCategory.find(
                      (item) => item.id === value.id
                    )
                    if (isSelected) {
                      const newVal = selectedCategory.filter(
                        (item) => item.id !== value.id
                      )

                      if (newVal.length === 0) {
                        setSelectedCategory([OptionAllCategories])
                      } else {
                        setSelectedCategory(newVal)
                      }
                    } else {
                      setSelectedCategory(
                        [...selectedCategory, value].filter(
                          (item) => item.id !== OptionAllCategories.id
                        )
                      )
                    }
                  },
                },
              ]}
            />
          </div>
        </div>

        <div className="mt-8 flex h-full flex-col overflow-hidden">
          <div className="genai-scrollbar h-full overflow-y-auto overflow-x-hidden">
            {(initializing || firstTimeLoadData) && (
              <ListSkeleton number={15} />
            )}

            {!initializing &&
              !firstTimeLoadData &&
              data.length === 0 &&
              query.length === 0 &&
              type.id === EWorkflowType.all &&
              selectedCategory[0].id === OptionAllCategories.id && (
                <div className="flex flex-col items-center">
                  <EmptyData
                    size="medium"
                    type="03"
                    className="mb-[24px] mt-[80px]"
                  />
                  <Button
                    type="primary"
                    size="medium"
                    onClick={() => setModalSelectWorkflowTypeVisible(true)}
                    className="w-[205px]"
                    text="New Workflow"
                    leftIcon={
                      <Icon
                        name="vuesax-outline-add"
                        color="#ffffff"
                        size={20}
                      />
                    }
                  />
                </div>
              )}

            {!initializing &&
              !firstTimeLoadData &&
              data.length === 0 &&
              (query.length !== 0 ||
                type.id !== EWorkflowType.all ||
                selectedCategory[0].id !== OptionAllCategories.id) && (
                <NoDataFound className="mt-[44px]" />
              )}
            {!initializing && !firstTimeLoadData && !isEmpty(data) && (
              <div className="flex flex-wrap gap-x-[20px] gap-y-[20px] pb-[1px]">
                {data.map((item, index) => {
                  if (item.id === workflowButtonAddNew) {
                    return (
                      <ButtonAddWorkflow
                        key={index}
                        onClick={() => setModalSelectWorkflowTypeVisible(true)}
                      />
                    )
                  }
                  return (
                    <WorkflowItem
                      id={item.id}
                      workflow_type={item.workflow_type}
                      is_global_access={item.is_global_access}
                      title={item.name}
                      description={item.description}
                      image={item.thumbnail}
                      onClick={() => {
                        if (item.workflow_type === EWorkflowType.conversation) {
                          navigate(`/workflows/my-workflows/${item.id}`)
                        } else {
                          navigate(
                            `/workflows/my-auto-processing-workflows/${item.id}`
                          )
                        }
                      }}
                      onDeleteWorkflow={() => handleDeleteWorkflow(item.id)}
                      appIntegrations={item.app_integrations!}
                      key={item.id}
                      published={item.is_public}
                      onPublish={() => {
                        if (item.is_public) {
                          cancelPublishWorkflow(item.id)
                        } else {
                          publishWorkflowHandler(
                            item.id,
                            item.workflow_type === EWorkflowType.autoProcessing
                          )
                        }
                      }}
                      addedToExtension={item.is_extension_added}
                      onAddToExtension={async () => {
                        if (item.is_extension_added) {
                          handleRemoveFromExtension(item.id)
                          if (
                            typeof chrome !== 'undefined' &&
                            chrome &&
                            chrome?.runtime
                          ) {
                            chrome?.runtime?.sendMessage(ID_EXTENSION, {
                              action: 'remove-menu-context',
                              item: item,
                            })
                          }
                        } else {
                          handleAddToExtension(item.id)

                          setTimeout(async () => {
                            const res =
                              await workflowReadAllStaredWorkflowsApi()

                            if (res.data?.data.length) {
                              const isStared = res.data.data.find(
                                (n) => n.id === item.id
                              )

                              if (isStared) {
                                if (
                                  typeof chrome !== 'undefined' &&
                                  chrome &&
                                  chrome?.runtime
                                ) {
                                  chrome?.runtime?.sendMessage(ID_EXTENSION, {
                                    action: 'add-menu-context',
                                    item: item,
                                  })
                                }
                              }
                            }
                          }, 2000)
                        }
                      }}
                      handleDuplicateWorkflow={() =>
                        handleDuplicateWorkflow(
                          item.id,
                          item.workflow_type as EWorkflowType
                        )
                      }
                      handleUpdateGlobalAccess={handleUpdateGlobalAccess}
                    />
                  )
                })}
              </div>
            )}
          </div>

          {totalPage > 1 && (
            <div className="mt-[12px] flex w-full flex-row justify-end">
              <Pagination
                onChangePage={(e) => {
                  fetchData({
                    tQuery: query,
                    tPage: e,
                  })
                }}
                page={page}
                totalPage={pagination.totalPage ?? 0}
                className="w-[220px]"
              />
            </div>
          )}
        </div>
      </div>
      <ModalPublishWorkflow
        isOpen={!isSelectedProcessingWorkflow && !!selectedWorkflow}
        id={selectedWorkflow}
        onClose={() => setSelectedWorkflow('')}
        onConfirm={() => {
          setSelectedWorkflowWithCategory(selectedWorkflow)
        }}
      />
      <ModalPublishWorkflowWithCategory
        isOpen={!!selectedWorkflowWithCategory}
        id={selectedWorkflow}
        setIsOpen={() => setSelectedWorkflowWithCategory('')}
        handlePublishingSuccess={() => {
          handlePublishingSuccess(selectedWorkflow)
        }}
        handlePublishingError={() => {
          setSelectedWorkflowWithCategory('')
          setSelectedWorkflow('')
        }}
      />
      {modalSelectWorkflowTypeVisible && (
        <ModalSelectWorkflowType
          open={modalSelectWorkflowTypeVisible}
          onClose={() => setModalSelectWorkflowTypeVisible(false)}
          onSelect={(type) => {
            if (type === EWorkflowType.conversation) {
              navigate('/workflows/my-workflows/new')
            } else {
              navigate('/workflows/my-workflows/new-auto-processing')
            }
          }}
        />
      )}
    </Layout>
  )
}

export default memo(Workflows)
