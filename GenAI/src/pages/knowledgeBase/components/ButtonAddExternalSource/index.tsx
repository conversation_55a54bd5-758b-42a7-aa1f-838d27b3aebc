import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { memo } from 'react'

const ButtonAddExternalSource = ({ onClick }: ButtonAddExternalSourceProps) => {
  return (
    <button
      onClick={onClick}
      className="flex h-[152px] w-72 flex-col items-center justify-center gap-2 rounded-[16px] border-[1px] border-dashed border-[#C6426E] px-4 py-3 duration-300 hover:bg-Hover-Color"
    >
      <div className="bg-Neutral-150 h-[40px] w-[40px] rounded-[8px] p-[4px]">
        <Icon
          name="Bold-MessagesConversation-PenNewRound"
          gradient={['#C6426E', '#642B73']}
          size={32}
        />
      </div>
      <div className="flex flex-col gap-1">
        <Text
          type="body"
          variant="bold"
          className="text-center text-Primary-Color"
          elementType="div"
        >
          Add new data source
        </Text>
        <Text type="subBody" className="text-center text-Secondary-Color">
          Click here to create new external source
        </Text>
      </div>
    </button>
  )
}

interface ButtonAddExternalSourceProps {
  onClick?: () => void
}

export default memo(ButtonAddExternalSource)
