import Icon from '@/assets/icon/Icon'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { formatBytes, getUrlImage } from '@/helpers'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { KnowledgeBaseActionItems, KnowledgeBaseActions } from '../../types'

const bgColors = [
  'bg-slate-50',
  'bg-gray-50',
  'bg-zinc-50',
  'bg-neutral-50',
  'bg-stone-50',
  'bg-red-50',
  'bg-orange-50',
  'bg-amber-50',
  'bg-yellow-50',
  'bg-lime-50',
  'bg-green-50',
  'bg-emerald-50',
  'bg-teal-50',
  'bg-cyan-50',
  'bg-sky-50',
  'bg-blue-50',
  'bg-indigo-50',
  'bg-violet-50',
  'bg-purple-50',
  'bg-fuchsia-50',
  'bg-pink-50',
  'bg-rose-50',
]

enum LoadImage {
  none = 'none',
  success = 'success',
  error = 'error',
}

const KnowlegedBaseItem = ({
  itemId,
  image,
  number,
  title,
  totalSize,
  onDelete,
  onClick,
  onClickItem,
}: KnowlegedBaseProps) => {
  const [isHover, setIsHover] = useState(false)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )
  const [openWorkerAction, setOpenWorkerAction] = useState(false)

  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const iconBg = useMemo(() => {
    if (!image || loadImageState === LoadImage.error) {
      return 'bg-Background-Color'
    }
    const randomIndex = Math.floor(Math.random() * bgColors.length)
    return bgColors[randomIndex]
  }, [image, loadImageState])

  useEffect(() => {
    if (!image) {
      setLoadImageState(LoadImage.error)
    } else {
      setLoadImageState(LoadImage.success)
    }
  }, [image])

  const displayNumber = useMemo(() => {
    if (number < 2) {
      return `${number} file`
    }
    return `${number} files`
  }, [number])

  const handleSelectWorkerAction = useCallback(({ key }: SelectedItemProps) => {
    switch (key) {
      case KnowledgeBaseActions.EDIT:
        onClick(itemId)
        break
      case KnowledgeBaseActions.DELETE:
        onDelete(itemId)
        break
    }
  }, [])

  return (
    <div
      className="relative flex h-[120px] w-[205px] cursor-pointer flex-col items-center rounded-[16px] bg-white p-[16px] pt-[8px] shadow hover:bg-Hover-Color"
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      onClick={() => {
        onClickItem?.(itemId)
      }}
    >
      <div
        className={`flex h-[48px] w-[48px] flex-col items-center justify-center rounded-[24px] ${iconBg}`}
      >
        {loadImageState !== LoadImage.error && !isEmpty(image) ? (
          <img
            src={getUrlImage(image)}
            alt="img"
            className="h-[32px] w-[32px] rounded-full"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : (
          <Icon
            name="database-01"
            size={32}
            gradient={['#642B734D', '#C6426E4D']}
          />
        )}
      </div>
      <Text
        type="body"
        variant="semibold"
        className="mt-[8px] !w-fit max-w-[173px] text-center text-Primary-Color"
        elementType="div"
        tooltipPosition="left"
        ellipsis
      >
        {title}
      </Text>

      <div className="mt-[4px] flex flex-row items-center gap-[4px]">
        <Text
          type="supportText"
          variant="regular"
          className="text-Secondary-Color"
          elementType="div"
        >
          {displayNumber}
        </Text>

        <div className="h-[4px] w-[4px] min-w-[4px] rounded-full bg-Main-04" />

        <Text
          type="supportText"
          variant="medium"
          className="bg-Main-Color bg-clip-text text-transparent"
          elementType="div"
        >
          {formatBytes({ bytes: totalSize })}
        </Text>
      </div>

      {(isHover || openWorkerAction) && (
        <div className="absolute right-2 top-3 flex flex-row-reverse gap-1">
          <Dropdown
            // overlayClassName="w-[71px]"
            open={openWorkerAction}
            items={KnowledgeBaseActionItems}
            onSelect={handleSelectWorkerAction}
            onOpenChange={(newOpen) => {
              setOpenWorkerAction(newOpen)
            }}
          >
            <More active={openWorkerAction} />
          </Dropdown>
        </div>
      )}
      {loadImageState === LoadImage.none && (
        <div className="right-0, absolute bottom-0 left-0 top-0 flex h-[120px] w-[205px] flex-col rounded-[16px] bg-white p-[16px] pt-[8px]">
          <div className="flex animate-pulse flex-col items-center">
            <div className="h-[48px] w-[48px] rounded-[24px] bg-slate-200"></div>
            <div className="mt-[8px] h-[20px] w-[163px] rounded bg-slate-200"></div>
            <div className="mt-[4px] h-[15px] w-[80px] rounded bg-slate-200"></div>
          </div>
        </div>
      )}
    </div>
  )
}

export default KnowlegedBaseItem

export interface KnowlegedBaseProps {
  itemId: string | null
  image: string | null
  title: string | null
  number: number
  totalSize: number
  onDelete: (id: string | null) => void
  onClick: (id: string | null) => void
  onClickItem?: (id: string | null) => void
}
