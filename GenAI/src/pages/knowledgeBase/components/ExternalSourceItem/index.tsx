import { KBExternalEmbeddingStatusTypes } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { formatBytes, getUrlImage } from '@/helpers'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isEmpty } from 'lodash'
import { useEffect, useMemo, useState } from 'react'
import { LoadImage, bgColors } from '../../constants'

interface ExternalSourceItemProps {
  itemId: string | null
  image?: string | null
  displayName: string | null
  description: string | null
  isSensitive: boolean | null
  totalSize: number
  embeddingStatus: KBExternalEmbeddingStatusTypes | string | null
  onDelete: (id: string | null) => void
  onClickItem?: (id: string | null) => void
}

const ExternalSourceItem = ({
  itemId,
  image,
  totalSize,
  displayName,
  description = 'Sample source description',
  isSensitive = true,
  embeddingStatus = KBExternalEmbeddingStatusTypes.NEW,
  onDelete,
  onClickItem,
}: ExternalSourceItemProps) => {
  const [isHover, setIsHover] = useState(false)
  const [loadImageState, setLoadImageState] = useState<LoadImage>(
    LoadImage.none
  )
  const handleImageLoad = () => {
    setLoadImageState(LoadImage.success)
  }

  const handleImageError = () => {
    setLoadImageState(LoadImage.error)
  }

  const iconBg = useMemo(() => {
    if (!image || loadImageState === LoadImage.error) {
      return 'bg-Background-Color'
    }
    const randomIndex = Math.floor(Math.random() * bgColors.length)
    return bgColors[randomIndex]
  }, [image, loadImageState])

  useEffect(() => {
    if (!image) {
      setLoadImageState(LoadImage.error)
    } else {
      setLoadImageState(LoadImage.success)
    }
  }, [image])

  const displayBytes = useMemo(() => {
    if (
      embeddingStatus === KBExternalEmbeddingStatusTypes.NEW ||
      embeddingStatus === KBExternalEmbeddingStatusTypes.PROCESSING
    ) {
      return (
        <div className="flex items-center gap-[1px]">
          Syncing data
          <Icon name="Bold-Typing-Ball-Animation" size={9} />
        </div>
      )
    } else if (
      embeddingStatus === KBExternalEmbeddingStatusTypes.QUOTA_EXCEEDED
    ) {
      return 'Limit exceeded'
    }
    return formatBytes({ bytes: totalSize })
  }, [totalSize, embeddingStatus])

  return (
    <div
      className="relative flex h-[152px] w-72 cursor-pointer flex-col items-center rounded-xl bg-white px-[12px] py-[8px] shadow duration-300 hover:bg-Hover-Color"
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      onClick={() => {
        onClickItem?.(itemId)
      }}
    >
      {isSensitive && (
        <div className="absolute left-2 top-2">
          <Icon
            name="Bold-Security-ShieldKeyhole"
            size={20}
            gradient={['#642B734D', '#C6426E4D']}
          />
        </div>
      )}

      <IconButton
        className={clsx(
          'absolute right-[8px] top-[8px] cursor-pointer duration-300',
          {
            'invisible opacity-0': !isHover,
          }
        )}
        nameIcon="Customize-Delete"
        sizeIcon={24}
        colorIcon={colors['border-base-icon']}
        hoverColor={['#642B73', '#C6426E']}
        onClick={() => onDelete?.(itemId)}
      />
      <div
        className={`flex h-[48px] w-[48px] items-center justify-center rounded-full ${iconBg}`}
      >
        {loadImageState !== LoadImage.error && !isEmpty(image) ? (
          <img
            src={getUrlImage(image)}
            alt="img"
            className="h-[32px] w-[32px] rounded-full"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : (
          <Icon
            name="database-01"
            size={32}
            gradient={['#642B734D', '#C6426E4D']}
          />
        )}
      </div>
      <Text
        type="body"
        variant="medium"
        className="mt-[8px] overflow-hidden break-words text-center text-Primary-Color"
        elementType="div"
        ellipsis
      >
        {displayName}
      </Text>
      <Text
        type="subBody"
        className="mt-[4px] min-h-[36px] overflow-hidden break-words text-center text-Secondary-Color"
        elementType="div"
        multipleLine={2}
        tooltipPosition="bottom"
        ellipsis
      >
        {description}
      </Text>
      <Text
        type="supportText"
        variant="medium"
        className={clsx(
          'mt-[4px]',
          embeddingStatus === KBExternalEmbeddingStatusTypes.QUOTA_EXCEEDED
            ? 'text-red-800'
            : 'bg-Main-Color bg-clip-text text-transparent'
        )}
        elementType="div"
      >
        {displayBytes}
      </Text>
      {loadImageState === LoadImage.none && (
        <div className="right-0, absolute bottom-0 left-0 top-0 flex h-[120px] w-[205px] flex-col rounded-[16px] bg-white p-[16px] pt-[8px]">
          <div className="flex animate-pulse flex-col items-center">
            <div className="h-[48px] w-[48px] rounded-[24px] bg-slate-200"></div>
            <div className="mt-[8px] h-[20px] w-[163px] rounded bg-slate-200"></div>
            <div className="mt-[4px] h-[15px] w-[80px] rounded bg-slate-200"></div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ExternalSourceItem
