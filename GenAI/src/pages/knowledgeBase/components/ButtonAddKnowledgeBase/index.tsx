import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { memo } from 'react'

const ButtonAddKnowledgeBase = ({ onClick }: ButtonAddKnowledgeBaseProps) => {
  return (
    <button
      onClick={onClick}
      className="flex h-[120px] w-[205px] flex-col items-center justify-center rounded-[16px] border-[1px] border-dashed border-[#C6426E] p-[16px] hover:bg-Hover-Color"
    >
      <div className="h-[40px] w-[40px] rounded-[8px] bg-Hover-Color p-[4px]">
        <Icon
          name="Bold-MessagesConversation-PenNewRound"
          gradient={['#C6426E', '#642B73']}
          size={32}
        />
      </div>
      <Text
        type="body"
        variant="bold"
        className="mt-[8px] w-[173px] max-w-[173px] overflow-hidden truncate text-center text-Primary-Color"
        elementType="div"
      >
        Add new directory
      </Text>
    </button>
  )
}

interface ButtonAddKnowledgeBaseProps {
  onClick?: () => void
}

export default memo(ButtonAddKnowledgeBase)
