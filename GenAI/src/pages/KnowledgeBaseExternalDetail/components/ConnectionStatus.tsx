import { twMerge } from 'tailwind-merge'
import IconDataTable from '../assets/Custom-Data-table.svg?react'
import Text from '@/components/Text'
import clsx from 'clsx'

interface Props {
  className?: string
  isFailed?: boolean
  error?: string
}
const ConnectionStatus = ({ className, isFailed, error }: Props) => {
  return (
    <div
      className={twMerge(
        clsx(
          'flex w-full flex-col items-center justify-center gap-[8px] rounded-[8px] border border-Highlight-Border px-[20px] py-[8px]',
          {
            'border-Error-Color': isFailed,
          },
          className
        )
      )}
    >
      <div
        className={clsx(
          'flex h-[36px] w-[36px] min-w-[36px] items-center justify-center rounded-lg',
          'bg-gradient-to-tr from-[#642B731A] to-[#C6426E1A]'
        )}
      >
        <IconDataTable />
      </div>

      <div className="flex flex-col items-center">
        <Text
          type="subBody"
          variant="semibold"
          className={twMerge(
            clsx('text-Primary-Color', { 'text-Error-Color': isFailed })
          )}
        >
          {isFailed ? 'Failed to connect' : 'Establishing connection...'}
        </Text>

        <Text
          type="supportText"
          variant="regular"
          className={twMerge(
            clsx('text-Secondary-Color', { 'text-Primary-Color': isFailed })
          )}
        >
          {isFailed
            ? (error ?? 'Cannot connect to your database, please retry')
            : 'Connect to your database and get all created Views'}
        </Text>
      </div>
    </div>
  )
}

export default ConnectionStatus
