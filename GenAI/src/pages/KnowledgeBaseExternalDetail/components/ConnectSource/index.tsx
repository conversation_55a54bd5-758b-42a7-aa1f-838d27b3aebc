import { GetListViewsResult, KBExternalPublic } from '@/apis/client'
import IconButton from '@/components/IconButton'
import { InputWithRef } from '@/components/Input'
import Text from '@/components/Text'
import Toggle from '@/components/Toggle'
import Tooltip from '@/components/Tooltip'
import { colors } from '@/theme'
import { useEffect, useRef, useState } from 'react'
import IconPostgre from '../../assets/icon_postgre.svg?react'
import {
  EConnectionStatus,
  IConnectionStatus,
  ISelectedTable,
} from '../../consts'
import ConnectionStatus from '../ConnectionStatus'
import ListTable from '../ListTable'

interface Props {
  connectStr: string
  setConnectStr: React.Dispatch<React.SetStateAction<string>>
  isSensitive: boolean
  setIsSensitive: React.Dispatch<React.SetStateAction<boolean>>
  handleConnectSource: (stringConnect: string) => Promise<void>

  connectionStatus?: IConnectionStatus
  setConnectionStatus: React.Dispatch<
    React.SetStateAction<IConnectionStatus | undefined>
  >

  listView?: ISelectedTable[]
  selectedTable?: GetListViewsResult
  setSelectedTable?: React.Dispatch<
    React.SetStateAction<GetListViewsResult | undefined>
  >
  setIsChanged: React.Dispatch<React.SetStateAction<boolean>>
  setIsChangedSelectedTable?: React.Dispatch<React.SetStateAction<boolean>>
  errorPostgres?: string
  isCreateNew?: boolean
  latestInitData: React.MutableRefObject<KBExternalPublic | undefined>
}

const ConnectSource = ({
  connectStr,
  setConnectStr,
  isSensitive,
  setIsSensitive,
  handleConnectSource,
  connectionStatus,
  listView,
  selectedTable,
  setSelectedTable,
  setIsChanged,
  setIsChangedSelectedTable,
  errorPostgres,
  isCreateNew,
  latestInitData,
}: Props) => {
  const inputRef = useRef<HTMLInputElement>(null)

  const [hideConnectIcon, setHideConnectIcon] = useState(false)

  const isUpdateAndConnectError =
    (!isCreateNew && connectionStatus?.status === EConnectionStatus.ERROR) ||
    (!isCreateNew && Boolean(errorPostgres))

  useEffect(() => {
    if (isUpdateAndConnectError || errorPostgres) {
      setHideConnectIcon(true)
    } else {
      setHideConnectIcon(false)
    }
  }, [isUpdateAndConnectError, errorPostgres])

  const handleChangeConnectStr = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConnectStr(e.target.value)
  }

  const handleChangeToggle = (checked: boolean) => {
    setIsSensitive(checked)
    setIsChanged(true)
  }

  const handleSelectTable = (view: GetListViewsResult) => {
    setIsChanged(true)
    setIsChangedSelectedTable?.(true)

    if (view.view_name === selectedTable?.view_name) {
      setSelectedTable?.(undefined)
      return
    }

    setSelectedTable?.(view)
  }

  const getInputError = () => {
    if (errorPostgres) {
      return (
        <Text type="supportText" variant="regular" className="text-Error-Color">
          {'Format must be '}
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            {
              'postgresql://<username>:<password>@<host>:<port>/<dbname>?current_schema=<schema_name>'
            }
          </Text>
        </Text>
      )
    }

    if (!isCreateNew && connectionStatus?.status === EConnectionStatus.ERROR) {
      return connectionStatus?.error
    }

    return
  }

  const getIsError = () => {
    if (errorPostgres) {
      return true
    }

    if (!isCreateNew && connectionStatus?.status === EConnectionStatus.ERROR) {
      return true
    }

    return false
  }

  return (
    <div className="flex w-full flex-col gap-[8px]">
      <Text type="body" variant="semibold" className="text-Primary-Color">
        Data Access
      </Text>

      <div className="flex w-full flex-col rounded-lg border border-border-base-icon bg-white px-[16px] pb-[16px] pt-[12px]">
        <div className="flex w-full flex-row gap-[8px]">
          <div className="mt-[5px] flex h-[58px] w-[58px] items-center justify-center">
            <IconPostgre />
          </div>

          <div className="flex w-full flex-col gap-1">
            <div className="flex h-[21px] items-center px-[4px]">
              <Text
                type="subBody"
                variant="medium"
                className="text-Tertiary-Color"
              >
                Source connection
              </Text>
            </div>

            <InputWithRef
              ref={inputRef}
              placeholder="Connection string must be: postgresql://<username>:<password>@<host>:<port>/<dbname>?current_schema=<schema_name>"
              className="w-full"
              suffix={
                !hideConnectIcon && (
                  <div
                    className="h-[16px] w-[16px] items-center justify-center"
                    onClick={(e) => {
                      if (!connectStr.trim()) {
                        e.preventDefault()
                        e.stopPropagation()
                        return
                      }
                    }}
                  >
                    <Tooltip text="Connect" disabled={!connectStr.trim()}>
                      <IconButton
                        disabled={!connectStr.trim()}
                        disableColor={colors.neutral[300]}
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          console.log('Connect 3')

                          inputRef.current?.blur()
                        }}
                        nameIcon="Bold-Arrows-Refresh"
                        sizeIcon={16}
                        hoverColor={['#642B73', '#C6426E']}
                        colorIcon={colors['Primary-Color']}
                      />
                    </Tooltip>
                  </div>
                )
              }
              isError={getIsError()}
              errorText={getInputError()}
              maxLength={4096}
              value={connectStr}
              onChange={handleChangeConnectStr}
              onBlur={() => {
                if (!isCreateNew) {
                  const s = connectStr.trim()
                    ? connectStr.trim()
                    : (latestInitData.current?.source_connection_string ?? '')
                  setConnectStr(s)
                  handleConnectSource(s)
                } else {
                  setConnectStr((str) => str.trim())
                  handleConnectSource(connectStr.trim())
                }

                if (isUpdateAndConnectError || errorPostgres) {
                  setHideConnectIcon(true)
                }
              }}
              onFocus={() => {
                setHideConnectIcon(false)
                setTimeout(() => {
                  inputRef.current?.focus()
                }, 100)
              }}
              onPressEnter={() => {
                if (isUpdateAndConnectError || errorPostgres) {
                  setHideConnectIcon(true)
                }

                //blur input
                inputRef.current?.blur()
              }}
            />
          </div>
        </div>

        {connectionStatus?.status === EConnectionStatus.CONNECTING && (
          <div className="mt-[8px] w-full">
            <ConnectionStatus />
          </div>
        )}

        {isCreateNew &&
          connectionStatus?.status === EConnectionStatus.ERROR && (
            <div className="mt-[8px] w-full">
              <ConnectionStatus
                error={connectionStatus?.error}
                isFailed={connectionStatus?.status === EConnectionStatus.ERROR}
              />
            </div>
          )}

        {(connectionStatus?.status === EConnectionStatus.CONNECTED ||
          isUpdateAndConnectError) && (
          <div className="mt-[8px] w-full">
            <ListTable
              listView={listView}
              selectedTable={selectedTable}
              handleSelect={handleSelectTable}
              isUpdateAndConnectError={isUpdateAndConnectError}
            />
          </div>
        )}

        <div className="mt-[20px] flex h-[24px] w-full justify-end">
          <div className="flex w-[223px] items-center gap-[8px]">
            <div className="flex min-w-[175px] items-center gap-[4px]">
              <Tooltip text="Sensitive data will not be suggested to connect in workflow to avoid leaking of confidential information">
                <div>
                  <IconButton
                    nameIcon="Bold-EssentionalUI-InfoCircle"
                    sizeIcon={16}
                    colorIcon={colors['border-base-icon']}
                    hoverColor={colors['Primary-Color']}
                  />
                </div>
              </Tooltip>

              <Text type="body" variant="medium" className="text-Primary-Color">
                Mark as sensitive data
              </Text>
            </div>

            <Toggle defaultValue={isSensitive} onChange={handleChangeToggle} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConnectSource
