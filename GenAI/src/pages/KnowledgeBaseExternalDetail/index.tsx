import {
  GetList<PERSON>iewsResult,
  KBExternalPublic,
  externalKnowledgeBaseCreateExternalKnowledgeBaseApi,
  externalKnowledgeBaseFetchAllViewApi,
  externalKnowledgeBaseReadExternalKnowledgeBaseByIdApi,
  externalKnowledgeBaseSyncDbmsEmbeddingApi,
  externalKnowledgeBaseUpdateExternalKnowledgeBaseApi,
} from '@/apis/client'
import Button from '@/components/Button'
import { AccessDenied } from '@/components/ErrorPage'
import NotFound from '@/components/ErrorPage/NotFound'
import Input from '@/components/Input'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import PageHeader from '@/components/PageHeader'
import RouteLeaving from '@/components/RouteLeaving'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { HTTP_STATUS_CODE } from '@/constants'
import { useLatestValue } from '@/hooks/useLatestValue'
import useWSExternalDataSource from '@/hooks/useWSExternalDataSource'
import { useEffect, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { EKBFileError } from '../knowledgeBaseDirectory/consts'
import ConnectSource from './components/ConnectSource'
import {
  EConnectionStatus,
  IConnectionStatus,
  ISelectedTable,
  KbExternalUrl,
  errorPostgresFormat,
  regexCheckPostgres,
} from './consts'

const DEFAULT_DATABASE_TYPE = 'PostgreSQL'

const KnowledgeBaseExternalDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()

  useWSExternalDataSource()

  const isCreateNew = !id
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [connectStr, setConnectStr] = useState('')
  const [kbFileError, setKbFileError] = useState<EKBFileError>()
  const [isSensitive, setIsSensitive] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<IConnectionStatus>()
  const [listView, setListView] = useState<ISelectedTable[]>()
  const [initData, setInitData] = useState<KBExternalPublic>()
  const latestInitData = useLatestValue(initData)

  const [selectedTable, setSelectedTable] = useState<ISelectedTable>()
  const [isChanged, setIsChanged] = useState(false)
  const [isChangedSelectedTable, setIsChangedSelectedTable] = useState(false)
  const [isLoadingSave, setIsLoadingSave] = useState(false)
  const [isLoadingSync, setIsLoadingSync] = useState(false)
  const [errorPostgres, setErrorPostgres] = useState<string>()
  const calledTimes = useRef(0)

  const handleConnectSource = async (stringConnect: string) => {
    calledTimes.current += 1

    // to refresh selected table after reconnected
    if (latestInitData.current?.view_name) {
      setSelectedTable({ view_name: latestInitData.current.view_name })
    }

    if (!stringConnect.trim()) {
      setErrorPostgres(undefined)
      setConnectionStatus(undefined)

      // setListView(undefined)
      return
    }

    if (!stringConnect.trim().match(regexCheckPostgres)) {
      if (!isCreateNew) {
        // Just show selected table if it's update mode
        setListView([
          {
            view_name: latestInitData.current?.view_name ?? '',
          },
        ])
      } else {
        setListView(undefined)
      }

      setConnectionStatus(undefined)
      setErrorPostgres(errorPostgresFormat)
      return
    }

    if (errorPostgres) setErrorPostgres(undefined)

    setConnectionStatus({
      status: EConnectionStatus.CONNECTING,
    })

    try {
      const res = await externalKnowledgeBaseFetchAllViewApi({
        query: {
          connection_string: stringConnect.trim(),
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setConnectionStatus({
          status: EConnectionStatus.CONNECTED,
        })

        if (isCreateNew) {
          setSelectedTable(undefined)
          setListView(res.data?.data)
          return
        }

        const isSelectedTableExist = res.data?.data?.find(
          (item: GetListViewsResult) =>
            item.view_name === latestInitData.current?.view_name
        )

        if (!isSelectedTableExist) {
          if (
            stringConnect.trim() ===
            latestInitData.current?.source_connection_string
          ) {
            // if string connect is not changed, push selected to top // refactored
            setListView([
              {
                view_name: latestInitData.current?.view_name ?? '',
                outdated: true,
              },
              ...(res.data?.data ?? []),
            ])

            // update selected table is outdated to hide sync button
            setSelectedTable((pre) => {
              if (!pre) return undefined
              return {
                view_name: pre.view_name,
                outdated: true,
              }
            })
          } else {
            setListView(res.data?.data)
            setSelectedTable(undefined)
          }
        } else {
          // push existing selected to top
          setListView([
            {
              view_name: latestInitData.current?.view_name ?? '',
            },
            ...(res.data?.data?.filter(
              (item: GetListViewsResult) =>
                item.view_name !== latestInitData.current?.view_name
            ) ?? []),
          ])
        }
      } else {
        let errDetail: string | undefined

        if (
          res.error &&
          res.error.detail &&
          typeof res.error.detail === 'string'
        ) {
          errDetail = res.error.detail
        }

        setConnectionStatus({
          status: EConnectionStatus.ERROR,
          error: !isCreateNew
            ? calledTimes.current > 1
              ? (errDetail ?? 'Cannot connect to your database, please retry')
              : 'It seems like your connection string got problem'
            : (errDetail ?? 'Cannot connect to your database, please retry'),
        })

        // update list view 1 item if it's update mode
        if (!isCreateNew && latestInitData.current?.view_name) {
          setListView([
            {
              view_name: latestInitData.current.view_name,
            },
          ])
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    const init = async () => {
      if (!id) return

      try {
        const res = await externalKnowledgeBaseReadExternalKnowledgeBaseByIdApi(
          {
            path: {
              external_knowledge_base_id: id,
            },
          }
        )

        if (res.status == HTTP_STATUS_CODE.NOT_FOUND) {
          setKbFileError(EKBFileError.NOT_FOUND)
          return
        } else if (res.status == HTTP_STATUS_CODE.FORBIDDEN) {
          setKbFileError(EKBFileError.NOT_HAVE_PERMISSION)
          return
        }

        if (res.status == 200) {
          const [{ data }] = [res]

          if (!data?.data) {
            return
          }
          setInitData(data.data)
          setName(data.data.name ?? '')
          setDescription(data.data.description ?? '')
          setIsSensitive(data.data.is_sensitive ?? false)
          setSelectedTable(
            data.data.view_name ? { view_name: data.data.view_name } : undefined
          )

          const str = data.data.source_connection_string ?? ''

          if (str) {
            setConnectStr(str)
            handleConnectSource(str)
          }
        }
      } finally {
        /* empty */
      }
    }

    init()
  }, [id])

  const onChangeName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value)
    setIsChanged(true)
  }

  const onChangeDescription = (e: string) => {
    setDescription(e)
    setIsChanged(true)
  }

  const handleSync = async () => {
    setIsLoadingSync(true)

    const res = await externalKnowledgeBaseSyncDbmsEmbeddingApi({
      query: {
        external_knowledge_base_id: id!,
      },
    })

    if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
      if (res.error && res.error.detail) {
        setTimeout(() => {
          Message.error({
            message: res.error.detail as unknown as string,
          })
        }, 500)
      } else {
        setTimeout(() => {
          Message.error({
            message: 'Something went wrong!',
          })
        }, 500)
      }

      setIsLoadingSync(false)
    }

    setIsChanged(false)

    setTimeout(() => {
      navigate(KbExternalUrl)

      Message.success({
        message: 'Data is being synced',
      })
    }, 200)
  }

  const handleSave = async () => {
    const handleCallApi = async () => {
      setIsLoadingSave(true)

      if (isCreateNew) {
        return await externalKnowledgeBaseCreateExternalKnowledgeBaseApi({
          body: {
            name,
            description,
            source_connection_string: connectStr,
            is_sensitive: isSensitive,
            view_name: selectedTable?.view_name ?? '',
            database_type: DEFAULT_DATABASE_TYPE,
          },
        })
      }

      return await externalKnowledgeBaseUpdateExternalKnowledgeBaseApi({
        path: {
          external_knowledge_base_id: id!,
        },
        body: {
          name,
          description,
          source_connection_string: connectStr,
          is_sensitive: isSensitive,
          view_name: selectedTable?.view_name ?? '',
          database_type: DEFAULT_DATABASE_TYPE,
        },
      })
    }

    const res = await handleCallApi()

    if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
      Message.error({
        message: 'Something went wrong!',
      })

      setIsLoadingSave(false)
      return
    }

    if (isChangedSelectedTable) {
      const kbId = res.data?.data?.id

      const resSync = await externalKnowledgeBaseSyncDbmsEmbeddingApi({
        query: {
          external_knowledge_base_id: kbId!,
        },
      })

      if (resSync.status !== HTTP_STATUS_CODE.SUCCESS) {
        if (resSync.error && resSync.error.detail) {
          setTimeout(() => {
            Message.error({
              message: resSync.error.detail as unknown as string,
            })
          }, 500)
        } else {
          setTimeout(() => {
            Message.error({
              message: 'Something went wrong!',
            })
          }, 500)
        }

        setIsLoadingSave(false)
      }
    }

    setIsChanged(false)
    setIsChangedSelectedTable(false)
    setIsLoadingSave(false)

    setTimeout(() => {
      navigate(KbExternalUrl)

      Message.success({
        message: isCreateNew
          ? 'Successfully created data source'
          : 'Successfully updated data source',
      })
    }, 200)
  }

  const renderContent = () => {
    if (kbFileError === EKBFileError.NOT_FOUND) {
      return <NotFound />
    }

    if (kbFileError === EKBFileError.NOT_HAVE_PERMISSION) {
      return <AccessDenied />
    }

    const selectedIsOutdated = selectedTable?.outdated

    const disableSaveButton =
      !isChanged ||
      !name.trim() ||
      !connectStr.trim() ||
      !selectedTable ||
      !!errorPostgres ||
      connectionStatus?.status === EConnectionStatus.CONNECTING ||
      connectionStatus?.status === EConnectionStatus.ERROR ||
      selectedIsOutdated

    const disableSyncButton =
      isChanged ||
      !connectStr.trim() ||
      connectionStatus?.status === EConnectionStatus.CONNECTING ||
      !selectedTable ||
      selectedIsOutdated

    return (
      <>
        <PageHeader
          hardRedirect={false}
          breadcrumbPaths={[
            {
              name: 'Knowledge base',
              url: KbExternalUrl,
            },
            {
              name: 'Data Source Configuration',
            },
          ]}
          title="Essential element for worker as it enhances efficiency and accuracy by providing quick access to relevant information, ensuring consistent and accurate responses."
        />

        <div className="flex overflow-hidden">
          <div className="genai-scrollbar mt-[24px] w-full flex-1 overflow-y-auto px-[45px]">
            <div className="flex w-full flex-col gap-[12px]">
              <div className="flex w-full flex-col gap-[8px]">
                <Text
                  type="body"
                  variant="semibold"
                  className="text-Primary-Color"
                >
                  General
                </Text>
                <div className="flex w-full flex-col gap-[12px] rounded-lg border border-border-base-icon bg-white px-[16px] pb-[16px] pt-[12px]">
                  <Input
                    placeholder="Type in name for your data source"
                    className="w-full"
                    maxLength={50}
                    value={name}
                    onChange={onChangeName}
                    onBlur={() => {
                      setName((str) => str.trim())
                    }}
                    label="Display name"
                  />
                  <TextArea
                    placeholder="Supplementary information to use your data"
                    className="h-[90px] w-full"
                    maxLength={255}
                    value={description}
                    onChange={onChangeDescription}
                    onBlur={() => {
                      setDescription((str) => str.trim())
                    }}
                    label="Description"
                  />
                </div>
              </div>

              <ConnectSource
                connectStr={connectStr}
                setConnectStr={setConnectStr}
                isSensitive={isSensitive}
                setIsSensitive={setIsSensitive}
                handleConnectSource={handleConnectSource}
                connectionStatus={connectionStatus}
                setConnectionStatus={setConnectionStatus}
                listView={listView}
                selectedTable={selectedTable}
                setSelectedTable={setSelectedTable}
                setIsChanged={setIsChanged}
                setIsChangedSelectedTable={setIsChangedSelectedTable}
                errorPostgres={errorPostgres}
                isCreateNew={isCreateNew}
                latestInitData={latestInitData}
              />

              <div className="flex w-full flex-row justify-end gap-[12px]">
                {!isCreateNew && (
                  <Button
                    text="Sync"
                    loading={isLoadingSync}
                    type="secondary"
                    disabled={disableSyncButton}
                    onClick={handleSync}
                  />
                )}

                <Button
                  text="Save"
                  loading={isLoadingSave}
                  type="primary"
                  disabled={disableSaveButton}
                  onClick={handleSave}
                />
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  return (
    <Layout>
      <RouteLeaving isChanged={isChanged} />

      <div className="flex h-full flex-col px-[8px]">{renderContent()}</div>
    </Layout>
  )
}

export default KnowledgeBaseExternalDetail
