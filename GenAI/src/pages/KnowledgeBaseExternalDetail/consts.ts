import { rootUrls } from '@/routes/rootUrls'

export enum EConnectionStatus {
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  ERROR = 'ERROR',
}

export interface IConnectionStatus {
  status: EConnectionStatus
  error?: string
}

export interface ISelectedTable {
  view_name: string
  outdated?: boolean
}

export const KbExternalUrl = `${rootUrls.KnowledgeBase}?type=external`

export const regexCheckPostgres =
  // eslint-disable-next-line no-useless-escape
  /^postgresql:\/\/([^:\/]+):([^@]+)@([^:\/]+):(\d+)\/([^\/]+)\?current_schema=[^&]+[^&]*$/

export const errorPostgresFormat =
  'Format must be postgresql://<username>:<password>@<host>:<port>/<dbname>?current_schema=<schema_name>'
