import {
  ValidationError,
  toolCategoriesReadToolCategoryByIdApi,
  toolCategoriesUpdateToolCategoryApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Text from '@/components/Text'
import Upload from '@/components/Upload'
import { Dispatch, memo, useCallback, useEffect, useState } from 'react'

const ModalUpdateCategory = (props: ModalAddNewCategoryProps) => {
  const { openEditModal, setOpenEditModal, idDetailTool, refresh } = props

  const [loading, setLoading] = useState(false)
  const [nameDetailTool, setNameDetailTool] = useState<string | undefined>()
  const [fileDetailTool, setFileDetailTool] = useState<File | null>(null)
  const [imageDetailTool, setImageDetailTool] = useState<
    string | undefined | null
  >()
  const [errorCreate, setErrorCreate] = useState<ValidationError[] | string>('')
  const [initNameDetailTool, setInitNameDetailTool] = useState<
    string | undefined
  >()
  const [initImageDetailTool, setInitImageDetailTool] = useState<
    string | undefined | null
  >()

  const detailToolCategory = async (id: string) => {
    const data = await toolCategoriesReadToolCategoryByIdApi({
      path: {
        tool_category_id: id,
      },
    })

    if (data.status === 200) {
      setNameDetailTool(data?.data?.data?.name)
      setInitNameDetailTool(data?.data?.data?.name)
      setImageDetailTool(data?.data?.data?.logo)
      setInitImageDetailTool(data?.data?.data?.logo)
    }
  }

  const updateToolCategory = async (id: string) => {
    try {
      setLoading(true)
      const body = {
        name: nameDetailTool?.trim() || '',
      }

      if (fileDetailTool) {
        // @ts-expect-error: Unreachable code error
        body.logo_file = fileDetailTool
      } else {
        // @ts-expect-error: Unreachable code error
        body.logo = imageDetailTool
      }
      const data = await toolCategoriesUpdateToolCategoryApi({
        path: {
          tool_category_id: id,
        },
        body,
      })

      if (data.status === 200) {
        setLoading(false)
        setOpenEditModal(false)
        setNameDetailTool(undefined)
        setFileDetailTool(null)
        setImageDetailTool(undefined)
        setErrorCreate('')
        refresh(null)
        Message.success({ message: 'Successfully updated tool category' })
      } else if (data.status === 400 && data.error) {
        setErrorCreate(data.error.detail ?? '')
        setLoading(false)
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  const contentEdit = useCallback(() => {
    return (
      <div className="flex flex-col gap-[8px] rounded-[12px] border-[1px] border-neutral-200 bg-white px-[24px] py-[16px]">
        <Input
          value={nameDetailTool}
          onChange={(e) => {
            setNameDetailTool(e.target.value)
          }}
          maxLength={50}
          errorText={errorCreate.toString()}
          isError={!!errorCreate.toString()}
          placeholder="Type in category name"
          label="Category"
        />

        <div className="flex flex-col gap-1">
          <Text
            type="subBody"
            variant="medium"
            className="pl-1 text-Tertiary-Color"
          >
            Logo
          </Text>

          <Upload
            dataTypes="image/png, image/jpeg"
            className="h-[149px]"
            image={imageDetailTool}
            onChange={(e) => {
              setImageDetailTool(e)
            }}
            onChangeFile={(e) => {
              setFileDetailTool(e)
            }}
          />
        </div>
      </div>
    )
  }, [imageDetailTool, errorCreate, nameDetailTool])

  useEffect(() => {
    if (idDetailTool) {
      detailToolCategory(idDetailTool)
    }
  }, [idDetailTool])

  return (
    <Modal
      okLoading={loading}
      open={openEditModal}
      title="Update tool category"
      subTitle="Edit your category to public tools in Marketplace"
      onClickCancel={() => {
        if (
          nameDetailTool !== initNameDetailTool ||
          imageDetailTool !== initImageDetailTool
        ) {
          MessageDialog.warning({
            mainMessage: 'Wanna leave?',
            subMessage: 'If continue, your changes may not be saved',
            onClick: () => {
              setLoading(false)
              setOpenEditModal(false)
              setNameDetailTool(undefined)
              setFileDetailTool(null)
              setImageDetailTool(undefined)
              setErrorCreate('')
            },
          })
        } else {
          setOpenEditModal(false)
          setNameDetailTool(undefined)
          setFileDetailTool(null)
          setImageDetailTool(undefined)
        }
      }}
      okDisable={
        !(
          nameDetailTool &&
          (nameDetailTool?.trim() !== initNameDetailTool ||
            imageDetailTool !== initImageDetailTool)
        ) || /^[\s]*$/.test(nameDetailTool)
      }
      onClickOk={() => {
        updateToolCategory(idDetailTool)
      }}
    >
      {contentEdit()}
    </Modal>
  )
}

interface ModalAddNewCategoryProps {
  openEditModal: boolean
  setOpenEditModal: Dispatch<boolean>
  idDetailTool: string
  refresh: (page: number | null) => void
}

export default memo(ModalUpdateCategory)
