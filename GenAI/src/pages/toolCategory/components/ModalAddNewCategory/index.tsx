import {
  ValidationError,
  toolCategoriesCreateToolCategoryApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Text from '@/components/Text'
import Upload from '@/components/Upload'
import { HTTP_STATUS_CODE } from '@/constants'
import { Dispatch, memo, useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const ModalAddNewCategory = (props: ModalAddNewCategoryProps) => {
  const navigate = useNavigate()

  const { openCreateModal, setOpenCreateModal, refresh } = props

  const [loading, setLoading] = useState(false)
  const [nameDetailTool, setNameDetailTool] = useState<string | undefined>()
  const [fileDetailTool, setFileDetailTool] = useState<File | null>(null)
  const [imageDetailTool, setImageDetailTool] = useState<
    string | undefined | null
  >()
  const [errorCreate, setErrorCreate] = useState<ValidationError[] | string>('')

  const clearState = () => {
    navigate(location.pathname, { state: {} })
  }

  const addToolCategory = async () => {
    try {
      setLoading(true)
      const data = await toolCategoriesCreateToolCategoryApi({
        body: {
          name: nameDetailTool?.trim() || '',
          logo_file: fileDetailTool,
        },
      })

      if (data.status === HTTP_STATUS_CODE.SUCCESS) {
        setLoading(false)
        setOpenCreateModal(false)
        setNameDetailTool(undefined)
        setFileDetailTool(null)
        setImageDetailTool(undefined)
        setErrorCreate('')
        refresh(1)
        Message.success({ message: 'Successfully created tool category' })
      } else if (data.status === HTTP_STATUS_CODE.BAD_REQUEST && data.error) {
        setErrorCreate(data.error.detail ?? '')
      } else {
        Message.error({ message: 'Something went wrong!' })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const contentAddNew = useCallback(() => {
    return (
      <div className="flex flex-col gap-[8px] rounded-[12px] border-[1px] border-neutral-200 bg-white px-[24px] py-[16px]">
        <Input
          value={nameDetailTool}
          onChange={(e) => {
            setNameDetailTool(e.target.value)
          }}
          maxLength={50}
          errorText={errorCreate.toString()}
          isError={!!errorCreate.toString()}
          onBlur={() => {
            if (nameDetailTool) {
              setNameDetailTool(nameDetailTool.trim())
            }
          }}
          placeholder="Type in category name"
          label="Category"
        />

        <div className="flex flex-col gap-1">
          <Text
            type="subBody"
            variant="medium"
            className="pl-1 text-Tertiary-Color"
          >
            Logo
          </Text>

          <Upload
            dataTypes="image/png, image/jpeg"
            className="h-[149px]"
            image={imageDetailTool}
            onChange={(e) => {
              setImageDetailTool(e)
            }}
            onChangeFile={(e) => {
              setFileDetailTool(e)
            }}
          />
        </div>
      </div>
    )
  }, [imageDetailTool, errorCreate, nameDetailTool])

  return (
    <Modal
      okLoading={loading}
      open={openCreateModal}
      title="New tool category"
      subTitle="Create new category to public tools in Marketplace"
      onClickCancel={() => {
        clearState()

        if (nameDetailTool || imageDetailTool) {
          MessageDialog.warning({
            mainMessage: 'Wanna leave?',
            subMessage: 'If continue, your changes may not be saved',
            onClick: () => {
              setLoading(false)
              setOpenCreateModal(false)
              setNameDetailTool(undefined)
              setFileDetailTool(null)
              setImageDetailTool(undefined)
              setErrorCreate('')
            },
          })
        } else {
          setOpenCreateModal(false)
        }
      }}
      okDisable={!nameDetailTool || /^[\s]*$/.test(nameDetailTool)}
      onClickOk={() => {
        clearState()

        addToolCategory()
      }}
    >
      {contentAddNew()}
    </Modal>
  )
}

interface ModalAddNewCategoryProps {
  openCreateModal: boolean
  setOpenCreateModal: Dispatch<boolean>
  refresh: (page: number | null) => void
}

export default memo(ModalAddNewCategory)
