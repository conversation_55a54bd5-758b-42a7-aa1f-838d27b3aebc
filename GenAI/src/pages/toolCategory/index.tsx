import {
  ToolCategoryPublic,
  toolCategoriesDeleteToolCategoryApi,
  toolCategoriesReadToolCategoriesApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import EmptyData from '@/components/EmptyData'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import PageHeader from '@/components/PageHeader'
import Pagination from '@/components/Pagination'
import SearchBar from '@/components/SearchBar'
import { PAGE_SIZE } from '@/constants'
import { debounce, isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'
import ButtonAddCategory from './components/ButtonAddCategory'
import CategoryItem from './components/CategoryItem'
import ListSkeleton from './components/ListSkeleton'
import ModalAddNewCategory from './components/ModalAddNewCategory'
import ModalUpdateCategory from './components/ModalUpdateCategory'

const toolCategoryButtonAddNew = 'toolCategoryButtonAddNew'
const UNCATEGORIZED_ID = '00000000-0000-0000-0000-000000000000'
const CREATE_ITEM = {
  id: toolCategoryButtonAddNew,
  name: '',
  logo: '',
  tool_count: 0,
}

interface IPagination {
  query: string
  page: number
  totalPage: number
}

const ToolCategory = () => {
  const { state } = useLocation()
  const [initializing, setInitializing] = useState(true)
  const [firstTimeLoadData, setFirstTimeLoadData] = useState(true)
  const [pagination, setPagination] = useState<IPagination>({
    query: '',
    page: 1,
    totalPage: 1,
  })
  const [data, setData] = useState<ToolCategoryPublic[]>([])
  const { query, page, totalPage } = pagination

  const [openCreateModal, setOpenCreateModal] = useState(
    state?.isCreate ?? false
  )

  const [openEditModal, setOpenEditModal] = useState(false)

  const [idDetailTool, setIdDetailTool] = useState('')

  const fetchData = async ({
    tQuery = '',
    tPage = 1,
    tTotalPage = 1,
  }: IFetchData) => {
    if (tPage > tTotalPage) {
      setData([CREATE_ITEM])
      return
    }
    try {
      setFirstTimeLoadData(true)
      const res = await toolCategoriesReadToolCategoriesApi({
        query: {
          page_number: tPage,
          page_size: PAGE_SIZE.LARGE,
          name: tQuery,
        },
      })
      if (res.status === 200) {
        const arr: ToolCategoryPublic[] = res?.data?.data?.data || []
        if (tQuery.length > 0) {
          setData(arr)
        } else {
          setData([CREATE_ITEM, ...arr])
        }
        setPagination({
          query: tQuery,
          page: tPage,
          totalPage: res?.data?.data?.total_pages || 1,
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
      setData([CREATE_ITEM])
    } finally {
      setFirstTimeLoadData(false)
    }
  }

  const refreshData = async () => {
    if (page > totalPage) {
      setData([CREATE_ITEM])
      return
    }
    try {
      const res = await toolCategoriesReadToolCategoriesApi({
        query: {
          page_number: page,
          page_size: PAGE_SIZE.LARGE,
          name: query,
        },
      })
      if (res.status === 200) {
        const arr: ToolCategoryPublic[] = res?.data?.data?.data || []
        if (query.length > 0) {
          setData(arr)
        } else {
          setData([CREATE_ITEM, ...arr])
        }
        setPagination({
          ...pagination,
          totalPage: res?.data?.data?.total_pages || 1,
        })
      }
    } catch (error) {
      setData([CREATE_ITEM])
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  const refresh = () => {
    refreshData()
  }

  const newCategory = () => {
    setOpenCreateModal(true)
  }

  const editCategory = (id: string) => {
    setOpenEditModal(true)
    setIdDetailTool(id)
  }

  const handleDeleteToolCategory = async (id: string) => {
    try {
      const data = await toolCategoriesDeleteToolCategoryApi({
        path: {
          tool_category_id: id,
        },
      })
      if (data.status === 204) {
        refresh()
        Message.success({ message: 'Successfully deleted tool category' })
      } else {
        Message.error({ message: 'Something wrong, please retry!' })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  const deleteToolCategory = (id: string) => {
    MessageDialog.warning({
      mainMessage: 'Delete tool category?',
      subMessage:
        'If continue, this category and all tools in marketplace will be permanently removed.',
      onClick: () => handleDeleteToolCategory(id),
    })
  }

  const debouncedFetchData = debounce(fetchData, 1000)

  const searchHandler = async (value: string) => {
    debouncedFetchData({
      tQuery: value,
      tPage: 1,
      tTotalPage: 1,
    })
  }

  useEffect(() => {
    setInitializing(true)
    setTimeout(() => {
      setInitializing(false)
    }, 750)
    fetchData({})
  }, [])

  return (
    <Layout>
      <div className="flex h-full flex-col overflow-hidden">
        <div className="flex items-center justify-between">
          <PageHeader
            breadcrumbPaths={[{ name: 'Tool Category' }]}
            title="Add new category to sort out your tool in Marketplace"
          />

          <SearchBar onSearch={searchHandler} />
        </div>

        <div className="flex h-full flex-col overflow-hidden">
          <div className="genai-scrollbar h-full overflow-y-auto overflow-x-hidden">
            {(initializing || firstTimeLoadData) && (
              <ListSkeleton number={PAGE_SIZE.MEDIUM} />
            )}
            {!initializing &&
              !firstTimeLoadData &&
              data.length === 0 &&
              query.length === 0 && (
                <div className="flex flex-col items-center">
                  <EmptyData
                    size="medium"
                    type="03"
                    className="mb-[24px] mt-[100px]"
                  />
                  <Button
                    type="primary"
                    size="medium"
                    onClick={newCategory}
                    className="w-[205px]"
                    text="New category"
                    leftIcon={
                      <Icon
                        name="vuesax-outline-add"
                        color="#ffffff"
                        size={20}
                      />
                    }
                  />
                </div>
              )}
            {!initializing &&
              !firstTimeLoadData &&
              data.length === 0 &&
              query.length !== 0 && <NoDataFound className="mt-[64px]" />}
            {!initializing && !firstTimeLoadData && !isEmpty(data) && (
              <div className="mt-[20px] flex flex-wrap gap-x-[24px] gap-y-[20px] pb-[1px]">
                {data.map((item, index) => {
                  if (item.id === toolCategoryButtonAddNew) {
                    return (
                      <ButtonAddCategory key={index} onClick={newCategory} />
                    )
                  }
                  return (
                    <CategoryItem
                      hideDelete={item.id === UNCATEGORIZED_ID}
                      key={index}
                      image={item.logo}
                      title={item.name}
                      number={item.tool_count || 0}
                      onDelete={() => {
                        deleteToolCategory(item.id)
                      }}
                      onClick={() => {
                        if (item.id === UNCATEGORIZED_ID) {
                          Message.error({
                            message: 'Cannot change default category',
                          })
                        } else {
                          editCategory(item.id)
                        }
                      }}
                    />
                  )
                })}
              </div>
            )}
          </div>

          {totalPage > 1 && (
            <div className="mt-[12px] flex w-full flex-row justify-end">
              <Pagination
                onChangePage={(e) => {
                  fetchData({
                    tQuery: query,
                    tPage: e,
                    tTotalPage: totalPage,
                  })
                }}
                page={page}
                totalPage={pagination.totalPage ?? 0}
                className="w-[220px]"
              />
            </div>
          )}

          <ModalAddNewCategory
            openCreateModal={openCreateModal}
            setOpenCreateModal={setOpenCreateModal}
            refresh={refresh}
          />

          <ModalUpdateCategory
            idDetailTool={idDetailTool}
            openEditModal={openEditModal}
            setOpenEditModal={(e) => {
              setOpenEditModal(e)
              setIdDetailTool('')
            }}
            refresh={refresh}
          />
        </div>
      </div>
    </Layout>
  )
}

export default ToolCategory

interface IFetchData {
  tQuery?: string
  tPage?: number
  tTotalPage?: number
}
