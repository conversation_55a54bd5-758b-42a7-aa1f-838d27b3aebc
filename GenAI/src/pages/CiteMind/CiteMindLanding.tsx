import Button from '@/components/Button'
import { memo, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import GenaiCiteMindIntro from './GenaiCiteMindIntro'
import { CITATION_WORKFLOW } from './const'

const CiteMindLanding = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const params = new URLSearchParams(location.search)

  const [isLoading, setLoading] = useState(false)

  const handleClickStart = () => {
    if (isLoading) return
    setLoading(true)

    setTimeout(() => {
      navigate(
        `/utilities/citemind/${params?.get('workflowId') ?? CITATION_WORKFLOW}`,
        { replace: true }
      )
    }, 1000)
  }

  return (
    <div className="flex h-full w-full flex-col items-center bg-Main-09 pt-[128px]">
      <GenaiCiteMindIntro />
      <Button
        type="primary"
        onClick={handleClickStart}
        className="mt-[44px] w-[160px]"
        loading={isLoading}
      >
        Start
      </Button>
    </div>
  )
}

export default memo(CiteMindLanding)
