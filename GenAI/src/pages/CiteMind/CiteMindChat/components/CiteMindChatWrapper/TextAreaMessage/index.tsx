import Text from '@/components/Text'
import clsx from 'clsx'
import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'

import { twMerge } from 'tailwind-merge'
import styles from './styles.module.scss'
import { ArrAIOperation, ILocalFile, ILocalMessageParams } from '../helper'
import { cn } from '@/helpers'
import IconCustomizeSummarize from './Icons/IconCustomizeSummarize'
import IconCustomizeExtract from './Icons/IconCustomizeExtract'
import IconQueryAndSearch from './Icons/IconQueryAndSearch'
import FileWrapper from './FileWrapper'
import { EMessageType } from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import Icon from '@/assets/icon/Icon'

interface IProps {
  label?: string
  className?: string
  classNameWrapper?: string
  placeholder?: string
  value?: string
  onChange: (value: string) => void
  maxLength?: number
  autoResize?: boolean
  maxHeight?: number
  onSend?: (type: EMessageType, params: ILocalMessageParams) => void
  onBlur?: () => void
  onFocus?: () => void
  isAutofocus?: boolean
  textAreaClassName?: string
  isSubtext?: boolean
  isError?: boolean
  helperText?: string
  files?: ILocalFile[]
  handleDeleteFile: (file: ILocalFile) => void
  classNameBtn?: string
  iconsProps?: any
  iconsBtnDisabled?: boolean
  classNameTextAreaWrapper?: string
}

export interface TextAreaRef {
  blur: () => void
  focus: () => void
}

const TextArea = forwardRef<TextAreaRef, IProps>(
  (
    {
      label,
      className,
      classNameWrapper,
      placeholder = 'Placeholder text',
      value,
      onChange,
      maxLength = 255,
      autoResize = false,
      maxHeight = 200,
      onSend,
      onBlur,
      onFocus,
      isAutofocus = false,
      textAreaClassName = '',
      isSubtext = false,
      isError = false,
      helperText = 'Error text',
      files,
      handleDeleteFile,
      classNameBtn,
      iconsProps,
      iconsBtnDisabled,
      classNameTextAreaWrapper,
    }: IProps,
    ref
  ) => {
    const [isFocus, setFocus] = useState(isAutofocus ?? false)
    const textAreaRef = useRef<HTMLTextAreaElement>(null)

    const isHaveFiles = Boolean(files?.length)

    const adjustHeight = () => {
      textAreaRef.current!.style.height = 'inherit'

      textAreaRef.current!.style.height = `${textAreaRef.current!.scrollHeight > maxHeight ? maxHeight : textAreaRef.current!.scrollHeight}px`
    }

    useEffect(() => {
      if (isFocus) {
        // Set the cursor to the last character
        textAreaRef.current!.selectionStart = value?.length ?? 0
        textAreaRef.current!.selectionEnd = value?.length ?? 0
      }
    }, [isFocus])

    useEffect(() => {
      if (autoResize) adjustHeight()
    }, [value])

    useImperativeHandle(ref, () => ({
      blur: () => onBlur?.(),
      focus: () => textAreaRef.current?.focus(),
    }))

    const handleSend = (value?: string, intent?: string) => {
      if (isHaveFiles) {
        if (!intent && !value?.trim()) return

        onSend?.(EMessageType.fileUploadMessage, {
          text: value,
          files,
          intent,
        })
        return
      } else {
        onSend?.(EMessageType.user, { text: value })
      }
    }

    return (
      <div className={classNameWrapper}>
        <div
          className={cn(isHaveFiles && styles['genai-textarea-focus'], {
            'flex w-full flex-col gap-[8px] rounded-[12px] border border-border-base-icon px-[12px] py-[8px]':
              isHaveFiles,
          })}
        >
          {isHaveFiles && (
            <div className="flex w-full flex-col gap-[8px]">
              <div className="genai-scrollbar flex max-h-[86px] w-full flex-wrap gap-[6px] overflow-y-auto">
                {files?.map((file) => (
                  <FileWrapper
                    file={file}
                    key={file.id}
                    className="h-[39px] w-[180px] overflow-hidden rounded-[8px] border border-border-base-icon px-[8px] py-0"
                    classNameFileItem="justify-start gap-[8px] overflow-hidden !p-0"
                    onClickDeleteItem={() => {
                      handleDeleteFile(file)
                    }}
                  />
                ))}
              </div>
              <div className="genai-scrollbar flex select-none items-center gap-[8px] overflow-x-auto">
                {ArrAIOperation.map((item) => (
                  <div
                    key={item.id}
                    className={cn(
                      'flex h-[26px] min-w-fit cursor-pointer items-center justify-center gap-[4px] rounded-[8px] bg-neutral-100 px-[8px] hover:bg-neutral-200'
                    )}
                    onClick={() => {
                      handleSend(value, item.id)
                    }}
                  >
                    <div className="flex size-[16px] min-w-[16px] items-center justify-center">
                      {item.id === 'Summarize content' ? (
                        <IconCustomizeSummarize />
                      ) : item.id === 'Extract information' ? (
                        <IconCustomizeExtract />
                      ) : item.id === 'Query and search' ? (
                        <IconQueryAndSearch />
                      ) : (
                        <IconCustomizeSummarize />
                      )}
                    </div>

                    <Text
                      type="subBody"
                      variant="medium"
                      className="text-Primary-Color"
                    >
                      {item.name}
                    </Text>
                  </div>
                ))}
              </div>
            </div>
          )}
          {isHaveFiles && <div className="h-[1px] w-full bg-neutral-100" />}

          <div className={clsx(twMerge('h-32', className), 'flex flex-col')}>
            {label && (
              <Text variant="medium" className="mb-2 pl-1 text-Primary-Color">
                {label}
              </Text>
            )}

            <div
              className={clsx(
                !isHaveFiles && styles['genai-textarea'],
                'genai-scrollbar flex h-full min-h-[80px] w-full resize-none flex-col rounded-lg bg-Input-Field px-3 py-[8px] text-subBody text-Primary-Color placeholder:text-Placeholder focus:shadow-focus',
                !isHaveFiles && 'border border-border-base-icon',
                !isHaveFiles && isFocus && !isError
                  ? styles['genai-textarea-focus']
                  : '',
                isError && '!border-Error-Color',
                classNameTextAreaWrapper
              )}
              style={{
                maxHeight: maxHeight,
              }}
              onClick={() => {
                setFocus(true)
                textAreaRef.current?.focus()
                onFocus?.()
              }}
              onBlur={() => setFocus(false)}
            >
              <textarea
                ref={textAreaRef}
                value={value}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    handleSend?.(e.currentTarget.value)
                  }
                }}
                onChange={(e) => {
                  onChange(e.target.value)
                }}
                placeholder={placeholder}
                maxLength={maxLength}
                className={clsx(
                  textAreaClassName,
                  'genai-scrollbar h-full w-full resize-none bg-transparent text-subBody text-Primary-Color outline-0 placeholder:text-Placeholder-Text'
                )}
                onBlur={onBlur}
                autoFocus={isAutofocus}
              />
              <div className="mt-[4px] flex justify-end">
                <button
                  className={classNameBtn}
                  onClick={() => {
                    handleSend(value)
                  }}
                  disabled={iconsBtnDisabled}
                >
                  <Icon name="vuesax-bold-send" size={16} {...iconsProps} />
                </button>
              </div>
            </div>

            {isSubtext && helperText && (
              <Text
                type="supportText"
                className={clsx(
                  'mt-1 pl-1',
                  isError ? 'text-Error-Color' : 'text-Secondary-Color'
                )}
              >
                {helperText}
              </Text>
            )}
          </div>
        </div>
      </div>
    )
  }
)

export default memo(TextArea)
