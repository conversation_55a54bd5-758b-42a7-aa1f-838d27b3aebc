/* eslint-disable max-len */
import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import Text from '@/components/Text'
import { GEN_AI_PATH, HTTP_STATUS_CODE } from '@/constants'
import { axiosCancelService } from '@/pages/CiteMind/axios/axiosCancel'
import CopiedButton from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/CopiedButton'
import {
  EMessageType,
  IMessage,
  LETS_START_MESSAGE,
  LETS_START_RESPONSE,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import clsx from 'clsx'
import { memo, useCallback, useEffect, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import PreviewMarkdown from '../PreviewMarkdown'
import SuggestionItem from '../SuggestionItem'
import PerplexityCitationItem from './PerplexityCitationItem'
import './styles.scss'

interface ICitationMessageProps {
  item: IMessage
  avatar?: string
  workerType: string
  className?: string
  responding?: boolean
  message: IMessage[]
  setText: (text: string) => void
  hasBorderMessages?: boolean
  hideCopy?: boolean
  related_question?: boolean
}

const PerplexityMessage = ({
  item,
  avatar,
  workerType,
  className,
  responding = false,
  message,
  setText,
  hasBorderMessages = false,
  hideCopy = false,
  related_question = false,
}: ICitationMessageProps) => {
  const { time, id, citations, text } = item

  const [isShowSuggestions, setShowSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState([])
  const [isSuggestionFetching, setSuggestionFetching] = useState(false)

  const fetchSuggestions = useCallback(async () => {
    try {
      if (isSuggestionFetching) return

      setSuggestionFetching(true)

      const body = {
        messages: message
          .filter(
            ({ text, type }) =>
              (type === EMessageType.user &&
                text!.toLocaleLowerCase() !== LETS_START_MESSAGE) ||
              (type === EMessageType.worker && text !== LETS_START_RESPONSE) ||
              type === EMessageType.citationMessage
          )
          .map(({ text, type }) => ({
            text,
            name: type,
          }))
          .reverse(),
      }

      const config = {
        requestId: id,
      }

      const res = await axiosCancelService.post(
        `${GEN_AI_PATH}/api/v1/citation/related-questions`,
        body,
        config
      )

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        const {
          data: { related_questions },
        } = data

        setSuggestions(
          related_questions?.map((item: any) => item.related_question)
        )
      }
    } catch (error) {
      console.log('err: ', error)
    } finally {
      setSuggestionFetching(false)
    }
  }, [message])

  useEffect(() => {
    if (related_question) {
      setShowSuggestions(true)
      fetchSuggestions()
    }
  }, [related_question])

  useEffect(() => {
    if (responding) {
      setShowSuggestions(false)
      axiosCancelService.cancel(id)
    }
  }, [responding])

  return (
    <div
      className={twMerge(
        'flex w-full items-center py-[4px] pl-[4px] pr-[40px]',
        className
      )}
    >
      <div className="!h-6 !w-6">
        <Avatar
          avatarUrl={avatar}
          size="smaller"
          avatarDefault={
            <div className="flex size-10 items-center justify-center rounded-full bg-Background-Color">
              <Icon
                name={
                  workerType === 'Human Worker'
                    ? 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
                    : 'face-id-square'
                }
                size={22}
                gradient={['#642B734D', '#C6426E4D']}
              />
            </div>
          }
        />
      </div>

      <div className="ml-[8px] flex w-full flex-col gap-1">
        <div
          className={clsx(
            'flex w-fit max-w-[calc(100%-28px)] flex-col justify-end rounded-[8px] bg-Base-03 p-[8px]',
            {
              'border border-neutral-200': hasBorderMessages,
            }
          )}
        >
          <Text
            type="supportText"
            variant="regular"
            elementType="div"
            className="mb-[4px] text-Secondary-Color"
          >
            {time}
          </Text>
          <Text
            type="subBody"
            variant="regular"
            className="bm-response-text w-full break-words text-Primary-Color"
            elementType="div"
          >
            <PreviewMarkdown message={text!} isPerplexityOn />
          </Text>

          {citations?.length && (
            <div className="mt-3 flex w-full flex-wrap gap-2 overflow-hidden py-1">
              {citations.map((item, index) => (
                <PerplexityCitationItem
                  key={index}
                  index={index + 1}
                  url={item}
                />
              ))}
            </div>
          )}

          {!hideCopy && <CopiedButton message={text!} />}
        </div>

        {isShowSuggestions && (
          <div className="flex w-full flex-col px-1 py-2">
            <div className="mb-1.5 flex items-center gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
              >
                <path
                  d="M9.33325 13.02C9.49659 13.02 9.66576 12.9792 9.81743 12.8975C10.1499 12.7225 10.3541 12.3725 10.3541 11.9991V11.1709C12.1158 10.99 13.2708 9.69497 13.2708 7.83997V4.34001C13.2708 2.33334 11.9233 0.98584 9.91659 0.98584H4.08325C2.07659 0.98584 0.729086 2.33334 0.729086 4.34001V7.83997C0.729086 9.84664 2.07659 11.1941 4.08325 11.1941H6.28243L8.76744 12.8509C8.9366 12.9617 9.13492 13.02 9.33325 13.02ZM9.91659 1.855C11.4216 1.855 12.3958 2.82917 12.3958 4.33417V7.8342C12.3958 9.3392 11.4216 10.3134 9.91659 10.3134C9.67742 10.3134 9.47909 10.5117 9.47909 10.7509V11.9934C9.47909 12.0692 9.43242 12.1042 9.40325 12.1217C9.37408 12.1392 9.31574 12.1567 9.25158 12.1158L6.65574 10.3892C6.58574 10.3425 6.49825 10.3134 6.41075 10.3134H4.07741C2.57241 10.3134 1.59825 9.3392 1.59825 7.8342V4.33417C1.59825 2.82917 2.57241 1.855 4.07741 1.855H9.91659Z"
                  fill="#817583"
                />
                <path
                  d="M7.00006 7.06396C6.76089 7.06396 6.56256 6.86563 6.56256 6.62646V6.50399C6.56256 5.82732 7.05838 5.49481 7.24505 5.36648C7.46088 5.22065 7.53087 5.12148 7.53087 4.96982C7.53087 4.67815 7.29172 4.43896 7.00006 4.43896C6.70839 4.43896 6.46924 4.67815 6.46924 4.96982C6.46924 5.20898 6.27091 5.40732 6.03174 5.40732C5.79257 5.40732 5.59424 5.20898 5.59424 4.96982C5.59424 4.19398 6.22422 3.56396 7.00006 3.56396C7.77589 3.56396 8.40587 4.19398 8.40587 4.96982C8.40587 5.63482 7.9159 5.96731 7.73506 6.08981C7.50756 6.24147 7.43756 6.34065 7.43756 6.50399V6.62646C7.43756 6.87146 7.23922 7.06396 7.00006 7.06396Z"
                  fill="#817583"
                />
                <path
                  d="M7 8.5166C7.245 8.5166 7.4375 8.31827 7.4375 8.0791C7.4375 7.83993 7.23917 7.6416 7 7.6416C6.76083 7.6416 6.5625 7.83993 6.5625 8.0791C6.5625 8.31827 6.755 8.5166 7 8.5166Z"
                  fill="#817583"
                />
              </svg>
              <Text
                variant="medium"
                type="subBody"
                className="text-Secondary-Color"
              >
                Suggestions
              </Text>
            </div>
            <div className="flex w-full flex-wrap gap-2 overflow-hidden">
              {isSuggestionFetching ? (
                <div className="flex w-full flex-col gap-4">
                  <div className="h-[34px] w-2/3 animate-pulse rounded-lg bg-Skeleton-Loader-Content" />
                  <div className="h-[34px] w-3/4 animate-pulse rounded-lg bg-Skeleton-Loader-Content" />
                  <div className="h-[34px] w-1/2 animate-pulse rounded-lg bg-Skeleton-Loader-Content" />
                </div>
              ) : (
                suggestions?.map((suggestion) => (
                  <SuggestionItem
                    suggestion={suggestion}
                    key={suggestion}
                    onClick={() => setText(suggestion)}
                  />
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default memo(PerplexityMessage)
