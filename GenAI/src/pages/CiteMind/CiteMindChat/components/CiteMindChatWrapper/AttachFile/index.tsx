import IconButton from '@/components/IconButton'
import { useCallback, useRef } from 'react'

interface Props {
  multiple?: boolean
  loading?: any
  disabled?: boolean
  dataTypes?: any
  sendFiles?: (value: any) => void
}

const AttachFile = ({
  multiple = true,
  dataTypes = [],
  loading = false,
  disabled = false,
  sendFiles,
}: Props) => {
  const inpRef = useRef<HTMLInputElement>(null)

  const onUpload = useCallback(
    (e: any) => {
      e.preventDefault()

      const files = [...e.target.files]

      sendFiles?.(files)
      e.target.value = ''
    },
    [multiple]
  )

  return (
    <>
      <input
        ref={inpRef}
        type="file"
        name="file"
        multiple={multiple}
        accept={dataTypes?.join(', ')}
        onChange={onUpload}
        style={{ display: 'none' }}
        disabled={disabled || loading}
      />

      <IconButton
        nameIcon="Bold-MessagesConversation-Paperclip"
        disabled={loading}
        onClick={() => inpRef?.current?.click()}
        sizeIcon={16}
        tooltipText="Attach files"
      />
    </>
  )
}

export default AttachFile
