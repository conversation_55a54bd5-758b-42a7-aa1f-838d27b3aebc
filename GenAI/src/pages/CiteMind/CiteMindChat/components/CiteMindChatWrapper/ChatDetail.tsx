import Icon from '@/assets/icon/Icon'
import BotMessage from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/BotMessage'
import Responding from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/Responding'
import UserMessage from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/components/UserMessage'
import {
  EMessageType,
  IMessage,
  MAX_LENGTH,
  VALID_DATA_TYPES_FILE_UPLOAD,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { colors } from '@/theme'
import { memo, useCallback, useMemo, useRef, useState } from 'react'
import { twMerge } from 'tailwind-merge'

import FileDownload from '@/components/PlaygroundMultiAgent/FileDownload'
import Text from '@/components/Text'
import { Switch } from '@headlessui/react'
import { nanoid } from 'nanoid'
import AttachFile from './AttachFile'
import ButtonNewChat from './ButtonNewChat'
import CitationMessage from './CitationMessage'
import FileProcessStatus from './FileProcessStatus'
import FileUploadMessage from './FileUploadMessage'
import {
  ILocalFile,
  ILocalMessageParams,
  getErrorValidateFileWithMax,
} from './helper'
import PerplexityMessage from './PerplexityMessage'
import TextAreaMessage from './TextAreaMessage'

interface Props {
  className?: string
  workerType: string

  message: IMessage[]

  responding: boolean
  text: string

  placeholder?: string

  userMessageAvatar?: React.ReactNode

  setText: (text: string) => void

  // resetHandler
  onClickNewChat: () => void
  // chatHandler
  onSend: (message: string) => void

  // attach files
  handleFiles: (type: EMessageType, params: ILocalMessageParams) => void
  isAttachingFile?: boolean

  isPerplexityOn?: boolean
  setPerplexityMode?: (value: boolean) => void
}

const ChatDetail = ({
  className,
  workerType,

  message,

  responding,

  text,
  placeholder = 'Send message to start',
  userMessageAvatar,

  setText,
  onClickNewChat,
  onSend,

  handleFiles,
  isAttachingFile = false,

  isPerplexityOn = false,
  setPerplexityMode,
}: Props) => {
  const textAreaRef = useRef<any>()
  const [files, setFiles] = useState<ILocalFile[]>([])

  const IconProps = useMemo(() => {
    if (text.trimStart().trimEnd().length === 0 || responding) {
      return { color: colors['Base-Neutral'] }
    }
    return { gradient: ['#642B73', '#C6426E'] }
  }, [text, responding])

  const classNameBtn = useMemo(() => {
    if (text.length === 0 || responding) {
      return 'h-[20px] w-[20px] rounded border-0 bg-transparent flex items-center justify-center'
    }
    return 'flex items-center justify-center h-[20px] w-[20px] rounded border-0 hover:bg-Hover-2'
  }, [text, responding])

  const _setText = useCallback((value: string) => {
    setText(value)
    textAreaRef.current?.focus()
  }, [])

  const renderChatDetail = useCallback(() => {
    return (
      <>
        {message.map((item) => {
          const messageAvatar =
            item.worker?.workerAvatar ??
            '/assets/images/avatar_bot_waiting.svg?react'

          if (item.type === EMessageType.user) {
            return (
              <UserMessage
                message={item.text!}
                time={item.time}
                key={item.id}
                userMessageAvatar={userMessageAvatar}
              />
            )
          }

          if (item.type === EMessageType.fileUploadMessage) {
            return (
              <FileUploadMessage
                key={item.id}
                files={item.files!}
                text={item.text}
                intent={item.intent}
                time={item.time}
              />
            )
          }

          if (item.type === EMessageType.fakeMessageFileProcessStatus) {
            return (
              <FileProcessStatus
                key={item.id}
                text={item.text!}
                className="p-[4px]"
                avatar={messageAvatar}
              />
            )
          }

          if (item.type === EMessageType.citationMessage) {
            return (
              <CitationMessage
                avatar="/assets/images/avatar_bot_waiting.svg?react"
                item={item}
                key={item.id}
                workerType={workerType}
                className="p-2"
                responding={responding}
                message={message}
                setText={_setText}
              />
            )
          }

          if (item.type === EMessageType.perplexityMessage) {
            return (
              <PerplexityMessage
                avatar="/assets/images/avatar_bot_waiting.svg?react"
                item={item}
                key={item.id}
                workerType={workerType}
                className="p-2"
                responding={responding}
                message={message}
                setText={_setText}
              />
            )
          }

          if (item.type === EMessageType.fileUrlCitation) {
            return (
              <FileDownload
                key={item.id}
                avatar={messageAvatar}
                workerType={workerType}
                time={item.time}
                file={item.file_url!}
                className="p-[4px]"
              />
            )
          }

          return (
            <BotMessage
              avatar={messageAvatar}
              message={item.text!}
              time={item.time}
              key={item.id}
              loading={item.loading}
              workerType={workerType}
              className="p-2"
              related_question={item.related_question}
              responding={responding}
              setText={_setText}
            />
          )
        })}
      </>
    )
  }, [message, responding])

  const _handleFiles = (files: FileList) => {
    const localFiles = Array.from(files).map((file) => {
      return {
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        id: nanoid(),
        error: getErrorValidateFileWithMax(file.size, file.type, 200),
      }
    })

    setFiles((prev) => [...prev, ...localFiles])
  }

  const _handleSend = (type: EMessageType, params: ILocalMessageParams) => {
    switch (type) {
      case EMessageType.user:
        onSend(params.text ?? '')
        break
      case EMessageType.fileUploadMessage: {
        const { files, intent, text } = params

        const filesFilteredError = files?.filter((file) => !file.error)

        if (filesFilteredError?.length === 0) {
          if (!intent && text?.trim()) {
            onSend(text)
            setFiles([])
            return
          } else if (intent) {
            setText('')
            setFiles([])
          }
          return
        }
        setFiles([])
        handleFiles(type, {
          ...params,
          files: filesFilteredError,
        })
        break
      }

      default:
        break
    }
  }

  return (
    <div
      className={twMerge(
        'flex w-full flex-1 flex-col overflow-hidden rounded-[12px] bg-white pb-1',
        className
      )}
    >
      {!message?.length && (
        <div className="flex w-full flex-col items-center justify-center gap-2 py-4">
          <div className="flex size-11 items-center justify-center rounded-full bg-Main-11">
            <Icon
              name="Bold-FacesEmotionsStickers-ExpressionlessSquare"
              gradient={['#642B73', '#C6426E']}
              size={32}
            />
          </div>

          <div className="flex flex-col items-center">
            <Text type="title" variant="semibold">
              Hi
            </Text>
            <Text
              type="subheading"
              variant="medium"
              className="text-Secondary-Color"
            >
              Tell me the information you need, I’ll find the citation for you!
            </Text>
          </div>
        </div>
      )}

      <div className="genai-scrollbar flex flex-1 flex-col-reverse overflow-y-auto">
        {responding && (
          <Responding
            avatar="/assets/images/avatar_bot_waiting.svg?react"
            workerType={workerType}
          />
        )}
        {renderChatDetail()}
      </div>

      <div className="mb-2 mt-[12px] flex justify-between px-[12px]">
        <div className="flex items-center pl-1">
          <Icon
            name="Outline-Map&Location-Global"
            size={16}
            color={colors['Primary-Color']}
          />
          <Text elementType="div" className="ml-1 mr-2" type="subBody">
            Online search
          </Text>
          <Switch
            checked={isPerplexityOn}
            onChange={setPerplexityMode}
            className="group inline-flex h-4 w-8 items-center rounded-full bg-Base-Neutral p-0.5 transition data-[checked]:bg-Main-Color"
          >
            <span className="size-3 translate-x-0 rounded-full bg-white transition group-data-[checked]:translate-x-4" />
          </Switch>
        </div>
        <div className="flex items-center gap-1">
          <AttachFile
            sendFiles={_handleFiles}
            dataTypes={VALID_DATA_TYPES_FILE_UPLOAD}
            loading={isAttachingFile || responding}
          />

          <ButtonNewChat
            onClickNewChat={onClickNewChat}
            disabled={responding || isAttachingFile}
          />
        </div>
      </div>
      <TextAreaMessage
        ref={textAreaRef}
        // isAutofocus={true}
        placeholder={placeholder}
        className="h-[unset] max-h-[128px] min-h-[80px] text-Primary-Color"
        classNameTextAreaWrapper="bg-transparent"
        classNameWrapper="px-[4px]"
        maxLength={MAX_LENGTH}
        maxHeight={128}
        autoResize
        onChange={setText}
        value={text}
        onSend={_handleSend}
        onBlur={() => setText(text.trim())}
        classNameBtn={classNameBtn}
        iconsProps={IconProps}
        iconsBtnDisabled={text.trimStart().trimEnd().length === 0 || responding}
        files={files}
        handleDeleteFile={(file) => {
          setFiles((prev) => prev.filter((item) => item.id !== file.id))
        }}
      />
    </div>
  )
}

export default memo(ChatDetail)
