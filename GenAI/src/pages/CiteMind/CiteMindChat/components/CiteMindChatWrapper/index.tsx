import { WorkflowResponseMessage } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import {
  GEN_AI_INTERNAL_PATH,
  GEN_AI_PATH,
  HTTP_STATUS_CODE,
} from '@/constants'
import { getWSCiteMindUrl } from '@/helpers'
import axiosService from '@/pages/CiteMind/axios'
import {
  EMessageType,
  IMessage,
  LETS_START_MESSAGE,
  LETS_START_RESPONSE,
  getTimeChat,
} from '@/pages/Workers/components/ModalUpdateWorker/WorkerSetting/Playground/helpers'
import { WorkerType } from '@/pages/Workers/types'
import { nanoid } from 'nanoid'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { v4 as uuid } from 'uuid'
import ChatDetail from './ChatDetail'
import { ILocalMessageParams } from './helper'

interface IProps {
  workflowId: string
}

const CiteMindChatWrapper = ({ workflowId }: IProps) => {
  const [message, setMessage] = useState<IMessage[]>([])
  const [text, setText] = useState('')
  const [responding, setResponding] = useState(false)

  const [responseMessage, setResponseMessage] = useState('')
  const [responseTime, setResponseTime] = useState('')
  const [hasRelatedQuestion, setHasRelatedQuestion] = useState(false)

  const [isAttachingFile, setAttachingFile] = useState(false)

  const [sessionId, setSessionId] = useState<string>(uuid())
  const [wsMessage, setWsMessage] = useState<WorkflowResponseMessage | null>(
    null
  )

  const messageQueue = useRef<WorkflowResponseMessage[]>([])
  const isProcessing = useRef(false)

  const processQueue = () => {
    if (messageQueue.current.length > 0 && !isProcessing.current) {
      isProcessing.current = true
      const message = messageQueue.current.shift()
      setWsMessage(message!)
      setTimeout(() => {
        isProcessing.current = false
        processQueue()
      }, 10)
    }
  }

  const [isPerplexityOn, setPerplexityMode] = useState(false)

  const socket = useRef<WebSocket | null>(null)
  const [isSocketReady, setSocketReady] = useState(false)

  const connectToPlayground = async () => {
    if (socket.current) {
      socket.current.close()
      socket.current = null
    }
    setResponding(false)
    if (!workflowId) return
    const tSocket = new WebSocket(getWSCiteMindUrl(workflowId, sessionId))
    tSocket.onopen = () => {
      console.log('Connected to playground')
      setSocketReady(true)
    }
    socket.current = tSocket
  }

  useEffect(() => {
    if (!socket.current) return

    socket.current.onmessage = (event) => {
      const res = JSON.parse(event.data) as WorkflowResponseMessage
      switch (res.type) {
        case EMessageType.worker:
          if (!responseTime) {
            setResponseTime(getTimeChat())
          }
          if (res.related_question) setHasRelatedQuestion(true)
          setResponseMessage((prev) => prev + res.text)

          break

        case EMessageType.end:
          setResponding(false)

          if (responseMessage) {
            setMessage((pre) => [
              {
                id: nanoid(),
                text: responseMessage,
                type: EMessageType.worker,
                time: responseTime,

                related_question: hasRelatedQuestion,
              },
              ...pre,
            ])
            setResponseMessage('')
            setResponseTime('')
            setHasRelatedQuestion(false)
          }
          break
        default:
          messageQueue.current.push(res)
          processQueue()
          break
      }
    }

    socket.current.onclose = (e) => {
      console.log('Onclose to playground', e)

      setTimeout(() => {
        console.log('Reconnect to playground')
        connectToPlayground()
      }, 1000)

      setSocketReady(false)
      socket.current = null
    }
  }, [sessionId, message, wsMessage, responseMessage, hasRelatedQuestion])

  useEffect(() => {
    if (!wsMessage) return

    switch (wsMessage.type) {
      // case EMessageType.worker:
      //   if (wsMessage.related_question) setResponding(false)

      //   setMessage((pre) => [
      //     {
      //       id: nanoid(),
      //       text: wsMessage.text,
      //       type: EMessageType.worker,
      //       time: getTimeChat(),
      //       related_question: wsMessage.related_question,
      //     },
      //     ...pre,
      //   ])
      //   break
      // case EMessageType.end:
      //   setResponding(false)
      //   break
      case EMessageType.noTokenMessage:
        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.noTokenMessage,
            time: getTimeChat(),
          },
          ...pre,
        ])
        setResponding(false)
        break
      case EMessageType.citationMessage:
        if (wsMessage.related_question) setResponding(false)

        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.citationMessage,
            time: getTimeChat(),
            citation: wsMessage.citation as any,
            speakerId: wsMessage.speaker_id!,
            related_question: wsMessage.related_question,
          },
          ...pre,
        ])
        break
      case EMessageType.fileUrlCitation:
        setMessage((pre) => [
          {
            id: nanoid(),
            text: wsMessage.text,
            type: EMessageType.fileUrlCitation,
            time: getTimeChat(),
            file_url: wsMessage.file_url!,
          },
          ...pre,
        ])
        break

      default:
        break
    }
  }, [wsMessage])

  useEffect(() => {
    connectToPlayground()
  }, [workflowId, sessionId])

  useEffect(() => {
    return () => {
      socket.current?.close()
    }
  }, [])

  const createNewChat = () => {
    setMessage([])
    setText('')
    setSessionId(uuid())
  }

  const handleChatPerplexity = async (value: string) => {
    try {
      if (responding) return
      setResponding(true)

      const res = await axiosService.post(
        `${GEN_AI_PATH}/api/v1/citation/perplexity-completion`,
        {
          messages: [
            {
              content: value,
              role: 'user',
            },
          ],
        }
      )

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setMessage((pre) => [
          {
            id: nanoid(),
            text: data?.choices?.[0]?.message?.content,
            type: EMessageType.perplexityMessage,
            time: getTimeChat(),
            citations: data?.citations,
            related_question: true,
          },
          ...pre,
        ])
      }
    } catch (error) {
      console.log(error)
    } finally {
      setResponding(false)
    }
  }

  const chatHandler = (tVal: string) => {
    const val = tVal.trim()
    if (val.length === 0 || responding || !isSocketReady) return
    setResponding(true)
    setText('')
    setMessage((pre) => [
      {
        id: nanoid(),
        text: val,
        type: EMessageType.user,
        time: getTimeChat(),
      },
      ...pre,
    ])
    if (val.toLocaleLowerCase() === LETS_START_MESSAGE) {
      setTimeout(() => {
        setMessage((pre) => [
          {
            id: nanoid(),
            text: LETS_START_RESPONSE,
            type: EMessageType.worker,
            time: getTimeChat(),
            hideCopy: true,
          },
          ...pre,
        ])
        setResponding(false)
      }, 1000)
    } else {
      if (isPerplexityOn) {
        handleChatPerplexity(val)
      } else {
        socket.current?.send(
          JSON.stringify({
            text: val,
            type: EMessageType.user,
          })
        )
      }
    }
  }

  const handleFiles = async (
    __type: EMessageType,
    params: ILocalMessageParams
  ) => {
    try {
      if (isAttachingFile || !params.files) return

      const { text, intent } = params

      if (text) {
        setText('')
      }
      setResponding(true)

      const files = [...params.files]

      if (files?.length) {
        // Fake file message
        let listFakeMessageFiles = files

        const idMessage = nanoid()
        const time = getTimeChat()

        setAttachingFile(true)

        setMessage((pre) => [
          {
            id: idMessage,
            files: files,
            text,
            intent,
            type: EMessageType.fileUploadMessage,
            time,
          },
          ...pre,
        ])

        const listFilePromises = files.map((file) => {
          const formData = new FormData()
          formData.append('file_display_name', file?.name)
          formData.append('file_storage_upload', file.file)
          if (intent) formData.append('file_intent', intent as any)

          return axiosService.post(
            `${GEN_AI_INTERNAL_PATH}/api/v1/workflow/${workflowId}/session/${sessionId}/upload-kb-citemind`,
            formData
          )
        })

        const listFileResponses = await Promise.all(listFilePromises)

        const fileSuccess: any = []

        for (let i = 0; i < listFileResponses?.length; i++) {
          if (listFileResponses[i].status === HTTP_STATUS_CODE.SUCCESS) {
            const dataRes = listFileResponses[i].data

            if (dataRes) fileSuccess.push(dataRes.data.id)
          } else {
            listFakeMessageFiles = listFakeMessageFiles?.map(
              (item: any, index: number) => {
                if (i === index && !item.error) {
                  return {
                    ...item,
                    error: 'Failed',
                  }
                }
                return item
              }
            )
          }
        }

        const newMessage = {
          id: idMessage,
          files: listFakeMessageFiles,
          text,
          intent,
          type: EMessageType.fileUploadMessage,
          time,
        }

        setMessage((prev) => {
          const newMessages = JSON.parse(JSON.stringify(prev)) as IMessage[]

          // update message with idMessage
          const index = newMessages.findIndex((item) => item.id === idMessage)
          if (index !== -1) {
            newMessages[index] = newMessage
          }

          return newMessages
        })

        if (fileSuccess?.length) {
          socket?.current?.send(
            JSON.stringify({
              type: EMessageType.fileUploadMessage,
              text: fileSuccess?.join(', '),
            })
          )

          setTimeout(() => {
            socket?.current?.send(
              JSON.stringify({
                type: EMessageType.user,
                text: intent ?? text,
              })
            )
          }, 100)
        } else {
          setResponding(false)
        }
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setAttachingFile(false)
    }
  }

  const userMessageAvatar = useMemo(() => {
    return (
      <div className="flex size-6 items-center justify-center rounded-full bg-Main-11">
        <Icon
          name="vuesax-bold-user"
          gradient={['#642B73', '#C6426E']}
          size={16}
        />
      </div>
    )
  }, [])

  const displayMessage = useMemo(() => {
    if (responding && responseMessage) {
      return [
        {
          text: responseMessage,
          type: EMessageType.worker,
          time: getTimeChat(),
          loading: true,
        },
        ...message,
      ]
    }

    return message
  }, [message, responding, responseMessage])

  return (
    <ChatDetail
      message={displayMessage}
      workerType={WorkerType.AI}
      text={text}
      setText={setText}
      responding={responding}
      onClickNewChat={createNewChat}
      onSend={chatHandler}
      handleFiles={handleFiles}
      isAttachingFile={isAttachingFile}
      userMessageAvatar={userMessageAvatar}
      isPerplexityOn={isPerplexityOn}
      setPerplexityMode={setPerplexityMode}
    />
  )
}

export default memo(CiteMindChatWrapper)
