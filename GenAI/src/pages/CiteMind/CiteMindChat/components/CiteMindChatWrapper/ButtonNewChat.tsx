import IconButton from '@/components/IconButton'
import { useViewDocument } from '@/pages/CiteMind/contexts/ViewDocumentContext'
import { memo } from 'react'

const ButtonNewChat = ({ onClickNewChat, disabled }: any) => {
  const { toggleSidebar, setData } = useViewDocument()

  return (
    <IconButton
      disabled={disabled}
      nameIcon="vuesax-outline-message-add"
      sizeIcon={16}
      onClick={() => {
        toggleSidebar(false)
        setData(undefined)
        onClickNewChat()
      }}
      tooltipText="New chat"
      tooltipPosition="topRight"
    />
  )
}

export default memo(ButtonNewChat)
