import { chatBaseGetAppIntegrationByWflApi } from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import { MessageDialog } from '@/components/DialogMessage'
import IconButton from '@/components/IconButton'
import Message from '@/components/Message'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { colors } from '@/theme'
import { memo, useCallback, useEffect, useReducer, useState } from 'react'
import ChooseApp from './components/ChooseApp'
import ChooseIntegrationType from './components/ChooseIntegrationType'
import {
  Embed,
  IntegrationComingSoon,
  Messenger,
  Telegram,
  WhatsApp,
  Zalo,
} from './components/ConfigIntegration'
import { APPS, CHANNEL_TYPE } from './constants'
import { Step } from './types'

const initialState: any = {
  currentStep: Step.CHOOSE_APP,
  selectedApp: null,
  embed: {
    isIntegrated: false,
    status: '',
  },
  whatsApp: {
    isIntegrated: false,
    status: '',
  },
  telegram: {
    isIntegrated: false,
    status: '',
  },
  instagram: {
    isIntegrated: false,
    status: '',
  },
  messenger: {
    isIntegrated: false,
    status: '',
  },
  zalo: {
    isIntegrated: false,
    status: '',
  },
}

interface IProps {
  isOpen?: boolean
  workflowId: string
  onClose: () => void
}

const WorkflowIntegration = ({
  isOpen = false,
  workflowId,
  onClose,
}: IProps) => {
  const [isLoading, setLoading] = useState(false)
  const [isDirtyData, setDirtyData] = useState(false)

  const [workflowIntegrationStore, setWorkflowIntegrationStore] = useReducer(
    (state: any, newState: Partial<any>) => ({
      ...state,
      ...newState,
    }),
    initialState
  )

  const fetchAppIntegration = useCallback(async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseGetAppIntegrationByWflApi({
        query: {
          workflow_id: workflowId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data }: any = res

        const embed = data?.data?.find(
          (item: any) => item.channel_type === CHANNEL_TYPE.EmbeddedWidget
        )
        const whatsApp = data?.data?.find(
          (item: any) => item.channel_type === CHANNEL_TYPE.WhatsApp
        )
        const telegram = data?.data?.find(
          (item: any) => item.channel_type === CHANNEL_TYPE.Telegram
        )
        const instagram = data?.data?.find(
          (item: any) => item.channel_type === CHANNEL_TYPE.Instagram
        )
        const messenger = data?.data?.find(
          (item: any) => item.channel_type === CHANNEL_TYPE.Messenger
        )
        const zalo = data?.data?.find(
          (item: any) => item.channel_type === CHANNEL_TYPE.Zalo
        )

        setWorkflowIntegrationStore({
          embed: {
            isIntegrated: embed?.integration_status ?? false,
            status: embed?.status ?? '',
          },
          whatsApp: {
            isIntegrated: whatsApp?.integration_status ?? false,
            status: whatsApp?.status ?? '',
          },
          telegram: {
            isIntegrated: telegram?.integration_status ?? false,
            status: telegram?.status ?? '',
          },
          instagram: {
            isIntegrated: instagram?.integration_status ?? false,
            status: instagram?.status ?? '',
          },
          messenger: {
            isIntegrated: messenger?.integration_status ?? false,
            status: messenger?.status ?? '',
          },
          zalo: {
            isIntegrated: zalo?.integration_status ?? false,
            status: zalo?.status ?? '',
          },
        })
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }, [workflowId])

  useEffect(() => {
    if (workflowId) fetchAppIntegration()
  }, [workflowId])

  const handleCloseModal = useCallback(() => {
    if (isDirtyData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          onClose()
        },
      })
    } else {
      onClose()
    }
  }, [isDirtyData])

  const renderContent = () => {
    switch (workflowIntegrationStore.currentStep) {
      case Step.CONFIG:
        switch (workflowIntegrationStore.selectedApp) {
          case APPS.API_BASE:
            return <></>
          case APPS.EMBED:
            return (
              <Embed
                workflowId={workflowId}
                workflowIntegrationStore={workflowIntegrationStore}
                setWorkflowIntegrationStore={setWorkflowIntegrationStore}
                setDirtyData={setDirtyData}
              />
            )
          case APPS.WHATSAPP:
            return (
              <WhatsApp
                workflowId={workflowId}
                workflowIntegrationStore={workflowIntegrationStore}
                setWorkflowIntegrationStore={setWorkflowIntegrationStore}
                setDirtyData={setDirtyData}
              />
            )
          case APPS.TELEGRAM:
            return (
              <Telegram
                workflowId={workflowId}
                workflowIntegrationStore={workflowIntegrationStore}
                setWorkflowIntegrationStore={setWorkflowIntegrationStore}
                setDirtyData={setDirtyData}
              />
            )

          case APPS.ZALO:
            return (
              <Zalo
                workflowId={workflowId}
                workflowIntegrationStore={workflowIntegrationStore}
                setWorkflowIntegrationStore={setWorkflowIntegrationStore}
                setDirtyData={setDirtyData}
              />
            )

          case APPS.MESSENGER:
            return (
              <Messenger
                workflowId={workflowId}
                workflowIntegrationStore={workflowIntegrationStore}
                setWorkflowIntegrationStore={setWorkflowIntegrationStore}
                setDirtyData={setDirtyData}
              />
            )

          case APPS.INSTAGRAM:
            return (
              <IntegrationComingSoon
                setWorkflowIntegrationStore={setWorkflowIntegrationStore}
              />
            )
        }
        break

      case Step.CHOOSE_APP:
        return (
          <ChooseApp
            workflowId={workflowId}
            workflowIntegrationStore={workflowIntegrationStore}
            setWorkflowIntegrationStore={setWorkflowIntegrationStore}
          />
        )

      default:
        return (
          <ChooseIntegrationType
            setWorkflowIntegrationStore={setWorkflowIntegrationStore}
          />
        )
    }
  }

  return (
    <BaseModal isPureModal isOpen={isOpen} onClose={handleCloseModal}>
      <div className="relative flex min-h-[417px] min-w-[712px] flex-col overflow-hidden rounded-[20px] bg-Base-03 shadow-md">
        <IconButton
          className="absolute right-[10px] top-[10px] cursor-pointer duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={handleCloseModal}
        />

        <div className="flex flex-col gap-1 bg-Base-03 p-3 pb-4">
          <Text
            value="Workflow Integration"
            type="subheading"
            variant="medium"
            className="text-Primary-Color"
          />
          <Text
            value="Export and integrate workflow with your business"
            type="subBody"
            className="text-Secondary-Color"
          />
        </div>

        {renderContent()}
      </div>
    </BaseModal>
  )
}

export default memo(WorkflowIntegration)
