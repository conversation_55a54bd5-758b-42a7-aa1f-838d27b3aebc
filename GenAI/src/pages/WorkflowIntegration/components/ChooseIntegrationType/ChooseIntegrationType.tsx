import Icon from '@/assets/icon/Icon'
import { CustomApiBase } from '@/pages/WorkflowIntegration/assets'
import { APPS } from '@/pages/WorkflowIntegration/constants'
import { memo, useMemo } from 'react'
import { IntegrationType, Step } from '../../types'
import IntegrationTypeItem from './IntegrationTypeItem'

interface IProps {
  setWorkflowIntegrationStore: (data: any) => void
}

const ChooseIntegrationType = ({ setWorkflowIntegrationStore }: IProps) => {
  const integrationTypes = useMemo(
    () => [
      {
        icon: <CustomApiBase />,
        name: 'API Base',
        description:
          'Leverage AI-powered APIs to enhance system efficiency and streamline operations',
        value: IntegrationType.API_BASE,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.API_BASE,
          })
        },
      },
      {
        icon: (
          <Icon
            name="Bold-MessagesConversation-ChatRoundUnread"
            size={44}
            gradient={['#642B73', '#C6426E']}
          />
        ),
        name: 'Chat Base',
        description:
          'Integrate workflow with your conversational process which your clients could directly interact with',
        value: IntegrationType.CHAT_BASE,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CHOOSE_APP,
          })
        },
      },
    ],
    []
  )

  return (
    <div className="flex w-full items-center justify-center p-3 pt-0">
      <div className="flex h-[315px] w-[676px] items-center justify-center gap-11 rounded-xl border border-neutral-200 bg-white">
        {integrationTypes.map((type) => (
          <IntegrationTypeItem key={type.value} {...type} />
        ))}
      </div>
    </div>
  )
}

export default memo(ChooseIntegrationType)
