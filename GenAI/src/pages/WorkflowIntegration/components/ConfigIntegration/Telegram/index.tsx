import {
  ChatBaseStatusTypes,
  chatBaseCreateTelegramConfiguration<PERSON>pi,
  chatBaseGetTelegramConfiguration<PERSON>pi,
} from '@/apis/client'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import { GEN_AI_PATH, HTTP_STATUS_CODE } from '@/constants'
import { Step } from '@/pages/WorkflowIntegration/types'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import CopyButton from '../common/CopyButton'

interface IProps {
  workflowId: string
  workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
}

const Telegram = ({
  workflowId,
  workflowIntegrationStore,
  setWorkflowIntegrationStore,
  setDirtyData,
}: IProps) => {
  const {
    telegram: { isIntegrated, status },
  } = workflowIntegrationStore

  const [isChangedData, setChangedData] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [isLoadingData, setLoadingData] = useState(false)

  const [botToken, setBotToken] = useState('')

  const handleBack = useCallback(() => {
    if (isChangedData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          setDirtyData(false)

          setWorkflowIntegrationStore({
            currentStep: Step.CHOOSE_APP,
            selectedApp: null,
          })
        },
      })
    } else {
      setDirtyData(false)

      setWorkflowIntegrationStore({
        currentStep: Step.CHOOSE_APP,
        selectedApp: null,
      })
    }
  }, [isChangedData])

  const fetchWhatsAppConfiguration = useCallback(async () => {
    try {
      if (isLoadingData) return
      setLoadingData(true)

      const res = await chatBaseGetTelegramConfigurationApi({
        query: {
          workflow_id: workflowId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setBotToken(data?.data?.telegram_bot_token ?? '')
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoadingData(false)
    }
  }, [workflowId])

  useEffect(() => {
    if (isIntegrated) {
      fetchWhatsAppConfiguration()
    }
  }, [isIntegrated])

  const callbackAPIValue = useMemo(
    () =>
      `${GEN_AI_PATH}/api/v1/chat-base/telegram/workflow/${workflowId}/webhook`,
    [workflowId, GEN_AI_PATH]
  )

  const isInvalidData = useMemo(() => !botToken?.trim(), [botToken])

  const handleIntegrate = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseCreateTelegramConfigurationApi({
        body: {
          telegram_bot_token: botToken,
          workflow_id: workflowId,
          callback_url: callbackAPIValue,
          status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setWorkflowIntegrationStore({
          currentStep: Step.CHOOSE_APP,
          selectedApp: null,
          telegram: {
            isIntegrated: true,
            status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
          },
        })

        Message.success({
          message: isIntegrated
            ? 'Successfully updated Telegram integration'
            : 'Successfully integrated workflow to Telegram',
        })

        setDirtyData(false)
        setChangedData(false)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex w-[1032px] flex-col gap-3 rounded-xl border border-neutral-200 bg-white px-11 pb-8 pt-5">
        <div className="w-full text-center">
          <Text
            value="Integrate to Telegram"
            variant="medium"
            type="subheading"
            className="text-Primary-Color"
          />
        </div>
        {isLoadingData ? (
          <div className="flex h-[286px] w-full items-center justify-center">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <div className="flex w-full flex-col gap-2">
              <Text
                value="Configuration"
                variant="semibold"
                className="text-Primary-Color"
              />
              <div className="flex w-full gap-3 rounded-lg border border-border-base-icon p-3">
                <div className="flex flex-1 flex-col gap-1 overflow-hidden">
                  <Text
                    value="AI Agency Callback API"
                    variant="medium"
                    type="subBody"
                    className="text-Tertiary-Color"
                  />
                  <div className="flex w-full items-center gap-3">
                    <Text
                      value={callbackAPIValue}
                      type="subBody"
                      ellipsis
                      elementType="div"
                      className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                    />
                    <CopyButton value={callbackAPIValue} />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex w-full flex-col gap-2">
              <Text
                value="Application Information"
                variant="semibold"
                className="text-Primary-Color"
              />
              <div className="flex w-full flex-col gap-2 rounded-lg border border-border-base-icon p-3">
                <Input
                  value={botToken}
                  label="Telegram bot token"
                  isFullWidth
                  onChange={(e) => {
                    setChangedData(true)
                    setDirtyData(true)
                    setBotToken(e.target.value)
                  }}
                  onBlur={() => setBotToken(botToken?.trim())}
                  placeholder="Send /newbot to BotFather on Telegram to get the token"
                />
              </div>
            </div>
          </>
        )}
      </div>

      <div className="flex justify-end gap-3">
        <Button text="Back" type="secondary" onClick={handleBack} />
        <Button
          text="Integrate"
          type="primary"
          onClick={handleIntegrate}
          disabled={!isChangedData || isInvalidData}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

export default memo(Telegram)
