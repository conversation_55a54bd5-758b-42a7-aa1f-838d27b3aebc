import {
  ChatBaseStatusTypes,
  chatBaseCreateWhatsappConfiguration<PERSON>pi,
  chatBaseGetWhatsappConfigurationApi,
} from '@/apis/client'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import { GEN_AI_PATH, HTTP_STATUS_CODE } from '@/constants'
import GenerateToken from '@/pages/WorkflowIntegration/components/ConfigIntegration/common/GenerateToken'
import { LIMIT_LENGTH_ID } from '@/pages/WorkflowIntegration/constants'
import { Step } from '@/pages/WorkflowIntegration/types'
import { nanoid } from 'nanoid'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import CopyButton from '../common/CopyButton'

interface IProps {
  workflowId: string
  workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
}

const WhatsApp = ({
  workflowId,
  workflowIntegrationStore,
  setWorkflowIntegrationStore,
  setDirtyData,
}: IProps) => {
  const {
    whatsApp: { isIntegrated, status },
  } = workflowIntegrationStore

  const [isChangedData, setChangedData] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [isLoadingData, setLoadingData] = useState(false)

  const [token, setToken] = useState('')
  const [appId, setAppId] = useState('')
  const [appSecret, setAppSecret] = useState('')
  const [accessToken, setAccessToken] = useState('')
  const [phoneNumberId, setPhoneNumberId] = useState('')
  const [whatsAppAccountId, setWhatsAppAccountId] = useState('')

  const handleBack = useCallback(() => {
    if (isChangedData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          setDirtyData(false)

          setWorkflowIntegrationStore({
            currentStep: Step.CHOOSE_APP,
            selectedApp: null,
          })
        },
      })
    } else {
      setDirtyData(false)

      setWorkflowIntegrationStore({
        currentStep: Step.CHOOSE_APP,
        selectedApp: null,
      })
    }
  }, [isChangedData])

  const fetchWhatsAppConfiguration = useCallback(async () => {
    try {
      if (isLoadingData) return
      setLoadingData(true)

      const res = await chatBaseGetWhatsappConfigurationApi({
        query: {
          workflow_id: workflowId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setToken(data?.data?.verify_token ?? '')
        setAppId(data?.data?.whatsapp_app_id ?? '')
        setAppSecret(data?.data?.whatsapp_app_secret ?? '')
        setAccessToken(data?.data?.whatsapp_access_token ?? '')
        setPhoneNumberId(data?.data?.whatsapp_phone_number_id ?? '')
        setWhatsAppAccountId(data?.data?.whatsapp_bussiness_account_id ?? '')
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoadingData(false)
    }
  }, [workflowId])

  useEffect(() => {
    if (isIntegrated) {
      fetchWhatsAppConfiguration()
    } else {
      setToken(nanoid(LIMIT_LENGTH_ID))
    }
  }, [isIntegrated])

  const callbackAPIValue = useMemo(
    () =>
      `${GEN_AI_PATH}/api/v1/chat-base/whatsapp/workflow/${workflowId}/webhook`,
    [workflowId, GEN_AI_PATH]
  )

  const isInvalidData = useMemo(
    () =>
      !token?.trim() ||
      !appId?.trim() ||
      !appSecret?.trim() ||
      !accessToken?.trim() ||
      !phoneNumberId?.trim() ||
      !whatsAppAccountId?.trim(),
    [token, appId, appSecret, accessToken, phoneNumberId, whatsAppAccountId]
  )

  const handleIntegrate = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseCreateWhatsappConfigurationApi({
        body: {
          callback_url: callbackAPIValue,
          verify_token: token,
          whatsapp_access_token: accessToken,
          whatsapp_app_id: appId,
          whatsapp_app_secret: appSecret,
          whatsapp_bussiness_account_id: whatsAppAccountId,
          whatsapp_phone_number_id: phoneNumberId,
          workflow_id: workflowId,
          status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setWorkflowIntegrationStore({
          currentStep: Step.CHOOSE_APP,
          selectedApp: null,
          whatsApp: {
            isIntegrated: true,
            status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
          },
        })

        Message.success({
          message: isIntegrated
            ? 'Successfully updated WhatsApp integration'
            : 'Successfully integrated workflow to WhatsApp',
        })

        setDirtyData(false)
        setChangedData(false)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex w-[1232px] flex-col gap-3 rounded-xl border border-neutral-200 bg-white px-11 pb-8 pt-5">
        <div className="w-full text-center">
          <Text
            value="Integrate to WhatsApp"
            variant="medium"
            type="subheading"
            className="text-Primary-Color"
          />
        </div>
        {isLoadingData ? (
          <div className="flex h-[430px] w-full items-center justify-center">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <div className="flex w-full flex-col gap-2">
              <Text value="Configuration" variant="semibold" />
              <div className="flex w-full gap-3 rounded-lg border border-border-base-icon p-3">
                <div className="flex flex-1 flex-col gap-1 overflow-hidden">
                  <Text
                    value="AI Agency Callback API"
                    variant="medium"
                    type="subBody"
                    className="text-Tertiary-Color"
                  />
                  <div className="flex w-full items-center gap-3">
                    <Text
                      value={callbackAPIValue}
                      type="subBody"
                      ellipsis
                      elementType="div"
                      className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                    />
                    <CopyButton value={callbackAPIValue} />
                  </div>
                </div>
                <div className="flex flex-1 flex-col gap-1 overflow-hidden">
                  <Text
                    value="Token"
                    variant="medium"
                    type="subBody"
                    className="text-Tertiary-Color"
                  />
                  <div className="flex w-full items-center gap-3">
                    <Text
                      value={token}
                      type="subBody"
                      ellipsis
                      elementType="div"
                      className="!w-fit max-w-[calc(100%-64px)] pl-2 text-Primary-Color"
                    />
                    <CopyButton value={token} />
                    <GenerateToken
                      generateToken={() => {
                        setChangedData(true)
                        setDirtyData(true)
                        setToken(nanoid(LIMIT_LENGTH_ID))
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex w-full flex-col gap-2">
              <Text value="Application Information" variant="semibold" />
              <div className="flex w-full flex-col gap-2 rounded-lg border border-border-base-icon p-3">
                <div className="flex w-full gap-3">
                  <div className="flex-1">
                    <Input
                      value={appId}
                      label="App ID"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAppId(e.target.value)
                      }}
                      onBlur={() => setAppId(appId?.trim())}
                      placeholder="Copy and paste app id in App Settings"
                    />
                  </div>

                  <div className="flex-1">
                    <Input
                      value={appSecret}
                      label="App Secret"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAppSecret(e.target.value)
                      }}
                      onBlur={() => setAppSecret(appSecret?.trim())}
                      placeholder="Copy and paste app secret in App Settings"
                    />
                  </div>
                </div>

                <Input
                  value={accessToken}
                  label="Access Token"
                  isFullWidth
                  onChange={(e) => {
                    setChangedData(true)
                    setDirtyData(true)
                    setAccessToken(e.target.value)
                  }}
                  onBlur={() => setAccessToken(accessToken?.trim())}
                  placeholder="Copy and paste generated access token after configure WhatsApp to your application"
                  maxLength={1000}
                />

                <div className="flex w-full gap-3">
                  <div className="flex-1">
                    <Input
                      value={phoneNumberId}
                      label="Phone Number ID"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setPhoneNumberId(e.target.value)
                      }}
                      onBlur={() => setPhoneNumberId(phoneNumberId?.trim())}
                      placeholder="Copy and paste phone number id in Application page"
                    />
                  </div>

                  <div className="flex-1">
                    <Input
                      value={whatsAppAccountId}
                      label="WhatsApp Account ID"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setWhatsAppAccountId(e.target.value)
                      }}
                      onBlur={() =>
                        setWhatsAppAccountId(whatsAppAccountId?.trim())
                      }
                      placeholder="Copy and paste account id in Application page"
                    />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      <div className="flex justify-end gap-3">
        <Button text="Back" type="secondary" onClick={handleBack} />
        <Button
          text="Integrate"
          type="primary"
          onClick={handleIntegrate}
          disabled={!isChangedData || isInvalidData}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

export default memo(WhatsApp)
