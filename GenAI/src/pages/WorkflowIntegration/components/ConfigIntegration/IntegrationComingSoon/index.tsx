import Button from '@/components/Button'
import ComingSoon from '@/components/ComingSoon'
import { Step } from '@/pages/WorkflowIntegration/types'
import { memo } from 'react'

interface IProps {
  //   workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
}

const IntegrationComingSoon = ({ setWorkflowIntegrationStore }: IProps) => {
  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex h-[348px] w-[810px] items-center justify-center rounded-xl border border-neutral-200">
        <ComingSoon size="sm" />
      </div>

      <div className="flex justify-end">
        <Button
          text="Back"
          type="secondary"
          onClick={() =>
            setWorkflowIntegrationStore({
              currentStep: Step.CHOOSE_APP,
              selectedApp: null,
            })
          }
        />
      </div>
    </div>
  )
}

export default memo(IntegrationComingSoon)
