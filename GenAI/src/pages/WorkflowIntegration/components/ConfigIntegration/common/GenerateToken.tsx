import IconButton from '@/components/IconButton'
import { colors } from '@/theme'
import { memo, useEffect, useState } from 'react'

const DEBOUNCE_TIME = 800

const GenerateToken = ({ generateToken }: { generateToken: () => void }) => {
  const [isGenerated, setGenerated] = useState(false)

  useEffect(() => {
    if (isGenerated) {
      setTimeout(() => setGenerated(false), DEBOUNCE_TIME)
    }
  }, [isGenerated])

  return (
    <IconButton
      nameIcon="Bold-Arrows-RestartCircle"
      colorIcon={colors.neutral[300]}
      hoverColor={colors['Primary-Color']}
      onClick={() => {
        setGenerated(true)
        generateToken()
      }}
      sizeIcon={16}
      tooltipText={isGenerated ? 'Re-generated token' : 'Re-generate token'}
    />
  )
}

export default memo(GenerateToken)
