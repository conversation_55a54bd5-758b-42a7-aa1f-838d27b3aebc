import IconButton from '@/components/IconButton'
import { copyToClipboard } from '@/helpers'
import { colors } from '@/theme'
import { memo, useEffect, useState } from 'react'

const DEBOUNCE_TIME = 800

const CopyButton = ({ value, size = 16, className }: Props) => {
  const [isCopied, setIsCopied] = useState(false)

  useEffect(() => {
    if (isCopied) {
      setTimeout(() => setIsCopied(false), DEBOUNCE_TIME)
    }
  }, [isCopied])

  return (
    <IconButton
      nameIcon="vuesax-bold-copy"
      colorIcon={colors.neutral[300]}
      hoverColor={colors['Primary-Color']}
      onClick={() => {
        copyToClipboard(value)
        setIsCopied(true)
      }}
      sizeIcon={size}
      tooltipText={isCopied ? 'Copied' : 'Copy'}
      className={className}
    />
  )
}

interface Props {
  value: string
  size?: number
  className?: string
}

export default memo(CopyButton)
