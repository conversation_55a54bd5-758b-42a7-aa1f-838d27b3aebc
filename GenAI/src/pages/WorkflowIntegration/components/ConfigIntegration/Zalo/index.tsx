import {
  ChatBaseStatusTypes,
  chatBaseCreateZaloConfiguration<PERSON>pi,
  chatBaseGetZaloConfigurationApi,
} from '@/apis/client'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import { GEN_AI_PATH, HTTP_STATUS_CODE } from '@/constants'
import { Step } from '@/pages/WorkflowIntegration/types'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import CopyButton from '../common/CopyButton'

interface IProps {
  workflowId: string
  workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
}

const Zalo = ({
  workflowId,
  workflowIntegrationStore,
  setWorkflowIntegrationStore,
  setDirtyData,
}: IProps) => {
  const {
    zalo: { isIntegrated, status },
  } = workflowIntegrationStore

  const [isChangedData, setChangedData] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [isLoadingData, setLoadingData] = useState(false)

  const [token, setToken] = useState('')
  const [appId, setAppId] = useState('')
  const [appSecret, setAppSecret] = useState('')
  const [accessToken, setAccessToken] = useState('')

  const handleBack = useCallback(() => {
    if (isChangedData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          setDirtyData(false)

          setWorkflowIntegrationStore({
            currentStep: Step.CHOOSE_APP,
            selectedApp: null,
          })
        },
      })
    } else {
      setDirtyData(false)

      setWorkflowIntegrationStore({
        currentStep: Step.CHOOSE_APP,
        selectedApp: null,
      })
    }
  }, [isChangedData])

  const fetchZaloConfiguration = useCallback(async () => {
    try {
      if (isLoadingData) return
      setLoadingData(true)

      const res = await chatBaseGetZaloConfigurationApi({
        query: {
          workflow_id: workflowId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setToken(data?.data?.zalo_refresh_token ?? '')
        setAppId(data?.data?.zalo_app_id ?? '')
        setAppSecret(data?.data?.zalo_app_secret ?? '')
        setAccessToken(data?.data?.zalo_access_token ?? '')
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoadingData(false)
    }
  }, [workflowId])

  useEffect(() => {
    if (isIntegrated) {
      fetchZaloConfiguration()
    }
  }, [isIntegrated])

  const callbackAPIValue = useMemo(
    () => `${GEN_AI_PATH}/api/v1/chat-base/zalo/workflow/${workflowId}/webhook`,
    [workflowId, GEN_AI_PATH]
  )

  const isInvalidData = useMemo(
    () =>
      !token?.trim() ||
      !appId?.trim() ||
      !appSecret?.trim() ||
      !accessToken?.trim(),
    [token, appId, appSecret, accessToken]
  )

  const handleIntegrate = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseCreateZaloConfigurationApi({
        body: {
          callback_url: callbackAPIValue,
          zalo_refresh_token: token,
          zalo_access_token: accessToken,
          zalo_app_id: appId,
          zalo_app_secret: appSecret,
          workflow_id: workflowId,
          status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setWorkflowIntegrationStore({
          currentStep: Step.CHOOSE_APP,
          selectedApp: null,
          zalo: {
            isIntegrated: true,
            status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
          },
        })

        Message.success({
          message: isIntegrated
            ? 'Successfully updated Zalo integration'
            : 'Successfully integrated workflow to Zalo',
        })

        setDirtyData(false)
        setChangedData(false)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex w-[1232px] flex-col gap-3 rounded-xl border border-neutral-200 bg-white px-11 pb-8 pt-5">
        <div className="w-full text-center">
          <Text
            value="Integrate to Zalo"
            variant="medium"
            type="subheading"
            className="text-Primary-Color"
          />
        </div>
        {isLoadingData ? (
          <div className="flex h-[430px] w-full items-center justify-center">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <div className="flex w-full flex-col gap-2">
              <Text value="Configuration" variant="medium" />
              <div className="flex w-full gap-3 rounded-lg border border-border-base-icon p-3">
                <div className="flex flex-1 flex-col gap-1 overflow-hidden">
                  <Text
                    value="AI Agency Callback API"
                    variant="medium"
                    type="subBody"
                    className="text-Tertiary-Color"
                  />
                  <div className="flex w-full items-center gap-3">
                    <Text
                      value={callbackAPIValue}
                      type="subBody"
                      ellipsis
                      elementType="div"
                      className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                    />
                    <CopyButton value={callbackAPIValue} />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex w-full flex-col gap-2">
              <Text value="Application Information" variant="medium" />
              <div className="flex w-full flex-col gap-2 rounded-lg border border-border-base-icon p-3">
                <div className="flex w-full gap-3">
                  <div className="flex-1">
                    <Input
                      value={appId}
                      label="App Id"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAppId(e.target.value)
                      }}
                      onBlur={() => setAppId(appId?.trim())}
                      placeholder="Copy and paste application id here"
                    />
                  </div>

                  <div className="flex-1">
                    <Input
                      value={appSecret}
                      label="Secret key"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAppSecret(e.target.value)
                      }}
                      onBlur={() => setAppSecret(appSecret?.trim())}
                      placeholder="Copy and paste generated secret key here"
                    />
                  </div>
                </div>

                <div className="flex w-full gap-3">
                  <div className="flex-1">
                    <Input
                      value={accessToken}
                      label="Zalo OA Access Token"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAccessToken(e.target.value)
                      }}
                      onBlur={() => setAccessToken(accessToken?.trim())}
                      placeholder="Copy and paste generated access token here"
                      maxLength={1000}
                    />
                  </div>

                  <div className="flex-1">
                    <Input
                      value={token}
                      label="Refresh Token"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setToken(e.target.value)
                      }}
                      onBlur={() => setToken(token?.trim())}
                      placeholder="Copy and paste generated refresh token here"
                      maxLength={1000}
                    />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      <div className="flex justify-end gap-3">
        <Button text="Back" type="secondary" onClick={handleBack} />
        <Button
          text="Integrate"
          type="primary"
          onClick={handleIntegrate}
          disabled={!isChangedData || isInvalidData}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

export default memo(Zalo)
