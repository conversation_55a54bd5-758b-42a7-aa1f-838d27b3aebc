import Message from '@/components/Message'
import Text from '@/components/Text'
import { validateDomainByRegex } from '@/helpers'
import clsx from 'clsx'
import { nanoid } from 'nanoid'
import { memo, useEffect, useRef, useState } from 'react'
import { IDomain } from '../types'
import DomainItem from './DomainItem'
import './styles.scss'

interface IProps {
  title: string
  placeholder?: string
  maxDomain?: number
  domains: IDomain[]
  addDomain: (domain: IDomain) => void
  deleteDomain: (domainId: string) => void
  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
  setChangedData: React.Dispatch<React.SetStateAction<boolean>>
}

const SettingDomain = ({
  title,
  placeholder = '',
  maxDomain = 3,
  domains,
  addDomain,
  deleteDomain,
  setDirtyData,
  setChangedData,
}: IProps) => {
  const [domain, setDomain] = useState<string>()

  const wrapperRef = useRef<HTMLDivElement>(null)

  const inpRef = useRef<HTMLInputElement>(null)

  const spanRef = useRef<HTMLSpanElement>(null)

  const [isFocus, setFocus] = useState<boolean>(false)

  useEffect(() => {
    // Update input width based on span width
    inpRef.current!.style.width = `${(spanRef.current!.offsetWidth ?? 0) + 3}px`
  }, [domain, placeholder, domains?.length])

  const validateDuplicateDomain = () => {
    return domains?.some((item) => item.value === domain?.trim())
  }

  const handleAddDomain = () => {
    if (domain?.trim()) {
      if (domains?.length < maxDomain) {
        if (!validateDuplicateDomain()) {
          addDomain?.({
            id: nanoid(),
            value: domain!,
            isError: !validateDomainByRegex(domain?.trim()),
          })
        } else {
          Message.warning({ message: 'Duplicated domain' })
        }
      } else {
        Message.warning({ message: 'Maximum 3 domains' })
      }
    }
    setDomain('')
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleAddDomain()
    }
    if (event.key === 'Backspace' && !domain) {
      deleteDomain(domains[domains.length - 1].id)
    }
  }

  useEffect(() => {
    function handleClickOutside(event: any) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setFocus(false)
      }
    }
    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [wrapperRef])

  return (
    <div className="flex w-full flex-col gap-1">
      <Text
        variant="medium"
        type="subBody"
        className="pl-1 text-Tertiary-Color"
      >
        {title}
      </Text>
      <div
        className={clsx(
          'setting-domain-wrapper genai-scrollbar flex h-[90px] w-full flex-wrap gap-2 overflow-y-auto rounded-lg border border-border-base-icon bg-Input-Field px-3 py-2',
          isFocus && 'setting-domain-wrapper-focus'
        )}
        onClick={() => {
          setFocus(true)
          inpRef.current?.focus()
        }}
        ref={wrapperRef}
      >
        {domains?.map((domain) => (
          <DomainItem
            domain={domain}
            deleteDomain={() => {
              deleteDomain(domain.id)
              setDirtyData(true)
              setChangedData(true)
              inpRef.current?.focus()
            }}
            key={domain.id}
          />
        ))}

        <input
          ref={inpRef}
          value={domain}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setDomain(e?.target?.value)
            setDirtyData(true)
            setChangedData(true)
          }}
          className={clsx('input-add-domain')}
          maxLength={255}
          placeholder={domains?.length ? undefined : placeholder}
          onBlur={() => {
            handleAddDomain()
          }}
          onKeyDown={handleKeyDown}
          onFocus={() => setFocus(true)}
        />

        <span
          ref={spanRef}
          className={clsx(
            'input-add-domain hidden-span-responsive-input-add-domain'
          )}
        >
          {domain || (domains?.length ? '' : placeholder)}
        </span>
      </div>
    </div>
  )
}

export default memo(SettingDomain)
