import {
  ChatBaseStatusTypes,
  chatBaseCreateWidgetConfiguration<PERSON>pi,
  chatBaseGetWidgetConfiguration<PERSON>pi,
  chatBaseWidgetAccessTokenApi,
  chatBaseWidgetGenerateClientApi,
} from '@/apis/client'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import IconButton from '@/components/IconButton'
import Message from '@/components/Message'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import { GEN_AI_PATH, HTTP_STATUS_CODE } from '@/constants'
import { Step } from '@/pages/WorkflowIntegration/types'
import { colors } from '@/theme'
import { nanoid } from 'nanoid'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import CopyButton from '../common/CopyButton'
import SettingDomain from './SettingDomain'
import { DomainType, IDomain } from './types'

interface IProps {
  workflowId: string
  workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
}

const Embed = ({
  workflowId,
  workflowIntegrationStore,
  setWorkflowIntegrationStore,
  setDirtyData,
}: IProps) => {
  const {
    embed: { isIntegrated, status },
  } = workflowIntegrationStore

  const [isChangedData, setChangedData] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [isLoadingData, setLoadingData] = useState(false)
  const [isShowClientSecret, setShowClientSecret] = useState(false)

  const [clientID, setClientID] = useState('')
  const [clientSecret, setClientSecret] = useState('')
  const [tokenEndpoint, setTokenEndpoint] = useState('')
  const [refreshTokenEndpoint, setRefreshTokenEndpoint] = useState('')
  const [sandboxDomain, setSandboxDomain] = useState<IDomain[]>([])
  const [officialDomain, setOfficialDomain] = useState<IDomain[]>([])

  const embeddedUrl = useMemo(
    () =>
      `${GEN_AI_PATH}/application/${clientID}/widget/${workflowId}?token={token_value}`,
    [clientID, workflowId, GEN_AI_PATH]
  )

  const callbackAPI = useMemo(
    () =>
      `${GEN_AI_PATH}/api/v1/chat-base/widget/workflow/${workflowId}/webhook`,
    [workflowId, GEN_AI_PATH]
  )

  const handleAddDomain = (domain: IDomain, type: DomainType = 'sandbox') => {
    if (type === 'sandbox') {
      setSandboxDomain((prev) => [...prev, domain])
    } else {
      setOfficialDomain((prev) => [...prev, domain])
    }
  }

  const handleDeleteDomain = (
    domainId: string,
    type: DomainType = 'sandbox'
  ) => {
    if (type === 'sandbox') {
      setSandboxDomain(() =>
        sandboxDomain.filter((domain) => domainId !== domain.id)
      )
    } else {
      setOfficialDomain(() =>
        officialDomain.filter((domain) => domainId !== domain.id)
      )
    }
  }

  const handleBack = useCallback(() => {
    if (isChangedData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          setDirtyData(false)

          setWorkflowIntegrationStore({
            currentStep: Step.CHOOSE_APP,
            selectedApp: null,
          })
        },
      })
    } else {
      setDirtyData(false)

      setWorkflowIntegrationStore({
        currentStep: Step.CHOOSE_APP,
        selectedApp: null,
      })
    }
  }, [isChangedData])

  const isInvalidData = useMemo(
    () =>
      !clientID ||
      !clientSecret ||
      !tokenEndpoint ||
      !refreshTokenEndpoint ||
      !officialDomain?.length ||
      officialDomain?.every((domain) => domain.isError),
    [
      clientID,
      clientSecret,
      tokenEndpoint,
      refreshTokenEndpoint,
      officialDomain,
    ]
  )

  const handleIntegrate = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseCreateWidgetConfigurationApi({
        body: {
          workflow_id: workflowId,
          widget_client_id: clientID,
          widget_client_secret: clientSecret,
          embedded_widget_url: embeddedUrl,
          access_token_endpoint: tokenEndpoint,
          refresh_token_endpoint: refreshTokenEndpoint,
          sandbox_domain: JSON.stringify(
            sandboxDomain
              .filter((item) => !item.isError)
              ?.map((domain) => domain.value)
          ),
          official_domain: JSON.stringify(
            officialDomain
              .filter((item) => !item.isError)
              ?.map((domain) => domain.value)
          ),
          callback_url: callbackAPI,
          status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setWorkflowIntegrationStore({
          currentStep: Step.CHOOSE_APP,
          selectedApp: null,
          embed: {
            isIntegrated: true,
            status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
          },
        })

        Message.success({
          message: isIntegrated
            ? 'Successfully updated integration'
            : 'Successfully created integration',
        })

        setDirtyData(false)
        setChangedData(false)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const fetchInitialInformation = async () => {
    try {
      if (isLoadingData) return
      setLoadingData(true)

      const res = await chatBaseWidgetGenerateClientApi()

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setClientID(data?.data?.client_id ?? '')
        setClientSecret(data?.data?.client_secret ?? '')
        setTokenEndpoint(data?.data?.access_token_endpoint ?? '')
        setRefreshTokenEndpoint(data?.data?.refresh_token_endpoint ?? '')
      } else {
        Message.error({
          message: 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoadingData(false)
    }
  }

  const fetchEmbedConfiguration = async () => {
    try {
      if (isLoadingData) return
      setLoadingData(true)

      const res = await chatBaseGetWidgetConfigurationApi({
        query: {
          workflow_id: workflowId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setClientID(data?.data?.widget_client_id ?? '')
        setClientSecret(data?.data?.widget_client_secret ?? '')
        setTokenEndpoint(data?.data?.access_token_endpoint ?? '')
        setRefreshTokenEndpoint(data?.data?.refresh_token_endpoint ?? '')

        if (data?.data?.sandbox_domain) {
          const sandboxDomain: IDomain[] = JSON.parse(
            data?.data?.sandbox_domain
          )?.map((domain: string) => ({
            id: nanoid(),
            value: domain,
            isError: false,
          }))

          setSandboxDomain(sandboxDomain)
        }

        if (data?.data?.official_domain) {
          const officialDomain: IDomain[] = JSON.parse(
            data?.data?.official_domain
          )?.map((domain: string) => ({
            id: nanoid(),
            value: domain,
            isError: false,
          }))

          setOfficialDomain(officialDomain)
        }
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoadingData(false)
    }
  }

  const handlePreview = async () => {
    try {
      if (isIntegrated) {
        const res = await chatBaseWidgetAccessTokenApi({
          body: {
            grant_type: 'client_credentials',
            client_id: clientID,
            client_secret: clientSecret,
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          const { data } = res

          window.open(
            embeddedUrl.replace('{token_value}', data?.access_token ?? '')
          )
        }
      } else {
        Message.warning({
          message: 'Complete the integration process to preview',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  useEffect(() => {
    if (isIntegrated) {
      fetchEmbedConfiguration()
    } else {
      fetchInitialInformation()
    }
  }, [isIntegrated])

  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex w-[1232px] flex-col gap-3 rounded-xl border border-neutral-200 bg-white px-8 pb-8 pt-5">
        <div className="w-full text-center">
          <Text
            value="Embed Chatbot"
            variant="medium"
            type="subheading"
            className="text-Primary-Color"
          />
        </div>
        {isLoadingData ? (
          <div className="flex h-[460px] w-full items-center justify-center">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <div className="flex w-full flex-col gap-2">
              <Text value="Authentication" variant="semibold" />
              <div className="flex w-full gap-3 rounded-lg border border-border-base-icon p-3">
                <div className="flex w-[calc((100%-12px)/2)] flex-col gap-3">
                  <div className="flex w-full flex-col gap-1">
                    <Text
                      value="Client ID"
                      variant="medium"
                      type="subBody"
                      className="pl-1 text-Tertiary-Color"
                    />
                    <div className="flex w-full items-center gap-3">
                      <Text
                        value={clientID}
                        type="subBody"
                        ellipsis
                        elementType="div"
                        className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                      />
                      <CopyButton value={clientID} />
                    </div>
                  </div>

                  <div className="flex w-full flex-col gap-1">
                    <Text
                      value="Token Endpoint"
                      variant="medium"
                      type="subBody"
                      className="pl-1 text-Tertiary-Color"
                    />
                    <div className="flex w-full items-center gap-3">
                      <Text
                        value={tokenEndpoint}
                        type="subBody"
                        ellipsis
                        elementType="div"
                        className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                      />
                      <CopyButton value={tokenEndpoint} />
                    </div>
                  </div>

                  <SettingDomain
                    title="SandBox Domain"
                    placeholder="Press “Enter” to complete adding your test environment, maximum 3 domains"
                    domains={sandboxDomain}
                    addDomain={handleAddDomain}
                    deleteDomain={handleDeleteDomain}
                    setDirtyData={setDirtyData}
                    setChangedData={setChangedData}
                  />
                </div>
                <div className="flex w-[calc((100%-12px)/2)] flex-col gap-3">
                  <div className="flex w-full flex-col gap-1">
                    <Text
                      value="Client Secret"
                      variant="medium"
                      type="subBody"
                      className="pl-1 text-Tertiary-Color"
                    />
                    <div className="flex w-full items-center gap-3">
                      <Text
                        value={
                          isShowClientSecret
                            ? clientSecret
                            : [...clientSecret]?.map(() => '•')
                        }
                        type="subBody"
                        ellipsis
                        elementType="div"
                        className="!w-fit max-w-[calc(100%-64px)] pl-2 text-Primary-Color"
                      />
                      <IconButton
                        nameIcon={
                          isShowClientSecret
                            ? 'vuesax-bold-eye-slash'
                            : 'vuesax-bold-eye'
                        }
                        colorIcon={colors.neutral[300]}
                        hoverColor={colors['Primary-Color']}
                        onClick={() => setShowClientSecret(!isShowClientSecret)}
                        sizeIcon={16}
                      />
                      <CopyButton value={clientSecret} />
                    </div>
                  </div>

                  <div className="flex w-full flex-col gap-1">
                    <Text
                      value="Refresh Token"
                      variant="medium"
                      type="subBody"
                      className="pl-1 text-Tertiary-Color"
                    />
                    <div className="flex w-full items-center gap-3">
                      <Text
                        value={refreshTokenEndpoint}
                        type="subBody"
                        ellipsis
                        elementType="div"
                        className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                      />
                      <CopyButton value={refreshTokenEndpoint} />
                    </div>
                  </div>

                  <SettingDomain
                    title="Official Domain"
                    placeholder="Press “Enter” to complete adding, maximum 3 domains"
                    domains={officialDomain}
                    addDomain={(domain) => handleAddDomain(domain, 'official')}
                    deleteDomain={(domainId) =>
                      handleDeleteDomain(domainId, 'official')
                    }
                    setDirtyData={setDirtyData}
                    setChangedData={setChangedData}
                  />
                </div>
              </div>
            </div>

            <div className="flex w-full flex-col gap-2">
              <Text value="Embedded Iframe" variant="semibold" />
              <div className="relative flex w-full flex-col gap-1 rounded-lg border border-border-base-icon p-3">
                <Text
                  value="URL"
                  variant="medium"
                  type="subBody"
                  className="text-Tertiary-Color"
                />
                <div className="flex w-full items-center gap-3">
                  <Text
                    value={embeddedUrl}
                    type="subBody"
                    ellipsis
                    elementType="div"
                    className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                  />
                  <CopyButton value={embeddedUrl} />
                </div>

                <IconButton
                  nameIcon="Bold-Security-EyeScan"
                  className="absolute right-2 top-2"
                  colorIcon={colors.neutral[300]}
                  hoverColor={colors['Primary-Color']}
                  sizeIcon={16}
                  tooltipText="Preview"
                  onClick={handlePreview}
                />
              </div>
            </div>
          </>
        )}
      </div>

      <div className="flex justify-end gap-3">
        <Button text="Back" type="secondary" onClick={handleBack} />
        <Button
          text="Integrate"
          type="primary"
          onClick={handleIntegrate}
          disabled={!isChangedData || isInvalidData}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

export default memo(Embed)
