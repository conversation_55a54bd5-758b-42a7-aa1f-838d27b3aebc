import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import { GEN_AI_PATH, HTTP_STATUS_CODE } from '@/constants'
import { Step } from '@/pages/WorkflowIntegration/types'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import CopyButton from '../common/CopyButton'
import { nanoid } from 'nanoid'
import { LIMIT_LENGTH_ID } from '@/pages/WorkflowIntegration/constants'
import GenerateToken from '../common/GenerateToken'
import {
  ChatBaseStatusTypes,
  chatBaseCreateMessengerConfigurationApi,
  chatBaseGetMessengerConfigurationApi,
} from '@/apis/client'

interface IProps {
  workflowId: string
  workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
}

const Messenger = ({
  workflowId,
  workflowIntegrationStore,
  setWorkflowIntegrationStore,
  setDirtyData,
}: IProps) => {
  const {
    messenger: { isIntegrated, status },
  } = workflowIntegrationStore

  const [isChangedData, setChangedData] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [isLoadingData, setLoadingData] = useState(false)

  const [pageId, setPageId] = useState('')
  const [appSecret, setAppSecret] = useState('')
  const [accessToken, setAccessToken] = useState('')
  const [token, setToken] = useState('')

  const handleBack = useCallback(() => {
    if (isChangedData) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          setDirtyData(false)

          setWorkflowIntegrationStore({
            currentStep: Step.CHOOSE_APP,
            selectedApp: null,
          })
        },
      })
    } else {
      setDirtyData(false)

      setWorkflowIntegrationStore({
        currentStep: Step.CHOOSE_APP,
        selectedApp: null,
      })
    }
  }, [isChangedData])

  const fetchMessengerConfiguration = useCallback(async () => {
    try {
      if (isLoadingData) return
      setLoadingData(true)

      const res = await chatBaseGetMessengerConfigurationApi({
        query: {
          workflow_id: workflowId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setPageId(data?.data?.messenger_bot_page_id ?? '')
        setAppSecret(data?.data?.messenger_bot_app_secret ?? '')
        setAccessToken(data?.data?.messenger_bot_token ?? '')
        setToken(data?.data?.verify_token ?? '')
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoadingData(false)
    }
  }, [workflowId])

  useEffect(() => {
    if (isIntegrated) {
      fetchMessengerConfiguration()
    } else {
      setToken(nanoid(LIMIT_LENGTH_ID))
    }
  }, [isIntegrated])

  const callbackAPIValue = useMemo(
    () =>
      `${GEN_AI_PATH}/api/v1/chat-base/messenger/workflow/${workflowId}/webhook`,
    [workflowId, GEN_AI_PATH]
  )

  const isInvalidData = useMemo(
    () => !pageId?.trim() || !appSecret?.trim() || !accessToken?.trim(),
    [pageId, appSecret, accessToken]
  )

  const handleIntegrate = async () => {
    try {
      if (isLoading) return

      setLoading(true)

      const res = await chatBaseCreateMessengerConfigurationApi({
        body: {
          workflow_id: workflowId,
          callback_url: callbackAPIValue,
          verify_token: token,
          messenger_bot_page_id: pageId,
          messenger_bot_token: accessToken,
          messenger_bot_app_secret: appSecret,
          status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setWorkflowIntegrationStore({
          currentStep: Step.CHOOSE_APP,
          selectedApp: null,
          messenger: {
            isIntegrated: true,
            status: isIntegrated ? status : ChatBaseStatusTypes.ENABLED,
          },
        })

        Message.success({
          message: isIntegrated
            ? 'Successfully updated Messenger integration'
            : 'Successfully integrated workflow to Messenger',
        })

        setDirtyData(false)
        setChangedData(false)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex w-[1232px] flex-col gap-3 rounded-xl border border-neutral-200 bg-white px-11 pb-8 pt-5">
        <div className="w-full text-center">
          <Text
            value="Integrate to Messenger"
            variant="medium"
            type="subheading"
            className="text-Primary-Color"
          />
        </div>
        {isLoadingData ? (
          <div className="flex h-[430px] w-full items-center justify-center">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <div className="flex w-full flex-col gap-2">
              <Text value="Configuration" variant="medium" />
              <div className="flex w-full gap-3 rounded-lg border border-border-base-icon p-3">
                <div className="flex flex-1 flex-col gap-2 overflow-hidden">
                  <Text
                    value="GenAI Callback API"
                    variant="medium"
                    type="subBody"
                    className="px-1 text-Tertiary-Color"
                  />
                  <div className="flex w-full items-center gap-3">
                    <Text
                      value={callbackAPIValue}
                      type="subBody"
                      ellipsis
                      elementType="div"
                      className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                    />
                    <CopyButton value={callbackAPIValue} />
                  </div>
                </div>

                <div className="flex flex-1 flex-col gap-2 overflow-hidden">
                  <Text
                    value="Verify token"
                    variant="medium"
                    type="subBody"
                    className="px-1 text-Tertiary-Color"
                  />
                  <div className="flex w-full items-center gap-3">
                    <Text
                      value={token}
                      type="subBody"
                      ellipsis
                      elementType="div"
                      className="!w-fit max-w-[calc(100%-32px)] pl-2 text-Primary-Color"
                    />
                    <div className="flex items-center gap-2">
                      <CopyButton value={token} />
                      <GenerateToken
                        generateToken={() => {
                          setChangedData(true)
                          setDirtyData(true)
                          setToken(nanoid(LIMIT_LENGTH_ID))
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex w-full flex-col gap-2">
              <Text value="Application Information" variant="medium" />
              <div className="flex w-full flex-col gap-2 rounded-lg border border-border-base-icon p-3">
                <div className="flex w-full gap-3">
                  <div className="flex-1">
                    <Input
                      value={pageId}
                      label="Bot Page Id"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setPageId(e.target.value)
                      }}
                      onBlur={() => setPageId(pageId?.trim())}
                      placeholder="Copy and paste bot page id here"
                    />
                  </div>

                  <div className="flex-1">
                    <Input
                      value={appSecret}
                      label="Bot App secret"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAppSecret(e.target.value)
                      }}
                      onBlur={() => setAppSecret(appSecret?.trim())}
                      placeholder="Copy and paste bot app secret here"
                    />
                  </div>
                </div>

                <div className="flex w-full gap-3">
                  <div className="flex-1">
                    <Input
                      value={accessToken}
                      label="Messenger Bot Token"
                      isFullWidth
                      onChange={(e) => {
                        setChangedData(true)
                        setDirtyData(true)
                        setAccessToken(e.target.value)
                      }}
                      onBlur={() => setAccessToken(accessToken?.trim())}
                      placeholder="Copy and paste token here"
                      maxLength={1000}
                    />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      <div className="flex justify-end gap-3">
        <Button text="Back" type="secondary" onClick={handleBack} />
        <Button
          text="Integrate"
          type="primary"
          onClick={handleIntegrate}
          disabled={!isChangedData || isInvalidData}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

export default memo(Messenger)
