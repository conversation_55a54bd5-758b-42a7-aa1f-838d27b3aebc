import {
  ChatBaseStatusTypes,
  chatBaseDeleteMessengerConfiguration<PERSON>pi,
  chatBaseDeleteTelegramConfiguration<PERSON>pi,
  chatBaseDeleteWhatsappConfigurationApi,
  chatBaseDeleteWidgetConfigurationApi,
  chatBaseDeleteZaloConfigurationApi,
  chatBaseUpdateMessengerConfigurationApi,
  chatBaseUpdateTelegramConfigurationApi,
  chatBaseUpdateWhatsappConfigurationApi,
  chatBaseUpdateWidgetConfigurationApi,
  chatBaseUpdateZaloConfigurationApi,
} from '@/apis/client'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE } from '@/constants'
import { memo, useMemo } from 'react'
import {
  IconEmbed,
  IconInstagram,
  IconMessenger,
  IconTelegram,
  IconWhatsApp,
  IconZalo,
} from '../../assets'
import { APPS } from '../../constants'
import { Step } from '../../types'
import AppItem from './AppItem'

interface IProps {
  workflowId: string
  workflowIntegrationStore: any
  setWorkflowIntegrationStore: (data: any) => void
}

const ChooseApp = ({
  workflowId,
  workflowIntegrationStore,
  setWorkflowIntegrationStore,
}: IProps) => {
  const updateStatus = async (appType: string, status: ChatBaseStatusTypes) => {
    try {
      let service

      switch (appType) {
        case 'embed':
          service = chatBaseUpdateWidgetConfigurationApi
          break
        case 'whatsApp':
          service = chatBaseUpdateWhatsappConfigurationApi
          break
        case 'telegram':
          service = chatBaseUpdateTelegramConfigurationApi
          break
        case 'zalo':
          service = chatBaseUpdateZaloConfigurationApi
          break
        case 'messenger':
          service = chatBaseUpdateMessengerConfigurationApi
          break

        default:
          break
      }

      if (service) {
        const res = await service({
          body: {
            workflow_id: workflowId,
            status,
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          setWorkflowIntegrationStore({
            [appType]: {
              ...workflowIntegrationStore[appType],
              status,
            },
          })
          Message.success({
            message: `Successfully ${status === ChatBaseStatusTypes.DISABLED ? 'disabled' : 'enabled'} integration`,
          })
        } else {
          Message.error({
            message: res.error?.detail ?? 'Something wrong, please retry!',
          })
        }
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  const deleteConfig = async (appType: string) => {
    try {
      let service

      switch (appType) {
        case 'embed':
          service = chatBaseDeleteWidgetConfigurationApi
          break
        case 'whatsApp':
          service = chatBaseDeleteWhatsappConfigurationApi
          break
        case 'telegram':
          service = chatBaseDeleteTelegramConfigurationApi
          break
        case 'zalo':
          service = chatBaseDeleteZaloConfigurationApi
          break
        case 'messenger':
          service = chatBaseDeleteMessengerConfigurationApi
          break

        default:
          break
      }

      if (service) {
        const res = await service({
          query: {
            workflow_id: workflowId,
          },
        })

        if (res.status === HTTP_STATUS_CODE.NO_CONTENT) {
          setWorkflowIntegrationStore({
            [appType]: {
              isIntegrated: false,
              status: '',
            },
          })
          Message.success({
            message: 'Successfully removed integration',
          })
        }
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    }
  }

  const apps = useMemo(
    () => [
      {
        icon: <IconEmbed />,
        name: 'Embedded Chatbot',
        isIntegrated: workflowIntegrationStore.embed.isIntegrated,
        status: workflowIntegrationStore.embed.status,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.EMBED,
          })
        },
        updateStatus: (status: ChatBaseStatusTypes) => {
          updateStatus('embed', status)
        },
        deleteConfig: () => {
          deleteConfig('embed')
        },
      },
      {
        icon: <IconWhatsApp />,
        name: 'WhatsApp',
        isIntegrated: workflowIntegrationStore.whatsApp.isIntegrated,
        status: workflowIntegrationStore.whatsApp.status,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.WHATSAPP,
          })
        },
        updateStatus: (status: ChatBaseStatusTypes) => {
          updateStatus('whatsApp', status)
        },
        deleteConfig: () => {
          deleteConfig('whatsApp')
        },
      },
      {
        icon: <IconTelegram />,
        name: 'Telegram',
        isIntegrated: workflowIntegrationStore.telegram.isIntegrated,
        status: workflowIntegrationStore.telegram.status,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.TELEGRAM,
          })
        },
        updateStatus: (status: ChatBaseStatusTypes) => {
          updateStatus('telegram', status)
        },
        deleteConfig: () => {
          deleteConfig('telegram')
        },
      },
      {
        icon: <IconZalo />,
        name: 'Zalo',
        isIntegrated: workflowIntegrationStore.zalo.isIntegrated,
        status: workflowIntegrationStore.zalo.status,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.ZALO,
          })
        },
        updateStatus: (status: ChatBaseStatusTypes) => {
          updateStatus('zalo', status)
        },
        deleteConfig: () => {
          deleteConfig('zalo')
        },
      },
      {
        icon: <IconMessenger />,
        name: 'Messenger',
        isIntegrated: workflowIntegrationStore.messenger.isIntegrated,
        status: workflowIntegrationStore.messenger.status,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.MESSENGER,
          })
        },
        updateStatus: (status: ChatBaseStatusTypes) => {
          updateStatus('messenger', status)
        },
        deleteConfig: () => {
          deleteConfig('messenger')
        },
      },
      {
        icon: <IconInstagram />,
        name: 'Instagram',
        isIntegrated: workflowIntegrationStore.instagram.isIntegrated,
        status: workflowIntegrationStore.instagram.status,
        onClick: () => {
          setWorkflowIntegrationStore({
            currentStep: Step.CONFIG,
            selectedApp: APPS.INSTAGRAM,
          })
        },
        updateStatus: (status: ChatBaseStatusTypes) => {
          updateStatus('instagram', status)
        },
        deleteConfig: () => {
          deleteConfig('instagram')
        },
      },
    ],
    [workflowIntegrationStore]
  )

  return (
    <div className="flex w-full flex-col gap-3 p-3 pt-0">
      <div className="flex w-[810px] flex-wrap gap-6 rounded-xl border border-neutral-200 bg-white px-11 pb-8 pt-5">
        {apps.map((app) => (
          <AppItem key={app.name} {...app} />
        ))}
      </div>
    </div>
  )
}

export default memo(ChooseApp)
