import { ChatBaseStatusTypes } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import { MessageDialog } from '@/components/DialogMessage'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { IntegrationEnum } from '@/pages/WorkflowIntegration/types'
import { colors } from '@/theme'
import clsx from 'clsx'
import { ReactNode, memo, useCallback, useMemo, useState } from 'react'
import { actionItems } from '../../constants'

interface IProps {
  icon: ReactNode
  name: string
  isIntegrated?: boolean
  status?: string
  onClick: () => void
  updateStatus: (status: ChatBaseStatusTypes) => void
  deleteConfig: () => void
}

const AppItem = ({
  icon,
  name,
  isIntegrated = false,
  status = '',
  onClick,
  updateStatus,
  deleteConfig,
}: IProps) => {
  const [isHover, setHover] = useState(false)
  const [isOpenMenu, setOpenMenu] = useState(false)

  const dropdownItems = useMemo(() => {
    if (isIntegrated) {
      if (status === 'Enabled') {
        return actionItems.filter(
          (item) => item.key !== IntegrationEnum.Enabled
        )
      }

      return actionItems.filter((item) => item.key !== IntegrationEnum.Disabled)
    }

    return []
  }, [isIntegrated, status])

  const handleSelectAction = useCallback(
    ({ key }: SelectedItemProps) => {
      switch (key) {
        case IntegrationEnum.Enabled:
        case IntegrationEnum.Disabled:
          updateStatus(key as unknown as ChatBaseStatusTypes)
          break
        case IntegrationEnum.Remove:
          MessageDialog.warning({
            mainMessage: 'Remove integration?',
            subMessage:
              'If continue, your customers will no longer be able to reach you via this application',
            onClick: deleteConfig,
          })
          break
      }
    },
    [updateStatus, deleteConfig]
  )

  return (
    <div
      className="relative flex w-[220px] cursor-pointer flex-col items-center justify-center gap-3 rounded-xl px-4 py-3 shadow-base hover:bg-Hover-Color"
      onClick={onClick}
      onMouseOver={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {icon}
      <Text variant="semibold" className="text-Primary-Color">
        {name}
      </Text>

      <div className="flex w-full items-center justify-end gap-1">
        <Text
          type="supportText"
          value={
            isIntegrated
              ? status === ChatBaseStatusTypes.ENABLED
                ? 'Connected'
                : 'Connection disabled'
              : 'Integrate'
          }
          className={clsx(
            isHover || isIntegrated
              ? 'bg-Main-Color bg-clip-text text-transparent'
              : 'text-Secondary-Color'
          )}
          variant={isIntegrated ? 'medium' : 'regular'}
        />
        {!isIntegrated && (
          <Icon
            name="vuesax-outline-arrow-right"
            color={colors['Secondary-Color']}
            size={14}
            gradient={isHover ? ['#642B73', '#C6426E'] : undefined}
          />
        )}
      </div>
      {isIntegrated && (
        <div className="absolute right-2 top-3 flex flex-row-reverse gap-1">
          <Dropdown
            open={isOpenMenu}
            onOpenChange={(newOpen) => {
              setOpenMenu(newOpen)
            }}
            items={dropdownItems}
            overlayClassName="z-10"
            onSelect={handleSelectAction}
          >
            <More active={isOpenMenu} />
          </Dropdown>
        </div>
      )}
    </div>
  )
}

export default memo(AppItem)
