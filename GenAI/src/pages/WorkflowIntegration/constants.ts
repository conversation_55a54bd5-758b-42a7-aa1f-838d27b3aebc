import { IntegrationEnum } from './types'

export const APPS = {
  API_BASE: 'API_BASE',
  EMBED: 'EMBED',
  WHATSAPP: 'WHATSAPP',
  TELEGRAM: 'TELEGRAM',
  INSTAGRAM: 'INSTAGRAM',
  MESSENGER: 'MESSENGER',
  // Slack: 'SLACK',
  ZALO: 'ZALO',
}

export const LIMIT_LENGTH_ID = 10

export const APP_NAME = {
  EmbededWidget: 'EmbededWidget',
  WhatsApp: 'WhatsApp',
  Telegram: 'Telegram',
  Instagram: 'Instagram',
  Messenger: 'Messenger',
  // Slack: 'Slack',
  Zalo: 'Zalo',
  API: 'API',
}

export const CHANNEL_TYPE = {
  EmbeddedWidget: 'EmbededWidget',
  WhatsApp: 'WhatsApp',
  Telegram: 'Telegram',
  Instagram: 'Instagram',
  Messenger: 'Messenger',
  // Slack: 'Slack',
  Zalo: 'Zalo',
  API: 'API',
}

export const actionItems = [
  {
    key: IntegrationEnum.Disabled,
    label: 'Disable',
  },
  {
    key: IntegrationEnum.Enabled,
    label: 'Enable',
  },
  {
    key: IntegrationEnum.Remove,
    label: 'Remove',
  },
]
