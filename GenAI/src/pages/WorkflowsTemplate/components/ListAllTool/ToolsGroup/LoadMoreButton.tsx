import clsx from 'clsx'
import { twMerge } from 'tailwind-merge'

interface Props {
  isLoading?: boolean
  onClick?: () => void
}

const LoadMoreButton = ({ isLoading, onClick }: Props) => {
  return (
    <div
      onClick={onClick}
      className={twMerge(
        'group bg-Main-Disable-2 hover:bg-Main-04',
        'flex h-[19px] w-[69px] items-center justify-center rounded-full px-[8px] py-[2px]',
        'cursor-pointer select-none',
        clsx({
          'bg-Main-04': isLoading,
        })
      )}
    >
      <span
        className={twMerge(
          clsx(
            'text-[10px] !font-medium leading-[15px] text-Secondary-Color group-hover:bg-gradient-to-br group-hover:from-[#642B73] group-hover:to-[#C6426E] group-hover:bg-clip-text group-hover:text-transparent',
            {
              'bg-gradient-to-br from-[#642B73] to-[#C6426E] bg-clip-text text-transparent':
                isLoading,
            }
          )
        )}
      >
        {isLoading ? 'Loading...' : 'Load more'}
      </span>
    </div>
  )
}

export default LoadMoreButton
