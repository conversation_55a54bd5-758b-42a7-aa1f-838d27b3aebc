import {
  BusinessCategoryMarketTemplatePublic,
  BusinessCategoryMarketTemplatesPublic,
  businessCategoriesGetListWfTemplateByCategoryApi,
} from '@/apis/client'
import Text from '@/components/Text'
import { PAGE_SIZE } from '@/constants'
import useMarketToolsCategory from '@/store/workflowsTemplate'
import clsx from 'clsx'
import { useState } from 'react'
import { twMerge } from 'tailwind-merge'
import LoadMoreButton from './LoadMoreButton'
import WorkflowItem from '@/pages/Workflows/components/WorkflowItem'
import IconButton from '@/components/IconButton'
import { colors } from '@/theme'
import ModalCloneWf from '../ModalCloneWf'

interface Props {
  tools: BusinessCategoryMarketTemplatesPublic
  categoryId: string
}

export const ToolsGroup = ({ tools, categoryId }: Props) => {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [showCloneWfModal, setShowCloneWfModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] =
    useState<BusinessCategoryMarketTemplatePublic | null>(null)

  const search = useMarketToolsCategory((state) => state.search)
  const addToolsByCategoryId = useMarketToolsCategory(
    (state) => state.addToolsByCategoryId
  )

  const [page, setPage] = useState(1)

  const isHaveMore = tools.length < (tools[0].sub_total ?? 0)
  const categoryName = tools[0].business_category_name ?? ''

  const toggleExpand = () => {
    setIsExpanded((prev) => !prev)
  }

  const handleLoadMoreItem = async () => {
    setIsLoading(true)

    const nextPage = page + 1
    setPage(nextPage)

    try {
      const { data } = await businessCategoriesGetListWfTemplateByCategoryApi({
        query: {
          name: search,
          business_category_id: categoryId,
          page_number: nextPage,
          page_size: PAGE_SIZE.SPECIAL_SMALL,
        },
      })

      if (
        data &&
        data.data &&
        data.data.data &&
        Array.isArray(data.data.data)
      ) {
        addToolsByCategoryId(
          categoryId,
          data.data.data.map(
            (item) =>
              ({
                ...item,
                category_name: categoryName,
                sub_total: data.data.total_count,
                workflow_id: item.workflow_id,
                workflow_name: item.workflow_name,
                row_id: 10,
              }) as BusinessCategoryMarketTemplatePublic
          )
        )
      }
    } catch (error: any) {
      console.log('error', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCloneWorkflow = (tool: BusinessCategoryMarketTemplatePublic) => {
    setSelectedTemplate(tool)
    setShowCloneWfModal(true)
  }

  return (
    <div className="flex flex-col gap-[12px]">
      <div className="flex h-[21px] w-full flex-row items-center justify-between gap-[12px]">
        <div className="flex flex-1 items-center overflow-hidden">
          <Text
            type="body"
            variant="semibold"
            className="overflow-hidden text-ellipsis text-nowrap !text-[#2D0136]"
          >
            {categoryName.toUpperCase()}
          </Text>
        </div>
        <IconButton
          nameIcon="Outline-Arrows-DoubleAltArrowDown"
          sizeIcon={20}
          hoverColor={!isExpanded ? colors['Main-Color'] : '#2D0136'}
          colorIcon={!isExpanded ? '#2D0136' : '#E5E5E5'}
          className={twMerge(
            clsx(
              'select-none transition-transform',
              !isExpanded && 'rotate-180'
            )
          )}
          onClick={toggleExpand}
          tooltipText={isExpanded ? 'Collapse' : 'Expand'}
          tooltipPosition="left"
        />
      </div>

      {isExpanded && (
        <div className="flex flex-col gap-[54px]">
          <div className="flex flex-row flex-wrap gap-x-[52px] gap-y-[36px] p-1 px-[35px]">
            {tools.map((tool) => (
              <WorkflowItem
                description={tool.workflow_description ?? ''}
                title={tool.workflow_name}
                key={tool.workflow_id}
                image={tool.workflow_thumbnail_url ?? ''}
                onClick={() => {}}
                marketView
                handleCloneWorkflow={() => {
                  handleCloneWorkflow(tool)
                }}
              />
            ))}
          </div>

          {isHaveMore && (
            <div className="flex justify-end px-[12px]">
              <LoadMoreButton
                isLoading={isLoading}
                onClick={handleLoadMoreItem}
              />
            </div>
          )}
        </div>
      )}

      {showCloneWfModal && (
        <ModalCloneWf
          onClose={() => {
            setShowCloneWfModal(false)
          }}
          template={selectedTemplate}
        />
      )}
    </div>
  )
}
