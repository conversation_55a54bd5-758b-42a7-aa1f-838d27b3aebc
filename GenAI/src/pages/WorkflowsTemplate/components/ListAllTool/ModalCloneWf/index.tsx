import {
  BusinessCategoryMarketTemplatePublic,
  WorkflowTemplateWorkersAndTools,
  processingWorkflowCloneWorkflow,
  workflowCloneWorkflowApi,
  workflowGetWorkersAndToolsByWorkflowIdApi,
} from '@/apis/client'
import AddItem from '@/components/AddItem'
import EmptyData from '@/components/EmptyData'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import AssigneeItem from '@/pages/WorkflowDetail/components/SelectAssignee/components/AssigneeItem'
import { EWorkflowType } from '@/pages/Workflows/const'
import clsx from 'clsx'
import { useEffect, useMemo, useState } from 'react'

interface Props {
  onClose?: () => void
  template?: BusinessCategoryMarketTemplatePublic | null
}
const ModalCloneWf = ({ onClose, template }: Props) => {
  const [data, setData] = useState<WorkflowTemplateWorkersAndTools>()
  const [initLoading, setInitLoading] = useState(true)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const init = async () => {
      if (!template) return

      const res = await workflowGetWorkersAndToolsByWorkflowIdApi({
        path: {
          workflow_id: template.workflow_id,
        },
      })

      setInitLoading(false)

      if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
        return
      }

      setData(res.data?.data)
    }

    init()
  }, [])

  const handleClone = async () => {
    try {
      if (!template) return

      setLoading(true)

      const service = !isConversationWorkflowType
        ? processingWorkflowCloneWorkflow
        : workflowCloneWorkflowApi

      const res = await service({
        path: {
          workflow_id: template.workflow_id,
        },
      })

      if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
        setLoading(false)
        Message.error({ message: 'Something went wrong!' })
        return
      }

      onClose?.()
      setTimeout(() => {
        Message.success({ message: 'Successfully cloned workflow template' })
      }, 100)
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    }
  }

  const isConversationWorkflowType = useMemo(() => {
    return template?.workflow_type === EWorkflowType.conversation
  }, [template])

  return (
    <Modal
      showCloseButton
      open={true}
      title="Clone workflow template"
      subTitle="Please confirm to buy all tools and workers to proceed"
      onClickClose={onClose}
      okDisable={initLoading}
      okLoading={loading}
      onClickOk={handleClone}
      okText="Get all free"
      classNameOkButton="w-[133px] min-w-[133px]"
      className="flex h-[576px] w-[1020px] flex-col justify-between border-none"
      classNameFooter="justify-end"
      classNameCancelButton="hidden"
    >
      <div className="flex h-[426px] gap-[8px]">
        {isConversationWorkflowType && (
          <div className="flex w-1/2 min-w-[428px] flex-col gap-[16px] rounded-[12px] border-[1px] border-neutral-200 bg-white p-[8px]">
            <div className="flex h-[370px] min-h-[370px] w-full flex-col gap-[12px]">
              <Text
                type="body"
                variant="semibold"
                className="bg-Main-Color bg-clip-text text-center text-transparent"
              >
                Workers
              </Text>

              <div className="genai-scrollbar h-[333px] w-full overflow-auto">
                <div className="flex w-[453px] flex-col gap-[8px] px-[4px]">
                  {Array.isArray(data?.workers) &&
                    data.workers.map((worker) => (
                      <AssigneeItem
                        key={worker.id}
                        name={worker.name}
                        avatar={worker.avatar}
                        worker_type={'AI'}
                        background={worker.background}
                        className="cursor-default hover:bg-transparent"
                      />
                    ))}
                </div>
              </div>
            </div>

            <div className="flex h-[24px] w-full flex-col justify-between px-[4px]">
              <div className="h-[1px] w-full bg-neutral-100" />
              <div className="flex w-full justify-between">
                <Text
                  type="supportText"
                  variant="medium"
                  className="text-Secondary-Color"
                >
                  {(data?.workers_number ?? 0) > 1 ? 'Workers' : 'Worker'}:{' '}
                  {data?.workers_number ?? 0}
                </Text>
                <Text
                  type="supportText"
                  variant="medium"
                  className="text-Primary-Color"
                >
                  Total: {data?.workers_price_total ?? 'Free'}
                </Text>
              </div>
            </div>
          </div>
        )}

        <div
          className={clsx(
            'flex w-1/2 min-w-[428px] flex-col gap-[16px] rounded-[12px] border-[1px] border-neutral-200 bg-white p-[8px]',
            !isConversationWorkflowType && 'w-full'
          )}
        >
          <div className="flex h-[370px] min-h-[370px] w-full flex-col gap-[12px]">
            <Text
              type="body"
              variant="semibold"
              className="bg-Main-Color bg-clip-text text-center text-transparent"
            >
              Tools
            </Text>

            <div className="genai-scrollbar h-[333px] w-full overflow-auto">
              <div
                className={clsx(
                  'flex w-[453px] flex-col gap-[8px]',
                  !isConversationWorkflowType && 'w-full'
                )}
              >
                {Array.isArray(data?.tools) &&
                  data.tools.map((tool) => (
                    <AddItem
                      key={tool.id}
                      avatar={tool.logo || ''}
                      title={tool.name}
                      content={tool.description || ''}
                      selected={false}
                      onClick={() => {}}
                      directory={tool.tool_category_name ?? 'Uncategorized'}
                      hideButton
                      className={'px-[8px] py-[4px]'}
                      showPrice
                    />
                  ))}
                {!data?.tools?.length && (
                  <EmptyData
                    type="03"
                    size="small"
                    icSize={120}
                    content="No tools added"
                    className="ml-auto mr-auto mt-8"
                  />
                )}
              </div>
            </div>
          </div>

          <div className="flex h-[24px] w-full flex-col justify-between px-[4px]">
            <div className="h-[1px] w-full bg-neutral-100" />
            <div className="flex w-full justify-between">
              <Text
                type="supportText"
                variant="medium"
                className="text-Secondary-Color"
              >
                {(data?.tools_number ?? 0) > 1 ? 'Tools' : 'Tool'}:{' '}
                {data?.tools_number ?? 0}
              </Text>
              <Text
                type="supportText"
                variant="medium"
                className="text-Primary-Color"
              >
                Total: {data?.tools_price_total ?? 'Free'}
              </Text>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default ModalCloneWf
