import { memo, useEffect, useRef, useState } from 'react'
import SkeletonLoadingCategory from '../Skeleton/SkeletonLoadingCategory'
import CategoryItem from './CategoryItem'
import { ICategory } from '../../consts'
import { checkOverflow } from '../../help'
import IconArrow from './IconArrow/IconArrow'
import { twMerge } from 'tailwind-merge'
import { size } from 'lodash'
import useWorkflowsTemplate from '@/store/workflowsTemplate'
import { selectCategories } from '@/store/workflowsTemplate/selector'
import { businessCategoriesReadBusinessCategoriesApi } from '@/apis/client'
import { PAGE_SIZE_LIST_CATEGORY, UNDEFINED_CATEGORY_ID } from './consts'
import Message from '@/components/Message'

interface Props {
  search: string
  onChangeCategory?: (category: any) => void
}

const ScrollOffset = 300
const DEFAULT_PAGINATION = {
  total_count: 0,
  next_page: null,
  prev_page: null,
  total_page: 0,
}

const ListCategory = ({ onChangeCategory, search }: Props) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isShowArrow, setIsShowArrow] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const [page, setPage] = useState(1)

  const [pagination, setPagination] = useState<{
    total_count: number
    total_page: number
    next_page: number | null
    prev_page: number | null
  }>(DEFAULT_PAGINATION)

  const [activeCategory] = useWorkflowsTemplate((state) => [
    state.activeCategory,
  ])

  const isAllCategory = activeCategory === undefined

  const data = useWorkflowsTemplate(selectCategories)
  const addCategories = useWorkflowsTemplate((state) => state.addCategories)
  const updateCategoryPagingByCache = useWorkflowsTemplate(
    (state) => state.updateCategoryPagingByCache
  )
  const mapCategoryIdToTools = useWorkflowsTemplate(
    (state) => state.mapCategoryIdToTools
  )
  const clearCategoryPaging = useWorkflowsTemplate(
    (state) => state.clearCategoryPaging
  )

  useEffect(() => {
    const container = containerRef.current
    const isOverflow = checkOverflow(container)
    setIsShowArrow(isOverflow)
  }, [data, isLoading])

  const loadCategory = async (page: number) => {
    try {
      setIsLoading(true)

      const { data } = await businessCategoriesReadBusinessCategoriesApi({
        query: {
          page_number: page,
          page_size: PAGE_SIZE_LIST_CATEGORY,
        },
      })

      if (data && data.data) {
        addCategories(data.data, page === 1)
        setPagination({
          total_count: data.data.total_count,
          total_page: data.data.total_pages,
          next_page: data.data.next_page,
          prev_page: data.data.prev_page,
        })
      }
    } catch (error) {
      console.log('🚀 ~ loadCategory ~ error:', error)
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setIsLoading(false)
    }
  }

  const loadMoreData = () => {
    if (pagination.next_page) {
      console.log('load more page....: ', pagination)
      setPage(pagination.next_page)
    }
  }

  useEffect(() => {
    loadCategory(page)

    return () => {
      clearCategoryPaging()
    }
  }, [page])

  useEffect(() => {
    updateCategoryPagingByCache()
  }, [activeCategory])

  const handleCategoryClick = (category: ICategory | undefined) => {
    if (activeCategory?.id === category?.id) {
      return
    }

    onChangeCategory?.(category)
  }

  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current
        if (scrollHeight - scrollTop === clientHeight) {
          loadMoreData()
        }
      }
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll)
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll)
      }
    }
  }, [isLoading, pagination])

  const handleScrollLeft = () => {
    if (containerRef.current) {
      containerRef.current.scrollLeft -= ScrollOffset
    }
  }

  const handleScrollRight = () => {
    if (containerRef.current) {
      containerRef.current.scrollLeft += ScrollOffset
    }
  }

  return (
    <div className="mt-[20px] h-[26px]">
      {isLoading && <SkeletonLoadingCategory className="" />}

      {!isLoading && (
        <div className="relative flex h-[26px] gap-[20px]">
          <CategoryItem
            onClick={handleCategoryClick}
            isMain
            isActive={activeCategory === undefined}
          />

          {isShowArrow && (
            <>
              <div
                onClick={handleScrollLeft}
                className={twMerge(
                  'absolute left-[154px] z-10 select-none',
                  'rotate-180 transform'
                )}
              >
                <IconArrow />
              </div>

              <div
                onClick={handleScrollRight}
                className={twMerge('absolute -right-[10px] z-10 select-none')}
              >
                <IconArrow />
              </div>
            </>
          )}

          {size(data) > 0 && (
            <div
              ref={containerRef}
              className={twMerge(
                'relative flex w-[calc(100%-106.26px)] flex-row gap-[20px] overflow-hidden pr-3'
              )}
            >
              {data?.map((item, index) => {
                const number = (() => {
                  if (isAllCategory && search) {
                    // if search on all category
                    return mapCategoryIdToTools?.[item.id]?.[0]?.sub_total ?? 0
                  }

                  // on specific category
                  return item.workflow_count
                })()

                if (
                  item?.id === UNDEFINED_CATEGORY_ID &&
                  number === 0 &&
                  !search
                ) {
                  return undefined
                }

                return (
                  <CategoryItem
                    onClick={handleCategoryClick}
                    key={index}
                    category={item}
                    isActive={activeCategory?.id === item.id}
                    number={number}
                  />
                )
              })}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default memo(ListCategory)
