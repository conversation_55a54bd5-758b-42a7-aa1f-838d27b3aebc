import {
  BusinessCategoryMarketTemplatePublic,
  BusinessCategoryMarketTemplatesPublic,
  BusinessCategoryPublic,
  businessCategoriesGetListWfTemplateByCategoryApi,
} from '@/apis/client'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import Pagination from '@/components/Pagination'
import { PAGE_SIZE } from '@/constants'
import WorkflowItem from '@/pages/Workflows/components/WorkflowItem'
import useMarketToolsCategory from '@/store/workflowsTemplate'
import { size } from 'lodash'
import { memo, useEffect, useState } from 'react'
import ModalCloneWf from '../ListAllTool/ModalCloneWf'
import EmptyListToolByCategory from './EmptyListToolByCategory'

const ListToolByCategory = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<
    BusinessCategoryMarketTemplatesPublic | undefined
  >()
  const [showCloneWfModal, setShowCloneWfModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] =
    useState<BusinessCategoryMarketTemplatePublic | null>(null)

  useState<BusinessCategoryMarketTemplatePublic>()

  const [
    search,
    activeCategory,
    currentPageToolByCategory,
    setCurrentPageToolByCategory,
    paginationToolByCategory,
    setPaginationToolByCategory,
  ] = useMarketToolsCategory((state) => [
    state.search,
    state.activeCategory,
    state.currentPageToolByCategory,
    state.setCurrentPageToolByCategory,
    state.paginationToolByCategory,
    state.setPaginationToolByCategory,
  ])

  const updateCountOfCategoryById = useMarketToolsCategory(
    (state) => state.updateCountOfCategoryById
  )

  const updateCategoryPagingByCache = useMarketToolsCategory(
    (state) => state.updateCategoryPagingByCache
  )

  useEffect(() => {
    const fetchToolsByCategory = async (
      category: BusinessCategoryPublic | undefined,
      page: number,
      search: string
    ) => {
      try {
        if (loading || !category?.id) return

        setLoading(true)

        const { data } = await businessCategoriesGetListWfTemplateByCategoryApi(
          {
            query: {
              name: search,
              business_category_id: category.id,
              page_number: page,
              page_size: PAGE_SIZE.LARGE,
            },
          }
        )

        if (data && data.data) {
          setPaginationToolByCategory({
            total_count: data.data.total_count,
            total_page: data.data.total_pages,
            next_page: data.data.next_page,
            prev_page: data.data.prev_page,
          })

          setData(data.data.data)

          if (search) {
            // update number data of category by id
            updateCountOfCategoryById(category.id, data.data.total_count)
          } else {
            updateCategoryPagingByCache()
          }
        }
      } catch (error) {
        console.log('🚀 ~ fetchToolsByCategory ~ error:', error)
        Message.error({ message: 'Something wrong, please retry!' })
      } finally {
        setLoading(false)
      }
    }

    fetchToolsByCategory(activeCategory, currentPageToolByCategory, search)
  }, [activeCategory, currentPageToolByCategory, search])

  const handleCloneWorkflow = (tool: BusinessCategoryMarketTemplatePublic) => {
    setSelectedTemplate(tool)
    setShowCloneWfModal(true)
  }

  return (
    <div className="flex h-full flex-col">
      <div className="genai-scrollbar h-full overflow-y-auto">
        {!loading && search && size(data) === 0 && (
          <NoDataFound className="mt-[64px]" />
        )}

        {!loading && !search && size(data) === 0 && <EmptyListToolByCategory />}

        {!loading && size(data) > 0 && (
          <div className="flex w-full flex-row flex-wrap gap-x-[52px] gap-y-[36px] px-[4px] pb-[1px]">
            {data!.map((tool) => (
              <WorkflowItem
                description={tool.workflow_description ?? ''}
                title={tool.workflow_name}
                key={tool.workflow_id}
                image={tool.workflow_thumbnail_url ?? ''}
                onClick={() => {}}
                marketView
                handleCloneWorkflow={() => {
                  handleCloneWorkflow(tool)
                }}
              />
            ))}
          </div>
        )}
      </div>

      {paginationToolByCategory.total_page > 1 && (
        <div className="mt-[12px] flex w-full flex-row justify-end">
          <Pagination
            onChangePage={setCurrentPageToolByCategory}
            page={currentPageToolByCategory}
            totalPage={paginationToolByCategory.total_page ?? 0}
            className="w-[220px]"
          />
        </div>
      )}

      {showCloneWfModal && (
        <ModalCloneWf
          onClose={() => {
            setShowCloneWfModal(false)
          }}
          template={selectedTemplate}
        />
      )}
    </div>
  )
}

export default memo(ListToolByCategory)
