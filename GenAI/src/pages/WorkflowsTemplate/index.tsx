import Layout from '@/components/Layout'
import PageHeader from '@/components/PageHeader'
import SearchBar, { SearchBarInterface } from '@/components/SearchBar'
import { useLatestValue } from '@/hooks/useLatestValue'
import useMarketToolsCategory, {
  DEFAULT_TOOLS_BY_CATEGORY_PAGINATION,
} from '@/store/workflowsTemplate'
import { memo, useEffect, useRef } from 'react'
import { twMerge } from 'tailwind-merge'
import ListAllTool from './components/ListAllTool'
import ListCategory from './components/ListCategory'
import ListToolByCategory from './components/ListToolByCategory'

const WorkflowsTemplate = () => {
  const searchBarRef = useRef<SearchBarInterface>(null)

  const [
    search,
    setSearch,
    activeCategory,
    setCurrentPageToolByCategory,
    setPaginationToolByCategory,
  ] = useMarketToolsCategory((state) => [
    state.search,
    state.setSearch,
    state.activeCategory,
    state.setCurrentPageToolByCategory,
    state.setPaginationToolByCategory,
  ])

  const [clearSearch, clearActiveCategory] = useMarketToolsCategory((state) => [
    state.clearSearch,
    state.clearActiveCategory,
  ])

  const isAllCategory = activeCategory === undefined

  const lastIsAllCategory = useLatestValue(isAllCategory)

  const onSearch = (value: string) => {
    if (!lastIsAllCategory.current) {
      setCurrentPageToolByCategory(1)
      setPaginationToolByCategory(DEFAULT_TOOLS_BY_CATEGORY_PAGINATION)
      setSearch(value)
      return
    }
    setSearch(value)
  }

  useEffect(() => {
    return () => {
      clearSearch()
      clearActiveCategory()
    }
  }, [])

  return (
    <Layout ref={searchBarRef}>
      <ToolMarketplace
        searchBarRef={searchBarRef}
        search={search}
        setSearch={setSearch}
        onSearch={onSearch}
      />
    </Layout>
  )
}

const ToolMarketplace = memo(
  ({
    searchBarRef,
    search,
    setSearch,
    onSearch,
  }: {
    searchBarRef?: React.RefObject<SearchBarInterface>
    search: string
    setSearch: (value: string) => void
    onSearch: (value: string) => void
  }) => {
    const [
      setCurrentPageToolByCategory,
      setPaginationToolByCategory,
      activeCategory,
      setActiveCategory,
    ] = useMarketToolsCategory((state) => [
      state.setCurrentPageToolByCategory,
      state.setPaginationToolByCategory,
      state.activeCategory,
      state.setActiveCategory,
    ])

    const handleChangeCategory = (category: any) => {
      searchBarRef?.current?.resetSearchBar()
      setSearch('')

      setActiveCategory(category)

      if (category) {
        setCurrentPageToolByCategory(1)
        setPaginationToolByCategory(DEFAULT_TOOLS_BY_CATEGORY_PAGINATION)
      }
    }

    return (
      <div className={twMerge('flex h-full flex-col overflow-hidden px-[8px]')}>
        <div className="flex items-center justify-between">
          <PageHeader
            breadcrumbPaths={[
              {
                name: 'Workflow Market',
              },
            ]}
            title={
              'Discover, purchase, and share customizable workflow templates designed to streamline business processes'
            }
          />
          <SearchBar ref={searchBarRef} onSearch={onSearch} />
        </div>

        <ListCategory search={search} onChangeCategory={handleChangeCategory} />

        <div className="mt-[20px] flex-1 overflow-hidden">
          {activeCategory === undefined && (
            <ListAllTool activeCategory={activeCategory} />
          )}

          {activeCategory && <ListToolByCategory />}
        </div>
      </div>
    )
  }
)

export default WorkflowsTemplate
