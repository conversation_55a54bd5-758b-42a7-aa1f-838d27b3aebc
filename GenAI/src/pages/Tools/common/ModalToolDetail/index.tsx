import BaseModal from '@/components/BaseModal'
import ComingSoon from '@/components/ComingSoon'
import Tabs from '@/components/Tabs'
import Text from '@/components/Text'
import { colors } from '@/theme'
import { memo, useCallback, useEffect, useState } from 'react'

import { toolsReadToolApi } from '@/apis/client'
import EmptyData from '@/components/EmptyData'
import Message from '@/components/Message'
import { HTTP_STATUS_CODE, TABS } from '@/constants'
import './ckeditor5-content.css'
import IconButton from '@/components/IconButton'

interface IProps {
  isOpen?: boolean
  toolId?: string
  onClose: () => void
}

const ModalToolDetail = ({ isOpen = false, toolId, onClose }: IProps) => {
  const [activeTab, setActiveTab] = useState<TABS>(TABS.GENERAL)
  const [toolDetail, setToolDetail] = useState<any>()

  const [isLoading, setLoading] = useState(false)

  const fetchToolDetail = async () => {
    try {
      if (isLoading || !toolId) return

      setLoading(true)

      const res = await toolsReadToolApi({
        path: {
          tool_id: toolId,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setToolDetail(res.data?.data)
      }
    } catch (error: any) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchToolDetail()
  }, [toolId])

  const renderContent = useCallback(() => {
    if (activeTab === TABS.GENERAL) {
      return (
        <div className="flex flex-col p-4">
          <Text
            variant="medium"
            className="mb-2 text-Primary-Color"
            value="Instruction"
          />
          {toolDetail?.instruction ? (
            <div
              className="ck-content genai-scrollbar h-[456px] w-full overflow-auto rounded-lg border border-border-base-icon bg-Input-Field px-5 py-4"
              dangerouslySetInnerHTML={{
                __html: toolDetail?.instruction ?? '',
              }}
            />
          ) : (
            <div className="flex flex-col items-center">
              <EmptyData
                type="02"
                content="No instruction"
                className="mt-[64px]"
              />
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <ComingSoon size="sm" />
      </div>
    )
  }, [activeTab, toolDetail])

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div className="relative flex h-[632px] w-[1083px] flex-col overflow-hidden rounded-[20px] bg-white shadow-md">
        <IconButton
          className="absolute right-[10px] top-[10px] cursor-pointer duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
        <div className="flex flex-col gap-1 bg-Base-03 px-3 pb-4 pt-3">
          <Text
            value="Tool Details"
            type="subheading"
            variant="medium"
            className="text-Primary-Color"
          />
          <Text
            value="See tool instruction & customize options"
            type="subBody"
            className="text-Secondary-Color"
          />
        </div>
        <Tabs
          tabs={[
            {
              label: TABS.GENERAL,
              value: TABS.GENERAL,
            },
            {
              label: TABS.ADVANCED,
              value: TABS.ADVANCED,
            },
          ]}
          value={activeTab}
          onChange={setActiveTab}
        />
        {renderContent()}
      </div>
    </BaseModal>
  )
}

export default memo(ModalToolDetail)
