import {
  MyToolPublic,
  MyToolsPublic,
  toolsDeleteTool<PERSON>pi,
  toolsGetListMyToolAndPurchasedApi,
  toolsUpdatePublishToolApi,
  toolsUpdateToolDuplicateApi,
} from '@/apis/client'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import Pagination from '@/components/Pagination'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import ModalToolDetail from '@/pages/Tools/common/ModalToolDetail'
import {
  ItemAddTool,
  ToolItem,
  ToolItemSkeleton,
} from '@/pages/Tools/ToolCollection/components/ToolItem'

import Icon from '@/assets/icon/Icon'
import { useGroupTool } from '@/hooks/useGroupTool'
import { useMyProfile } from '@/hooks/useMyProfile'
import { memo, useCallback, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import ModalChangeToolCategory from './components/ModalChangeToolCategory'
import ModalPublishTool from './components/ModalPublishTool'
import ModalUpdateTools from './components/ModalUpdateTools'
import ToolGroupItem from './components/ToolItem/ToolGroupItem'
import { ToolActionProps } from './components/ToolItem/ToolItem'

interface ToolPurchasedProps {
  searchKey?: string
  categories?: string[]
}

const ToolPurchased = ({ searchKey, categories }: ToolPurchasedProps) => {
  const { isSuperAdmin } = useMyProfile()
  const navigate = useNavigate()
  const [isLoading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [totalPage, setTotalPage] = useState<number>()
  const [listTools, setListTools] = useState<MyToolsPublic>([])

  const [isOpenModalToolDetail, setOpenModalToolDetail] =
    useState<boolean>(false)
  const [isOpenModalUpdateTool, setOpenModalUpdateTool] =
    useState<boolean>(false)
  const [isOpenModalPublishTool, setOpenModalPublishTool] =
    useState<boolean>(false)
  const [isOpenModalChangeCategory, setOpenModalChangeCategory] =
    useState<boolean>(false)

  const [selectedTool, setSelectedTool] = useState<string | undefined>()
  const [selectedTool2, setSelectedTool2] = useState<MyToolPublic | undefined>()
  const { groupTool } = useGroupTool()

  const fetchMyTools = async (
    currentPage = page,
    search = searchKey,
    selectedCategories = categories
  ) => {
    try {
      if (isLoading) return

      setLoading(true)

      const categoryIds = selectedCategories?.length
        ? (selectedCategories as string[])
        : undefined

      const res = await toolsGetListMyToolAndPurchasedApi({
        query: {
          page_number: currentPage,
          page_size: PAGE_SIZE.EXTRA_LARGE,
          name: search || undefined,
          tool_category_id: categoryIds,
        },
        paramsSerializer: {
          indexes: null,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setListTools(data!.data?.data)
        setTotalPage(data?.data?.total_pages ?? 0)
        setPage(currentPage)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error: any) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteTool = useCallback(
    async ({ toolId }: ToolActionProps) => {
      if (!toolId) {
        return
      }

      const response = await toolsDeleteToolApi({
        path: {
          tool_id: toolId,
        },
      })

      if (response.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully deleted tool' })

        if (listTools?.length <= 1 && page > 1) {
          fetchMyTools?.(page - 1)
        } else {
          fetchMyTools?.()
        }
      } else if (response.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        Message.error({
          message: response?.error?.detail ?? 'Something went wrong!',
        })
      }
    },
    [listTools, page, fetchMyTools]
  )

  const handleDuplicateTool = useCallback(
    async ({ toolId }: ToolActionProps) => {
      if (!toolId) {
        return
      }

      const response = await toolsUpdateToolDuplicateApi({
        path: {
          tool_id: toolId,
        },
      })

      if (response.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully duplicated tool' })

        fetchMyTools?.()
      } else if (response.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        Message.error({
          message: response?.error?.detail ?? 'Something went wrong!',
        })
      }
    },
    [listTools, page, fetchMyTools]
  )

  const handleCancelPublishing = useCallback(
    async ({ toolId }: ToolActionProps) => {
      if (!toolId) {
        return
      }

      const response = await toolsUpdatePublishToolApi({
        path: {
          tool_id: toolId,
        },
        body: {
          is_public: false,
        },
      })

      if (response.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully cancelled publishing' })
        fetchMyTools?.()
      } else if (response.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message: 'Something went wrong!',
        })
      } else {
        if (
          response.error &&
          response.error.detail &&
          typeof response.error.detail === 'string'
        ) {
          Message.error({ message: response.error.detail })
          return
        }

        Message.error({ message: 'Something went wrong!' })
      }
    },
    [fetchMyTools]
  )

  const handlePublishingTool = (tool: MyToolPublic) => {
    setSelectedTool2(tool)
    setOpenModalPublishTool(true)
  }

  const handleChangeCategory = (tool: MyToolPublic) => {
    setSelectedTool2(tool)
    setOpenModalChangeCategory(true)
  }

  const handleCloseModalToolDetail = useCallback(() => {
    setOpenModalToolDetail(false)
    setSelectedTool(undefined)
  }, [])

  const handleOpenModalToolDetail = useCallback((id: string) => {
    setSelectedTool(id)
    setOpenModalToolDetail(true)
  }, [])

  useEffect(() => {
    fetchMyTools(1)
  }, [searchKey, categories])

  const handleOpenModalUpdateTool = useCallback((data?: MyToolPublic) => {
    setSelectedTool2(data)
    setOpenModalUpdateTool(true)
  }, [])

  const onCloseModalUpdateTool = useCallback(() => {
    setOpenModalUpdateTool(false)
    setSelectedTool2(undefined)
  }, [])

  if (isLoading) {
    return (
      <div className="genai-scrollbar mt-4 flex flex-wrap gap-3 overflow-auto px-1 pb-2">
        {Array.from({ length: PAGE_SIZE.MEDIUM }).map((_, index) => (
          <ToolItemSkeleton key={index} />
        ))}
      </div>
    )
  }

  if (!listTools?.length) {
    if (searchKey || categories?.length) {
      return <NoDataFound className="mb-[24px] mt-[100px]" />
    }

    return (
      <div className="flex flex-col items-center">
        <EmptyData size="medium" type="03" className="mb-[24px] mt-[100px]" />

        {isSuperAdmin ? (
          <Button
            type="primary"
            size="medium"
            onClick={handleOpenModalUpdateTool}
            className="w-[205px]"
            text="Add Tool"
            leftIcon={
              <Icon name="vuesax-outline-add" color="#ffffff" size={20} />
            }
          />
        ) : (
          <Button
            type="primary"
            size="medium"
            onClick={() => navigate('/marketplace/tools')}
            className="w-[205px]"
            text="Go to Marketplace"
          />
        )}
        {isOpenModalUpdateTool && (
          <ModalUpdateTools
            isOpen
            data={selectedTool2}
            refetchData={fetchMyTools}
            onClose={onCloseModalUpdateTool}
            setData={setSelectedTool2}
          />
        )}
      </div>
    )
  }

  const showAddTool = !searchKey && !categories?.length && isSuperAdmin

  return (
    <>
      <div className="genai-scrollbar mt-4 h-full overflow-auto">
        <div className="flex flex-wrap gap-[20px] px-1 pb-2">
          {showAddTool && (
            <ItemAddTool addNewTool={handleOpenModalUpdateTool} />
          )}
          {groupTool?.map((item: any) => {
            if (!listTools.find((tool) => tool.group_id === item.id)) return

            return (
              <ToolGroupItem
                key={item.id}
                data={{
                  id: item.id,
                  name: item.name ?? '',
                  description: item?.description ?? '',
                  logo: item?.logo ?? '',
                  tool_category: item?.tool_category || undefined,
                  type: item?.type ?? '',
                  is_public: item?.is_public ?? false,
                }}
                group={item.tools || []}
                onClickToolGroup={(tool) => {
                  if (tool?.is_my_tool) {
                    handleOpenModalUpdateTool(tool)
                  } else {
                    handleOpenModalToolDetail(tool.id)
                  }
                }}
                searchKey={searchKey}
              />
            )
          })}
          {listTools.map((tool: MyToolPublic) => {
            if (groupTool?.find((n) => n.tools?.find((t) => t.id === tool.id)))
              return

            return (
              <ToolItem
                key={tool.id}
                data={{
                  id: tool.id,
                  name: tool.name ?? '',
                  description: tool?.description ?? '',
                  logo: tool?.logo ?? '',
                  tool_category: tool?.tool_category || undefined,
                  type: tool?.type ?? '',
                  is_public: tool?.is_public ?? false,
                }}
                onClick={() => {
                  if (tool.is_my_tool) {
                    handleOpenModalUpdateTool(tool)
                  } else {
                    handleOpenModalToolDetail(tool.id)
                  }
                }}
                onPublishing={() => handlePublishingTool(tool)}
                onChangeCategory={() => handleChangeCategory(tool)}
                onCancelPublishing={handleCancelPublishing}
                onDeleteTool={handleDeleteTool}
                handleDuplicateTool={handleDuplicateTool}
                toolType={tool.is_my_tool ? 'Free' : 'Purchased'}
                isShowAction={tool.is_my_tool}
              />
            )
          })}
        </div>
      </div>

      {totalPage! > 1 && (
        <Pagination
          page={page}
          totalPage={totalPage}
          onChangePage={(page) => fetchMyTools(page)}
          className="mt-3 flex w-full justify-end"
        />
      )}
      {isOpenModalToolDetail && (
        <ModalToolDetail
          toolId={selectedTool}
          isOpen={isOpenModalToolDetail}
          onClose={handleCloseModalToolDetail}
        />
      )}
      {isOpenModalUpdateTool && (
        <ModalUpdateTools
          isOpen
          data={selectedTool2}
          refetchData={fetchMyTools}
          onClose={onCloseModalUpdateTool}
          setData={setSelectedTool2}
        />
      )}

      {isOpenModalPublishTool && (
        <ModalPublishTool
          isOpen={isOpenModalPublishTool}
          setIsOpen={setOpenModalPublishTool}
          selectedTool={selectedTool2}
          handlePublishingSuccess={fetchMyTools}
        />
      )}
      {isOpenModalChangeCategory && (
        <ModalChangeToolCategory
          isOpen={isOpenModalChangeCategory}
          setIsOpen={setOpenModalChangeCategory}
          selectedTool={selectedTool2}
          handlePublishingSuccess={fetchMyTools}
        />
      )}
    </>
  )
}

export default memo(ToolPurchased)
