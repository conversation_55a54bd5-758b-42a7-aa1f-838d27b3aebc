import { memo, useCallback, useMemo, useState } from 'react'

import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import { MessageDialog } from '@/components/DialogMessage'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import IconBuiltInTool from '@/components/ModalAddToolkit/ToolkitItem/IconBuiltInTool'

import More from '@/components/More'
import Text from '@/components/Text'
import { TOOL_TYPE } from '@/pages/Tools/const'
import { colors } from '@/theme'
import clsx from 'clsx'
import styles from './styles.module.scss'

enum TOOL_ACTIONS {
  DUPLICATE = 'duplicate',
  CANCEL_PUBLISHING = 'cancelPublishing',
  PUBLISHING = 'publishing',
  CHANGE_CATEGORY = 'changeCategory',
  DELETE = 'delete',
}

export interface ToolActionProps {
  toolId?: string
}

const ToolItem = ({
  data,
  onClick,
  onCancelPublishing,
  onDeleteTool,
  onPublishing,
  onChangeCategory,
  handleDuplicateTool,
  isShowAction = true,
  toolType = 'Free',
}: {
  data: Partial<{
    id: string
    name: string
    logo: string
    description: string
    tool_category: {
      id: string
      name: string
    }
    is_public: boolean
    type: string
  }>
} & {
  onCancelPublishing?: ({ toolId }: ToolActionProps) => void
  onDeleteTool?: ({ toolId }: ToolActionProps) => void
  onPublishing?: ({ toolId, isPublic }: any) => void
  onChangeCategory?: () => void
  handleDuplicateTool?: ({ toolId }: ToolActionProps) => void
  onClick: () => void
  isShowAction?: boolean
  toolType?: 'Free' | 'Purchased'
}) => {
  const {
    name,
    logo,
    description,
    tool_category: toolCategory,
    is_public: isPublic,
    type,
  } = data

  const isBuiltInTool = useMemo(() => type === TOOL_TYPE.BUILT_IN, [type])

  const [openToolAction, setOpenToolAction] = useState(false)

  const toolItems = useMemo(() => {
    const items = []
    items.push({
      key: TOOL_ACTIONS.DUPLICATE,
      label: 'Duplicate',
    })

    if (isPublic) {
      items.push({
        key: TOOL_ACTIONS.CANCEL_PUBLISHING,
        label: 'Cancel publishing',
      })
    } else {
      items.push({
        key: TOOL_ACTIONS.PUBLISHING,
        label: 'Publish',
      })
    }

    if (toolCategory) {
      items.push({
        key: TOOL_ACTIONS.CHANGE_CATEGORY,
        label: 'Change category',
      })
    }

    items.push({
      key: TOOL_ACTIONS.DELETE,
      label: 'Delete',
    })

    return items
  }, [isPublic, toolCategory])

  const handleCancelPublishing = useCallback(() => {
    MessageDialog.confirm({
      mainMessage: 'Cancel publishing tool on the marketplace?',
      subMessage:
        'If continue, this tool will be unlisted and hide from the marketplace.',
      onClick: () => onCancelPublishing?.({ toolId: data?.id }),
    })
  }, [data, onCancelPublishing])

  const handleDeleteTool = useCallback(() => {
    MessageDialog.warning({
      mainMessage: 'Delete tool?',
      subMessage:
        'If continue, this tool will be permanently deleted and removed from the market (if published).',
      onClick: () => onDeleteTool?.({ toolId: data?.id }),
    })
  }, [data, onDeleteTool])

  const handleSelectToolAction = useCallback(
    ({ key }: SelectedItemProps) => {
      switch (key) {
        case TOOL_ACTIONS.CANCEL_PUBLISHING:
          handleCancelPublishing()
          break
        case TOOL_ACTIONS.PUBLISHING:
          onPublishing?.({ toolId: data?.id })
          break
        case TOOL_ACTIONS.CHANGE_CATEGORY:
          onChangeCategory?.()
          break
        case TOOL_ACTIONS.DELETE:
          handleDeleteTool()
          break
        case TOOL_ACTIONS.DUPLICATE:
          handleDuplicateTool?.({ toolId: data?.id })
          break
      }
    },
    [handleCancelPublishing, handleDeleteTool, handleDuplicateTool]
  )

  return (
    <div className={styles['tool-item']} onClick={onClick}>
      {isShowAction && (
        <>
          {isBuiltInTool && (
            <div className="absolute left-1 top-2 flex flex-row-reverse gap-1">
              <IconBuiltInTool width={14.36} height={16} />
            </div>
          )}
          <div className="absolute right-2 top-3 flex flex-row-reverse gap-1">
            <Dropdown
              open={openToolAction}
              items={toolItems}
              onSelect={handleSelectToolAction}
              onOpenChange={(newOpen) => {
                setOpenToolAction(newOpen)
              }}
            >
              <More active={openToolAction} />
            </Dropdown>
            <Icon
              name="Bold-ShoppingEcommerce-CartLargeMinimalistic"
              size={20}
              color={colors['border-base-icon']}
              gradient={isPublic ? ['#642B734D', '#C6426E4D'] : undefined}
            />
          </div>
        </>
      )}

      <Avatar
        name={name}
        avatarUrl={logo}
        avatarDefault={
          <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
            <Icon
              name="tool-01"
              size={18}
              gradient={['#642B734D', '#C6426E4D']}
            />
          </div>
        }
        hasBorder
        variant="square"
      />
      <Text
        variant="medium"
        className="mb-1 mt-2 text-Primary-Color"
        value={name}
        elementType="div"
        ellipsis
      />
      <Text
        className={clsx(styles['tool-description'], 'h-[72px]')}
        value={description}
        type="subBody"
        elementType="div"
        ellipsis
        multipleLine={4}
        tooltipPosition="bottom"
      />

      <div className="mt-[8px] h-[60px] w-full"></div>
      <div className="mt-[8px] flex w-full items-center justify-between gap-1 px-[4px]">
        {toolCategory?.name ? (
          <div className="max-w-[77%]">
            <Text
              value={toolCategory?.name}
              type="supportText"
              variant="medium"
              className="bg-Main-Color bg-clip-text text-transparent"
              ellipsis
              elementType="div"
            />
          </div>
        ) : (
          <div />
        )}

        <Text
          value={toolType}
          type="supportText"
          variant="medium"
          className="text-Secondary-Color"
          elementType="div"
        />
      </div>
    </div>
  )
}

export default memo(ToolItem)
