import { memo, useEffect, useMemo, useRef, useState } from 'react'

import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import IconBuiltInToolLarge from '@/components/IconBuiltInTool'

import Text from '@/components/Text'

import clsx from 'clsx'
import styles from './styles.module.scss'
import { ModifyToolGroupPublic } from '@/components/ModalAddToolkit/types'
import Tooltip from '@/components/Tooltip'
import { ToolItem_Input } from '@/apis/client'
import Popover from '@/components/Popover'

export interface ToolActionProps {
  toolId?: string
}

const containerWidth = 480
const moreButtonWidth = 70
const padding = 4

const ToolGroupItem = ({
  group = [],
  data,
  onClickToolGroup,
  searchKey,
}: {
  searchKey?: string | undefined
  group?: ModifyToolGroupPublic[] | undefined
  data: Partial<{
    id: string
    name: string
    logo: string
    description: string
    tool_category: {
      id: string
      name: string
    }
    is_public: boolean
    type: string
  }>
} & {
  onClickToolGroup?: (tool: ToolItem_Input) => void
}) => {
  const { name, logo, description, tool_category: toolCategory } = data

  const itemRefs = useRef<HTMLDivElement[]>([])
  const [visibleItems, setVisibleItems] = useState<ModifyToolGroupPublic[]>([])
  const [moreItems, setMoreItems] = useState<ModifyToolGroupPublic[]>([])

  const [isPopoverOpen, setIsPopoverOpen] = useState(false)

  const toolsOrderedBySearchText = useMemo(() => {
    if (!searchKey) return group

    return [...(group ?? [])].sort((a, b) => {
      const aMatches = a.name.toLowerCase().includes(searchKey.toLowerCase())
      const bMatches = b.name.toLowerCase().includes(searchKey.toLowerCase())

      if (aMatches && !bMatches) return -1
      if (!aMatches && bMatches) return 1
      return 0
    })
  }, [group, searchKey])

  const contentMoreItem = useMemo(() => {
    return moreItems.map((item) => item.name).join(', ')
  }, [moreItems])

  useEffect(() => {
    if (!itemRefs.current) return

    let totalWidth = 0
    const visible: typeof toolsOrderedBySearchText = []
    const hidden: typeof toolsOrderedBySearchText = []

    for (let i = 0; i < toolsOrderedBySearchText.length; i++) {
      const el = itemRefs.current[i]
      if (!el) continue

      const itemWidth = el.offsetWidth + padding

      if (totalWidth + itemWidth + moreButtonWidth <= containerWidth) {
        totalWidth += itemWidth
        visible.push(toolsOrderedBySearchText[i])
      } else {
        hidden.push(toolsOrderedBySearchText[i])
      }
    }

    setVisibleItems(visible)
    setMoreItems(hidden)
  }, [toolsOrderedBySearchText])

  return (
    <div className={styles['tool-item-group']}>
      <div className="absolute right-[12px] top-[12px]">
        <IconBuiltInToolLarge />
      </div>

      <Avatar
        name={name}
        avatarUrl={logo}
        avatarDefault={
          <div className="flex size-8 items-center justify-center rounded-lg border border-border-base-icon bg-Background-Color">
            <Icon
              name="tool-01"
              size={18}
              gradient={['#642B734D', '#C6426E4D']}
            />
          </div>
        }
        hasBorder
        variant="square"
      />
      <Text
        variant="medium"
        className="mb-1 mt-2 text-Primary-Color"
        value={name}
        elementType="div"
        ellipsis
      />
      <Text
        className={clsx(styles['tool-description'], 'h-[72px]')}
        value={description}
        type="subBody"
        elementType="div"
        ellipsis
        multipleLine={4}
        tooltipPosition="bottom"
      />

      <div className="mt-[8px] h-[60px] w-full">
        <div className="flex h-full w-full flex-wrap gap-[8px] self-stretch">
          {/* Hidden item render to measure */}
          <div className="invisible absolute flex h-[60px] w-[258px] flex-wrap gap-[8px] self-stretch">
            {toolsOrderedBySearchText.map((item, index) => (
              <div
                key={index}
                ref={(el) => (itemRefs.current[index] = el)}
                className="flex h-fit w-fit max-w-[174px] cursor-pointer items-center justify-center rounded-full bg-neutral-100 px-[12px] py-[4px]"
              >
                <Text
                  type="subBody"
                  variant="medium"
                  className="text-Tertiary-Color"
                  ellipsis
                  elementType="div"
                >
                  {item.name}
                </Text>
              </div>
            ))}
          </div>
          {visibleItems.map((item, index) => (
            <div
              key={index}
              onClick={() => onClickToolGroup?.(item)}
              className="flex h-fit w-fit max-w-[174px] cursor-pointer items-center justify-center rounded-full bg-neutral-100 px-[12px] py-[4px] hover:bg-neutral-200"
            >
              <Text
                type="subBody"
                variant="medium"
                className="text-Tertiary-Color"
                ellipsis
                elementType="div"
              >
                {item.name}
              </Text>
            </div>
          ))}
          {moreItems.length > 0 && (
            <Popover
              onOpenChange={(value) => setIsPopoverOpen(value)}
              overlayClassName="!p-[12px]"
              content={
                <div className="genai-scrollbar flex h-[168px] w-[262px] flex-col gap-[8px] overflow-auto">
                  {moreItems.map((item, index) => (
                    <div
                      key={index}
                      onClick={() => onClickToolGroup?.(item)}
                      className="flex cursor-pointer flex-col gap-[4px] rounded-[8px] px-[6px] py-[4px] hover:bg-neutral-100"
                    >
                      <Text
                        type="body"
                        variant="medium"
                        className="text-Primary-Color"
                      >
                        {item.name}
                      </Text>
                      <Text
                        type="subBody"
                        variant="regular"
                        className="text-Secondary-Color"
                      >
                        {item.description}
                      </Text>
                    </div>
                  ))}
                </div>
              }
            >
              <Tooltip
                align={{
                  offset: [-15, -4],
                  overflow: { shiftX: true },
                }}
                text={contentMoreItem}
              >
                <div
                  className={clsx(
                    'flex h-fit w-fit cursor-pointer items-center justify-center rounded-full bg-neutral-100 px-[12px] py-[4px] hover:bg-neutral-200',
                    isPopoverOpen && 'bg-neutral-200'
                  )}
                >
                  <Text
                    type="subBody"
                    variant="medium"
                    className="text-Tertiary-Color"
                  >
                    + {toolsOrderedBySearchText.length - visibleItems.length}{' '}
                    more
                  </Text>
                </div>
              </Tooltip>
            </Popover>
          )}
        </div>
      </div>
      <div className="mt-[8px] flex w-full items-center justify-between gap-1 px-[4px]">
        {toolCategory?.name ? (
          <div className="max-w-[77%]">
            <Text
              value={toolCategory?.name}
              type="supportText"
              variant="medium"
              className="bg-Main-Color bg-clip-text text-transparent"
              ellipsis
              elementType="div"
            />
          </div>
        ) : (
          <div />
        )}

        <Text
          value={'System'}
          type="supportText"
          variant="medium"
          className="text-Secondary-Color"
          elementType="div"
        />
      </div>
    </div>
  )
}

export default memo(ToolGroupItem)
