import { memo } from 'react'

import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import styles from './styles.module.scss'

interface IProps {
  [key: string]: any
}

const ItemAddTool = ({ addNewTool }: IProps) => {
  return (
    <div id="add-tool" className={styles['item-add-tool']} onClick={addNewTool}>
      <div className="flex items-center justify-center rounded-lg bg-Hover-Color p-1">
        <Icon
          name="Bold-MessagesConversation-PenNewRound"
          gradient={['#C6426E', '#642B73']}
        />
      </div>
      <Text
        value="Add new tool"
        variant="semibold"
        className="mb-1 mt-2 text-Primary-Color"
      />
      <Text
        value="Click here to create your own tool"
        type="subBody"
        className="text-Secondary-Color"
      />
    </div>
  )
}

export default memo(ItemAddTool)
