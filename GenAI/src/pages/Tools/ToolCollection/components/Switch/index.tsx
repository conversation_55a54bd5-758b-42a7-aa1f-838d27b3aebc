import Text from '@/components/Text'
import clsx from 'clsx'
import { ReactNode } from 'react'

interface SwitchOptionProp {
  label?: ReactNode
  active?: boolean
  onClick?: (value: any) => void
}

interface SwitchProp {
  className?: string
  checked?: boolean
  checkedChildren?: ReactNode
  unCheckedChildren?: ReactNode
  onChange?: (checked: boolean, event: Event) => void
}

const SwitchOption = ({ label, active, onClick }: SwitchOptionProp) => (
  <div
    className={clsx(
      'flex cursor-pointer items-center justify-center rounded-lg px-2 py-[7px]',
      active ? 'bg-Main-Disable-2' : 'bg-neutral-100'
    )}
    onClick={onClick}
  >
    <Text
      type="subBody"
      variant="medium"
      className={clsx(
        active ? 'text-linear-gradient-main2-color' : 'text-Primary-Color'
      )}
    >
      {label}
    </Text>
  </div>
)

const Switch = ({
  className,
  checked,
  checkedChildren,
  unCheckedChildren,
  onChange,
}: SwitchProp) => (
  <div className={clsx('flex rounded-lg bg-neutral-100', className)}>
    <SwitchOption
      active={checked}
      label={checkedChildren}
      onClick={(event) => onChange?.(true, event)}
    />
    <SwitchOption
      active={!checked}
      label={unCheckedChildren}
      onClick={(event) => onChange?.(false, event)}
    />
  </div>
)

export default Switch
