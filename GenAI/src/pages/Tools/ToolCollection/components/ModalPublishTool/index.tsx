import {
  MyToolPublic,
  ToolCategoryPublic,
  toolsUpdatePublishToolApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Select from '@/components/Select'
import { HTTP_STATUS_CODE } from '@/constants'
import { useLoadCategory } from '@/pages/Tools/hooks/useLoadCategory'
import { memo, useState } from 'react'

interface Props {
  isOpen: boolean
  selectedTool: MyToolPublic | undefined
  setIsOpen: (value: boolean) => void
  handlePublishingSuccess?: () => void
}

const ModalPublishTool = ({
  isOpen,
  selectedTool,
  setIsOpen,
  handlePublishingSuccess,
}: Props) => {
  const [selectedCategory, setSelectedCategory] = useState<
    ToolCategoryPublic | undefined
  >(
    selectedTool?.tool_category
      ? {
          id: selectedTool?.tool_category?.id ?? '',
          name: selectedTool?.tool_category?.name ?? '',
          logo: null,
        }
      : undefined
  )

  const {
    isLoading,
    data,
    inputValue,
    handleReachEnd,
    onChangeInputValue,
    reLoadCategory,
  } = useLoadCategory()

  const [isLoadingPublishing, setIsLoadingPublishing] = useState(false)

  const publishCallApi = async () => {
    if (!selectedTool || !selectedCategory) return

    setIsLoadingPublishing(true)

    try {
      const res = await toolsUpdatePublishToolApi({
        path: {
          tool_id: selectedTool?.id,
        },
        body: {
          tool_category_id: selectedCategory.id,
          is_public: true,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        handlePublishingSuccess?.()
        Message.success({
          message: 'Successfully published tool',
        })

        setIsOpen(false)
        setIsLoadingPublishing(false)
      } else if (res.status === HTTP_STATUS_CODE.NOT_FOUND) {
        Message.error({
          message:
            res?.error?.detail ??
            'Tool category has been removed, please retry',
        })
        setIsLoadingPublishing(false)
        reLoadCategory()
      } else {
        Message.error({
          message: res?.error?.detail ?? 'Something went wrong!',
        })
        setIsLoadingPublishing(false)
        reLoadCategory()
      }
    } catch (error) {
      setIsLoadingPublishing(false)
      Message.error({
        message: (error as any)?.message ?? 'Something went wrong!',
      })
    }
  }

  const handlePublishing = async () => {
    if (!selectedTool || !selectedCategory) return

    MessageDialog.confirm({
      mainMessage: 'Publish tool on the marketplace?',
      subMessage:
        'If continue, this tool will be listed and available for sale on the marketplace.',
      onClick: () => {
        publishCallApi()
      },
    })
  }

  const idDisable = !selectedCategory

  return (
    <Modal
      open={isOpen}
      title="Publish tool"
      subTitle="Choose the appropriate category in the marketplace for adding your tool."
      className="w-[496px]"
      okText="Publish"
      classNameFooter="justify-center gap-[12px]"
      classNameCancelButton="w-[218px]"
      classNameOkButton="w-[218px]"
      okLoading={isLoadingPublishing}
      okDisable={idDisable}
      onClickCancel={() => {
        setIsOpen(false)
      }}
      onClickOk={handlePublishing}
    >
      <div className="flex h-[105px] w-full flex-col gap-[8px] rounded-[12px] border border-neutral-200 bg-white px-[24px] pb-[24px] pt-[16px]">
        <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
          Category
        </div>

        <Select
          placeholder="Select category or type in to search"
          isLoading={isLoading}
          data={data}
          selected={selectedCategory}
          inputValue={inputValue}
          onChangeSelectedValue={(item) => {
            setSelectedCategory(item)
          }}
          displayValue={(item) => item?.name ?? ''}
          onChangeInputValue={onChangeInputValue}
          onReachEnd={handleReachEnd}
        />
      </div>
    </Modal>
  )
}

export default memo(ModalPublishTool)
