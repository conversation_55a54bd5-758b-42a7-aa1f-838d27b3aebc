import {
  MyToolPublic,
  ToolCategoryPublic,
  toolsUpdatePublishToolApi,
} from '@/apis/client'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Select from '@/components/Select'
import { isDevelopment } from '@/helpers'
import { useLoadCategory } from '@/pages/Tools/hooks/useLoadCategory'
import { memo, useState } from 'react'

interface Props {
  isOpen: boolean
  selectedTool: MyToolPublic | undefined
  setIsOpen: (value: boolean) => void
  handlePublishingSuccess?: () => void
}

const ModalChangeToolCategory = ({
  isOpen,
  selectedTool,
  setIsOpen,
  handlePublishingSuccess,
}: Props) => {
  const [selectedCategory, setSelectedCategory] = useState<ToolCategoryPublic>({
    id: selectedTool?.tool_category?.id ?? '',
    name: selectedTool?.tool_category?.name ?? '',
    logo: null,
  })

  const {
    isLoading,
    data,
    inputValue,
    handleReachEnd,
    onChangeInputValue,
    reLoadCategory,
  } = useLoadCategory()

  const [isLoadingPublishing, setIsLoadingPublishing] = useState(false)

  const handlePublishing = async () => {
    if (!selectedTool || !selectedCategory) return

    try {
      setIsLoadingPublishing(true)

      const { data, error } = await toolsUpdatePublishToolApi({
        path: {
          tool_id: selectedTool.id,
        },
        body: {
          tool_category_id: selectedCategory.id,
          is_public: selectedTool.is_public,
        },
      })

      if (isDevelopment && error) {
        console.log('🚀 ~ handlePublishing ~ error:', error)
      }

      if (error && error.detail) {
        Message.error({ message: error.detail })
        setIsLoadingPublishing(false)

        reLoadCategory()
        return
      }

      if (data && data.data) {
        handlePublishingSuccess?.()
        Message.success({
          message: 'Successfully updated tool category',
        })
      }

      setIsOpen(false)
      setIsLoadingPublishing(false)
    } catch (error) {
      setIsLoadingPublishing(false)

      console.log('🚀 ~ handlePublishing ~ error:', error)
      Message.error({
        message: (error as any)?.message ?? 'Something went wrong!',
      })
    }
  }

  const idDisable =
    !selectedCategory || selectedCategory.id === selectedTool?.tool_category?.id

  return (
    <Modal
      open={isOpen}
      title="Edit category"
      subTitle="Choose the appropriate category in the marketplace for adding your tool."
      className="w-[496px]"
      okText="Update"
      classNameFooter="justify-center gap-[12px]"
      classNameCancelButton="w-[218px]"
      classNameOkButton="w-[218px]"
      okLoading={isLoadingPublishing}
      okDisable={idDisable}
      onClickCancel={() => {
        setIsOpen(false)
      }}
      onClickOk={handlePublishing}
    >
      <div className="flex h-[105px] w-full flex-col gap-[8px] rounded-[12px] border border-neutral-200 bg-white px-[24px] pb-[24px] pt-[16px]">
        <div className="px-[4px] text-[14px] font-medium leading-[21px] text-Primary-Color">
          Category
        </div>

        <Select
          placeholder="Select category or type in to search"
          isLoading={isLoading}
          data={data}
          selected={selectedCategory}
          inputValue={inputValue}
          onChangeSelectedValue={(item) => {
            setSelectedCategory(item)
          }}
          displayValue={(item) => item?.name ?? ''}
          onChangeInputValue={onChangeInputValue}
          onReachEnd={handleReachEnd}
        />
      </div>
    </Modal>
  )
}

export default memo(ModalChangeToolCategory)
