import Input from '@/components/Input'
import Text from '@/components/Text'
import clsx from 'clsx'
import { memo } from 'react'

export enum EParamType {
  str = 'str',
  int = 'int',
  float = 'float',
  bool = 'bool',
}

const InputByType = ({
  onChange,
  onPressEnter,
  type,
  value,
  placeholder,
  disabled,
  onBlur,
}: IInputByType) => {
  const changeHandler = (e: { target: { value: any } }) => {
    const value = e.target.value
    if (type === EParamType.int) {
      const tVal = value.replace(/[^0-9]/g, '')
      onChange(tVal)
    } else if (type === EParamType.float) {
      const tVal = value.replace(/[^0-9.]/g, '')
      onChange(tVal)
    } else {
      onChange(value)
    }
  }

  if (type === EParamType.bool) {
    return (
      <div className="flex items-center gap-[4px]">
        <div
          onClick={() => {
            if (!disabled) {
              onChange(true)
            }
          }}
          className={clsx(
            'flex cursor-pointer items-center justify-center rounded-md px-[12px] py-[6px]',
            value ? 'bg-Main-Disable-2' : 'bg-Disable hover:bg-Hover-2',
            disabled && '!cursor-default !bg-Disable'
          )}
        >
          <Text
            type="subBody"
            className={clsx(
              !value ? 'text-Disable-Text' : 'text-gradient',
              disabled && '!text-Disable-Text'
            )}
          >
            TRUE
          </Text>
        </div>

        <div
          onClick={() => {
            if (!disabled) {
              onChange(false)
            }
          }}
          className={clsx(
            'flex cursor-pointer items-center justify-center rounded-md px-[12px] py-[6px]',
            value === false ? 'bg-Main-Disable-2' : 'bg-Disable',
            !disabled && 'hover:bg-Hover-2',
            disabled && '!cursor-default bg-Disable'
          )}
        >
          <Text
            type="subBody"
            className={clsx(
              !(value === false) || disabled
                ? 'text-Disable-Text'
                : 'text-gradient'
            )}
          >
            FALSE
          </Text>
        </div>
      </div>
    )
  }

  return (
    <Input
      value={value as string}
      onChange={changeHandler}
      onPressEnter={onPressEnter}
      onBlur={onBlur}
      maxLength={3000}
      isFullWidth
      placeholder={placeholder}
      disabled={disabled}
    />
  )
}

interface IInputByType {
  value: string | boolean
  type: EParamType
  onChange: (value: string | boolean) => void
  onPressEnter?: () => void
  onBlur?: () => void
  placeholder?: string
  disabled?: boolean
}

export default memo(InputByType)
