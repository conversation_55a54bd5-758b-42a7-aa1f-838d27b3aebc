import { useCallback, useState } from 'react'
import Text from '@/components/Text'
import {
  DefaultValueType,
  DefaultValueTypeBody,
  DefaultValueTypeHeader,
  DefaultValueTypeTitle,
  ParamTypes,
} from '../../consts'
import InputByType, { EParamType } from '../../InputByType'
import EmptyData from '@/components/EmptyData'
import { size } from 'lodash'
import { ToolDefaultEnv, ToolDefaultParameter } from '@/apis/client'
import clsx from 'clsx'
import { colors } from '@/theme'
import Input, { InputProps } from '@/components/Input'
import IconButton from '@/components/IconButton'

const EnvInput = (props: InputProps & { data: any }) => {
  const { data } = props
  const [isShow, setIsShow] = useState(false)

  return (
    <Input
      {...props}
      suffix={
        data.is_secret && (
          <IconButton
            nameIcon={
              isShow ? 'vuesax-outline-eye' : 'vuesax-outline-eye-slash'
            }
            colorIcon={colors['neutral']['300']}
            hoverColor={colors['Primary-Color']}
            sizeIcon={16}
            className="select-none"
            onClick={() => setIsShow(!isShow)}
          />
        )
      }
      secure={!isShow && data.is_secret}
    />
  )
}

interface DefaultValueTabConfigurationProps {
  type?: DefaultValueType
  body?: DefaultValueTypeBody[]
  onChange?: (data: DefaultValueTypeBody, value: string | boolean) => void
}

const DefaultValueTabConfiguration = ({
  type = DefaultValueType.InputParameters,
  body,
  onChange,
}: DefaultValueTabConfigurationProps) => {
  const dataTypeContent = useCallback(
    (data: DefaultValueTypeBody) => {
      if (type === DefaultValueType.InputParameters) {
        return ParamTypes[data.type ?? 'str']
      }

      return data?.is_secret ? 'Secret' : 'Public'
    },
    [type]
  )

  return (
    <div className="flex h-full w-full flex-col items-start gap-1">
      <Text
        type="subBody"
        variant="medium"
        className="flex w-full items-center px-1 text-Primary-Color"
      >
        {DefaultValueTypeTitle[type]}
      </Text>
      <div className="flex h-full w-full flex-col items-start gap-2 rounded-xl border border-border-base-icon px-4 py-3">
        <div className="flex w-full flex-col items-start gap-2">
          <div className="header flex items-center gap-2">
            <Text
              type="subBody"
              variant="medium"
              className="flex min-w-[600px] items-center text-Secondary-Color"
            >
              {DefaultValueTypeHeader[type][0]}
            </Text>
            <Text
              type="subBody"
              variant="medium"
              className="flex min-w-[100px] items-center text-Secondary-Color"
            >
              {DefaultValueTypeHeader[type][1]}
            </Text>
            <Text
              type="subBody"
              variant="medium"
              className="flex w-full min-w-[240px] max-w-[487px] items-center px-2 text-Secondary-Color"
            >
              {DefaultValueTypeHeader[type][2]}
            </Text>
          </div>

          <div className="body flex w-full flex-col items-start gap-2">
            {body?.map((data) => (
              <div
                key={data.name}
                className="flex min-h-[34px] w-full items-center gap-2"
              >
                <div className="flex min-w-[600px] flex-col items-start">
                  <Text
                    variant="medium"
                    className={clsx(
                      'max-w-[600px] overflow-hidden text-Primary-Color',
                      data?.disabled && '!text-neutral-400'
                    )}
                    elementType="div"
                    ellipsis
                  >
                    {data.name}
                  </Text>
                  {data.description && (
                    <Text
                      type="subBody"
                      className={clsx(
                        'max-w-[600px] overflow-hidden text-Secondary-Color',
                        data?.disabled && '!text-neutral-400'
                      )}
                      elementType="div"
                      ellipsis
                    >
                      {data.description}
                    </Text>
                  )}
                </div>
                <div className="flex min-w-[100px] items-center">
                  <Text
                    variant="medium"
                    className={clsx(
                      'flex max-w-[100px] text-Primary-Color',
                      data?.disabled && '!text-neutral-400'
                    )}
                  >
                    {dataTypeContent(data)}
                  </Text>
                </div>
                <div className="flex w-full min-w-[240px] items-center">
                  {type === DefaultValueType.InputParameters ? (
                    <InputByType
                      disabled={data?.disabled}
                      type={data?.type ?? EParamType.str}
                      value={data.value}
                      placeholder="Enter default value for parameter"
                      onChange={(value) => onChange?.(data, value)}
                      onBlur={() => {
                        if (data?.type === EParamType.str) {
                          onChange?.(data, (data.value as string)?.trim() ?? '')
                        }
                      }}
                    />
                  ) : (
                    <EnvInput
                      data={data}
                      disabled={data?.disabled}
                      maxLength={3000}
                      isFullWidth
                      value={(data.value as string) || ''}
                      onChange={(e) => onChange?.(data, e.target.value)}
                      onBlur={() =>
                        onChange?.(data, (data.value as string)?.trim() ?? '')
                      }
                      placeholder="Enter default value for variable"
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

interface DefaultValueTabProps {
  parameters?: ToolDefaultParameter[]
  environments?: ToolDefaultEnv[]
  onChangeDefaultParam?: (
    data: ToolDefaultParameter,
    value: string | boolean
  ) => void
  onChangeDefaultEnv?: (data: ToolDefaultEnv, value: string) => void
}
const DefaultValueTab = ({
  parameters,
  environments,
  onChangeDefaultParam,
  onChangeDefaultEnv,
}: DefaultValueTabProps) => {
  if (!parameters?.length && !environments?.length) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <EmptyData type="01" content="No added parameters" />
      </div>
    )
  }

  return (
    <div className="flex w-full flex-col items-start gap-3">
      {!!size(parameters) && (
        <DefaultValueTabConfiguration
          type={DefaultValueType.InputParameters}
          body={parameters as unknown as DefaultValueTypeBody[]}
          onChange={onChangeDefaultParam}
        />
      )}

      {!!size(environments) && (
        <DefaultValueTabConfiguration
          type={DefaultValueType.EnvironmentVariables}
          body={environments as unknown as DefaultValueTypeBody[]}
          onChange={onChangeDefaultEnv}
        />
      )}
    </div>
  )
}

export default DefaultValueTab
