import { ToolParametersPublic } from '@/apis/client'
import { EParamType } from './InputByType'

export enum DefaultValueType {
  InputParameters = 'inputParameters',
  EnvironmentVariables = 'environmentVariables',
}

export const DefaultValueTypeTitle = {
  [DefaultValueType.InputParameters]: 'Input Parameters',
  [DefaultValueType.EnvironmentVariables]: 'Environment variables',
}

export const DefaultValueTypeHeader = {
  [DefaultValueType.InputParameters]: [
    'Parameter name',
    'Data type',
    'Default value',
  ],
  [DefaultValueType.EnvironmentVariables]: [
    'Variable name',
    'Variable type',
    'Default value',
  ],
}

export type DefaultValueTypeBody = {
  name: string
  description?: string
  type?: EParamType
  value: string | boolean
  is_secret?: boolean
  disabled?: boolean
}

export enum ParamTypes {
  str = 'string',
  int = 'integer',
  float = 'float',
  bool = 'boolean',
}

export type DefaultValueData = {
  toolId?: string
  defaultValue?: ToolParametersPublic | null
}
