/* eslint-disable react/jsx-key */
import {
  Body_tools_create_tool_api,
  Body_tools_update_tool_api,
  MyToolPublic,
  ToolDefaultEnv,
  ToolDefaultParameter,
  ToolEnvironmentVariable,
  ToolInputParameter,
  ToolParametersPublic,
  ToolPublic,
  toolsCreateToolApi,
  toolsCreateToolDefaultParamApi,
  toolsGetToolParametersApi,
  toolsPostTestToolOutputApi,
  toolsUpdateToolApi,
  toolsUpdateToolDefaultParamApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import Message from '@/components/Message'
import Spin from '@/components/Spin'
import Tabs from '@/components/Tabs'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import TextEditor from '@/components/TextEditor'
import Tooltip from '@/components/Tooltip'
import Upload from '@/components/Upload'
import { HTTP_STATUS_CODE, TABS } from '@/constants'
import { TOOL_TYPE } from '@/pages/Tools/const'
import { EParamType } from '@/pages/WorkflowDetail/components/ModalGeneralToolParameters/InputByType'
import { colors } from '@/theme'
import { Editor } from '@monaco-editor/react'
import clsx from 'clsx'
import { isEmpty, isNil } from 'lodash'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import EnvironmentVariableItem from '../EnvironmentVariableItem'
import DefaultValueTab from './components/DefaultValueTab'
import { DefaultValueData } from './consts'
import { Empty } from './Empty'
import { IconRun } from './IconRun'
import './index.scss'
import InputByType from './InputByType'

const VARIABLE_NAME_REGEX = /^[A-Z_]+$/
const TOOL_CONFIGURATION_TABS = [
  {
    label: TABS.GENERAL,
    value: TABS.GENERAL,
    className: 'py-1 px-2',
  },
  {
    label: TABS.ADVANCED,
    value: TABS.ADVANCED,
    className: 'py-1 px-2',
  },
  {
    label: TABS.DEFAULT_VALUE,
    value: TABS.DEFAULT_VALUE,
    className: 'py-1 px-2',
  },
]

interface EnvironmentVariableItemData {
  name: string
  description: string
  isSecret: boolean
  is_secret?: boolean
}

const initVariable: EnvironmentVariableItemData = {
  name: '',
  description: '',
  isSecret: true,
}

const DEFAULT_LENGTH_OF_VARIABLES = 3

interface IToolEnvironmentVariable extends ToolEnvironmentVariable {
  feIsSecret: boolean
}

interface IProps {
  isOpen?: boolean
  data?: MyToolPublic
  onComplete?: (tool?: ToolPublic) => void
  refetchData?: () => void
  onClose: () => void
  setData?: (data: ToolPublic | undefined) => void
}

const ModalUpdateTools = ({
  isOpen = false,
  data,
  onComplete,
  refetchData,
  onClose,
  setData,
}: IProps) => {
  const [isLoading, setLoading] = useState(false)
  const [isLoadingParams, setLoadingParams] = useState(true)
  const [isLoadingRunTest, setLoadingRunTest] = useState(false)
  const [activeTab, setActiveTab] = useState<TABS>(TABS.GENERAL)

  const [name, setName] = useState<string>()
  const [description, setDescription] = useState<string>()
  const [instruction, setInstruction] = useState<string>()
  const [logo_file, setLogoFile] = useState<File>()
  const [logo, setLogo] = useState<string>()
  const [content_execute, setContentExecute] = useState<string>()

  const [params, setParams] = useState<ToolInputParameter[]>([])
  const [envs, setEnvs] = useState<IToolEnvironmentVariable[]>([])
  const [defaultParams, setDefaultParams] = useState<ToolDefaultParameter[]>([])
  const [defaultEnvs, setDefaultEnvs] = useState<ToolDefaultEnv[]>([])
  const [isDefaultValue, setIsDefaultValue] = useState(false)

  const [openTest, setOpenTest] = useState(false)
  const [result, setResult] = useState<string>()

  const scroll = useRef<HTMLDivElement>(null)

  const [variables, setVariables] = useState<
    Array<EnvironmentVariableItemData>
  >([])

  const [errorVariables, setErrorVariables] = useState<Array<boolean>>(
    Array.from({ length: DEFAULT_LENGTH_OF_VARIABLES }, () => false)
  )

  const [isDirtyModal, setDirtyModal] = useState<boolean>(false)

  const isBuildInTool = useMemo(() => data?.type === TOOL_TYPE.BUILT_IN, [data])

  const resetModalState = useCallback(() => {
    setName(undefined)
    setDescription(undefined)
    setInstruction(undefined)
    setLogoFile(undefined)
    setLogo(undefined)
    setContentExecute(undefined)
    setVariables([])
    setErrorVariables([])
    setDirtyModal(false)
  }, [])

  useEffect(() => {
    return () => resetModalState()
  }, [])

  useEffect(() => {
    if (!isEmpty(data)) {
      if (data?.name) setName(data.name)
      if (data?.description) setDescription(data.description)
      if (data?.instruction) setInstruction(data.instruction)
      if (data?.logo) setLogo(data.logo)
      if (data?.content_execute) setContentExecute(data.content_execute)
      if (!isEmpty(data.secrets)) {
        const secrets = (
          data.secrets as unknown as Array<EnvironmentVariableItemData>
        ).map((secret) => ({
          name: secret.name,
          description: secret.description,
          isSecret: !!secret?.is_secret,
        })) as Array<EnvironmentVariableItemData>
        setVariables(secrets)
        setErrorVariables(
          secrets.map(
            (_, index) => !VARIABLE_NAME_REGEX.test(secrets[index].name)
          )
        )
      }
    }
  }, [data])

  const fetchInfo = useCallback(
    async ({ isSaveDefaultVale = false, toolId = data?.id }: any) => {
      if (!toolId || isBuildInTool) return

      try {
        setLoadingParams(true)
        const res = await toolsGetToolParametersApi({
          path: {
            tool_id: toolId,
          },
        })
        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          const params = res.data?.data?.input_parameters || []

          setParams(params)

          const envs = res.data?.data?.environment_variables || []

          setEnvs(envs)

          if (isSaveDefaultVale) {
            const defaultParameters = res.data?.data?.default_parameters
            if (defaultParameters?.length) {
              setDefaultParams(
                defaultParameters.map((defaultParam) => {
                  const mappingParam = params.find(
                    (param) =>
                      param.name === defaultParam.name &&
                      param.description === defaultParam.description
                  )

                  if (mappingParam) {
                    return defaultParam
                  }

                  return { ...defaultParam, disabled: true }
                })
              )
            } else {
              setDefaultParams(
                params.map((param) => ({
                  name: param.name,
                  description: param.description ?? '',
                  type: param.type,
                  value: param.default_value,
                }))
              )
            }

            const defaultVariables = res.data?.data?.default_envs
            if (defaultVariables?.length) {
              setDefaultEnvs(
                defaultVariables.map((defaultEnv) => {
                  const mappingEnv = envs.find(
                    (env) =>
                      env.name === defaultEnv.name &&
                      env.description === defaultEnv.description
                  )

                  if (mappingEnv) {
                    return defaultEnv
                  }

                  return { ...defaultEnv, disabled: true }
                })
              )
            } else {
              setDefaultEnvs(
                envs.map((env) => ({
                  name: env.name,
                  description: env.description,
                  is_secret: env.is_secret,
                  value: env.default_value,
                }))
              )
            }

            if (!isNil(defaultParameters) || !isNil(defaultVariables)) {
              setIsDefaultValue(true)
            }
          }

          return res.data?.data
        } else {
          setParams([])
          setEnvs([])

          return null
        }
      } catch (error) {
        console.log(error)
        setParams([])
        setEnvs([])

        return null
      } finally {
        setLoadingParams(false)
      }
    },
    [isBuildInTool, data]
  )

  const isValidForm = useMemo(() => {
    return name?.trim() && content_execute?.trim() && description?.trim()
  }, [name, content_execute, description])

  const covertVariables = useCallback(
    (value: Array<EnvironmentVariableItemData>) =>
      value?.map((variable) => ({
        name: variable.name,
        description: variable.description,
        is_secret: variable.isSecret,
      })),
    []
  )

  const getValidVariable = useCallback(
    () =>
      variables.filter(
        (variable, index) =>
          !(errorVariables[index] || !VARIABLE_NAME_REGEX.test(variable.name))
      ),
    [variables, errorVariables]
  )

  const handleBlurVariableName = useCallback(
    (name: string, indexOfVariable: number) => {
      setErrorVariables((prop) =>
        prop.map((item, index) => {
          if (indexOfVariable === index) {
            return name ? !VARIABLE_NAME_REGEX.test(name) : false
          }
          return item
        })
      )
    },
    []
  )

  const handleChangeVariableName = useCallback(
    (variable: EnvironmentVariableItemData, value: string) => {
      setVariables((prop) => {
        const targetVariable = prop?.find((item) => item === variable)
        if (targetVariable) {
          targetVariable.name = value
        }

        return [...prop]
      })

      if (!isDirtyModal) setDirtyModal(true)
    },
    [isDirtyModal]
  )

  const handleChangeVariableDescription = useCallback(
    (variable: EnvironmentVariableItemData, value: string) => {
      setVariables((prop) => {
        const targetVariable = prop?.find((item) => item === variable)
        if (targetVariable) {
          targetVariable.description = value
        }

        return [...prop]
      })

      if (!isDirtyModal) setDirtyModal(true)
    },
    [isDirtyModal]
  )

  const handleChangeVariableIsSecret = useCallback(
    (variable: EnvironmentVariableItemData, value: boolean) => {
      setVariables((prop) => {
        const targetVariable = prop?.find((item) => item === variable)
        if (targetVariable) {
          targetVariable.isSecret = value
        }

        return [...prop]
      })

      setErrorVariables((prop) => [...prop, false])

      if (!isDirtyModal) setDirtyModal(true)
    },
    [isDirtyModal]
  )

  const handleDeleteVariable = useCallback(
    (variable: EnvironmentVariableItemData, indexOfVariable: number) => {
      setVariables((prop) => {
        return [...prop.filter((item) => item !== variable)]
      })

      setErrorVariables((prop) => {
        return prop.filter((_, index) => index !== indexOfVariable)
      })

      if (!isDirtyModal) setDirtyModal(true)
    },
    [isDirtyModal]
  )

  const handleCreateDefaultValue = useCallback(
    async ({ toolId = data?.id, defaultValue }: DefaultValueData) => {
      try {
        if (!toolId) {
          return
        }

        const paramData = defaultValue?.input_parameters?.map((param) => {
          const findParam = defaultParams.find(
            (defaultParam) => defaultParam.name === param.name
          )

          return {
            name: param.name,
            description: param.description ?? '',
            type: param.type,
            value: findParam?.value ?? null,
          }
        })

        const envData = defaultValue?.environment_variables?.map((env) => {
          const findEnv = defaultEnvs.find(
            (defaultEnv) => defaultEnv.name === env.name
          )

          return {
            name: env.name,
            description: env.description ?? '',
            is_secret: env.is_secret,
            value: findEnv?.value ?? null,
          }
        })

        const res = await toolsCreateToolDefaultParamApi({
          path: { tool_id: toolId },
          body: {
            default_parameters: paramData ?? [],
            default_envs: envData ?? [],
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          await fetchInfo({ isSaveDefaultVale: true, toolId })
        } else {
          Message.error({
            message: res.error?.detail ?? 'Something wrong, please retry!',
          })
        }
      } catch (error: any) {
        Message.error({ message: 'Something wrong, please retry!' })
      }
    },
    [defaultParams, defaultEnvs, fetchInfo]
  )

  const handleUpdateDefaultValue = useCallback(
    async ({ defaultValue }: DefaultValueData) => {
      try {
        if (!data?.id) {
          return
        }

        const paramData = defaultValue?.input_parameters ?? []
        const envData = defaultValue?.environment_variables ?? []

        const updatedParamData = paramData
          .filter(
            (param) =>
              !defaultParams.some(
                (defaultParam) => defaultParam.name === param.name
              )
          )
          .map((param) => ({
            name: param.name,
            description: param.description ?? '',
            type: param.type,
            value: param.default_value,
          }))

        const updatedEnvData = envData
          .filter(
            (env) =>
              !defaultEnvs.some((defaultEnv) => defaultEnv.name === env.name)
          )
          .map((env) => ({
            name: env.name,
            description: env.description,
            is_secret: env.is_secret,
            value: env.default_value,
          }))

        const default_parameters = [
          ...defaultParams.map((defaultParam) => {
            const currentParamData = paramData.find(
              (param) => defaultParam.name === param.name
            )

            return {
              name: defaultParam.name,
              description:
                currentParamData?.description ?? defaultParam?.description,
              type: currentParamData?.type ?? defaultParam.type,
              value:
                currentParamData?.type !== defaultParam.type
                  ? null
                  : defaultParam.value,
            }
          }),
          ...updatedParamData,
        ]

        const default_envs = [
          ...defaultEnvs.map((defaultEnv) => {
            const currentEnvData = envData.find(
              (env) => defaultEnv.name === env.name
            )

            return {
              name: defaultEnv.name,
              description:
                currentEnvData?.description ?? defaultEnv.description,
              is_secret: currentEnvData?.is_secret ?? defaultEnv.is_secret,
              value: defaultEnv.value,
            }
          }),
          ...updatedEnvData,
        ]

        const res = await toolsUpdateToolDefaultParamApi({
          path: { tool_id: data?.id },
          body: {
            default_parameters,
            default_envs,
          },
        })

        if (res.status === HTTP_STATUS_CODE.SUCCESS) {
          await fetchInfo({ isSaveDefaultVale: true })
        } else {
          Message.error({
            message: res.error?.detail ?? 'Something wrong, please retry!',
          })
        }
      } catch (error: any) {
        Message.error({ message: 'Something wrong, please retry!' })
      }
    },
    [data, defaultParams, defaultEnvs, fetchInfo]
  )

  const handleAddVariable = useCallback(() => {
    setVariables((prop) => [...prop, { ...initVariable }])
    setErrorVariables((prop) => [...prop, false])

    if (!isDirtyModal) setDirtyModal(true)
  }, [isDirtyModal])

  const handleCreateTool = async () => {
    try {
      if (isLoading) return

      const body: Body_tools_create_tool_api = {
        name: name?.trim() || '',
        description: description || '',
        instruction: instruction?.trim() || '',
        logo_file,
        content_execute: content_execute || '',
        secrets_string: '{}',
      }

      const validVariables = getValidVariable()

      if (validVariables?.length) {
        body.secrets_string = JSON.stringify(covertVariables(validVariables))
        setVariables(validVariables)
        setErrorVariables(
          Array.from({ length: validVariables?.length }, () => false)
        )
      } else {
        setVariables([])
        setErrorVariables([])
      }

      setLoading(true)

      const res = await toolsCreateToolApi({
        body,
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully created tool' })
        onComplete?.(res.data?.data)
        setData?.(res.data?.data)
        if (isDirtyModal) {
          setDirtyModal(false)
        }
        const defaultValue = await fetchInfo({
          isSaveDefaultVale: false,
          toolId: res.data?.data?.id,
        })

        // return tool data
        return { tool: res.data?.data, defaultValue }
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error: any) {
      Message.error({ message: 'Something wrong, please retry!' })
      return null
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateTool = async () => {
    try {
      if (isLoading || !data?.id) return

      const body: Body_tools_update_tool_api = {
        name: name || '',
        description: description || '',
        instruction: instruction?.trim() || '',
        logo_file,
        content_execute: content_execute || '',
        logo: logo_file ? '' : (logo ?? ''), // If user upload new logo => set logo to empty string
        secrets_string: '{}',
      }

      const validVariables = getValidVariable()

      if (validVariables?.length) {
        body.secrets_string = JSON.stringify(covertVariables(validVariables))
        setVariables(validVariables)
        setErrorVariables(
          Array.from({ length: validVariables?.length }, () => false)
        )
      } else {
        setVariables([])
        setErrorVariables([])
      }

      setLoading(true)

      const res = await toolsUpdateToolApi({
        path: {
          tool_id: data.id,
        },
        body,
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({ message: 'Successfully updated tool' })
        if (openTest) {
          setOpenTest(false)
        }
        if (isDirtyModal) setDirtyModal(false)

        const defaultValue = await fetchInfo({ isSaveDefaultVale: false })
        return defaultValue
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })

        return null
      }
    } catch (error: any) {
      Message.error({ message: 'Something wrong, please retry!' })

      return null
    } finally {
      setLoading(false)
    }
  }

  const handleSaveDefaultValue = useCallback(
    async ({ toolId = data?.id, defaultValue }: DefaultValueData) => {
      if (!toolId || isBuildInTool) {
        return
      }

      if (!isDefaultValue) {
        await handleCreateDefaultValue({
          toolId,
          defaultValue,
        })
        return
      }
      await handleUpdateDefaultValue({
        defaultValue,
      })
    },
    [
      isBuildInTool,
      isDefaultValue,
      handleCreateDefaultValue,
      handleUpdateDefaultValue,
    ]
  )

  const handleSave = useCallback(async () => {
    if (data?.id) {
      const defaultValue: ToolParametersPublic | null | undefined =
        await handleUpdateTool()
      await handleSaveDefaultValue({ defaultValue })
      return
    }
    const toolData = await handleCreateTool()
    await handleSaveDefaultValue({
      toolId: toolData?.tool?.id,
      defaultValue: toolData?.defaultValue,
    })
  }, [
    variables,
    data,
    handleCreateTool,
    handleUpdateTool,
    handleSaveDefaultValue,
  ])

  const onChangeName = (value: any) => {
    setName(value)
    if (!isDirtyModal) setDirtyModal(true)
  }

  const onChangeDescription = (value: any) => {
    setDescription(value)

    if (!isDirtyModal && description !== value && value) setDirtyModal(true)
  }

  const onChangeInstruction = (text: string) => {
    setInstruction(text)

    // if (!isDirtyModal) setDirtyModal(true)
  }

  const handleEditorChange = (value: any) => {
    setContentExecute(value)
    if (!isDirtyModal) setDirtyModal(true)
  }

  const handleCloseModal = useCallback(() => {
    if (isDirtyModal) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          refetchData?.()
          onClose()
        },
      })
    } else {
      refetchData?.()

      onClose()
    }
  }, [isDirtyModal])

  const handleChangeDefaultParam = useCallback(
    (data: ToolDefaultParameter, value: string | boolean) => {
      setDefaultParams((prev) =>
        prev.map((param) => {
          if (param.name === data.name && param.type === data.type) {
            if (param.value !== value && value) {
              setDirtyModal(true)
            }
            param.value = value
          }
          return param
        })
      )
    },
    []
  )

  const handleChangeDefaultEnv = useCallback(
    (data: ToolDefaultEnv, value: string) => {
      setDefaultEnvs((prev) =>
        prev.map((env) => {
          if (env.name === data.name && env.is_secret === data.is_secret) {
            if (env.value !== value && value) {
              setDirtyModal(true)
            }
            env.value = value
          }
          return env
        })
      )
    },
    []
  )

  const runTest = useCallback(async () => {
    if (!data?.id) return

    try {
      setLoadingRunTest(true)

      const transformedParams = Object.fromEntries(
        params.map(({ name, default_value }) => [name, default_value])
      )
      const transformedEnvs = Object.fromEntries(
        envs.map(({ name, default_value }) => [name, default_value])
      )
      const res = await toolsPostTestToolOutputApi({
        path: {
          tool_id: data?.id,
        },
        body: {
          input_parameters: transformedParams,
          environment_variables: transformedEnvs,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setResult(JSON.stringify(res.data?.data) || '')
      }
    } catch (error) {
      console.log(error)
    } finally {
      setLoadingRunTest(false)
    }
  }, [envs, params])

  useEffect(() => {
    if (isOpen) {
      fetchInfo({ isSaveDefaultVale: true })
    }
  }, [isOpen])

  useEffect(() => {
    if (openTest) {
      fetchInfo({ isSaveDefaultVale: false })
    } else {
      setParams([])
      setEnvs([])
      setLoadingParams(true)
    }
  }, [openTest])

  useEffect(() => {
    if (scroll.current) {
      scroll.current.scrollTop = scroll.current.scrollHeight
    }
  }, [variables.length])

  const renderGeneralContent = useCallback(() => {
    return (
      <div className="flex h-full w-full flex-col gap-3 p-4">
        <div className="flex shrink-0 items-end gap-3 px-2">
          <Upload
            onChangeFile={(file: File | null) => {
              if (file) setLogoFile(file)
              else setLogoFile(undefined)
            }}
            image={logo}
            type="mini"
            onChange={(image) => {
              setLogo(image)
              setDirtyModal(true)
            }}
            dataTypes="image/png, image/jpeg"
          />
          <Input
            label="Tool name"
            placeholder="Type in tool name"
            isFullWidth
            value={name}
            onChange={(e) => onChangeName(e.target.value)}
            onBlur={() => setName(name?.trim())}
            maxLength={100}
          />
        </div>
        <TextArea
          label="Description"
          placeholder="Type in description for this tool"
          className="h-[100px]"
          value={description}
          onChange={onChangeDescription}
          onBlur={() => onChangeDescription(description?.trim())}
        />
        <TextEditor
          className="tool-configuration-editor"
          onChange={onChangeInstruction}
          value={instruction!}
          label="Instruction"
          placeholder="Provide instructions on how to use this tool, including images for visualization is highly recommended."
        />
      </div>
    )
  }, [name, description, instruction, logo])

  const renderAdvancedContent = useCallback(() => {
    return (
      <div className="genai-scrollbar relative flex h-full w-full items-center overflow-hidden">
        {openTest && data?.id && (
          <div
            style={{
              right: 'calc(50% - 16px)',
            }}
            onClick={() => setOpenTest(!openTest)}
            className="absolute top-[7px] z-50 flex cursor-pointer gap-[2px] rounded-full bg-Base-Neutral-02 p-[6px] shadow-md hover:bg-Hover-2"
          >
            <Icon
              name="arrow-left-01"
              size={20}
              color={colors['Tertiary-Color']}
              className="rotate-180"
            />
          </div>
        )}

        <div
          ref={scroll}
          className={clsx(
            'genai-scrollbar relative flex h-full w-full flex-col gap-[12px] overflow-y-auto overflow-x-hidden py-4 pl-[16px]',
            openTest ? 'pr-[8px]' : 'pr-[16px]'
          )}
        >
          {!openTest && data?.id && (
            <div
              onClick={() => setOpenTest(!openTest)}
              className="absolute right-[-2px] top-[7px] flex cursor-pointer gap-[4px] rounded-l-full bg-Base-Neutral-02 px-[8px] py-[4px] shadow-base hover:bg-Hover-2"
            >
              <Icon
                name="arrow-left-01"
                size={20}
                color={colors['Tertiary-Color']}
              />
              <Text
                variant="medium"
                type="subBody"
                className="text-Tertiary-Color"
              >
                Test
              </Text>
            </div>
          )}

          <div className="flex h-fit w-full flex-col gap-[4px]">
            <div className="flex items-center gap-1 px-1">
              <Text
                variant="medium"
                type="subBody"
                className="pl-1 text-Tertiary-Color"
              >
                Function
              </Text>
              <Tooltip text="This support workers to automate repetitive tasks, making them more efficient.">
                <div>
                  <Icon
                    name="vuesax-bold-info-circle"
                    size={12}
                    color={colors['border-base-icon']}
                  />
                </div>
              </Tooltip>
            </div>

            <Editor
              className="h-[451px] max-h-[451px] rounded-lg border border-border-base-icon px-3 py-2"
              defaultLanguage="python"
              defaultValue={
                content_execute ||
                '# Adding function for this tool by writing code'
              }
              onChange={handleEditorChange}
            />
          </div>

          <div className="flex w-full flex-col gap-[8px]">
            {!!variables.length && (
              <div className="flex flex-wrap items-center gap-1">
                <Text
                  variant="medium"
                  type="subBody"
                  className="pl-1 text-Tertiary-Color"
                >
                  Environment variables
                </Text>
                {errorVariables?.includes(true) && (
                  <div className="flex items-center gap-1">
                    <Icon
                      name="vuesax-bold-info-circle"
                      size={12}
                      color={colors['Error-Color']}
                    />
                    <Text
                      variant="medium"
                      type="supportText"
                      className="flex text-Error-Color"
                    >
                      Spaces, numbers, special characters and punctuation are
                      not allowed
                    </Text>
                  </div>
                )}
              </div>
            )}

            {variables?.map((variable, index) => {
              const { name, description, isSecret } = variable
              return (
                <EnvironmentVariableItem
                  name={name}
                  description={description}
                  isSecret={isSecret}
                  isErrorName={errorVariables[index]}
                  onBlurName={() => {
                    handleChangeVariableName(variable, name.trim())
                    handleBlurVariableName(name.trim(), index)
                  }}
                  onChangeName={(value) =>
                    handleChangeVariableName(variable, value)
                  }
                  onBlurDescription={() =>
                    handleChangeVariableDescription(
                      variable,
                      description.trim()
                    )
                  }
                  onChangeDescription={(value) =>
                    handleChangeVariableDescription(variable, value)
                  }
                  onChangeIsSecret={(value) =>
                    handleChangeVariableIsSecret(variable, value)
                  }
                  onDelete={() => handleDeleteVariable(variable, index)}
                />
              )
            })}

            <div
              onClick={handleAddVariable}
              className="flex w-full cursor-pointer items-center justify-center rounded-[70px] border-[2px] border-[#F1EEEE] bg-Base-Button hover:bg-Hover-2"
            >
              <Icon name="plus" size={16} color={colors['Tertiary-Color']} />
              <Text
                variant="medium"
                type="subBody"
                className="pl-1 text-Tertiary-Color"
              >
                Environment variables
              </Text>
            </div>
          </div>
        </div>
        {openTest && (
          <div className="flex h-full w-full flex-col gap-[8px] overflow-hidden bg-[#F3F3F3] p-[12px]">
            {result ? (
              <div className="flex h-full w-full flex-col items-end gap-[8px] overflow-hidden">
                <div
                  onClick={() => setResult('')}
                  className="flex cursor-pointer items-center justify-center gap-[4px] rounded-[4px] px-[6px] hover:bg-neutral-200"
                >
                  <Icon
                    name="vuesax-outline-arrow-left"
                    size={16}
                    color={colors['Tertiary-Color']}
                  />
                  <Text
                    type="subBody"
                    variant="semibold"
                    className="text-Tertiary-Color"
                  >
                    Back
                  </Text>
                </div>
                <div className="genai-scrollbar h-full w-full overflow-y-scroll rounded-[12px] border-[1px] border-border-base-icon bg-white p-[12px]">
                  <Text
                    type="subBody"
                    variant="regular"
                    className="text-Primary-Color"
                  >
                    {result}
                  </Text>
                </div>
              </div>
            ) : (
              <div className="genai-scrollbar flex h-full w-full flex-col gap-[8px] overflow-y-scroll rounded-[12px] border-[1px] border-border-base-icon bg-white p-[12px]">
                {!params.length && !envs.length && !isLoadingParams && (
                  <Empty />
                )}

                {isLoadingParams && (
                  <div className="flex h-full w-full items-center justify-center">
                    <Spin />
                  </div>
                )}
                {!!params?.length && (
                  <div className="flex w-full flex-col gap-[8px]">
                    <div className="flex items-center gap-[8px]">
                      <Text
                        type="subBody"
                        variant="medium"
                        className="min-w-[300px] text-Secondary-Color"
                        elementType="div"
                      >
                        Parameter name
                      </Text>
                      <Text
                        type="subBody"
                        variant="medium"
                        className="w-full text-Secondary-Color"
                        elementType="div"
                      >
                        Test value
                      </Text>
                    </div>

                    <div className="flex w-full flex-col gap-2">
                      {params.map((param, index) => (
                        <div
                          key={index}
                          className="flex w-full items-center gap-[8px]"
                        >
                          <div className="flex w-[300px] min-w-[300px] flex-col">
                            <Text
                              type="body"
                              variant="medium"
                              value={param.name}
                              className="!w-fit max-w-full overflow-hidden text-Primary-Color"
                              elementType="div"
                              ellipsis
                            />
                            {param.description && (
                              <Text
                                type="subBody"
                                variant="regular"
                                value={param.description}
                                className="!w-fit max-w-full overflow-hidden text-Secondary-Color"
                                elementType="div"
                                ellipsis
                              />
                            )}
                          </div>
                          <InputByType
                            value={param.default_value ?? ''}
                            type={param.type as EParamType}
                            onChange={(val) => {
                              const newParams = [...params]
                              newParams[index].default_value = val as string
                              setParams(newParams)
                            }}
                            onBlur={() => {
                              const newParams = [...params]
                              newParams[index].default_value =
                                param.default_value?.trim()
                              setParams(newParams)
                            }}
                            placeholder="Enter default value for parameter"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {!!params.length && !!envs.length && (
                  <div className="min-h-[1px] w-full bg-border-base-icon"></div>
                )}
                {!!envs?.length && (
                  <div className="flex w-full flex-col gap-[8px]">
                    <div className="flex items-center gap-[8px]">
                      <Text
                        type="subBody"
                        variant="medium"
                        className="min-w-[300px] text-Secondary-Color"
                        elementType="div"
                      >
                        Variable name
                      </Text>
                      <Text
                        type="subBody"
                        variant="medium"
                        className="w-full text-Secondary-Color"
                        elementType="div"
                      >
                        Test value
                      </Text>
                    </div>

                    <div className="flex w-full flex-col gap-2">
                      {envs.map((val, index) => (
                        <div
                          key={index}
                          className="flex w-full items-center gap-[8px]"
                        >
                          <div className="flex w-[300px] min-w-[300px] flex-col">
                            <Text
                              type="body"
                              variant="medium"
                              value={val.name}
                              className="!w-fit max-w-full overflow-hidden text-Primary-Color"
                              elementType="div"
                              ellipsis
                            />

                            {val.description && (
                              <Text
                                type="subBody"
                                variant="regular"
                                value={val.description}
                                className="!w-fit max-w-full overflow-hidden text-Secondary-Color"
                                elementType="div"
                                ellipsis
                              />
                            )}
                          </div>
                          <Input
                            maxLength={3000}
                            isFullWidth
                            value={val.default_value || ''}
                            onChange={(e) => {
                              const newEnvs = [...envs]
                              newEnvs[index].default_value = e.target.value
                              setEnvs(newEnvs)
                            }}
                            onBlur={() => {
                              const newEnvs = [...envs]
                              newEnvs[index].default_value =
                                val.default_value?.trim()
                              setEnvs(newEnvs)
                            }}
                            placeholder="Enter default value for variable"
                            suffix={
                              val.is_secret && (
                                <IconButton
                                  nameIcon={
                                    val.feIsSecret
                                      ? 'vuesax-outline-eye'
                                      : 'vuesax-outline-eye-slash'
                                  }
                                  colorIcon={colors['neutral']['300']}
                                  hoverColor={colors['Primary-Color']}
                                  sizeIcon={16}
                                  className="select-none"
                                  onClick={() => {
                                    const newEnvs = [...envs]
                                    newEnvs[index].feIsSecret = !val.feIsSecret
                                    setEnvs(newEnvs)
                                  }}
                                />
                              )
                            }
                            secure={!val.feIsSecret && val.is_secret}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <Button
              onClick={() => runTest()}
              type="secondary"
              size="small"
              leftIcon={<IconRun />}
              loading={isLoadingRunTest}
            >
              {!result ? 'Run' : 'Retry'}
            </Button>
          </div>
        )}
      </div>
    )
  }, [
    content_execute,
    variables,
    errorVariables,
    openTest,
    params,
    envs,
    result,
    isLoadingRunTest,
    scroll,
    data?.id,
  ])

  const renderContent = useCallback(() => {
    let content = renderGeneralContent()

    if (activeTab === TABS.ADVANCED) {
      content = renderAdvancedContent()
    }
    if (activeTab === TABS.DEFAULT_VALUE) {
      content = (
        <DefaultValueTab
          parameters={defaultParams}
          environments={defaultEnvs}
          onChangeDefaultParam={handleChangeDefaultParam}
          onChangeDefaultEnv={handleChangeDefaultEnv}
        />
      )
    }

    return (
      <div
        className={clsx(
          'genai-scrollbar transition-[width,height] duration-500',
          activeTab === TABS.GENERAL &&
            'relative h-[535px] min-h-[535px] w-[947px] overflow-hidden',
          activeTab === TABS.ADVANCED &&
            'h-[535px] w-[947px] overflow-hidden p-4',
          activeTab === TABS.DEFAULT_VALUE &&
            'h-[710px] w-[1284px] overflow-auto p-4'
        )}
      >
        {content}
      </div>
    )
  }, [activeTab, renderGeneralContent, renderAdvancedContent])

  return (
    <BaseModal isOpen={isOpen} onClose={handleCloseModal} isPureModal>
      <div
        className={clsx(
          'relative min-w-[947px] overflow-hidden rounded-[20px] bg-white shadow-md',
          activeTab === TABS.ADVANCED && openTest && 'w-[1300px] min-w-[1300px]'
        )}
      >
        <IconButton
          className="absolute right-[10px] top-[10px] cursor-pointer duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={handleCloseModal}
        />
        <div className="flex flex-col gap-1 bg-Base-03 px-3 pb-4 pt-3">
          <Text
            value="Tool Configuration"
            type="subheading"
            variant="medium"
            className="text-Primary-Color"
          />
          <Text
            value="Configure tool to support your worker perform task"
            type="subBody"
            className="text-Secondary-Color"
          />
        </div>
        <Tabs
          tabs={TOOL_CONFIGURATION_TABS}
          value={activeTab}
          onChange={setActiveTab}
        />

        {renderContent()}
        <div className="flex items-center justify-end gap-3 bg-Base-03 px-5 pb-4 pt-3">
          <Button
            type="primary"
            onClick={handleSave}
            text="Save"
            loading={isLoading}
            disabled={!isValidForm || !isDirtyModal}
          />
        </div>
      </div>
    </BaseModal>
  )
}

export default memo(ModalUpdateTools)
