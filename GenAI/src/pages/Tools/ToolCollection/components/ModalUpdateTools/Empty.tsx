import Text from '@/components/Text'

export const Empty = () => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-[8px]">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="121"
        height="121"
        viewBox="0 0 121 121"
        fill="none"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          // eslint-disable-next-line max-len
          d="M104.646 54.0771L102.058 63.7364C99.0368 75.0118 97.5262 80.6495 94.1016 84.3059C91.3976 87.193 87.8981 89.2134 84.0458 90.1116C83.5645 90.2238 83.0758 90.3103 82.575 90.3721C77.9997 90.9364 72.4168 89.4405 62.2539 86.7173C50.9785 83.6961 45.3408 82.1855 41.6843 78.7609C38.7973 76.0568 36.7769 72.5574 35.8786 68.7051C34.741 63.8262 36.2517 58.1885 39.2729 46.9131L41.8611 37.2539C42.2955 35.6327 42.6989 34.127 43.081 32.7281C45.3564 24.3979 46.8855 19.8149 49.8176 16.6843C52.5216 13.7973 56.021 11.7769 59.8733 10.8786C64.7522 9.74105 70.3899 11.2517 81.6653 14.2729C92.9407 17.2941 98.5784 18.8048 102.235 22.2294C105.122 24.9334 107.142 28.4328 108.041 32.2851C109.178 37.164 107.667 42.8017 104.646 54.0771ZM55.7621 49.5294C56.2981 47.5289 58.3544 46.3417 60.3549 46.8778L84.503 53.3483C86.5035 53.8843 87.6907 55.9406 87.1546 57.941C86.6186 59.9415 84.5624 61.1287 82.5619 60.5927L58.4137 54.1222C56.4132 53.5862 55.226 51.5299 55.7621 49.5294ZM51.8778 64.0167C52.4138 62.0162 54.4701 60.8291 56.4706 61.3651L70.9595 65.2474C72.96 65.7834 74.1471 67.8397 73.6111 69.8402C73.0751 71.8407 71.0188 73.0278 69.0183 72.4918L54.5294 68.6095C52.5289 68.0735 51.3417 66.0172 51.8778 64.0167Z"
          fill="url(#paint0_linear_17481_7622)"
        />
        <path
          opacity="0.5"
          // eslint-disable-next-line max-len
          d="M82.5744 90.3723C81.5319 93.564 79.6991 96.4515 77.2348 98.7595C73.5784 102.184 67.9407 103.695 56.6653 106.716C45.3899 109.737 39.7522 111.248 34.8733 110.11C31.021 109.212 27.5216 107.192 24.8176 104.305C21.3929 100.648 19.8823 95.0104 16.8611 83.735L14.2729 74.0758C11.2517 62.8003 9.74105 57.1627 10.8786 52.2838C11.7769 48.4315 13.7973 44.9321 16.6843 42.228C20.3408 38.8034 25.9785 37.2928 37.2539 34.2716C39.387 33.7 41.3184 33.1825 43.081 32.7267C43.0809 32.7273 43.0812 32.7262 43.081 32.7267C42.6989 34.1257 42.2955 35.6328 41.8611 37.254L39.2729 46.9133C36.2517 58.1887 34.741 63.8264 35.8786 68.7053C36.7769 72.5576 38.7973 76.057 41.6843 78.761C45.3408 82.1856 50.9785 83.6963 62.2539 86.7175C72.4165 89.4406 77.9993 90.9364 82.5744 90.3723Z"
          fill="url(#paint1_linear_17481_7622)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_17481_7622"
            x1="108.419"
            y1="50.4951"
            x2="46.4257"
            y2="78.1462"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.2" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_17481_7622"
            x1="82.5744"
            y1="71.6077"
            x2="21.6404"
            y2="99.2411"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#642B73" stopOpacity="0.2" />
            <stop offset="1" stopColor="#C6426E" stopOpacity="0.2" />
          </linearGradient>
        </defs>
      </svg>

      <div className="flex flex-col items-center gap-[4px]">
        <Text type="body" variant="semibold" className="text-Primary-Color">
          Empty!
        </Text>
        <Text type="subBody" variant="regular" className="text-Secondary-Color">
          No added parameters and variables
        </Text>
      </div>
    </div>
  )
}
