import Input from '@/components/Input'
import Switch from '../Switch'
import { colors } from '@/theme'
import './index.scss'
import Icon from '@/assets/icon/Icon'

interface EnvironmentVariableItemProps {
  name?: string
  description?: string
  isSecret?: boolean
  isErrorName?: boolean
  onBlurName?: () => void
  onChangeName?: (value: string) => void
  onBlurDescription?: () => void
  onChangeDescription?: (value: string) => void
  onChangeIsSecret?: (value: boolean) => void
  onDelete?: () => void
}

const EnvironmentVariableItem = ({
  name,
  description,
  isSecret,
  isErrorName,
  onBlurName,
  onChangeName,
  onBlurDescription,
  onChangeDescription,
  onChangeIsSecret,
  onDelete,
}: EnvironmentVariableItemProps) => (
  <div className="variable-item flex w-full items-center gap-0.5">
    <div className="relative flex w-full items-center gap-2">
      <Input
        subTextClassName="absolute w-[300px]"
        classNameInputWrapper="w-[30%] min-w-[130px] relative"
        value={name}
        maxLength={255}
        placeholder="Variable"
        isError={isErrorName}
        onChange={(event) => onChangeName?.(event.target.value)}
        onBlur={onBlurName}
      />
      <Input
        classNameInputWrapper="min-w-[194px] w-[60%]"
        value={description}
        maxLength={255}
        placeholder="Description"
        onChange={(event) => onChangeDescription?.(event.target.value)}
        onBlur={onBlurDescription}
      />
      <Switch
        checked={isSecret}
        checkedChildren="Secret"
        unCheckedChildren="Public"
        onChange={onChangeIsSecret}
      />

      <div
        onClick={onDelete}
        className="cursor-pointer rounded-[6px] p-[4px] hover:bg-neutral-100"
      >
        <Icon name="trash-04" size={16} color={colors['Primary-Color']} />
      </div>
    </div>
  </div>
)

export default EnvironmentVariableItem
