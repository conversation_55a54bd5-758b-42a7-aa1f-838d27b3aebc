import {
  KBFilePublic,
  KBFilesPublic,
  KnowledgeBaseDirectoryPublicWithoutFileCount,
  knowledgeBaseDirectoryReadKnowledgeBaseDirectoryByIdApi,
  knowledgeBaseFileDeleteKbFileByIdApi,
  knowledgeBaseFileFetchMyKbFilesApi,
  knowledgeBaseFileUpdateEmbeddingKbFileByIdApi,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import { AccessDenied } from '@/components/ErrorPage'
import NotFound from '@/components/ErrorPage/NotFound'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import PageHeader from '@/components/PageHeader'
import Pagination from '@/components/Pagination'
import { PAGE_SIZE } from '@/constants'
import { getAccessTokenLocalStorage, getWsKbFileUrl } from '@/helpers'
import { rootUrls } from '@/routes/rootUrls'
import { produce } from 'immer'
import { size } from 'lodash'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import EmptyBaseDirectory from './components/EmptyBaseDirectory'
import Item from './components/Item'
import AddNewItem from './components/Item/AddNewItem'
import { ModalUpdateKnowledgeFile } from './components/ModalUpdateKnowledgeFile'
import {
  FileUpload,
  ModalUploadMultipleKnowledgeFile,
} from './components/ModalUploadMultipleKnowledgeFile'
import SkeletonLoadingPage from './components/SkeletonLoadingPage'
import {
  EEventKbFile,
  EKBFileEmbeddingStatus,
  EKBFileError,
  IEventResponse,
} from './consts'
import { useWs } from './hooks/use-Ws'
import SearchBar from '@/components/SearchBar'

const DEFAULT_PAGINATION = {
  total_count: 0,
  next_page: null,
  prev_page: null,
  total_page: 1,
}

const KnowledgeBaseDirectory = () => {
  const { id } = useParams()

  const [searchKey, setSearchKey] = useState('')
  const [isInitLoad, setIsInitLoad] = useState(true)
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<KBFilesPublic>()
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<{
    total_count: number
    total_page: number
    next_page: number | null
    prev_page: number | null
  }>(DEFAULT_PAGINATION)
  const [openModal, setOpenModal] = useState(false)

  const [idDetail, setIdDetail] = useState('')
  const [openModalEdit, setOpenModalEdit] = useState(false)
  const [messageQueue, setMessageQueue] = useState<IEventResponse[]>([])

  const [knowledgeBaseDirectoryDetail, setKnowledgeBaseDirectoryDetail] =
    useState<KnowledgeBaseDirectoryPublicWithoutFileCount>()
  const [kbFileError, setKbFileError] = useState<EKBFileError>()

  // because we need to wait for the data to be updated before we can process the next message
  const [isWaitingNewData, setIsWaitingNewData] = useState(false)

  useWs({
    url: getWsKbFileUrl(getAccessTokenLocalStorage() || ''),

    onMessage: (message) => {
      const dataRes = JSON.parse(message.data)

      if (dataRes && dataRes.event === EEventKbFile.SendEmbeddingProgress) {
        setMessageQueue((prev) => [...prev, dataRes])

        // Display message
        if (!dataRes.success) {
          Message.error({
            message: 'Something went wrong!',
          })
        } else {
          Message.success({
            message: 'Completely processed file',
          })
        }
      }
    },
  })

  useEffect(() => {
    if (!data || isWaitingNewData || messageQueue.length === 0 || loading) {
      return
    }

    const updateLocalItemStatus = (
      itemId: string,
      status: EKBFileEmbeddingStatus
    ) => {
      setData(
        produce(data, (draft) => {
          if (!draft) return

          const index = draft.findIndex((i) => i.id === itemId)
          if (index !== -1) {
            draft[index].embedding_status = status
          }
        })
      )
    }

    const handleMessage = (message: IEventResponse) => {
      const { knowledge_base_file_storage_id, knowledge_base_directory_id } =
        message.data ?? {}

      if (!message.success) {
        if (knowledge_base_directory_id === id) {
          updateLocalItemStatus(
            knowledge_base_file_storage_id,
            EKBFileEmbeddingStatus.Failed
          )
        }

        return
      }

      if (knowledge_base_directory_id === id) {
        updateLocalItemStatus(
          knowledge_base_file_storage_id,
          EKBFileEmbeddingStatus.Completed
        )
      }
    }

    const processQueue = async () => {
      if (messageQueue.length === 0) return

      const message = messageQueue[0]

      handleMessage(message)

      setMessageQueue((prev) => prev.slice(1))
    }

    processQueue()
  }, [messageQueue, data, isWaitingNewData, loading])

  useEffect(() => {
    const init = async () => {
      if (!id) return

      try {
        const [kbDetailRes] = await Promise.all([
          //fetch detail
          knowledgeBaseDirectoryReadKnowledgeBaseDirectoryByIdApi({
            path: {
              knowledge_base_directory_id: id,
            },
          }),
        ])

        if (kbDetailRes.status == 200) {
          const [{ data: dataDetail }] = [kbDetailRes]

          setKnowledgeBaseDirectoryDetail(dataDetail?.data)
        }
      } finally {
        setIsInitLoad(false)
      }
    }

    init()
  }, [id])

  const fetchFileData = async (fileUploads: FileUpload[] = []) => {
    if (!id) return

    setLoading(true)

    try {
      const { data, status } = await knowledgeBaseFileFetchMyKbFilesApi({
        path: {
          knowledge_base_directory_id: id,
        },
        query: {
          page_number: currentPage,
          file_display_name: searchKey,
          page_size: PAGE_SIZE.LARGE,
        },
      })

      if (status == 404) {
        setKbFileError(EKBFileError.NOT_FOUND)
        return
      } else if (status == 403) {
        setKbFileError(EKBFileError.NOT_HAVE_PERMISSION)
        return
      }

      if (data && data.data && data.data.data) {
        // automatically updating embedding status of uploaded files is Processing
        setData(
          data.data.data.map((file) => {
            if (
              fileUploads?.some(
                (fileUpload: any) => file.id === fileUpload.knowledgeFileId
              )
            ) {
              file.embedding_status = EKBFileEmbeddingStatus.Processing
            }
            return { ...file }
          })
        )
        setPagination({
          total_count: data.data.total_count,
          total_page: data.data.total_pages ?? 0,
          next_page: data.data.next_page,
          prev_page: data.data.prev_page,
        })
      }
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
      setIsWaitingNewData(false)
    }
  }

  useEffect(() => {
    fetchFileData()
  }, [currentPage, searchKey])

  const onEdit = (id: string) => {
    setIdDetail(id)
    setOpenModalEdit(true)
  }

  const handleDelete = async (item: KBFilePublic) => {
    if (!id) return

    const callDelete = async () => {
      try {
        const res = await knowledgeBaseFileDeleteKbFileByIdApi({
          path: {
            knowledge_base_directory_id: id,
            KB_file_id: item.id,
          },
        })

        if (res.status === 200) {
          Message.success({
            message: 'Successfully deleted knowledge file',
          })

          // if data have only 1 item, we need to go back to previous page
          if (size(data) === 1 && currentPage > 1) {
            setCurrentPage(currentPage - 1)
            return
          }

          fetchFileData()
          return
        }

        Message.error({
          message: 'Something went wrong!',
        })
      } catch (error) {
        Message.error({
          message: 'Something went wrong!',
        })
      }
    }

    MessageDialog.warning({
      mainMessage: 'Delete knowledge file?',
      subMessage:
        'If continue, this file will be permanently removed. Consequently, any workflow steps associated with this file will no longer be able to access its data.',
      onClick: callDelete,
    })
  }

  const handleSearch = (value: string) => {
    setSearchKey(value)
    setCurrentPage(1)
  }

  const handleRetry = (item: KBFilePublic) => {
    if (!id) return

    //update status of item on data
    setData(
      produce(data, (draft) => {
        if (!draft) return

        const index = draft.findIndex((i) => i.id === item.id)
        if (index !== -1) {
          draft[index].embedding_status = EKBFileEmbeddingStatus.Processing
        }
      })
    )

    try {
      knowledgeBaseFileUpdateEmbeddingKbFileByIdApi({
        path: {
          knowledge_base_directory_id: id,
          KB_file_id: item.id,
        },
      })
    } catch (error) {
      console.log(error)
    }
  }

  const renderContent = () => {
    if (isInitLoad) {
      return null
    }

    if (kbFileError === EKBFileError.NOT_FOUND) {
      return <NotFound />
    }

    if (kbFileError === EKBFileError.NOT_HAVE_PERMISSION) {
      return <AccessDenied />
    }

    return (
      <>
        <div className="flex items-center justify-between">
          <PageHeader
            breadcrumbPaths={[
              {
                name: 'Knowledge base',
                url: rootUrls.KnowledgeBase,
              },
              {
                name: knowledgeBaseDirectoryDetail?.name ?? '',
              },
            ]}
          />
          <SearchBar onSearch={handleSearch} />
        </div>

        <div className="mt-[12px] flex h-full flex-col overflow-hidden">
          <div className="genai-scrollbar h-full overflow-y-auto">
            {loading && <SkeletonLoadingPage className="" />}

            {!loading && searchKey && size(data) === 0 && (
              <NoDataFound className="mt-16" />
            )}

            {!loading && !searchKey && size(data) === 0 && (
              <EmptyBaseDirectory
                handleClick={() => {
                  setOpenModal(true)
                }}
              />
            )}

            {!loading && size(data) > 0 && (
              <div className="flex w-full flex-row flex-wrap gap-x-[12px] gap-y-[8px] px-[4px] pb-2">
                <AddNewItem
                  onClick={() => {
                    setOpenModal(true)
                  }}
                />
                {data!.map((item) => (
                  <Item
                    key={item.id}
                    item={item}
                    onClickFailed={handleRetry}
                    onEdit={(id) => onEdit(id)}
                    onDelete={handleDelete}
                  />
                ))}
              </div>
            )}
          </div>

          {pagination.total_page > 1 && (
            <div className="mt-[12px] flex w-full flex-row justify-end">
              <Pagination
                onChangePage={setCurrentPage}
                page={currentPage}
                totalPage={pagination.total_page ?? 0}
                className="w-[220px]"
              />
            </div>
          )}
        </div>

        {openModal && (
          <ModalUploadMultipleKnowledgeFile
            kbDirectoryId={id}
            setIsWaitingNewData={setIsWaitingNewData}
            open={openModal}
            onClose={() => {
              setOpenModal(false)
            }}
            onUploadSuccess={(uploadedFiles) => {
              fetchFileData(uploadedFiles)
              setOpenModal(false)
            }}
          />
        )}

        <ModalUpdateKnowledgeFile
          idDetail={idDetail}
          idDirectory={id}
          openEditModal={openModalEdit}
          setOpenEditModal={(e) => {
            setOpenModalEdit(e)
            setIdDetail('')
          }}
          refresh={() => fetchFileData()}
        />
      </>
    )
  }

  return (
    <Layout>
      <div className="flex h-full flex-col">{renderContent()}</div>
    </Layout>
  )
}

export default KnowledgeBaseDirectory
