import { Dispatch, useCallback, useEffect, useState } from 'react'
import Modal from '@/components/Modal'
import Input from '@/components/Input'
import Message from '@/components/Message'
import {
  ValidationError,
  knowledgeBaseFileReadKbFileByIdApi,
  knowledgeBaseFileUpdateKbFileByIdApi,
} from '@/apis/client'
import TextArea from '@/components/TextArea'
import Button from '@/components/Button'
import { HTTP_STATUS_CODE } from '@/constants'
import { MessageDialog } from '@/components/DialogMessage'

export const ModalUpdateKnowledgeFile = (
  props: ModalUpdateKnowledgeFileProps
) => {
  const { openEditModal, setOpenEditModal, idDetail, refresh, idDirectory } =
    props

  const [loading, setLoading] = useState(false)
  const [nameDetail, setNameDetail] = useState<string | undefined | null>()
  const [descriptionDetail, setDescriptionDetail] = useState<
    string | undefined | null
  >()

  const [errorCreate, setErrorCreate] = useState<ValidationError[] | string>('')
  const [initNameDetail, setInitNameDetail] = useState<
    string | undefined | null
  >()
  const [initDescriptionDetail, setInitDescriptionDetail] = useState<
    string | undefined | null
  >()

  const detailFile = async (id: string) => {
    if (!idDirectory) return

    const data = await knowledgeBaseFileReadKbFileByIdApi({
      path: {
        KB_file_id: id,
        knowledge_base_directory_id: idDirectory,
      },
    })

    if (data.status === HTTP_STATUS_CODE.SUCCESS) {
      setNameDetail(data?.data?.data?.file_display_name)
      setInitNameDetail(data?.data?.data?.file_display_name)
      setDescriptionDetail(data?.data?.data?.file_description)
      setInitDescriptionDetail(data?.data?.data?.file_description)
    }
  }

  const handleBlurDescription = () => {
    setDescriptionDetail(descriptionDetail?.trim())
  }

  const handleBlurName = () => {
    setNameDetail(nameDetail?.trim())
  }

  const updateFile = async (id: string) => {
    try {
      if (!idDirectory) return

      setLoading(true)
      const body = {
        file_display_name: nameDetail?.trim() || '',
        file_description: descriptionDetail?.trim() || '',
      }

      const data = await knowledgeBaseFileUpdateKbFileByIdApi({
        path: {
          KB_file_id: id,
          knowledge_base_directory_id: idDirectory,
        },
        body,
      })

      if (data.status === 200) {
        setLoading(false)
        setOpenEditModal(false)
        setNameDetail(undefined)
        setDescriptionDetail(undefined)
        setErrorCreate('')
        refresh()
        Message.success({ message: 'Successfully updated knowledge file' })
      } else if (data.status === HTTP_STATUS_CODE.BAD_REQUEST && data.error) {
        setErrorCreate('File name already exists')
        setLoading(false)
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    }
  }

  const contentEdit = useCallback(() => {
    return (
      <div className="flex w-full flex-col items-end gap-[24px]">
        <div className="flex w-full flex-col gap-[8px] rounded-[12px] border-[1px] border-neutral-200 bg-white px-[24px] py-[16px]">
          <div className="flex flex-col gap-[8px]">
            <div className="text-[14px] font-medium leading-[21px] text-Primary-Color">
              Display name
            </div>
            <Input
              value={nameDetail || ''}
              onChange={(e) => {
                setNameDetail(e.target.value)
                if (errorCreate) setErrorCreate('')
              }}
              maxLength={255}
              errorText={errorCreate.toString()}
              placeholder="Type in name for your knowledge file"
              isError={!!errorCreate.toString()}
              onBlur={() => handleBlurName()}
            />
          </div>

          <div className="flex flex-col gap-[8px]">
            <div className="text-[14px] font-medium leading-[21px] text-Primary-Color">
              Description
            </div>

            <TextArea
              value={descriptionDetail || ''}
              autoResize
              maxLength={255}
              className="h-[63px]"
              onChange={(e) => {
                setDescriptionDetail(e)
              }}
              placeholder="Supplementary information to use knowledge file"
              onBlur={handleBlurDescription}
            />
          </div>
        </div>

        <Button
          disabled={
            !(
              nameDetail &&
              (nameDetail?.trim() !== initNameDetail ||
                descriptionDetail?.trim() !== initDescriptionDetail)
            ) || /^[\s]*$/.test(nameDetail)
          }
          type="primary"
          loading={loading}
          text={'Save'}
          className="h-[31px]"
          onClick={() => updateFile(idDetail)}
        />
      </div>
    )
  }, [errorCreate, nameDetail, descriptionDetail, loading])

  useEffect(() => {
    if (idDetail) {
      detailFile(idDetail)
    }
  }, [idDetail])

  return (
    <Modal
      okLoading={loading}
      open={openEditModal}
      title="Update knowledge file"
      subTitle="Edit knowledge file information"
      className="w-[522px] border-none"
      showFooter={false}
      showCloseButton
      onClickClose={() => {
        if (
          nameDetail !== initNameDetail ||
          descriptionDetail !== initDescriptionDetail
        ) {
          MessageDialog.warning({
            mainMessage: 'Wanna leave?',
            subMessage: 'If continue, your changes may not be saved',
            onClick: () => {
              setLoading(false)
              setOpenEditModal(false)
              setNameDetail(undefined)
              setDescriptionDetail(undefined)
              setErrorCreate('')
            },
          })
        } else {
          setLoading(false)
          setOpenEditModal(false)
          setNameDetail(undefined)
          setDescriptionDetail(undefined)
          setErrorCreate('')
        }
      }}
    >
      {contentEdit()}
    </Modal>
  )
}

interface ModalUpdateKnowledgeFileProps {
  openEditModal: boolean
  setOpenEditModal: Dispatch<boolean>
  idDetail: string
  idDirectory: string | undefined
  refresh: () => void
}
