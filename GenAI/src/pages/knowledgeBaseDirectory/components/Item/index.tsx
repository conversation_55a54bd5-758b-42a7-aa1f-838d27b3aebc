import { KBFilePublic } from '@/apis/client'
import Dropdown, { SelectedItemProps } from '@/components/Dropdown'
import More from '@/components/More'
import Text from '@/components/Text'
import { EFileType } from '@/components/Upload/const'
import { getFileTypeByMime } from '@/components/Upload/helper'
import IconFile from '@/components/Upload/IconFile'
import clsx from 'clsx'
import { useCallback, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import { useDebouncedCallback } from 'use-debounce'
import { EKBFileEmbeddingStatus } from '../../consts'
import { formatTime } from '../../helper'

import { formatBytes, removeSpaceBetween } from '@/helpers'
import './styles.css'

interface Props {
  item: KBFilePublic
  onClickFailed?: (item: KBFilePublic) => void
  onEdit: (id: string) => void
  onDelete: (item: KBFilePublic) => void
}

const Item = ({ item, onClickFailed, onEdit, onDelete }: Props) => {
  const fileExt = getFileTypeByMime(item.file_extension)
  const isEmbedding =
    item.embedding_status === EKBFileEmbeddingStatus.Processing
  const isFailed =
    item.embedding_status === EKBFileEmbeddingStatus.New ||
    item.embedding_status === EKBFileEmbeddingStatus.Failed

  const [openWorkerAction, setOpenWorkerAction] = useState(false)

  const handleSelectAction = useCallback(({ key }: SelectedItemProps) => {
    switch (key) {
      case 'edit':
        onEdit(item.id)
        break
      case 'delete':
        onDelete(item)
        break
    }
  }, [])

  const handleClickFailed = useDebouncedCallback(
    () => {
      onClickFailed?.(item)
    },
    600,
    {
      leading: true,
      trailing: false,
    }
  )

  return (
    <div className={twMerge('w-full')}>
      <div
        className={twMerge(
          'relative flex h-[93px] w-full items-center justify-between rounded-[8px] bg-white px-4 py-[12px] shadow-base',
          clsx({
            'border border-Error-Color': isFailed,
          })
        )}
      >
        <div className="flex w-full max-w-[calc(100%-212px)] items-center gap-[12px]">
          <div className="flex h-[67px] w-fit flex-col items-center gap-[4px]">
            <IconFile size={48} fileExt={fileExt} />

            <Text
              type="supportText"
              variant="medium"
              className="text-Secondary-Color"
            >
              {removeSpaceBetween(
                `${formatBytes({ bytes: item?.file_size ?? 0 })}`
              )}
            </Text>
          </div>

          <div className="flex h-[69px] w-fit max-w-full flex-col justify-center gap-[4px]">
            <Text
              type="body"
              variant="medium"
              className="overflow-hidden text-Primary-Color"
              elementType="div"
              hasTooltip={false}
              ellipsis
            >
              {item.file_display_name}
            </Text>
            <Text
              type="subBody"
              variant="regular"
              className="overflow-hidden break-words text-Secondary-Color"
              elementType="div"
              multipleLine={2}
              hasTooltip={false}
              ellipsis
            >
              {item.file_description}
            </Text>
          </div>

          <div
            className={twMerge(
              'flex h-[20px] w-auto items-center justify-center rounded-full px-[8px] pb-[1px]',
              clsx({
                'bg-emerald-50': fileExt === EFileType.csv,
                'bg-red-50': fileExt === EFileType.pdf,
                'bg-blue-50':
                  fileExt === EFileType.doc || fileExt === EFileType.docx,
                'bg-black/5': fileExt === EFileType.txt,
                'bg-orange-50':
                  fileExt === EFileType.ppt || fileExt === EFileType.pptx,
                'bg-[#E7F1D1]': fileExt === EFileType.epub,
              })
            )}
          >
            <Text
              type="subBody"
              variant="medium"
              className={clsx({
                'text-emerald-700': fileExt === EFileType.csv,
                'text-red-700': fileExt === EFileType.pdf,
                'text-blue-600':
                  fileExt === EFileType.doc || fileExt === EFileType.docx,
                'text-black': fileExt === EFileType.txt,
                'text-orange-600':
                  fileExt === EFileType.ppt || fileExt === EFileType.pptx,
                'text-[#74A111]': fileExt === EFileType.epub,
              })}
            >
              {fileExt}
            </Text>
          </div>
        </div>

        {!isEmbedding && !isFailed && (
          <div className="flex h-full w-auto flex-col justify-end">
            <Text
              type="subBody"
              variant="medium"
              className="w-max text-Secondary-Color"
            >
              {formatTime(item.created_at)}
            </Text>
          </div>
        )}

        {!isEmbedding && (
          <div className="absolute right-[6px] top-[6px] flex flex-row-reverse gap-1">
            <Dropdown
              // overlayClassName="w-[71px]"
              open={openWorkerAction}
              placement="bottom end"
              items={[
                {
                  key: 'edit',
                  label: 'Edit',
                },
                {
                  key: 'delete',
                  label: 'Delete',
                },
              ]}
              onSelect={(key) => handleSelectAction(key)}
              onOpenChange={(newOpen) => {
                setOpenWorkerAction(newOpen)
              }}
            >
              <More active={openWorkerAction} />
            </Dropdown>
          </div>
        )}

        {isEmbedding && (
          <div className="flex h-full w-auto select-none items-end justify-center px-[4px]">
            <Text
              type="subBody"
              variant="medium"
              className="w-max text-Secondary-Color"
            >
              Processing...
            </Text>
          </div>
        )}

        {isFailed && (
          <div className="flex h-full w-auto select-none items-end justify-center px-[4px]">
            <Text
              type="subBody"
              variant="semibold"
              className="w-max cursor-pointer text-Error-Color duration-300 hover:underline hover:decoration-solid hover:decoration-[1px] hover:underline-offset-[2.5px]"
              onClick={handleClickFailed}
            >
              Retry process
            </Text>
          </div>
        )}
      </div>
    </div>
  )
}

export default Item
