import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { twMerge } from 'tailwind-merge'

interface Props {
  onClick?: () => void
}

const AddNewItem = ({ onClick }: Props) => {
  return (
    <div
      className={twMerge(
        'flex h-[84px] w-full cursor-pointer flex-col items-center justify-center gap-[8px] rounded-[8px] border border-dashed border-Base-Single-Color px-4 py-3',
        'hover:bg-Hover-Color'
      )}
      onClick={onClick}
    >
      <div className="flex h-[32px] w-[32px] items-center justify-center rounded-[8px] bg-neutral-150 p-1">
        <Icon
          name="Bold-MessagesConversation-PenNewRound"
          size={24}
          gradient={['#642B73', '#C6426E']}
        />
      </div>

      <Text type="subBody" variant="semibold" className="text-Primary-Color">
        Add new file
      </Text>
    </div>
  )
}

export default AddNewItem
