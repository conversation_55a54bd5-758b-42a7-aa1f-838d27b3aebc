import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'

interface Props {
  handleClick: () => void
}
const EmptyBaseDirectory = ({ handleClick }: Props) => {
  return (
    <div className="flex flex-col items-center">
      <EmptyData size="medium" type="02" className="mt-[80px]" />

      <Button
        onClick={handleClick}
        type="primary"
        size="medium"
        className="mt-[24px] w-[133px]"
        text="New File"
        leftIcon={<Icon name="vuesax-outline-add" color="#ffffff" size={20} />}
      />
    </div>
  )
}

export default EmptyBaseDirectory
