import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { rootUrls } from '@/routes/rootUrls'
import ChatWidget from '@/pages/ChatEmbedIframe/ChatWidget'
import { useState } from 'react'

const Home = () => {
  const navigate = useNavigate()
  const { logout, refreshAccessToken } = useAuth()

  const handleLogout = () => {
    logout()
    navigate(rootUrls.Login, { replace: true })
  }

  const queryString = window.location.search

  const urlParams = new URLSearchParams(queryString)

  const embedUrl = urlParams.get('embedUrl')

  const [url, setUrl] = useState('')

  const handleKeyPress = (event: any) => {
    if (event.key === 'Enter') {
      const newUrl = new URL(url)
      const pathUrl = newUrl.pathname + newUrl.search

      window.location.href =
        window.location.origin +
        '/?embedUrl=' +
        `${window.location.origin}${pathUrl}`
    }
  }

  return (
    <div className="flex flex-col">
      <input
        className="w-1/6 border-2"
        placeholder="Press enter to change url"
        value={url}
        onChange={(e) => setUrl(e.target.value)}
        onKeyPress={handleKeyPress}
      />

      {Object.keys(rootUrls).map((key) => {
        const url = rootUrls[key as keyof typeof rootUrls]
        return (
          <div
            className="w-fit cursor-pointer"
            key={key}
            onClick={() => navigate(url)}
          >
            {key}
          </div>
        )
      })}
      <div className="cursor-pointer" onClick={refreshAccessToken}>
        Refresh token
      </div>
      <div className="cursor-pointer" onClick={handleLogout}>
        Logout
      </div>

      {embedUrl && <ChatWidget url={embedUrl} />}
    </div>
  )
}

export default Home
