/* eslint-disable max-len */
import Icon from '@/assets/icon/Icon'
import { cn } from '@/helpers'
import { EWorkflowType } from '@/pages/Workflows/const'
import { colors } from '@/theme'
import { memo } from 'react'
import ButtonGroupItem from './ButtonGroupItem'
import { IButtonGroupItem } from './types'

interface Props {
  id?: string
  workflowType: EWorkflowType
  onClickIntegrate?: () => void
  onClickMemory?: () => void
  onClickAI?: () => void
  aiGenerateButtonActive?: boolean
  isActiveCitation?: boolean
}
const ButtonGroup = ({
  id,
  workflowType,
  onClickIntegrate,
  onClickMemory,
  onClickAI,
  aiGenerateButtonActive = false,
  isActiveCitation = false,
}: Props) => {
  const items: IButtonGroupItem[] = [
    {
      name: 'Integrate',
      className: 'h-[39px] overflow-hidden',
      icon: (
        <Icon
          name="vuesax-outline-format-circle"
          size={24}
          color={colors['Primary-Color']}
        />
      ),
      hoverIcon: (
        <Icon
          name="vuesax-bold-format-circle"
          size={24}
          color={colors['Primary-Color']}
        />
      ),
      hidden: !id,
      onClick: onClickIntegrate,
    },
    {
      name: 'Memory',
      className: 'h-[39px] overflow-hidden',
      icon: (
        <Icon
          name="vuesax-outline-ram-2"
          size={24}
          color={colors['Primary-Color']}
        />
      ),
      hoverIcon: (
        <Icon
          name="vuesax-bold-ram-2"
          size={24}
          color={colors['Primary-Color']}
        />
      ),
      hidden: !id || workflowType === EWorkflowType.autoProcessing,
      onClick: onClickMemory,
    },
    {
      name: 'CiteMind',
      className: 'h-[39px] overflow-hidden',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.3585 2.31882C17.8294 1.7898 17.1268 1.5 16.3776 1.5H8.14952C6.22068 1.5 4.64998 3.0707 4.64998 4.99954V10.9488C4.64998 11.3343 4.96439 11.6487 5.34988 11.6487C5.73538 11.6487 6.04979 11.3343 6.04979 10.9488V4.99954C6.04979 3.84169 6.99166 2.89982 8.14952 2.89982H16.3776C16.7522 2.89982 17.1022 3.04746 17.3674 3.30993L20.6878 6.6304C20.9503 6.88877 21.0979 7.25374 21.0979 7.62011V19.0006C21.0979 20.1585 20.1561 21.1004 18.9982 21.1004H16.8985C16.513 21.1004 16.1986 21.4148 16.1986 21.8003C16.1986 22.1858 16.513 22.5002 16.8985 22.5002H18.9982C20.9271 22.5002 22.4978 20.9295 22.4978 19.0006V7.62291C22.4978 6.87379 22.208 6.17116 21.6789 5.6421L18.3585 2.32435V2.31882ZM10.5541 15.8485H9.58767C9.23771 15.8485 8.91509 16.0863 8.86042 16.4295C8.78661 16.8669 9.12699 17.2483 9.54942 17.2483L10.6061 17.2551C11.0258 17.2551 11.3443 17.6297 11.2882 18.0425C11.1734 18.8901 10.2767 20.3774 9.12835 21.2345C8.78933 21.486 8.74697 21.9905 9.06136 22.298C9.317 22.5468 9.72985 22.5359 10.0128 22.3185C11.4755 21.2017 12.6266 19.3439 12.6976 17.9798C12.74 17.1746 12.4557 16.6989 12.211 16.4406C11.8364 16.0455 11.2759 15.8459 10.5555 15.8459L10.5541 15.8485ZM4.23862 15.8482H5.20509L5.20648 15.8457C5.9269 15.8457 6.48739 16.0453 6.86194 16.4403C7.10663 16.6987 7.39096 17.1744 7.34859 17.9796C7.27751 19.3437 6.12648 21.2015 4.66377 22.3183C4.3808 22.5357 3.96795 22.5466 3.71232 22.2978C3.39792 21.9902 3.44302 21.4858 3.7793 21.2343C4.92761 20.3772 5.82436 18.8899 5.93919 18.0423C5.99525 17.6295 5.67672 17.2549 5.25705 17.2549L4.20037 17.2481C3.77794 17.2481 3.43756 16.8667 3.51138 16.4292C3.56604 16.0861 3.88866 15.8482 4.23862 15.8482Z"
            fill="#2D0136"
          />
        </svg>
      ),
      hoverIcon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M15.929 1.5C16.691 1.5 17.4057 1.79477 17.9438 2.33288V2.3385L21.3213 5.71319C21.8594 6.25134 22.1541 6.96603 22.1541 7.728V19.301C22.1541 21.263 20.5565 22.8606 18.5945 22.8606H9.92462C9.93781 22.8517 9.95076 22.8424 9.96346 22.8326C10.4776 22.44 10.9538 21.9575 11.36 21.4368C12.1292 20.4506 12.647 19.3275 12.6944 18.4194C12.7375 17.6004 12.4482 17.1165 12.1994 16.8537C11.8184 16.4519 11.2483 16.2489 10.5155 16.2489L10.5141 16.2515H9.531C9.17504 16.2515 8.84688 16.4934 8.79128 16.8424C8.71619 17.2874 9.06242 17.6753 9.4921 17.6753L10.5669 17.6823C10.9938 17.6823 11.3178 18.0633 11.2608 18.4832C11.1562 19.2551 10.4139 20.5487 9.42133 21.4368H4.9022C5.67144 20.4506 6.18924 19.3275 6.23655 18.4194C6.27965 17.6004 5.99043 17.1165 5.74155 16.8537C5.36057 16.4519 4.79046 16.2489 4.05768 16.2489L4.05626 16.2515H4V5.05961C4 3.09766 5.59766 1.5 7.55961 1.5H15.929Z"
            fill="#2D0136"
          />
          <path
            d="M8.99567 22.8117C8.67588 22.4989 8.71897 21.9858 9.0638 21.7299C9.18599 21.6387 9.30537 21.5405 9.42133 21.4368H4.9022C4.64071 21.772 4.35016 22.0914 4.03917 22.3812C4.13596 22.6597 4.40125 22.8606 4.71192 22.8606H9.05201C9.03252 22.8456 9.01371 22.8293 8.99567 22.8117Z"
            fill="#2D0136"
          />
        </svg>
      ),
      hidden: !isActiveCitation,
      onClick: () => window.open(`/utilities/citemind/?workflowId=${id}`),
    },
    {
      name: 'AI',
      className: 'h-[39px] overflow-hidden',
      icon: (
        <Icon
          name={
            aiGenerateButtonActive
              ? 'Bold-EssentionalUI-MagicStick3'
              : 'Outline-EssentionalUI-MagicStick3'
          }
          size={24}
          color={colors['Primary-Color']}
          className="rotate-90"
        />
      ),
      hoverIcon: (
        <Icon
          name="Bold-EssentionalUI-MagicStick3"
          size={24}
          color={colors['Primary-Color']}
          className="rotate-90"
        />
      ),
      onClick: onClickAI,
    },
  ]

  return (
    <div
      className={cn(
        'flex w-[58px] min-w-[58px] flex-col items-center justify-center gap-[8px] rounded-xl bg-white p-1.5 shadow-base'
      )}
    >
      {items
        .filter((item) => !item.hidden)
        .map((item, index) => {
          return <ButtonGroupItem key={index} item={item} />
        })}
    </div>
  )
}

export default memo(ButtonGroup)
