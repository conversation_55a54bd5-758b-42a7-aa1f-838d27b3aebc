import BaseModal from '@/components/BaseModal'
import ModalHeader from '@/components/Modal/ModalHeader'
import PlaygroundMultiAgent from '@/components/PlaygroundMultiAgent'
import { colors } from '@/theme'
import { memo } from 'react'
import { CustomNode } from '../../types'
import PlaygroundAutomationProcess from '../PlaygroundAutomationProcess'
import { EWorkflowType } from '@/pages/Workflows/const'
import { ProcessingStartNode_Output } from '@/apis/client'
import clsx from 'clsx'
import StepParamsProvider from '../PlaygroundAutomationProcess/provider/StepParamsProvider'
import IconButton from '@/components/IconButton'

interface IModalPlaygroundProps {
  isOpen: boolean
  onClose: () => void
  language?: string
  communicationStyle?: string
  nodes: CustomNode[]
  startNode?: ProcessingStartNode_Output
  workflowId: string
  workflowName: string
  workflowType: EWorkflowType
}

const ModalPlaygroundMultiAgent = ({
  isOpen,
  nodes,
  startNode,
  workflowId,
  workflowName,
  workflowType,
  onClose,
}: IModalPlaygroundProps) => {
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div
        className={clsx(
          'relative flex h-[654px] w-[1016px] flex-col gap-[16px] overflow-hidden rounded-[20px] border-[1px] border-neutral-200 bg-Base-03 p-3 shadow-md',
          workflowType === EWorkflowType.autoProcessing && '!h-fit !w-fit'
        )}
      >
        <IconButton
          className="absolute right-[10px] top-[10px] border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />

        <ModalHeader
          title="Playground"
          subTitle={
            workflowType === EWorkflowType.conversation
              ? 'Run your workflow here'
              : 'Enter parameters to run your automation process'
          }
        />

        {workflowType === EWorkflowType.conversation && (
          <PlaygroundMultiAgent
            workerType={'AI'}
            nodes={nodes}
            workflowId={workflowId}
            headerTitle={workflowName}
            helloMessage={
              'Hi, I am your omnipotent virtual assistant! Tell me what you need and I am here to help'
            }
          />
        )}
        {workflowType === EWorkflowType.autoProcessing && (
          <StepParamsProvider>
            <PlaygroundAutomationProcess
              workflowId={workflowId}
              inputParams={startNode?.data?.inputParams}
              startNode={startNode?.data}
            />
          </StepParamsProvider>
        )}
      </div>
    </BaseModal>
  )
}

export default memo(ModalPlaygroundMultiAgent)
