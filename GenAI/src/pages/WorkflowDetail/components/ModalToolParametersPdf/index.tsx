import { ToolItemPDFGenerateParameter } from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { cn } from '@/helpers'
import { colors } from '@/theme'
import { useState } from 'react'

interface Props {
  params: ToolItemPDFGenerateParameter
  isOpen: boolean
  onClose: () => void
  handleSaveParamsPdf: ({ prompt }: any) => void
}
const ModalToolParametersPdf = ({
  params,
  isOpen,
  onClose,
  handleSaveParamsPdf,
}: Props) => {
  const [text, setText] = useState(params?.prompt ?? '')
  const [isChange, setIsChange] = useState(false)

  const handleSave = () => {
    handleSaveParamsPdf({
      prompt: text,
    })
    onClose()
  }

  const disableSave = text.trim().length === 0 || !isChange

  return (
    <BaseModal isPureModal isOpen={isOpen} onClose={onClose}>
      <div className="relative flex h-[227px] max-h-[227px] w-[591px] flex-col gap-[12px] rounded-[20px] bg-white p-3 shadow-md">
        <IconButton
          nameIcon="x-close"
          sizeIcon={16}
          onClick={() => {
            onClose()
          }}
          className="absolute right-[10px] top-[10px] duration-300 hover:bg-neutral-200"
          colorIcon={colors.neutral[500]}
        />
        <div className="flex size-full h-[167px] flex-col gap-1">
          <Text
            type="subBody"
            variant="medium"
            className="pl-1 text-Tertiary-Color"
          >
            When to use
          </Text>

          <TextArea
            value={text}
            onChange={(e) => {
              setIsChange(true)
              setText(e)
            }}
            onBlur={() => {
              if (text.trim() !== text) setText(text.trim())
            }}
            placeholder={'Describe when the tool will be executed'}
            className={cn('h-[138px]')}
            maxLength={1000}
          />
        </div>

        <div className="flex w-full justify-end">
          <Button
            disabled={disableSave}
            className="px-[16px]"
            type="primary"
            size="small"
            onClick={handleSave}
            text="Save"
          />
        </div>
      </div>
    </BaseModal>
  )
}

export default ModalToolParametersPdf
