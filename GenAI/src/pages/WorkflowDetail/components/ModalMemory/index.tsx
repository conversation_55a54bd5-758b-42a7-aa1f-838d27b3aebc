import {
  ProcessingWorkflowPublic,
  WorkflowPublic,
  workflowUpdateWorkflowMemoriesApi,
} from '@/apis/client'
import Message from '@/components/Message'
import Modal from '@/components/Modal'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { nanoid } from 'nanoid'
import { useMemo, useRef, useState } from 'react'
import InputClose from './components/InputClose'

interface Props {
  onClose: () => void
  wfId: string
  strMemories?: string[] | null
  setWfData: React.Dispatch<
    React.SetStateAction<WorkflowPublic | ProcessingWorkflowPublic | undefined>
  >
}

interface InputMemory {
  id: string
  value: string
}

const init = (strMemories?: string[] | null) => {
  if (strMemories && strMemories.length > 0) {
    return strMemories.map((memory) => ({
      id: nanoid(),
      value: memory,
    }))
  }

  return Array.from({ length: 3 }, () => ({
    id: nanoid(),
    value: '',
  }))
}

const ModalMemory = ({ strMemories, wfId, onClose, setWfData }: Props) => {
  const [memories, setMemories] = useState<InputMemory[]>(() =>
    init(strMemories)
  )
  const [isLoading, setIsLoading] = useState(false)
  const [isChanged, setIsChanged] = useState(false)
  const lstRef = useRef<HTMLDivElement>(null)

  const isUpdated = useMemo(() => {
    return Array.isArray(strMemories) && strMemories.length > 0
  }, [strMemories])

  const onClickAdd = () => {
    setIsChanged(true)
    setMemories((prev) => {
      return [
        ...prev,
        {
          id: nanoid(),
          value: '',
        },
      ]
    })

    //scroll to bottom
    setTimeout(() => {
      lstRef.current?.scrollTo({
        top: lstRef.current.scrollHeight,
        behavior: 'smooth',
      })
    }, 100)
  }

  const handleSave = async () => {
    setIsLoading(true)

    const newMemories = memories.map((memory) => memory.value).filter(Boolean)
    const res = await workflowUpdateWorkflowMemoriesApi({
      path: {
        workflow_id: wfId,
      },
      body: {
        memories: newMemories,
      },
    })

    if (res.status !== HTTP_STATUS_CODE.SUCCESS) {
      Message.error({ message: 'Something went wrong!' })
      setIsLoading(false)

      return
    }

    Message.success({ message: 'Successfully save memory settings' })

    //remove all blank memories, if all memories are blank, keep 1
    setMemories((prev) => {
      const newMemories = prev.filter((memory) => memory.value)
      if (newMemories.length === 0) {
        return [
          {
            id: nanoid(),
            value: '',
          },
        ]
      }

      return newMemories
    })

    setWfData((prev) => {
      if (!prev) return prev

      return {
        ...prev,
        memories: newMemories,
      }
    })
    setIsLoading(false)
    setIsChanged(false)
  }

  const okDisable = (() => {
    if (!isUpdated) {
      return memories.filter((memory) => memory.value).length === 0
    } else {
      return !isChanged
    }
  })()

  return (
    <Modal
      showCloseButton
      onClickClose={onClose}
      onClickOk={handleSave}
      okLoading={isLoading}
      title="Workflow Memory"
      subTitle="Configure meaningful information need to be extracted and stored in long-term memory"
      open={true}
      okDisable={okDisable}
      className="w-[537px]"
      classNameCancelButton="invisible"
      classNameFooter="justify-end mt-[12px]"
      classNameOkButton="w-[133px] min-w-[133px]"
    >
      <div className="flex w-[489px] flex-col rounded-[12px] border border-neutral-200 bg-white py-[20px] pl-8 pr-[12px]">
        <div className="flex h-auto w-[445px] flex-col gap-1">
          <Text className="text-Tertiary-Color" type="subBody" variant="medium">
            Memory settings
          </Text>

          <div className="flex flex-col gap-[4px]">
            <div
              ref={lstRef}
              className="no-scrollbar flex max-h-[378px] flex-col gap-[8px] overflow-y-auto p-1"
            >
              {memories.map((memory, index) => (
                <InputClose
                  allowShowClose={index !== 0}
                  key={memory.id}
                  value={memory.value}
                  onChange={(e) => {
                    setIsChanged(true)
                    setMemories((prev) => {
                      const newMemories = [...prev]
                      newMemories[index].value = e.target.value
                      return newMemories
                    })
                  }}
                  onDelete={() => {
                    setIsChanged(true)
                    setMemories((prev) => {
                      const newMemories = [...prev]
                      newMemories.splice(index, 1)
                      return newMemories
                    })
                  }}
                />
              ))}
            </div>

            <div className="flex items-center justify-end px-[20px]">
              <Text
                onClick={onClickAdd}
                className="cursor-pointer text-Primary-Color hover:bg-Main-Color hover:bg-clip-text hover:text-transparent"
                type="subBody"
                variant="semibold"
              >
                Add
              </Text>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default ModalMemory
