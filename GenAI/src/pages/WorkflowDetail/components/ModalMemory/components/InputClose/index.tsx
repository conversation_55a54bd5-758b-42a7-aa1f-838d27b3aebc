import Input from '@/components/Input'
import { cn } from '@/helpers'
import { memo, useState } from 'react'
import './InputClose.scss'

interface Props {
  value: string
  allowShowClose?: boolean
  className?: string
  onChange?: (value: React.ChangeEvent<HTMLInputElement>) => void
  onDelete?: () => void
}
const InputClose = ({
  value,
  className,
  allowShowClose = true,
  onChange,
  onDelete,
}: Props) => {
  const [isFocus, setIsFocus] = useState(false)

  return (
    <div className={cn('group flex items-center gap-1', className)}>
      <Input
        value={value}
        onChange={onChange}
        maxLength={50}
        placeholder="Type in information that your worker need to remember"
        onFocus={() => {
          setIsFocus(true)
        }}
        onBlur={() => {
          setIsFocus(false)
          onChange?.({
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            target: {
              value: value.trim(),
            },
          })
        }}
        isFullWidth
        className={cn(
          'placeholder:select-none',
          'group-hover:!hover-bg-clip-padding-border group-hover:!border-transparent group-hover:!bg-Input-Main-03 group-hover:!bg-origin-border'
        )}
      />

      {allowShowClose ? (
        <div
          onClick={onDelete}
          className={cn(
            'button-close-wfl-memory invisible h-[16px] w-[16px] cursor-pointer items-center justify-center group-hover:visible',
            {
              visible: isFocus,
            }
          )}
        ></div>
      ) : (
        <div className="w-[16px]" />
      )}
    </div>
  )
}

export default memo(InputClose)
