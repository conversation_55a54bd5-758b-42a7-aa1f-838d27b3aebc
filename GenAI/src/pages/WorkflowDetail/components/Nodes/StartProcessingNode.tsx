import { Handle, NodeProps, Position } from '@xyflow/react'
import { memo, useCallback, useMemo } from 'react'

import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { colors } from '@/theme'
import clsx from 'clsx'
import { get } from 'lodash'
import { CustomNode } from '../../types'
import IconOutlook from './assets/IconOutlook'
import IconPlayCircle from './assets/IconPlayCircle'
import IconSlack from './assets/IconSlack'

const StartProcessingNode = ({ data, selected }: NodeProps<CustomNode>) => {
  const trigger_on = get(data, 'trigger_on', false)
  const inputParams = get(data, 'inputParams', [])
  const trigger = get(data, 'trigger', null)

  const isSlackTrigger = useMemo(() => {
    return trigger?.triggerType?.toLowerCase()?.includes('slack')
  }, [trigger?.triggerType])

  const isOutlookTrigger = useMemo(
    () => trigger?.triggerType?.toLowerCase()?.includes('outlook'),
    [trigger?.triggerType]
  )

  const renderTriggerContent = useCallback(() => {
    if (!trigger_on) {
      return <Text type="supportText">Add trigger</Text>
    }

    if (isSlackTrigger) {
      return (
        <div className="flex items-center gap-1">
          <IconSlack />
          <Text type="supportText">Slack</Text>
        </div>
      )
    }

    if (isOutlookTrigger) {
      return (
        <div className="flex items-center gap-1">
          <IconOutlook />
          <Text type="supportText">Microsoft Outlook</Text>
        </div>
      )
    }
  }, [trigger_on, trigger])

  return (
    <div
      className={clsx(
        'relative flex w-[240px] flex-col rounded-lg bg-white px-3 py-2 shadow-base hover:bg-Hover-Color',
        selected && 'border border-Base-Single-Color'
      )}
    >
      <Handle type="source" position={Position.Right} id="new" />

      <div className="flex w-full flex-col items-center">
        <div className="flex items-center gap-1">
          <IconPlayCircle />
          <Text type="subBody" variant="semibold">
            Start
          </Text>
        </div>
        <Text type="supportText" className="text-Secondary-Color">
          Define how and when workflow will begin
        </Text>
      </div>
      <div className="mt-0.5 flex flex-col gap-2 p-2">
        <div className="flex items-center gap-1">
          <Icon
            name="vuesax-outline-add"
            color={colors['Primary-Color']}
            gradient={inputParams?.length ? ['#642B73', '#C6426E'] : undefined}
            size={16}
          />
          <Text type="supportText">
            {inputParams?.length
              ? `${inputParams?.length} parameter${inputParams?.length > 1 ? 's' : ''} added`
              : 'Add input parameters'}
          </Text>
        </div>
        <div className="flex items-center gap-1">
          <Icon
            name="vuesax-bold-flash-1"
            color={colors['Primary-Color']}
            gradient={trigger_on ? ['#642B73', '#C6426E'] : undefined}
            size={16}
          />
          {renderTriggerContent()}
        </div>
      </div>
    </div>
  )
}

export default memo(StartProcessingNode)
