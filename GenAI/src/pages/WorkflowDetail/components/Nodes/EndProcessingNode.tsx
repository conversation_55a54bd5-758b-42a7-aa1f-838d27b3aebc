import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import { colors } from '@/theme'
import { Handle, NodeProps, Position } from '@xyflow/react'
import clsx from 'clsx'
import { memo } from 'react'
import { CustomNode } from '../../types'
import IconCheckCircle from './assets/IconCheckCircle'

const EndNode = ({ selected }: NodeProps<CustomNode>) => {
  return (
    <div
      className={clsx(
        'relative flex w-[240px] flex-col rounded-lg bg-white px-3 py-2 shadow-base hover:bg-Hover-Color',
        selected && 'border border-Base-Single-Color'
      )}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="end-handle-target"
        style={{ left: -2 }}
      />

      <div className="flex w-full flex-col items-center">
        <div className="flex items-center gap-1">
          <Text type="subBody" variant="semibold">
            End
          </Text>
          <IconCheckCircle />
        </div>
        <Text type="supportText" className="text-Secondary-Color">
          Manage output expectations
        </Text>
      </div>
      <div className="mt-0.5 flex flex-col items-center p-2">
        <div className="flex items-center gap-1">
          <Icon
            name="vuesax-outline-add"
            color={colors['Primary-Color']}
            size={16}
          />
          <Text type="supportText">Configure output format</Text>
        </div>
      </div>
    </div>
  )
}

export default memo(EndNode)
