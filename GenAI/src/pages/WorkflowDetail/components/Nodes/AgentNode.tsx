import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import Badge from '@/components/Badge'
import Text from '@/components/Text'
import { getUrlImage } from '@/helpers'
import { ModelsContext } from '@/pages/Workers/contexts'
import { WorkerType } from '@/pages/Workers/types'
import { CustomNode } from '@/pages/WorkflowDetail/types'
import { Handle, NodeProps, Position } from '@xyflow/react'
import clsx from 'clsx'
import { memo, useContext, useMemo } from 'react'

const AgentNode = ({ data, selected }: NodeProps<CustomNode>) => {
  const {
    workerType,
    workerName,
    name,
    description,
    tools,
    knowledgeBases,
    workerAvatar,
    workerId,
    modelId,
  } = data
  const { models } = useContext(ModelsContext)

  const isErrorWorker = useMemo(() => !workerId, [workerId])

  const avatar = useMemo(() => {
    return workerAvatar
  }, [workerAvatar, tools])

  const iconName = useMemo(() => {
    if (workerType === WorkerType.HUMAN) {
      return 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
    }
    return 'face-id-square'
  }, [workerType, tools])

  const getAvatarModel = (id: string | undefined | null) => {
    if (id) {
      const model = models.find((n) => n.id === id)

      return model?.image_url
    }
    return
  }
  return (
    <div
      className={clsx(
        'relative flex min-w-[264px] max-w-[288px] flex-col gap-1 rounded-lg bg-white px-3 py-2 shadow-base hover:bg-Hover-Color',
        selected && 'border border-Base-Single-Color',
        isErrorWorker && 'border border-Error-Color'
      )}
    >
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />

      <div className="flex w-full gap-2 overflow-hidden">
        <div className="flex max-w-[100px] flex-col items-center gap-1">
          {isErrorWorker ? (
            <>
              <div className="flex size-8 items-center justify-center rounded-full bg-red-50">
                <Text
                  value="E"
                  variant="semibold"
                  className="text-Error-Color"
                />
              </div>

              <Text
                value="Error"
                className="text-center text-Error-Color"
                variant="medium"
                type="subBody"
              />
            </>
          ) : (
            <>
              <div className="relative">
                <Avatar
                  avatarUrl={avatar!}
                  avatarDefault={
                    <div className="flex size-4 items-center justify-center rounded-full bg-Background-Color">
                      <Icon
                        name={iconName}
                        size={28}
                        gradient={['#642B734D', '#C6426E4D']}
                      />
                    </div>
                  }
                />

                <div
                  style={{
                    backgroundImage: `url(${getAvatarModel(modelId) ? getUrlImage(getAvatarModel(modelId)) : ''}`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '10px',
                    backgroundPosition: 'center',
                  }}
                  className="absolute bottom-[-0.5px] right-[-8.5px] z-10 flex size-[16px] min-w-[16px] items-center justify-center rounded-full border border-border-base-icon bg-white"
                />
              </div>

              <Text
                value={workerName}
                className="break-words text-center text-Primary-Color"
                variant="medium"
                type="subBody"
              />
            </>
          )}
        </div>
        <div className="flex grow flex-col gap-1">
          <Text
            value={name}
            className="text-Primary-Color"
            variant="semibold"
            type="subBody"
          />
          <Text
            value={description}
            className="max-w-[156px] text-Secondary-Color"
            type="supportText"
            elementType="div"
            ellipsis
            multipleLine={3}
          />
        </div>
      </div>
      <div className="flex items-center justify-end gap-3">
        <Badge showZero count={tools?.length || 0}>
          <div className="flex size-5 items-center justify-center rounded-md bg-neutral-150">
            <Icon name="tool-01" size={14} gradient={['#642B73', '#C6426E']} />
          </div>
        </Badge>

        <Badge showZero count={knowledgeBases?.length || 0}>
          <div className="flex size-5 items-center justify-center rounded-md bg-neutral-150">
            <Icon
              name="database-01"
              size={14}
              gradient={['#642B73', '#C6426E']}
            />
          </div>
        </Badge>
      </div>
    </div>
  )
}

export default memo(AgentNode)
