import { nanoid } from 'nanoid'

export const getErrorValidateFile = (size: number, type: string) => {
  if (!DATA_TYPES?.includes(type)) {
    return 'File type is not supported'
  }
  if (size > 10485760) return 'Maximum size 10MB'
  return ''
}

export const DATA_TYPES = [
  'application/pdf',
  'text/plain',
  'text/csv',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/png',
  'image/jpeg',
  'image/jpg',
]

export const ArrAIOperation = [
  { id: 'Summarize content', name: 'Summarize content' },
  { id: 'Extract information', name: 'Extract information' },
  { id: 'Query and search', name: 'Query and search' },
]

export const transformFiles = (files: FileList | null) => {
  if (!files) return

  return Array.from(files).map((file) => {
    return {
      id: nanoid(),
      name: file.name,
      size: file.size,
      type: file.type,
      error: getErrorValidateFile(file.size, file.type),
      file,
    }
  })
}
