import Text from '@/components/Text'
import OperationGroup from './OperationGroup'
import { DATA_TYPES, transformFiles } from './helper'
import { memo, useContext, useRef, useState } from 'react'
import { map, size } from 'lodash'
import { StepParamsContext } from '../provider/StepParamsProvider'
import SelectAttachFile from './SelectAttachFile'

const AddFileProcess = () => {
  const inpRef = useRef<HTMLInputElement>(null)
  const [selectedOperation, setSelectedOperation] = useState<any>(null)

  const {
    mapOperationID2Files,
    setMapOperation2Files,
    groupOperationPos,
    setGroupOperationPos,
  } = useContext(StepParamsContext)

  const handleSelectedOperationValue = (value: any) => {
    //open file dialog
    inpRef.current?.click()

    setSelectedOperation(value)
  }

  const handleDeleteGroup = (index: string) => {
    //update position
    setGroupOperationPos((pre) => {
      const newGroupPosition = pre.filter(
        (groupIndex: string) => groupIndex !== index
      )
      return newGroupPosition
    })

    setMapOperation2Files((pre) => {
      const newMap = { ...pre }
      delete newMap[index]
      return newMap
    })
  }

  const handleDeleteFile = (groupIndex: string, fileId: string) => {
    const newFiles = mapOperationID2Files[groupIndex].filter(
      (file: any) => file.id !== fileId
    )

    if (newFiles.length === 0) {
      handleDeleteGroup(groupIndex)
    } else {
      setMapOperation2Files((pre) => {
        return {
          ...pre,
          [groupIndex]: newFiles,
        }
      })
    }
  }

  const handleGroupChangeSelectedValue = (oldId: string, newValue: any) => {
    const newGroupId = newValue.id
    // if newGroupId not exist in mapOperationID2Files, replace oldId by newGroupId
    if (!mapOperationID2Files[newGroupId]) {
      setGroupOperationPos((pre) => {
        const newGroupPosition = [...pre]
        const index = newGroupPosition.indexOf(oldId)
        if (index !== -1) {
          newGroupPosition[index] = newGroupId
        }
        return newGroupPosition
      })
      setMapOperation2Files((pre) => {
        const newMap = { ...pre }
        newMap[newGroupId] = newMap[oldId]
        delete newMap[oldId]
        return newMap
      })
    } else {
      // Merge all files from oldId to newGroupId
      setMapOperation2Files((pre) => {
        const newMap = { ...pre }
        newMap[newGroupId] = [...newMap[newGroupId], ...newMap[oldId]]
        delete newMap[oldId]
        return newMap
      })

      //update position
      setGroupOperationPos((pre) => {
        const newGroupPosition = pre.filter(
          (groupIndex: string) => groupIndex !== oldId
        )
        return newGroupPosition
      })
    }
  }

  const onSelectFiles = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files

    if (!files) return

    const transformedFiles = transformFiles(files)

    if (!selectedOperation || !transformedFiles) return

    //update position
    setGroupOperationPos((pre) => {
      const newGroupPosition = [...pre]

      if (newGroupPosition.includes(selectedOperation.id)) {
        return newGroupPosition
      }

      newGroupPosition.push(selectedOperation.id)
      return newGroupPosition
    })

    // check if selectedOperation.id exist in mapOperationID2Files => merge files
    if (mapOperationID2Files[selectedOperation.id]) {
      setMapOperation2Files((old) => {
        return {
          ...old,
          [selectedOperation.id]: [
            ...old[selectedOperation.id],
            ...transformedFiles,
          ],
        }
      })
      return
    } else {
      setMapOperation2Files((pre) => {
        return {
          ...pre,
          [selectedOperation.id]: transformedFiles,
        }
      })
    }
  }

  return (
    <div className="flex flex-col gap-[4px]">
      <input
        ref={inpRef}
        type="file"
        name="file"
        multiple={true}
        accept={DATA_TYPES.join(', ')}
        onChange={onSelectFiles}
        style={{ display: 'none' }}
        value={''}
      />

      <div className="flex w-full items-center justify-between">
        <div className="flex flex-col">
          <Text variant="medium" type="body" className="text-Primary-Color">
            Attach files
          </Text>
          <Text
            variant="regular"
            type="subBody"
            className="text-Secondary-Color"
          >
            Add files and choose AI operation to execute
          </Text>
        </div>

        <SelectAttachFile
          handleSelectedOperationValue={handleSelectedOperationValue}
        />
      </div>

      {size(mapOperationID2Files) > 0 &&
        groupOperationPos.length > 0 &&
        map(groupOperationPos, (groupId) => {
          return (
            <OperationGroup
              key={groupId}
              files={mapOperationID2Files[groupId]}
              groupIndex={groupId}
              onClickDeleteGroup={handleDeleteGroup}
              onChangeSelectedGroupValue={handleGroupChangeSelectedValue}
              handleDeleteFile={handleDeleteFile}
            />
          )
        })}
    </div>
  )
}

export default memo(AddFileProcess)
