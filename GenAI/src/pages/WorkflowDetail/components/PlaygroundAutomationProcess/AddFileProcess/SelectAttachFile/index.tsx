import SelectOneCustom from '@/components/SelectOneCustom'
import { useState } from 'react'
import { ArrAIOperation } from '../helper'
import Tooltip from '@/components/TooltipRC'
import IconBoldMessagesConversationPaperclip from '../Icons/IconBoldMessagesConversationPaperclip'

interface Props {
  handleSelectedOperationValue: (value: any) => void
}
const SelectAttachFile = ({ handleSelectedOperationValue }: Props) => {
  const [visibleSelect, setVisibleSelect] = useState(false)
  const [tooltipVisible, setTooltipVisible] = useState(false)

  const handleTooltipAttachFileChange = (visible: boolean) => {
    // if select is visible, don't show tooltip
    if (visibleSelect) return
    setTooltipVisible(visible)
  }

  const handleVisibleSelectChange = (visible: boolean) => {
    // close tooltip when select is visible
    if (visible) setTooltipVisible(false)
    setVisibleSelect(visible)
  }

  return (
    <SelectOneCustom
      open={visibleSelect}
      onVisibleChange={handleVisibleSelectChange}
      data={ArrAIOperation}
      onChangeSelectedValue={handleSelectedOperationValue}
    >
      <div className="flex size-[20px] cursor-pointer items-center justify-center rounded-[4px] hover:bg-Hover-Color">
        <Tooltip
          visible={tooltipVisible}
          onVisibleChange={handleTooltipAttachFileChange}
          text={<p className="min-w-[65px] text-white">Attach file</p>}
          trigger={['hover']}
          getTooltipContainer={(node: HTMLElement) =>
            node.parentNode as HTMLElement
          }
          align={{
            overflow: {
              shiftX: true,
              adjustY: true,
            },
          }}
        >
          <div>
            <IconBoldMessagesConversationPaperclip />
          </div>
        </Tooltip>
      </div>
    </SelectOneCustom>
  )
}

export default SelectAttachFile
