import { useContext, useEffect, useMemo, useRef, useState } from 'react'
import Icon from '@/assets/icon/Icon'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import Select from '../SelectAssignee/components/Select'
import {
  extractNumbers,
  isStringArray,
  isStringInteger,
  isStringObject,
} from '../../helpers'
import {
  ProcessingParameter,
  ProcessingStartNodeData_Output,
  processingWorkflowExecuteWorkflowApi,
} from '@/apis/client'
import { colors } from '@/theme'
import {
  BooleanParamItems,
  DEFAULT_MAX_LENGTH_OF_INPUT,
  EDataType,
} from '../ModalConfigStartNode/const'
import { HTTP_STATUS_CODE } from '@/constants'
import { v4 as uuid } from 'uuid'
import AddFileProcess from './AddFileProcess'
import {
  FAILED_UPLOAD_FILE,
  filterValidFiles,
  isAllFilesError,
  isConfigParams,
  isHaveFile,
  isInvalidParams,
  uploadKb,
} from './helper'
import { StepParamsContext } from './provider/StepParamsProvider'
import { size } from 'lodash'
import EmptyData from '@/components/EmptyData'

interface ProcessingParameterValue extends ProcessingParameter {
  value: any
  error: boolean
}

interface IPlaygroundAutomationProcessProps {
  workflowId: string
  inputParams?: Array<ProcessingParameter>
  startNode?: ProcessingStartNodeData_Output
}

const PlaygroundAutomationProcess = ({
  workflowId,
  inputParams,
  startNode,
}: IPlaygroundAutomationProcessProps) => {
  const {
    mapOperationID2Files,
    setMapOperation2Files,
    paramInputs,
    setParamInputs,
    handleChangeParamInput,
    handleChangeParamInputError,
  } = useContext(StepParamsContext)

  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState()

  const sessionId = useRef<string>(uuid())

  const enableAttachFile = startNode?.allow_file_upload

  const inputParameters = useMemo(() => {
    if (!paramInputs?.length && !enableAttachFile) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <EmptyData
            size="small"
            type="04"
            title="Ready to run!"
            content="No input parameters needed"
          />
        </div>
      )
    }

    return paramInputs?.map((inputParam: ProcessingParameterValue) => {
      const { paramName, description, paramType, value, error } = inputParam
      let input = undefined

      if (paramType === EDataType.string) {
        input = (
          <Input
            placeholder="Type in here"
            maxLength={DEFAULT_MAX_LENGTH_OF_INPUT}
            value={value}
            onChange={(e) =>
              handleChangeParamInput({
                key: paramName,
                value: e.target.value,
              })
            }
          />
        )
      } else if (
        paramType === EDataType.integer ||
        paramType === EDataType.number
      ) {
        input = (
          <Input
            placeholder="Type in here"
            // type="number"
            maxLength={DEFAULT_MAX_LENGTH_OF_INPUT}
            value={value}
            isError={error}
            errorText={
              error
                ? `Data type: ${paramType === EDataType.integer ? 'Integer' : 'Number'}`
                : ''
            }
            onChange={(e) => {
              const value = e.target.value
              if (paramType === EDataType.number) {
                const isValidFloat = /^-?\d*\.?\d*$/.test(e.target.value)

                if (!isValidFloat) {
                  return
                }
              }
              handleChangeParamInput({
                key: paramName,
                value:
                  paramType === EDataType.integer
                    ? extractNumbers(value)
                    : value,
              })
            }}
            onBlur={() =>
              paramType === EDataType.integer &&
              handleChangeParamInputError({
                key: paramName,
                value: value && !isStringInteger(value),
              })
            }
          />
        )
      } else if (paramType === EDataType.boolean) {
        input = (
          <Select
            className="min-w-[330px]"
            overlayClassName="w-[var(--button-width)]"
            placeholder="Select here"
            data={BooleanParamItems}
            selected={value}
            getPopupContainer={(triggerNode) => triggerNode?.parentNode}
            onChangeSelectedValue={(item) => {
              handleChangeParamInput({
                key: paramName,
                value: item,
              })
            }}
          />
        )
      } else if (paramType === EDataType.array) {
        input = (
          <TextArea
            className="h-[95px]"
            maxHeight={95}
            placeholder="Type in here"
            maxLength={DEFAULT_MAX_LENGTH_OF_INPUT}
            value={value}
            isSubtext={error}
            isError={error}
            helperText={error ? 'Data type: Array' : ''}
            onChange={(value) =>
              handleChangeParamInput({
                key: paramName,
                value,
              })
            }
            onBlur={() =>
              handleChangeParamInputError({
                key: paramName,
                value: value && !isStringArray(value),
              })
            }
          />
        )
      } else if (paramType === EDataType.object) {
        input = (
          <TextArea
            className="h-[95px]"
            maxHeight={95}
            placeholder="Type in here"
            maxLength={DEFAULT_MAX_LENGTH_OF_INPUT}
            value={value}
            isSubtext={error}
            isError={error}
            helperText={error ? 'Data type: Object' : ''}
            onChange={(value) =>
              handleChangeParamInput({
                key: paramName,
                value,
              })
            }
            onBlur={() =>
              handleChangeParamInputError({
                key: paramName,
                value: value && !isStringObject(value),
              })
            }
          />
        )
      }

      return (
        <div key={paramName} className="flex flex-col gap-2">
          <div className="flex items-center px-1">
            <Text
              className="!w-fit max-w-full overflow-hidden"
              variant="medium"
              type="body"
              value={paramName}
              elementType="div"
              ellipsis
            />
            {description && (
              <IconButton
                nameIcon="Bold-EssentionalUI-InfoCircle"
                colorIcon={colors.neutral[300]}
                hoverColor={colors['Primary-Color']}
                sizeIcon={14}
                tooltipText={description}
                className="ml-1 flex h-fit"
              />
            )}
          </div>
          {input}
        </div>
      )
    })
  }, [
    enableAttachFile,
    paramInputs,
    handleChangeParamInput,
    handleChangeParamInputError,
  ])

  const handleRunAutomationProcess = async () => {
    setLoading(true)

    // clear FAILED_UPLOAD_FILE error && remove file with error
    setMapOperation2Files((pre) => {
      const newMap = { ...pre }
      for (const [operationId, files] of Object.entries(newMap)) {
        newMap[operationId] = files
          .map((file) => {
            if (file.error && file.error !== FAILED_UPLOAD_FILE) {
              return null
            }
            return {
              ...file,
              error: '',
            }
          })
          .filter(Boolean)
      }
      return newMap
    })

    try {
      const fileIDSuccess = []
      const mapFailedOperationID2FilePos: Record<string, Array<number>> = {}

      if (enableAttachFile) {
        const mapValidFiles = filterValidFiles(mapOperationID2Files)

        for (const [operationId, files] of Object.entries(mapValidFiles)) {
          const filesPromise = files.map(async (file: any) => {
            return uploadKb(workflowId, sessionId.current, {
              file_display_name: file?.name,
              file_storage_upload: file.file,
              file_intent: operationId as any,
            })
          })

          const res = await Promise.all(filesPromise)

          for (let i = 0; i < res.length; i++) {
            if (res[i].status === HTTP_STATUS_CODE.SUCCESS) {
              const data = res[i].data?.data
              fileIDSuccess.push(data?.id)
            } else {
              if (!mapFailedOperationID2FilePos[operationId]) {
                mapFailedOperationID2FilePos[operationId] = []
              }
              mapFailedOperationID2FilePos[operationId].push(i)
            }
          }
        }

        console.log('🚀 ~ fileIDSuccess', fileIDSuccess)
        console.log(
          '🚀 ~ mapFailedOperationID2FilePos',
          mapFailedOperationID2FilePos
        )
      }

      const executeBody =
        paramInputs?.reduce((acc, param) => {
          const { paramName, paramType, value } = param
          let currentValue = value

          if (paramType === EDataType.integer) {
            currentValue = parseInt(param.value)
          } else if (paramType === EDataType.number) {
            currentValue = parseFloat(param.value)
          } else if (paramType === EDataType.boolean) {
            currentValue = param.value.value
          } else if (
            paramType === EDataType.array ||
            paramType === EDataType.object
          ) {
            currentValue = JSON.parse(value)
          }
          return {
            ...acc,
            [paramName]: currentValue,
          }
        }, {}) ?? ({} as any)
      executeBody.__list_file_id = fileIDSuccess

      const response: any = await processingWorkflowExecuteWorkflowApi({
        path: {
          workflow_id: workflowId,
        },
        body: executeBody,
      })

      if (response?.status === HTTP_STATUS_CODE.SUCCESS) {
        setResult(response?.data)
      } else {
        setResult(response?.error)
      }

      setLoading(false)

      if (size(mapFailedOperationID2FilePos) > 0) {
        // Update error for files
        setMapOperation2Files((pre) => {
          const newMap = { ...pre }
          for (const [operationId, filePos] of Object.entries(
            mapFailedOperationID2FilePos
          )) {
            const files = newMap[operationId]
            for (const pos of filePos) {
              files[pos] = {
                ...files[pos],
                error: FAILED_UPLOAD_FILE,
              }
            }
            newMap[operationId] = files
          }
          return newMap
        })
      }
    } catch (error) {
      //
    }
  }

  useEffect(() => {
    if (inputParams) {
      setParamInputs(
        inputParams?.map((paramInput) => ({
          ...paramInput,
          value: '',
          error: false,
        }))
      )
    }
  }, [inputParams])

  const isDisabledRunButton = useMemo(() => {
    if (!isConfigParams(paramInputs)) {
      if (enableAttachFile) {
        return !isHaveFile(mapOperationID2Files)
      }
      return true
    } else {
      if (enableAttachFile) {
        return (
          !isHaveFile(mapOperationID2Files) ||
          isInvalidParams(paramInputs!) ||
          isAllFilesError(mapOperationID2Files)
        )
      }
      return isInvalidParams(paramInputs!)
    }
  }, [enableAttachFile, mapOperationID2Files, paramInputs])

  return (
    <div className="genai-scrollbar flex h-fit gap-[20px] overflow-auto">
      <div className="flex w-[407px] min-w-[407px] flex-col items-end gap-3 rounded-xl border border-border-base-icon bg-white px-5 py-4">
        <div className="genai-scrollbar flex h-full max-h-[469px] min-h-[300px] w-full min-w-[367px] flex-col gap-2 overflow-auto px-1 pb-[4px]">
          {inputParameters}

          {enableAttachFile && <AddFileProcess />}
        </div>

        <Button
          className="py-[3px] pl-6 pr-4 text-white"
          size="small"
          type="primary"
          text="Run"
          rightIcon={
            <Icon
              className="flex items-center"
              size={16}
              color="white"
              name="vuesax-bold-flash-circle"
            />
          }
          loading={loading}
          disabled={isDisabledRunButton}
          onClick={handleRunAutomationProcess}
        />
      </div>

      <div className="flex max-w-[1369px]">
        <div className="flex w-fit min-w-[565px] max-w-[calc(100vw-600px)] flex-col items-center gap-2 rounded-xl border border-border-base-icon bg-white px-5 pb-6 pt-4">
          <Text variant="semibold" type="body" value="Results" />
          <Text
            className="genai-scrollbar flex h-full max-h-[468px] min-h-[300px] w-full overflow-auto rounded-lg bg-Base-03 px-1 text-black"
            type="subBody"
          >
            <pre>
              <code>{JSON.stringify(result, null, 2)}</code>
            </pre>
          </Text>
        </div>
      </div>
    </div>
  )
}

export default PlaygroundAutomationProcess
