import {
  Body_workflow_upload_file_workflow,
  workflowUploadFileWorkflow,
} from '@/apis/client'

export const FAILED_UPLOAD_FILE = 'Failed to process'

export type ProcessingParameter = {
  paramName: string
  paramType:
    | 'string'
    | 'integer'
    | 'number'
    | 'array'
    | 'boolean'
    | 'object'
    | 'file'
  description?: string | null
  defaultValue?: unknown | null
}

export interface ProcessingParameterValue extends ProcessingParameter {
  value: any
  error: boolean
}

export const isInvalidParams = (params: ProcessingParameterValue[]) => {
  return params.some((param) => !param?.value || param?.error)
}

export const isHaveFile = (mapOperationID2Files: Record<string, any[]>) => {
  return (
    Object.keys(mapOperationID2Files).length > 0 &&
    Object.values(mapOperationID2Files).some((files) => files.length > 0)
  )
}

export const isAllFilesError = (
  mapOperationID2Files: Record<string, any[]>
) => {
  return Object.values(mapOperationID2Files).every((files) =>
    files.every((file) => file.error && file.error !== FAILED_UPLOAD_FILE)
  )
}

export const isConfigParams = (
  paramInputs: ProcessingParameterValue[] | undefined
) => {
  return Boolean(paramInputs)
}

export const filterValidFiles = (
  mapOperationID2Files: Record<string, any[]>
) => {
  return Object.entries(mapOperationID2Files).reduce<Record<string, any[]>>(
    (acc, [key, files]) => {
      const validFiles = files.filter(
        (file) => !file.error || file.error === FAILED_UPLOAD_FILE
      )
      if (validFiles.length > 0) {
        acc[key] = validFiles
      }
      return acc
    },
    {}
  )
}

export const uploadKb = (
  workflowId: string,
  sessionId: string,
  formData: Body_workflow_upload_file_workflow
) => {
  return workflowUploadFileWorkflow({
    path: {
      workflow_id: workflowId,
      session_id: sessionId,
    },
    body: formData,
  })
}
