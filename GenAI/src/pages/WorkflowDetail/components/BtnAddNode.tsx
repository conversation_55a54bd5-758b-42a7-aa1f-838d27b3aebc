import Text from '@/components/Text'
import { DragEvent<PERSON>and<PERSON>, memo } from 'react'
import IconCursorGrabbing from '../assets/IconCursorGrabbing'
import { NODE_TYPE } from '../constants'
import { EWorkflowType } from '@/pages/Workflows/const'

const BtnAddNode = ({
  workflowType = EWorkflowType.conversation,
}: IBtnAddNodeProps) => {
  const onDragStart: DragEventHandler<HTMLDivElement> = (event) => {
    event.dataTransfer.setData('application/reactflow', NODE_TYPE.AGENT)
    event.dataTransfer.effectAllowed = 'move'
  }

  return (
    <div
      className="flex cursor-grab items-center justify-center gap-1 rounded-full bg-Main-07 px-4 py-2 hover:bg-Main-08"
      onDragStart={onDragStart}
      draggable
    >
      <Text
        className="bg-Main2-02 bg-clip-text text-transparent"
        variant="medium"
        type="subheading"
        elementType="div"
      >
        {workflowType === EWorkflowType.conversation
          ? 'Conversation node'
          : 'Processing node'}
      </Text>
      <IconCursorGrabbing />
    </div>
  )
}

interface IBtnAddNodeProps {
  workflowType?: EWorkflowType
}

export default memo(BtnAddNode)
