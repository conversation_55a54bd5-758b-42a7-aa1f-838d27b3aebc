import BaseModal from '@/components/BaseModal'
import Text from '@/components/Text'
import clsx from 'clsx'
import { memo, useEffect, useState } from 'react'
import Select from '../SelectAssignee/components/Select'
import { Editor } from '@monaco-editor/react'
import Message from '@/components/Message'
import { MessageDialog } from '@/components/DialogMessage'

const DATA = [
  {
    name: 'JSON Object',
    id: 'json',
  },
]

const ModalConfigEndNode = ({
  handleCloseModal,
  isOpen,
  defaultValue,
  onDone,
}: IModalConfigEndNode) => {
  const [outputFormat, setOutputFormat] = useState<any>('')
  const [error, setError] = useState<boolean>(false)
  const [isDirtyModal, setDirtyModal] = useState<boolean>(false)
  const [isLoaded, setLoaded] = useState<boolean>(false)

  const handleEditorDidMount = (editor: any) => {
    const handler = editor.onDidChangeModelDecorations(() => {
      handler.dispose()
      editor
        .getAction('editor.action.formatDocument')
        .run()
        .then(() => {
          editor.setScrollPosition({ scrollTop: 0 })
          setLoaded(true)
        })
    })
  }

  const handleEditorChange = (value: any) => {
    setOutputFormat(value)
    setError(false)
    if (isLoaded) {
      setDirtyModal(true)
    }
  }

  const doneHandler = () => {
    const isValidJson = (json: string) => {
      try {
        JSON.parse(json)
        return true
      } catch (e) {
        return false
      }
    }
    if (!isValidJson(outputFormat)) {
      Message.error({
        message: 'Invalid JSON Schema',
      })
      setError(true)
    } else {
      onDone(JSON.parse(outputFormat))
      handleCloseModal()
    }
  }

  useEffect(() => {
    if (defaultValue) {
      setOutputFormat(JSON.stringify(defaultValue))
    } else {
      setOutputFormat('')
    }
  }, [defaultValue])

  const onClose = () => {
    if (isDirtyModal) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          handleCloseModal()
          setDirtyModal(false)
        },
      })
    } else {
      handleCloseModal()
    }
  }

  return (
    <BaseModal
      isOpen={isOpen}
      title={'Output parameter'}
      subTitle={'Configure out params to specify what will be returned'}
      isShowCloseButton={true}
      hasCancelButton={false}
      onClose={onClose}
      onAgree={doneHandler}
      bodyClassName="!px-3"
    >
      <div
        className={clsx(
          'h-fit rounded-[20px] border border-neutral-200 bg-white p-8 pt-5'
        )}
      >
        <Text
          type="body"
          variant="medium"
          value="Output format"
          elementType="div"
          className="mb-2 pl-1 text-Primary-Color"
        />
        <Select
          data={DATA}
          className="w-[132px]"
          selected={DATA[0]}
          onChangeSelectedValue={() => {}}
          disabled
        />

        <Editor
          className={clsx(
            'mt-4 h-[376px] !w-[964px] rounded-lg border border-border-base-icon py-2 pl-0 pr-2',
            error && '!border-Error-Color'
          )}
          defaultLanguage="json"
          onChange={handleEditorChange}
          value={outputFormat}
          onMount={handleEditorDidMount}
        />

        <Text
          type="supportText"
          variant="regular"
          className="mt-2 text-right text-Secondary-Color"
          elementType="div"
        >
          Follow JSON Schema{' '}
          <a
            href="https://json-schema.org/learn"
            target="_blank"
            className="underline" rel="noreferrer"
          >
            here
          </a>
        </Text>
      </div>
    </BaseModal>
  )
}

interface IModalConfigEndNode {
  isOpen: boolean
  handleCloseModal: () => void
  onDone: (data: any) => void
  defaultValue: string
}

export default memo(ModalConfigEndNode)
