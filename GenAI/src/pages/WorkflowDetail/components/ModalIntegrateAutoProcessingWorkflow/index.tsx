import { processingWorkflowApiBaseIntegrationApi } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import CopyButton from '@/pages/WorkflowIntegration/components/ConfigIntegration/common/CopyButton'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useEffect, useMemo, useState } from 'react'

const ModalIntegrateAutoProcessingWorkflow = ({
  handleCloseModal,
  isOpen,
  id,
}: IModalIntegrateAutoProcessingWorkflow) => {
  const [data, setData] = useState<any>(null)
  const [hidden, setHidden] = useState<boolean>(true)

  const fetchData = async () => {
    try {
      const res = await processingWorkflowApiBaseIntegrationApi({
        path: {
          workflow_id: id,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setData(res.data?.data)
      } else {
        setData(null)
      }
    } catch (error) {
      setData(null)
      console.error(error)
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchData()
    }
  }, [id, isOpen])

  const secret = useMemo(() => {
    if (hidden) {
      if (!data?.client_secret) {
        return ''
      }
      return data?.client_secret.replace(/./g, '*')
    }
    return data?.client_secret
  }, [data, hidden])

  return (
    <BaseModal
      isOpen={isOpen}
      title={'Workflow Integration'}
      subTitle={'Export and integrate workflow with your business'}
      isShowCloseButton={true}
      hasCancelButton={false}
      hideFooter
      onClose={handleCloseModal}
      bodyClassName="!px-3"
    >
      <div
        className={clsx(
          'h-[477px] w-[1109px] rounded-[20px] border border-neutral-200 bg-white p-8 pt-5',
          data?.upload_file_endpoint && 'h-[589px]'
        )}
      >
        <Text
          type="subheading"
          variant="medium"
          value="API Base"
          elementType="div"
          className="mb-3 w-full text-center text-Primary-Color"
        />
        <div className="flex w-full">
          <div className="mr-[18px] w-[582px]">
            <Text
              value="Authentication"
              elementType="div"
              type="body"
              variant="semibold"
              className="mb-2 text-Primary-Color"
            />
            <div className="h-[248px] w-full rounded-lg border border-neutral-300 p-3">
              <Text
                value="Client ID"
                elementType="div"
                type="subBody"
                variant="medium"
                className="mb-1 ml-1 text-Tertiary-Color"
              />
              <div className="mb-3 flex w-full gap-3 px-2">
                <Text
                  value={data?.client_id || ''}
                  elementType="div"
                  type="subBody"
                  variant="regular"
                  className="!w-fit max-w-full text-Primary-Color"
                  ellipsis
                />
                <CopyButton value={data?.client_id || ''} />
              </div>
              <Text
                value="Client Secret "
                elementType="div"
                type="subBody"
                variant="medium"
                className="mb-1 ml-1 text-Tertiary-Color"
              />
              <div className="mb-3 flex w-full gap-2 px-2">
                <Text
                  value={secret || ''}
                  elementType="div"
                  type="subBody"
                  variant="regular"
                  className="mr-1 !w-fit max-w-full text-Primary-Color"
                  ellipsis
                />
                <IconButton
                  nameIcon={
                    hidden ? 'vuesax-bold-eye-slash' : 'vuesax-bold-eye'
                  }
                  colorIcon={colors['neutral']['300']}
                  hoverColor={colors['Primary-Color']}
                  sizeIcon={16}
                  onClick={() => setHidden(!hidden)}
                />
                <CopyButton value={data?.client_secret || ''} />
              </div>
              <Text
                value="Token Endpoint"
                elementType="div"
                type="subBody"
                variant="medium"
                className="mb-1 ml-1 text-Tertiary-Color"
              />
              <div className="mb-3 flex w-full gap-3 px-2">
                <Text
                  value={data?.access_token_endpoint || ''}
                  elementType="div"
                  type="subBody"
                  variant="regular"
                  className="!w-fit max-w-full text-Primary-Color"
                  ellipsis
                />
                <CopyButton value={data?.access_token_endpoint || ''} />
              </div>
              <Text
                value="Refresh Token"
                elementType="div"
                type="subBody"
                variant="medium"
                className="mb-1 ml-1 text-Tertiary-Color"
              />
              <div className="flex w-full gap-3 px-2">
                <Text
                  value={data?.refresh_token_endpoint || ''}
                  elementType="div"
                  type="subBody"
                  variant="regular"
                  className="!w-fit max-w-full text-Primary-Color"
                  ellipsis
                />
                <CopyButton value={data?.refresh_token_endpoint || ''} />
              </div>
            </div>
            {data?.upload_file_endpoint && (
              <>
                <Text
                  value="Attachments"
                  elementType="div"
                  type="body"
                  variant="semibold"
                  className="mb-2 mt-3 text-Primary-Color"
                />
                <div className="h-[71px] w-full rounded-lg border border-neutral-300 p-3">
                  <Text
                    value="Upload File"
                    elementType="div"
                    type="subBody"
                    variant="medium"
                    className="mb-1 ml-1 text-Tertiary-Color"
                  />
                  <div className="flex w-full gap-3 px-2">
                    <Text
                      value={data?.upload_file_endpoint || ''}
                      elementType="div"
                      type="subBody"
                      variant="regular"
                      className="!w-fit max-w-full text-Primary-Color"
                      ellipsis
                    />
                    <CopyButton value={data?.upload_file_endpoint || ''} />
                  </div>
                </div>
              </>
            )}
            <Text
              value="Integration"
              elementType="div"
              type="body"
              variant="semibold"
              className="mb-2 mt-3 text-Primary-Color"
            />
            <div className="h-[71px] w-full rounded-lg border border-neutral-300 p-3">
              <Text
                value="API Endpoint"
                elementType="div"
                type="subBody"
                variant="medium"
                className="mb-1 ml-1 text-Tertiary-Color"
              />
              <div className="flex w-full gap-3 px-2">
                <Text
                  value={data?.api_endpoint || ''}
                  elementType="div"
                  type="subBody"
                  variant="regular"
                  className="!w-fit max-w-full text-Primary-Color"
                  ellipsis
                />
                <CopyButton value={data?.api_endpoint || ''} />
              </div>
            </div>
          </div>
          <div className="w-[445px]">
            <Text
              value="Sample"
              elementType="div"
              type="body"
              variant="semibold"
              className="mb-2 text-Primary-Color"
            />
            <div
              className={clsx(
                'relative h-[351px] w-full rounded-lg border border-neutral-300 bg-Base-03 p-3',
                data?.upload_file_endpoint && 'h-[410px]'
              )}
            >
              <div className="flex w-[376px] gap-1">
                <Text
                  value="curl"
                  elementType="div"
                  type="subBody"
                  variant="regular"
                />

                <div className="flex w-[calc(100%-25.92px)] flex-col">
                  {data?.sample_request
                    ?.replace('curl ', '')
                    ?.split('\n')
                    ?.map((item: string, index: number) => (
                      <Text
                        key={index}
                        value={item}
                        elementType="div"
                        type="subBody"
                        variant="regular"
                        className="w-full break-all text-Primary-Color"
                      />
                    ))}
                </div>
              </div>

              <CopyButton
                value={data?.sample_request || ''}
                size={24}
                className="absolute right-2 top-2"
              />
            </div>

            {data?.upload_file_endpoint && (
              <div className="mt-2 flex items-center gap-2">
                <Icon
                  name="Bold-EssentionalUI-InfoCircle"
                  size={16}
                  color={colors['Secondary-Color']}
                />
                <span className="leading-[18px]">
                  <Text
                    value="To attach files to the automation process, please upload them using the API in the"
                    type="subBody"
                    className="text-Secondary-Color"
                  />
                  <Text
                    value=" Attachments "
                    type="subBody"
                    className="text-Primary-Color"
                  />
                  <Text
                    value="section to obtain the"
                    type="subBody"
                    className="text-Secondary-Color"
                  />
                  <Text
                    value=" fileID "
                    type="subBody"
                    className="text-Primary-Color"
                  />
                  <Text
                    value="of successfully uploaded files"
                    type="subBody"
                    className="text-Secondary-Color"
                  />
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="h-3 w-full" />
    </BaseModal>
  )
}

interface IModalIntegrateAutoProcessingWorkflow {
  isOpen: boolean
  handleCloseModal: () => void
  id: string
}

export default memo(ModalIntegrateAutoProcessingWorkflow)
