import { ToolItemExcelGenerateColumnParameter } from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import { MessageDialog } from '@/components/DialogMessage'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import { cn } from '@/helpers'
import { colors } from '@/theme'
import { useCallback, useEffect, useState } from 'react'
import ExcelColumnNameItem from './components/ExcelColumnNameItem'

interface Props {
  params: any
  isOpen: boolean
  onClose: () => void
  handleSaveParamsExcel: ({ prompt, columns }: any) => void
}
const ModalToolParametersExcel = ({
  params,
  isOpen,
  onClose,
  handleSaveParamsExcel,
}: Props) => {
  const [excelColumnNames, setExcelColumnNames] = useState<Array<string>>(
    Array(3).fill('')
  )
  const [text, setText] = useState(params?.prompt ?? '')
  const [isChange, setIsChange] = useState(false)
  const [errorText, setErrorText] = useState<boolean>(false)

  const handleAddExcelColumnName = useCallback(() => {
    setIsChange(true)
    setExcelColumnNames((props) => [...props, ''])
  }, [])

  const handleRemoveExcelColumnName = useCallback((index: number) => {
    setIsChange(true)
    setExcelColumnNames((props) =>
      props?.length > 1 ? props.filter((_, i) => i !== index) : ['']
    )
  }, [])

  const handleChangeExcelColumnNameValue = useCallback(
    (index: number, value: string) => {
      setIsChange(true)
      setExcelColumnNames((props) => {
        props[index] = value

        return [...props]
      })
    },
    []
  )

  const handleClose = useCallback(() => {
    if (isChange) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          setIsChange(false)
          onClose()
        },
      })
    } else {
      onClose()
    }
  }, [isChange, onClose])

  const handleSave = useCallback(() => {
    if (!text) {
      setErrorText(true)
      return
    }

    handleSaveParamsExcel({
      prompt: text,
      columns: excelColumnNames
        .filter((columnName) => columnName)
        .map((columnName) => ({
          column_name: columnName,
          description: '',
        })),
    })
    onClose()
  }, [text, excelColumnNames, handleSaveParamsExcel, onClose])

  useEffect(() => {
    const filteredExcelColumnNames = params?.columns?.filter(
      (columnName: ToolItemExcelGenerateColumnParameter) =>
        columnName?.column_name
    )

    if (filteredExcelColumnNames?.length) {
      setExcelColumnNames(
        filteredExcelColumnNames.map(
          (column: ToolItemExcelGenerateColumnParameter) =>
            column?.column_name ?? ''
        )
      )
    }
  }, [params])

  return (
    <BaseModal isPureModal isOpen={isOpen} onClose={handleClose}>
      <div className="relative flex max-h-[641px] w-[972px] flex-col gap-[12px] rounded-[20px] bg-white p-3 shadow-md">
        <IconButton
          nameIcon="x-close"
          sizeIcon={16}
          onClick={() => {
            handleClose()
          }}
          className="absolute right-[10px] top-[10px] duration-300 hover:bg-neutral-200"
          colorIcon={colors.neutral[500]}
        />
        <div className="flex w-full flex-col gap-1 px-1">
          <Text
            type="subheading"
            variant="medium"
            className="text-Primary-Color"
          >
            Tool Parameters
          </Text>
          <Text type="subBody" className="text-Secondary-Color">
            Configure tool parameter to execute function
          </Text>
        </div>

        <div className="flex h-full w-full flex-col gap-2 rounded-lg px-2">
          <div className="flex size-full flex-col gap-1">
            <Text
              type="body"
              variant="medium"
              className="w-full pl-[22px] pr-1 text-Primary-Color"
            >
              Add excel column name
            </Text>

            <div className="genai-scrollbar flex h-full max-h-[336px] w-full flex-col gap-2 overflow-auto py-1 pr-1">
              {excelColumnNames?.map((columnName, index) => (
                <ExcelColumnNameItem
                  key={index}
                  columnName={columnName}
                  onChangeExcelColumnName={(value) =>
                    handleChangeExcelColumnNameValue(index, value)
                  }
                  onRemove={() => handleRemoveExcelColumnName(index)}
                />
              ))}
            </div>

            <div className="flex w-full justify-end">
              <Text
                variant="semibold"
                type="subBody"
                className="hover:text-linear-gradient-main2-color cursor-pointer text-Primary-Color"
                value="Add"
                onClick={handleAddExcelColumnName}
              />
            </div>
          </div>

          <div className="mt-1 flex h-[1px] w-full bg-neutral-200" />

          <div className="flex h-[119px] flex-col gap-1 pt-1">
            <Text
              type="subBody"
              variant="medium"
              className="pl-1 text-Tertiary-Color"
            >
              When to use
            </Text>

            <TextArea
              value={text}
              placeholder="Describe when the tool will be used"
              className={cn('h-[82px]')}
              maxLength={1000}
              isError={errorText}
              onChange={(e) => {
                setErrorText(false)
                setIsChange(true)
                setText(e)
              }}
              onBlur={() => {
                if (text.trim() !== text) setText(text.trim())
              }}
            />
          </div>
        </div>

        <div className="flex w-full justify-end">
          <Button
            className="px-[16px]"
            type="primary"
            size="small"
            onClick={handleSave}
            text="Save"
          />
        </div>
      </div>
    </BaseModal>
  )
}

export default ModalToolParametersExcel
