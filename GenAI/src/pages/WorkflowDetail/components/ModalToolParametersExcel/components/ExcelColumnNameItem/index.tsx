import { useState } from 'react'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import { colors } from '@/theme'
import clsx from 'clsx'

interface Props {
  columnName?: string
  onChangeExcelColumnName: (value: string) => void
  onRemove: () => void
}

const ExcelColumnNameItem = ({
  columnName,
  onChangeExcelColumnName,
  onRemove,
}: Props) => {
  const [hovered, setHovered] = useState(false)

  return (
    <div
      className="flex w-full cursor-pointer items-center gap-[2px]"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <IconButton
        className={clsx('opacity-0', hovered && 'opacity-100')}
        nameIcon="Close2"
        sizeIcon={16}
        colorIcon={colors.neutral[300]}
        hoverColor={colors['Primary-Color']}
        onClick={onRemove}
      />
      <Input
        classNameInputWrapper="w-full"
        value={columnName}
        placeholder="Type in column name"
        maxLength={255}
        onBlur={() => onChangeExcelColumnName(columnName?.trim() ?? '')}
        onChange={(e) => onChangeExcelColumnName(e.target.value)}
      />
    </div>
  )
}

export default ExcelColumnNameItem
