/* eslint-disable max-len */
import {
  WorkflowPubicResponse,
  processingWorkflowProcessingWorkflowAiGenerateApi,
  workflowGenerateConversationWorkflowApi,
} from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Message from '@/components/Message'
import TextArea from '@/components/TextArea'
import { HTTP_STATUS_CODE } from '@/constants'
import { EWorkflowType } from '@/pages/Workflows/const'
import { colors } from '@/theme'
import { memo, useState } from 'react'
import ModalLoadingAiModel from '../ModalLoadingAiModel'

import './styles.scss'

const ModalAi = ({
  isOpen,
  onClose,
  onDone,
  defaultText,
  updateText,
  workflowType,
}: IModalAiProps) => {
  const [loading, setLoading] = useState(false)

  const doneHandler = async () => {
    try {
      setLoading(true)

      const service =
        workflowType === EWorkflowType.conversation
          ? workflowGenerateConversationWorkflowApi
          : processingWorkflowProcessingWorkflowAiGenerateApi

      const res = await service({
        body: {
          prompt: defaultText,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        if (workflowType === EWorkflowType.conversation) {
          onDone(res.data as any)
          onClose()
        } else {
          const tData = res?.data || ({} as any)
          const data = {
            ...tData,
            data: {
              ...tData.data,
              config: {
                ...tData?.data?.config,
                nodes: [
                  tData?.data?.config?.startNode,
                  tData?.data?.config?.endNode,
                  ...(tData?.data?.config?.nodes || []),
                ],
              },
            },
          }
          onDone(data as any)
          onClose()
        }
      } else {
        Message.error({
          message: res?.error?.detail || 'Something went wrong!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something went wrong!' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={() => !loading && onClose()}
      isPureModal
      overlayClassName="bg-transparent"
      className="modalai"
    >
      <div
        className="relative h-full w-full"
        onClick={() => !loading && onClose()}
      >
        <div
          className="absolute right-[108px] top-[94px] h-[322px] w-[286px] rounded-2xl bg-white shadow-md"
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <div className="relative flex h-full w-full flex-col py-3">
            <div className="p-3 pt-4">
              <TextArea
                placeholder="Describe how your workflow gonna be...
eg:
A workflow for a virtual dermatology clinic. The workflow involves several steps, including client check-in, diagnosis and treatment recommendation by a doctor, and payment processing by a cashier. The workflow caters to clients of different age groups, with separate paths for those above and below 18 years old."
                className="h-[246px] w-[262px]"
                value={defaultText}
                onChange={(e) => {
                  updateText && updateText(e)
                }}
                maxLength={1000}
                onBlur={() => {
                  updateText && updateText(defaultText?.trim() || '')
                }}
              />
            </div>
            <Button
              onClick={doneHandler}
              loading={loading}
              text="Generate"
              type="secondary"
              size="small"
              className="ml-auto mr-4 w-24"
              disabled={!defaultText?.trim()}
            />

            <IconButton
              nameIcon="Customize-Close"
              onClick={onClose}
              className="absolute right-[8px] top-[8px]"
              colorIcon={colors['neutral']['300']}
              hoverColor={colors['Primary-Color']}
              sizeIcon={16}
            />
          </div>
        </div>
        {loading && <ModalLoadingAiModel />}
      </div>
    </BaseModal>
  )
}

export default memo(ModalAi)

interface IModalAiProps {
  isOpen: boolean
  onClose: () => void
  onDone: (data: WorkflowPubicResponse | undefined) => void
  defaultText?: string
  updateText?: (text: string) => void
  workflowType: EWorkflowType
}
