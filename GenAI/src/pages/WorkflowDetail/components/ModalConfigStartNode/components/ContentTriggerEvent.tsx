import Text from '@/components/Text'
import Toggle from '@/components/Toggle'
import { IconOutlook } from '../icons/IconOutlook'
import { IconSlack } from '../icons/IconSlack'
import Select from '../../SelectAssignee/components/Select'
import Input from '@/components/Input'
import Icon from '@/assets/icon/Icon'
import { colors } from '@/theme'
import IconButton from '@/components/IconButton'
import Checkbox from '@/components/Checkbox'
import SettingDomain from './SettingCondition'
import { ProcessingWorkflowTrigger } from '..'
import { EDataType, IStartNodeRow } from '../const'
import { useEffect, useState } from 'react'

interface IContentTriggerEvent {
  trigger: ProcessingWorkflowTrigger
  setTrigger: React.Dispatch<React.SetStateAction<ProcessingWorkflowTrigger>>
  isOnTriggerEnvent: boolean
  setIsOnTriggerEnvent: any
  setRowsTrigger: React.Dispatch<React.SetStateAction<IStartNodeRow[]>>
  setDirtyModal: any
  errorsEmptyValue: string[]
  setErrorsEmptyValue: React.Dispatch<React.SetStateAction<string[]>>
}
export const regexEmail =
  /^(?!.*\.@)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/

const ContentTriggerEvent = ({
  trigger,
  setTrigger,
  isOnTriggerEnvent,
  setIsOnTriggerEnvent,
  setRowsTrigger,
  setDirtyModal,
  errorsEmptyValue,
  setErrorsEmptyValue,
}: IContentTriggerEvent) => {
  const [hiddenSecretId, setHiddenSecretId] = useState(true)
  const [validType, setValidType] = useState(true)
  const [validMailbox, setValidMailbox] = useState(true)
  const [validTenantId, setValidTenantId] = useState(true)
  const [validClientId, setValidClientId] = useState(true)
  const [validClientSecret, setValidClientSecret] = useState(true)
  const [validFilter, setValidFilter] = useState<'senders' | 'folders' | ''>('')

  useEffect(() => {
    if (errorsEmptyValue.length > 0) {
      setValidType(true)
      setValidMailbox(true)
      setValidTenantId(true)
      setValidClientId(true)
      setValidClientSecret(true)
      setValidFilter('')

      if (errorsEmptyValue.find((n) => n === 'type')) setValidType(false)
      if (errorsEmptyValue.find((n) => n === 'tenantId'))
        setValidTenantId(false)
      if (errorsEmptyValue.find((n) => n === 'clientId'))
        setValidClientId(false)
      if (errorsEmptyValue.find((n) => n === 'clientSecret'))
        setValidClientSecret(false)
      if (errorsEmptyValue.find((n) => n === 'mailbox')) setValidMailbox(false)

      setErrorsEmptyValue([])
    }
  }, [errorsEmptyValue])
  const changeTriggerType = (newTriggerType: 'Slack' | 'Microsoft Outlook') => {
    setDirtyModal(true)
    setTrigger((prevData) => ({
      ...prevData,
      triggerType: newTriggerType,
    }))
  }

  const changeSetting = (value: any, field: any) => {
    setDirtyModal(true)
    setTrigger((prevData) => ({
      ...prevData,
      settings: {
        ...prevData.settings,
        [field]: value,
      },
    }))
  }

  const addSenders = (value: any) => {
    setDirtyModal(true)
    setTrigger((prevData) => ({
      ...prevData,
      settings: {
        ...prevData.settings,
        senders: prevData?.settings?.senders
          ? [...prevData.settings.senders, value]
          : [value],
      },
    }))
  }

  const removeSender = (value: any) => {
    setDirtyModal(true)
    setTrigger((prevData) => ({
      ...prevData,
      settings: {
        ...prevData.settings,
        senders: prevData?.settings?.senders
          ? prevData.settings.senders.filter((_, i) => i !== value)
          : prevData?.settings?.senders,
      },
    }))
  }

  const addFolders = (value: any) => {
    setDirtyModal(true)
    setTrigger((prevData) => ({
      ...prevData,
      settings: {
        ...prevData.settings,
        folders: prevData?.settings?.folders
          ? [...prevData.settings.folders, value]
          : [value],
      },
    }))
  }

  const removeFolders = (value: any) => {
    setDirtyModal(true)
    setTrigger((prevData) => ({
      ...prevData,
      settings: {
        ...prevData.settings,
        folders: prevData?.settings?.folders
          ? prevData.settings.folders.filter((_, i) => i !== value)
          : prevData?.settings?.folders,
      },
    }))
  }

  const addRowHandler = (type: 'Recipients' | 'Subject' | 'Body') => {
    setDirtyModal(true)
    setRowsTrigger((pre) => {
      return [
        ...pre,
        {
          ...{
            paramName: type,
            description: '',
            paramType:
              type === 'Recipients' ? EDataType.array : EDataType.string,
            defaultValue: '',
          },
        },
      ]
    })
  }

  const removeRowHandler = (type: 'Recipients' | 'Subject' | 'Body') => {
    setDirtyModal(true)
    setRowsTrigger((pre) => {
      return pre.filter((n) => n.paramName !== type)
    })
  }

  const renderErrorCredential = () => {
    if (!validClientId || !validClientSecret || !validTenantId) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon
            name="vuesax-bold-info-circle"
            size={12}
            color={colors['Error-Color']}
          />

          <Text
            type="supportText"
            variant="medium"
            value="Credentials are required"
            elementType="div"
            className="text-Error-Color"
          />
        </div>
      )
    }

    if (!validMailbox) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon
            name="vuesax-bold-info-circle"
            size={12}
            color={colors['Error-Color']}
          />

          <Text
            type="supportText"
            variant="medium"
            value="Invalid email address"
            elementType="div"
            className="text-Error-Color"
          />
        </div>
      )
    }
  }

  const renderErrorSenders = () => {
    if (!(validFilter === '')) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon
            name="vuesax-bold-info-circle"
            size={12}
            color={colors['Error-Color']}
          />

          <Text
            type="supportText"
            variant="medium"
            value={`Maximum 5 ${validFilter}`}
            elementType="div"
            className="text-Error-Color"
          />
        </div>
      )
    }
  }
  const renderContentTrigger = () => {
    if (trigger?.triggerType && isOnTriggerEnvent) {
      switch (trigger?.triggerType) {
        case 'Microsoft Outlook':
          return (
            <div className="flex w-full flex-col gap-[8px] px-[8px]">
              <div className="flex w-full flex-col gap-[4px]">
                <div className="flex items-center gap-[4px] px-[4px]">
                  <Text
                    type="subBody"
                    variant="medium"
                    value="Credentials"
                    elementType="div"
                    className="text-Primary-Color"
                  />
                  {renderErrorCredential()}
                </div>

                <div className="flex w-full flex-col gap-[8px]">
                  <div className="flex w-full gap-[8px]">
                    <Input
                      isFullWidth
                      className="w-full"
                      placeholder="Paste Directory ID of your Azure Active Directory"
                      maxLength={36}
                      onChange={(e) => {
                        changeSetting(e.target.value, 'tenantId')
                      }}
                      onBlur={() => {
                        const value = trigger.settings.tenantId.trim()
                        changeSetting(value, 'tenantId')
                      }}
                      isError={!validTenantId}
                      value={trigger.settings.tenantId}
                    />
                    <Input
                      prefix={
                        <Icon
                          name="vuesax-outline-sms"
                          size={18}
                          color={!validMailbox ? '#B91C1C' : '#A8A8A8'}
                        />
                      }
                      onChange={(e) => {
                        changeSetting(e.target.value, 'mailbox')
                      }}
                      onBlur={() => {
                        const value = trigger.settings.mailbox.trim()
                        changeSetting(value, 'mailbox')

                        if (value && !regexEmail.test(value)) {
                          setValidMailbox(false)
                        } else {
                          setValidMailbox(true)
                        }
                      }}
                      isError={!validMailbox}
                      isFullWidth
                      className="w-full"
                      classNameInput={!validMailbox ? '!text-Error-Color' : ''}
                      placeholder="Type in mailbox used to catch trigger event"
                      maxLength={255}
                      value={trigger.settings.mailbox}
                    />
                  </div>

                  <div className="flex w-full gap-[8px]">
                    <Input
                      isFullWidth
                      className="w-full"
                      placeholder="Paste Client ID of your application in Azure Active Directory"
                      maxLength={36}
                      onChange={(e) => {
                        changeSetting(e.target.value, 'clientId')
                      }}
                      onBlur={() => {
                        const value = trigger.settings.clientId.trim()
                        changeSetting(value, 'clientId')
                      }}
                      value={trigger.settings.clientId}
                      isError={!validClientId}
                    />
                    <Input
                      isFullWidth
                      type={hiddenSecretId ? 'password' : 'text'}
                      className="w-full"
                      placeholder="Paste Client Secret created in your application in Azure Active Directory"
                      maxLength={128}
                      isError={!validClientSecret}
                      onChange={(e) => {
                        changeSetting(e.target.value, 'clientSecret')
                      }}
                      onBlur={() => {
                        const value = trigger.settings.clientSecret.trim()
                        changeSetting(value, 'clientSecret')
                      }}
                      suffix={
                        <IconButton
                          nameIcon={
                            hiddenSecretId
                              ? 'vuesax-outline-eye-slash'
                              : 'vuesax-outline-eye'
                          }
                          onClick={() => {
                            setHiddenSecretId(!hiddenSecretId)
                          }}
                          hoverColor={colors['Primary-Color']}
                          colorIcon="#D4D4D4"
                          sizeIcon={14.3}
                        />
                      }
                      value={trigger.settings.clientSecret}
                    />
                  </div>
                </div>
              </div>

              <div className="flex w-full flex-col gap-[4px]">
                <Text
                  type="subBody"
                  variant="medium"
                  value="Trigger condition"
                  elementType="div"
                  className="ml-[4px] text-Primary-Color"
                />

                <div className="flex w-full items-center gap-[8px]">
                  <div className="flex w-full flex-col gap-[4px]">
                    <Text
                      type="supportText"
                      variant="medium"
                      value="Trigger when"
                      elementType="div"
                      className="ml-[4px] text-Secondary-Color"
                    />
                    <Select
                      disabled
                      className="!w-full"
                      contentClassName="!w-[470px]"
                      data={[
                        {
                          id: 'created',
                          name: 'New inbox received',
                          value: 'created',
                        },
                      ]}
                      selected={{
                        id: 'created',
                        name: 'New inbox received',
                        value: 'created',
                      }}
                      onChangeSelectedValue={(value) => {
                        console.log(value)
                      }}
                    />
                  </div>

                  <div className="flex w-full flex-col gap-[4px]">
                    <Text
                      type="supportText"
                      variant="medium"
                      value="Trigger required"
                      elementType="div"
                      className="ml-[4px] text-Secondary-Color"
                    />
                    <div className="flex w-full items-center justify-between rounded-[8px] border-[1px] border-border-base-icon bg-Input-Field px-[24px] py-[6px]">
                      <div className="flex items-center gap-[8px]">
                        <Text
                          type="subBody"
                          variant="medium"
                          value="Recipients"
                          elementType="div"
                          className="text-Primary-Color"
                        />
                        <Checkbox
                          type={
                            trigger.settings.recipients ? 'solid' : 'outline'
                          }
                          defaultChecked={trigger.settings.recipients}
                          onChange={(e) => {
                            changeSetting(e, 'recipients')
                            if (e) {
                              addRowHandler('Recipients')
                            } else {
                              removeRowHandler('Recipients')
                            }
                          }}
                        />
                      </div>

                      <div className="flex items-center gap-[8px]">
                        <Text
                          type="subBody"
                          variant="medium"
                          value="Mail subject"
                          elementType="div"
                          className="text-Primary-Color"
                        />
                        <Checkbox
                          type={trigger.settings.subject ? 'solid' : 'outline'}
                          defaultChecked={trigger.settings.subject}
                          onChange={(e) => {
                            changeSetting(e, 'subject')
                            if (e) {
                              addRowHandler('Subject')
                            } else {
                              removeRowHandler('Subject')
                            }
                          }}
                        />
                      </div>
                      <div className="flex items-center gap-[8px]">
                        <Text
                          type="subBody"
                          variant="medium"
                          value="Mail body"
                          elementType="div"
                          className="text-Primary-Color"
                        />
                        <Checkbox
                          type={trigger.settings.body ? 'solid' : 'outline'}
                          defaultChecked={trigger.settings.body}
                          onChange={(e) => {
                            changeSetting(e, 'body')
                            if (e) {
                              addRowHandler('Body')
                            } else {
                              removeRowHandler('Body')
                            }
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex w-full flex-col gap-[4px]">
                <div className="flex items-center gap-[4px] px-[4px]">
                  <Text
                    type="subBody"
                    variant="medium"
                    value="Filters"
                    elementType="div"
                    className="text-Primary-Color"
                  />
                  {renderErrorSenders()}
                </div>

                <div className="flex w-full items-center gap-[8px]">
                  <Select
                    disabled
                    containerClassName="pr-[10px]"
                    className="!w-[100px] !min-w-[100px]"
                    data={[
                      {
                        id: 'Senders',
                        name: 'Senders',
                        value: 'Senders',
                      },
                    ]}
                    selected={{
                      id: 'Senders',
                      name: 'Senders',
                      value: 'Senders',
                    }}
                    onChangeSelectedValue={(value) => {
                      console.log(value)
                    }}
                  />

                  <SettingDomain
                    validate={regexEmail}
                    domains={trigger.settings.senders}
                    addDomain={addSenders}
                    deleteDomain={removeSender}
                    maxDomain={5}
                    setValidFilter={setValidFilter}
                    placeholder="Filter email sent from one of added sender’s emails, press “Enter” or comma to complete adding"
                  />
                </div>

                <div className="flex w-full items-center gap-[8px]">
                  <Select
                    disabled
                    containerClassName="pr-[10px]"
                    className="!w-[100px] !min-w-[100px]"
                    data={[
                      {
                        id: 'Folders',
                        name: 'Folders',
                        value: 'Folders',
                      },
                    ]}
                    selected={{
                      id: 'Folders',
                      name: 'Folders',
                      value: 'Folders',
                    }}
                    onChangeSelectedValue={(value) => {
                      console.log(value)
                    }}
                  />

                  <SettingDomain
                    domains={trigger.settings.folders}
                    addDomain={addFolders}
                    deleteDomain={removeFolders}
                    maxDomain={5}
                    setValidFilter={setValidFilter}
                    placeholder="Filter email sent to folders by typing in folder’s display name, press “Enter” or comma to complete"
                  />
                </div>
              </div>
            </div>
          )

        default:
          break
      }
    }
  }

  const changeStateTriggerEvent = (state: boolean) => {
    setDirtyModal(true)
    setIsOnTriggerEnvent(state)
  }

  const generateIcon = (type: 'Microsoft Outlook' | 'Slack') => {
    switch (type) {
      case 'Microsoft Outlook':
        return <IconOutlook />
      case 'Slack':
        return <IconSlack />
      default:
        break
    }
  }
  return (
    <div className="flex w-full flex-col gap-[8px] px-8">
      <div className="flex w-full items-center justify-between gap-[12px]">
        <div className="flex flex-col">
          <Text
            type="body"
            variant="medium"
            value="Trigger event"
            elementType="div"
            className="text-Primary-Color"
          />
          <Text
            type="subBody"
            variant="regular"
            value="Activate the workflow when a specific events within platforms such as Slack, Outlook happens"
            elementType="div"
            className="text-Secondary-Color"
          />
        </div>

        <Toggle
          defaultValue={isOnTriggerEnvent}
          onChange={changeStateTriggerEvent}
        />
      </div>

      {isOnTriggerEnvent && (
        <div className="flex w-full items-center justify-center p-[4px]">
          <Select
            className="!w-full"
            contentClassName="!w-[1024px]"
            itemClassName="!gap-[12px]"
            placeholder="Select application"
            data={[
              {
                id: 'Microsoft Outlook',
                name: 'Microsoft Outlook',
                value: 'Microsoft Outlook',
                icon: <IconOutlook />,
              },
              {
                id: 'Slack',
                name: 'Slack',
                value: 'Slack',
                icon: <IconSlack />,
              },
            ]}
            selected={
              trigger?.triggerType
                ? {
                    id: trigger?.triggerType,
                    name: trigger?.triggerType,
                    value: trigger?.triggerType,
                  }
                : undefined
            }
            icon={
              trigger?.triggerType ? (
                <div className="mr-[12px]">
                  {generateIcon(trigger?.triggerType)}
                </div>
              ) : undefined
            }
            error={!validType}
            onChangeSelectedValue={(value) => {
              setValidType(true)
              changeTriggerType(value.value)
            }}
          />
        </div>
      )}

      {renderContentTrigger()}
    </div>
  )
}

export default ContentTriggerEvent
