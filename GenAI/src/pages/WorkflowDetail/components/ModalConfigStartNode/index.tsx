import BaseModal from '@/components/BaseModal'
import Text from '@/components/Text'
import clsx from 'clsx'
import { SetStateAction, memo, useEffect, useState } from 'react'
import {
  DATA_TYPES,
  DEFAULT_START_NODE_ROWS,
  EDataType,
  IStartNodeRow,
  IStartNodeRowError,
} from './const'
import Input from '@/components/Input'
import Select from '../SelectAssignee/components/Select'
import IconButton from '@/components/IconButton'
import { colors } from '@/theme'
import { MessageDialog } from '@/components/DialogMessage'
import Toggle from '@/components/Toggle'
import ContentTriggerEvent, {
  regexEmail,
} from './components/ContentTriggerEvent'
import Icon from '@/assets/icon/Icon'

const DEFAULT_ROWS_STATE: SetStateAction<IStartNodeRow[]> = [
  DEFAULT_START_NODE_ROWS,
]
const INIT_SETTINH_OUTLOOK = {
  triggerType: null,
  settings: {
    changeType: 'created',
    tenantId: '',
    clientId: '',
    clientSecret: '',
    mailbox: '',
    subject: true,
    body: true,
    recipients: true,
    senders: [],
    folders: [],
  },
}
type ProcessingOutlookTriggerSettings = {
  changeType?: string
  tenantId: string
  clientId: string
  clientSecret: string
  mailbox: string
  subject?: boolean
  body?: boolean
  recipients?: boolean
  senders?: Array<string> | null
  folders?: Array<string> | null
}
export type ProcessingWorkflowTrigger = {
  triggerType: 'Slack' | 'Microsoft Outlook' | null
  settings: ProcessingOutlookTriggerSettings
}
const ModalConfigStartNode = ({
  handleCloseModal,
  isOpen,
  onDone,
  defaultRows,
  allowFileUpload,
  triggerOn,
  defaultSettings,
}: IModalConfigStartNode) => {
  const [rows, setRows] = useState<IStartNodeRow[]>(DEFAULT_ROWS_STATE)
  const [rowsTrigger, setRowsTrigger] = useState<IStartNodeRow[]>([])
  const [focused, setFocused] = useState<number>(-1)
  const [errors, setErrors] = useState<IStartNodeRowError[]>([])
  const [errorsDuplicate, setErrorsDuplicate] = useState<boolean>(false)
  const [isDirtyModal, setDirtyModal] = useState<boolean>(false)
  const [isOnFileAttach, setIsOnFileAttach] = useState<boolean>(false)
  const [isOnTriggerEnvent, setIsOnTriggerEnvent] = useState<boolean>(true)
  const [trigger, setTrigger] =
    useState<ProcessingWorkflowTrigger>(INIT_SETTINH_OUTLOOK)

  const [errorsEmptyValue, setErrorsEmptyValue] = useState<string[]>([])

  useEffect(() => {
    if (!isOpen) {
      setRows(DEFAULT_ROWS_STATE)
      setErrors([])
      setErrorsDuplicate(false)
      setTrigger(INIT_SETTINH_OUTLOOK)
      setIsOnTriggerEnvent(false)
    } else {
      const settingsDefault: any = defaultSettings
      if (allowFileUpload !== undefined) setIsOnFileAttach(allowFileUpload)

      if (triggerOn !== undefined) {
        setIsOnTriggerEnvent(triggerOn)
      } else {
        setIsOnTriggerEnvent(true)
      }

      if (settingsDefault) {
        setTrigger(settingsDefault)
      }

      if (settingsDefault?.triggerType === 'outlook') {
        settingsDefault.triggerType = 'Microsoft Outlook'
      } else if (settingsDefault?.triggerType === 'slack') {
        settingsDefault.triggerType = 'Slack'
      }

      if (defaultRows && defaultRows.length > 0) {
        if (triggerOn && settingsDefault?.triggerType === 'Microsoft Outlook') {
          let rowsParams = defaultRows
          const rowsTriggers = []

          if (settingsDefault.settings.recipients) {
            rowsParams = rowsParams.filter((n) => n.paramName !== 'Recipients')

            const value = defaultRows.find((n) => n.paramName === 'Recipients')
            if (value !== undefined) rowsTriggers.push(value)
          }
          if (settingsDefault.settings.subject) {
            rowsParams = rowsParams.filter((n) => n.paramName !== 'Subject')

            const value = defaultRows.find((n) => n.paramName === 'Subject')
            if (value !== undefined) rowsTriggers.push(value)
          }
          if (settingsDefault.settings.body) {
            rowsParams = rowsParams.filter((n) => n.paramName !== 'Body')

            const value = defaultRows.find((n) => n.paramName === 'Body')
            if (value !== undefined) rowsTriggers.push(value)
          }
          setRowsTrigger(rowsTriggers)
          setRows(rowsParams)
        } else {
          setRows(defaultRows)
        }
      } else {
        setRows(DEFAULT_ROWS_STATE)
      }
    }
  }, [isOpen, defaultRows])

  useEffect(() => {
    if (!isOnTriggerEnvent || trigger.triggerType !== 'Microsoft Outlook') {
      setRowsTrigger([])
    } else {
      if (trigger.triggerType === 'Microsoft Outlook') {
        const rowTrigger = []
        if (trigger.settings.recipients)
          rowTrigger.push({
            paramName: 'Recipients',
            description: '',
            paramType: EDataType.array,
            defaultValue: '',
          })

        if (trigger.settings.subject)
          rowTrigger.push({
            paramName: 'Subject',
            description: '',
            paramType: EDataType.string,
            defaultValue: '',
          })
        if (trigger.settings.body)
          rowTrigger.push({
            paramName: 'Body',
            description: '',
            paramType: EDataType.string,
            defaultValue: '',
          })

        setRowsTrigger(rowTrigger)
      }
    }
  }, [isOnTriggerEnvent, trigger?.triggerType])
  const addRowHandler = () => {
    setDirtyModal(true)
    setRows((pre) => {
      return [...pre, { ...DEFAULT_START_NODE_ROWS }]
    })
  }

  const onSave = () => {
    let filteredRows = rows.filter(
      (row) =>
        row.paramName !== '' ||
        row.paramType !== EDataType.none ||
        row.description !== '' ||
        row.defaultValue
    )
    if (
      isOnTriggerEnvent &&
      trigger.triggerType === 'Microsoft Outlook' &&
      filteredRows.length === 0 &&
      rows.length > 0 &&
      !rowsTrigger.length
    ) {
      filteredRows = [rows[0]]
    }
    setRows(filteredRows)

    let duplicateValue = false
    const newErrors = filteredRows.map((row) => {
      const isDuplicate =
        filteredRows.filter((item) => item.paramName === row.paramName).length >
          1 ||
        (isOnTriggerEnvent &&
          trigger.triggerType === 'Microsoft Outlook' &&
          rowsTrigger.filter((n) => n.paramName === row.paramName).length > 0)

      if (isDuplicate) duplicateValue = true
      return {
        paramName: row.paramName === '' || isDuplicate,
        paramType: row.paramType === EDataType.none,
        defaultValue:
          isOnTriggerEnvent && trigger.triggerType === 'Microsoft Outlook'
            ? !row.defaultValue
            : false,
      }
    })

    setErrorsDuplicate(duplicateValue)
    setErrors(newErrors)
    const isEmptyValue = []

    if (isOnTriggerEnvent) {
      if (!trigger.triggerType) isEmptyValue.push('type')
      if (trigger.triggerType === 'Microsoft Outlook') {
        if (!trigger.settings.tenantId) isEmptyValue.push('tenantId')
        if (!trigger.settings.clientId) isEmptyValue.push('clientId')
        if (!trigger.settings.clientSecret) isEmptyValue.push('clientSecret')
        if (
          !trigger.settings.mailbox ||
          !regexEmail.test(trigger.settings.mailbox)
        )
          isEmptyValue.push('mailbox')
      }
    }

    setErrorsEmptyValue(isEmptyValue)
    if (
      newErrors.filter(
        (item) =>
          item.paramName ||
          item.paramType ||
          (isOnTriggerEnvent && item.defaultValue)
      ).length === 0 &&
      (!isOnTriggerEnvent || (isOnTriggerEnvent && isEmptyValue.length === 0))
    ) {
      const formatTrigger: any = trigger

      if (isOnTriggerEnvent) {
        if (trigger?.triggerType) {
          if (trigger.triggerType === 'Microsoft Outlook') {
            formatTrigger.triggerType = 'outlook'
            formatTrigger.settings.senders = trigger.settings.senders?.filter(
              (n) => regexEmail.test(n?.trim())
            )
          } else if (trigger.triggerType === 'Slack') {
            formatTrigger.triggerType = 'slack'
          }
        }
      }

      onDone(
        rowsTrigger.concat(filteredRows),
        isOnFileAttach,
        isOnTriggerEnvent,
        formatTrigger
      )
      handleCloseModal()
    }
  }

  const onClose = () => {
    if (isDirtyModal) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          handleCloseModal()
          setDirtyModal(false)
        },
      })
    } else {
      handleCloseModal()
    }
  }

  const renderPlaceholder = (paramName: string) => {
    switch (paramName) {
      case 'Recipients':
        return 'Email of recipients, use comma to separate'
      case 'Subject':
        // eslint-disable-next-line quotes
        return "A brief summary of the email's content"
      case 'Body':
        return 'The main content of the email'
      default:
        break
    }
  }

  useEffect(() => {
    if (rows.length === 0 && rowsTrigger.length === 0) {
      setRows((pre) => {
        const newRows = [...pre]

        newRows.push({ ...DEFAULT_START_NODE_ROWS })
        return newRows
      })
    }
  }, [rows, rowsTrigger])

  return (
    <BaseModal
      isOpen={isOpen}
      title={'Start node configuration'}
      subTitle={
        'Configure input params & event to trigger on to activate your automation process'
      }
      isShowCloseButton={true}
      hasCancelButton={false}
      onClose={onClose}
      onAgree={onSave}
      bodyClassName="!px-3"
    >
      <div
        className={clsx(
          isOnTriggerEnvent && trigger.triggerType === 'Microsoft Outlook'
            ? 'w-[1102px]'
            : 'w-[1024px]',
          'flex flex-col gap-[4px] rounded-[20px] border border-neutral-200 bg-white pb-8 pt-5'
        )}
      >
        <div className="flex w-full flex-col gap-[8px]">
          <div className="flex items-center gap-[4px]">
            <Text
              type="body"
              variant="medium"
              value="API configuration"
              elementType="div"
              className="pl-8 text-Primary-Color"
            />

            {errors.find(
              (n) => n?.paramName || n?.paramType || n?.defaultValue
            ) && (
              <div className="flex items-center gap-[4px]">
                <Icon
                  name="vuesax-bold-info-circle"
                  size={12}
                  color={colors['Error-Color']}
                />

                <Text
                  type="supportText"
                  variant="medium"
                  value={
                    errors.find(
                      (n) =>
                        (n?.paramName && !errorsDuplicate) ||
                        n?.paramType ||
                        n?.defaultValue
                    )
                      ? 'Parameters are required'
                      : 'Duplicated param name'
                  }
                  elementType="div"
                  className="text-Error-Color"
                />
              </div>
            )}
          </div>

          <div className="flex w-fit flex-col gap-[8px] pl-[8px]">
            <div className="flex w-full flex-col">
              <Text
                type="body"
                variant="medium"
                value="Parameters"
                elementType="div"
                className="px-10 text-Primary-Color"
              />
              <Text
                type="subBody"
                variant="regular"
                value="Define all parameters required for successful API calls along with their default values"
                elementType="div"
                className="px-10 text-Secondary-Color"
              />
            </div>

            <div className="genai-scrollbar flex max-h-[156px] w-fit flex-col gap-[4px] overflow-auto p-[4px]">
              {rowsTrigger.map((row, index) => (
                <div
                  key={index}
                  className="flex gap-2 pl-4"
                  onMouseEnter={() => setFocused(index)}
                  onMouseLeave={() => setFocused(-1)}
                >
                  <div className="flex w-4 items-center"></div>
                  <Input
                    disabled
                    key={`${index}-name`}
                    className="pointer-events-none !w-[240px]"
                    value={row.paramName}
                    placeholder="Param name"
                    onChange={(e) => {
                      setRowsTrigger((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          paramName: e.target.value.replaceAll(' ', ''),
                        }
                        return newRows
                      })

                      setDirtyModal(true)
                    }}
                    maxLength={255}
                  />
                  <Input
                    key={`${index}-description`}
                    className="pointer-events-none !w-[316.5px]"
                    value={row.description}
                    placeholder={renderPlaceholder(row.paramName)}
                    onChange={(e) => {
                      setRowsTrigger((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          description: e.target.value,
                        }
                        return newRows
                      })
                      setDirtyModal(true)
                    }}
                    maxLength={255}
                    onBlur={() => {
                      setRowsTrigger((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          description: newRows[index].description.trim(),
                        }
                        return newRows
                      })
                    }}
                  />
                  <Input
                    key={`${index}-value`}
                    className="pointer-events-none !w-[316.5px]"
                    value={row.defaultValue}
                    placeholder="Enter value"
                    disabled
                    onChange={(e) => {
                      setRowsTrigger((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          defaultValue: e.target.value,
                        }
                        return newRows
                      })
                      setDirtyModal(true)
                    }}
                    maxLength={255}
                  />
                  <Select
                    key={`${index}-data-type`}
                    className="pointer-events-none !w-[120px]"
                    contentClassName="!w-[120px]"
                    placeholder="Data type"
                    data={DATA_TYPES}
                    selected={DATA_TYPES.find(
                      (item) => item.id === row.paramType
                    )}
                    disabled
                    onChangeSelectedValue={(value) => {
                      setRowsTrigger((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          paramType: value.id,
                        }
                        return newRows
                      })

                      setDirtyModal(true)
                    }}
                  />
                </div>
              ))}

              {rows.map((row, index) => (
                <div
                  key={index}
                  className="flex gap-2 pl-4"
                  onMouseEnter={() => setFocused(index)}
                  onMouseLeave={() => setFocused(-1)}
                >
                  <div className="flex w-4 items-center">
                    {index === focused && (
                      <IconButton
                        nameIcon="Customize-Close"
                        sizeIcon={16}
                        colorIcon={colors['neutral']['300']}
                        hoverColor={colors['Primary-Color']}
                        key={`${index}-delete`}
                        className="my-auto"
                        onClick={() => {
                          setDirtyModal(true)
                          setRows((pre) => {
                            const newRows = [...pre]
                            newRows.splice(index, 1)
                            if (
                              newRows.length === 0 &&
                              !(
                                trigger.triggerType === 'Microsoft Outlook' &&
                                (trigger.settings.subject ||
                                  trigger.settings.body ||
                                  trigger.settings.recipients)
                              )
                            ) {
                              newRows.push({ ...DEFAULT_START_NODE_ROWS })
                            }
                            return newRows
                          })
                          setErrors((pre) => {
                            const newErrors = [...pre]
                            newErrors.splice(index, 1)
                            return newErrors
                          })
                          setErrorsDuplicate(false)
                        }}
                      />
                    )}
                  </div>
                  <Input
                    key={`${index}-name`}
                    className="!w-[240px]"
                    value={row.paramName}
                    placeholder="Param name"
                    onChange={(e) => {
                      setRows((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          paramName: e.target.value.replaceAll(' ', ''),
                        }
                        return newRows
                      })

                      setDirtyModal(true)
                    }}
                    maxLength={255}
                    isError={errors[index]?.paramName}
                  />
                  <Input
                    key={`${index}-description`}
                    className={
                      isOnTriggerEnvent &&
                      trigger.triggerType === 'Microsoft Outlook'
                        ? '!w-[316.5px]'
                        : '!w-[525px]'
                    }
                    value={row.description}
                    placeholder="Description"
                    onChange={(e) => {
                      setRows((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          description: e.target.value,
                        }
                        return newRows
                      })
                      setDirtyModal(true)
                    }}
                    maxLength={255}
                    onBlur={() => {
                      setRows((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          description: newRows[index].description.trim(),
                        }
                        return newRows
                      })
                    }}
                  />
                  {isOnTriggerEnvent &&
                    trigger.triggerType === 'Microsoft Outlook' && (
                      <Input
                        key={`${index}-value`}
                        className="!w-[316.5px]"
                        value={row.defaultValue}
                        placeholder="Enter value"
                        onChange={(e) => {
                          setRows((pre) => {
                            const newRows = [...pre]
                            newRows[index] = {
                              ...newRows[index],
                              defaultValue: e.target.value,
                            }
                            return newRows
                          })
                          setDirtyModal(true)
                        }}
                        maxLength={255}
                        onBlur={() => {
                          setRows((pre) => {
                            const newRows = [...pre]
                            newRows[index] = {
                              ...newRows[index],
                              defaultValue: newRows[index].defaultValue
                                ? newRows[index].defaultValue.trim()
                                : newRows[index].defaultValue,
                            }
                            return newRows
                          })
                        }}
                        isError={errors[index]?.defaultValue}
                      />
                    )}

                  <Select
                    key={`${index}-data-type`}
                    className={
                      isOnTriggerEnvent &&
                      trigger.triggerType === 'Microsoft Outlook'
                        ? '!w-[120px]'
                        : '!w-[150px]'
                    }
                    contentClassName={
                      isOnTriggerEnvent ? '!w-[120px]' : '!w-[150px]'
                    }
                    placeholder="Data type"
                    data={DATA_TYPES}
                    selected={DATA_TYPES.find(
                      (item) => item.id === row.paramType
                    )}
                    onChangeSelectedValue={(value) => {
                      setRows((pre) => {
                        const newRows = [...pre]
                        newRows[index] = {
                          ...newRows[index],
                          paramType: value.id,
                        }
                        return newRows
                      })

                      setDirtyModal(true)
                    }}
                    error={errors[index]?.paramType}
                  />
                </div>
              ))}
            </div>
            <div
              className="mt-[-2px] h-[18px] cursor-pointer self-end bg-Main2-02 bg-clip-text px-2 leading-[18px] text-Primary-Color hover:text-transparent"
              onClick={addRowHandler}
            >
              <Text type="subBody" variant="semibold">
                Add
              </Text>
            </div>

            <div className="flex w-full flex-col gap-[8px] pl-10 pr-4">
              <div className="flex w-full items-center justify-between gap-[12px]">
                <div className="flex flex-col">
                  <Text
                    type="body"
                    variant="medium"
                    value="File attachment"
                    elementType="div"
                    className="text-Primary-Color"
                  />
                  <Text
                    type="subBody"
                    variant="regular"
                    value="Enables file transmission during API calls to execute workflows."
                    elementType="div"
                    className="text-Secondary-Color"
                  />
                </div>

                <Toggle
                  defaultValue={isOnFileAttach}
                  onChange={() => {
                    setDirtyModal(true)
                    setIsOnFileAttach(!isOnFileAttach)
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <ContentTriggerEvent
          trigger={trigger}
          setTrigger={setTrigger}
          isOnTriggerEnvent={isOnTriggerEnvent}
          setIsOnTriggerEnvent={setIsOnTriggerEnvent}
          setRowsTrigger={setRowsTrigger}
          setDirtyModal={setDirtyModal}
          errorsEmptyValue={errorsEmptyValue}
          setErrorsEmptyValue={setErrorsEmptyValue}
        />
      </div>
    </BaseModal>
  )
}

interface IModalConfigStartNode {
  isOpen: boolean
  handleCloseModal: () => void
  onDone: (
    rows: IStartNodeRow[],
    isOnFileAttach: boolean,
    isOnTriggerEnvent: boolean,
    trigger: ProcessingWorkflowTrigger
  ) => void
  defaultRows?: IStartNodeRow[]
  allowFileUpload: boolean | undefined
  triggerOn: boolean | undefined
  defaultSettings: ProcessingWorkflowTrigger | null | undefined
}

export default memo(ModalConfigStartNode)
