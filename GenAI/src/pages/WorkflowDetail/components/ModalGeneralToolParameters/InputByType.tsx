import Input from '@/components/Input'
import { memo, useMemo } from 'react'
import Select from '../SelectAssignee/components/Select'
import { BooleanParamItems } from '../ModalConfigStartNode/const'

export enum EParamType {
  str = 'str',
  int = 'int',
  float = 'float',
  bool = 'bool',
}

const InputByType = ({
  onChange,
  onPressEnter,
  type,
  value,
  placeholder,
  onBlur,
}: IInputByType) => {
  const changeHandler = (e: { target: { value: any } }) => {
    const value = e.target.value
    if (type === EParamType.int) {
      const tVal = value.replace(/[^0-9]/g, '')
      onChange(tVal)
    } else if (type === EParamType.float) {
      const tVal = value.replace(/[^0-9.]/g, '')
      onChange(tVal)
    } else {
      onChange(value)
    }
  }

  const selectedValue = useMemo(() => {
    return BooleanParamItems.find((item) => item.value === value)
  }, [value])

  if (type === EParamType.bool) {
    return (
      <Select
        className="min-w-[330px]"
        overlayClassName="w-[var(--button-width)]"
        placeholder="Select here"
        data={BooleanParamItems}
        selected={selectedValue}
        getPopupContainer={(triggerNode) => triggerNode?.parentNode}
        onChangeSelectedValue={(item) => {
          onChange(item.value)
        }}
      />
    )
  }

  return (
    <Input
      value={value as string}
      onChange={changeHandler}
      onPressEnter={onPressEnter}
      onBlur={onBlur}
      maxLength={3000}
      isFullWidth
      placeholder={placeholder}
    />
  )
}

interface IInputByType {
  value: string | boolean
  type: EParamType
  onChange: (value: string | boolean) => void
  onPressEnter?: () => void
  onBlur?: () => void
  placeholder?: string
}

export default memo(InputByType)
