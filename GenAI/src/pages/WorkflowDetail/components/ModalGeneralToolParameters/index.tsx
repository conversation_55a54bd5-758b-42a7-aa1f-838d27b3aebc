import {
  ToolEnvironmentVariable,
  ToolInputParameter,
  toolsGetToolParametersApi,
} from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import { GeneralToolParamConfigurationData } from '@/components/ModalAddToolkit/types'
import Spin from '@/components/Spin'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { colors } from '@/theme'
import { isNil } from 'lodash'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import InputByType, { EParamType } from './InputByType'

interface IToolEnvironmentVariable extends ToolEnvironmentVariable {
  feIsSecret: boolean
}

const ModalGeneralToolParameters = ({
  onClose,
  onSave,
  toolId,
  generalToolParamConfigurations,
}: IModalGeneralToolParameters) => {
  const [params, setParams] = useState<ToolInputParameter[]>([])
  const [envs, setEnvs] = useState<IToolEnvironmentVariable[]>([])
  const [loading, setLoading] = useState(true)
  const [initializing, setInitializing] = useState(true)

  const fetchInfo = useCallback(async () => {
    try {
      setLoading(true)
      const res = await toolsGetToolParametersApi({
        path: {
          tool_id: toolId,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const params = res.data?.data?.input_parameters || []
        const mappingParams = params.map((param) => {
          const defaultValue: any =
            generalToolParamConfigurations?.inputParameters
              ? generalToolParamConfigurations?.inputParameters?.find(
                  (genParam) => genParam.name === param.name
                )?.value
              : res.data?.data?.default_parameters?.find(
                  (genParam) => genParam.name === param.name
                )?.value

          if (defaultValue || defaultValue === false) {
            param.default_value = defaultValue
          }

          return param
        })
        setParams(mappingParams)

        const envs = res.data?.data?.environment_variables || []
        const mapping = envs.map((env) => {
          const defaultValue =
            generalToolParamConfigurations?.environmentVariables
              ? generalToolParamConfigurations?.environmentVariables?.find(
                  (genEnv) => genEnv.name === env.name
                )?.value
              : res.data?.data?.default_envs?.find(
                  (genEnv) => genEnv.name === env.name
                )?.value

          return {
            ...env,
            feIsSecret: !env.is_secret,
            default_value: defaultValue,
          }
        })
        setEnvs(mapping)
      } else {
        setParams([])
        setEnvs([])
      }
    } catch (error) {
      console.log(error)
      setParams([])
      setEnvs([])
    } finally {
      setLoading(false)
    }
  }, [toolId, generalToolParamConfigurations])

  useEffect(() => {
    setInitializing(true)
    setTimeout(() => {
      setInitializing(false)
    }, 500)
    fetchInfo()
  }, [])

  const body = useMemo(() => {
    if (loading || initializing) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <Spin size="larger" />
        </div>
      )
    }

    if (!params?.length && !envs?.length) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <EmptyData
            type="01"
            size="small"
            content="No parameters added to your code function"
          />
        </div>
      )
    }

    return (
      <div className="genai-scrollbar h-full w-full overflow-y-scroll">
        {!!params?.length && (
          <div className="mt-2 w-full">
            <Text
              type="body"
              variant="semibold"
              className="px-1 text-Primary-Color"
              elementType="div"
            >
              Input parameters
            </Text>
            <div className="mt-2 flex w-full flex-col gap-2 px-2">
              {params.map((param, index) => (
                <div key={index} className="flex w-full flex-col gap-1">
                  <div className="flex items-center gap-1 px-1">
                    <Text
                      type="subBody"
                      variant="medium"
                      value={param.name}
                      className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                      elementType="div"
                      ellipsis
                    />
                    {param.description && (
                      <IconButton
                        nameIcon="Bold-EssentionalUI-InfoCircle"
                        colorIcon={colors['neutral']['300']}
                        hoverColor={colors['Primary-Color']}
                        sizeIcon={14}
                        tooltipText={param.description || ''}
                      />
                    )}
                  </div>
                  <InputByType
                    value={param.default_value ?? ''}
                    type={param.type as EParamType}
                    onChange={(val) => {
                      const newParams = [...params]
                      newParams[index].default_value = val as string
                      setParams(newParams)
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
        {!!envs?.length && (
          <div className="mt-2 w-full">
            <Text
              type="body"
              variant="semibold"
              className="px-1 text-Primary-Color"
              elementType="div"
            >
              Environment variables
            </Text>
            <div className="mt-2 flex w-full flex-col gap-2 px-2 py-1">
              {envs.map((val, index) => (
                <div key={index} className="flex w-full flex-col gap-1">
                  <div className="flex items-center gap-1 px-1">
                    <Text
                      type="subBody"
                      variant="medium"
                      value={val.name}
                      className="!w-fit max-w-full overflow-hidden text-Tertiary-Color"
                      elementType="div"
                      ellipsis
                    />
                    {val.description && (
                      <IconButton
                        nameIcon="Bold-EssentionalUI-InfoCircle"
                        colorIcon={colors['neutral']['300']}
                        hoverColor={colors['Primary-Color']}
                        sizeIcon={14}
                        tooltipText={val.description || ''}
                      />
                    )}
                  </div>
                  <Input
                    maxLength={3000}
                    value={val.default_value || ''}
                    onChange={(e) => {
                      const newEnvs = [...envs]
                      newEnvs[index].default_value = e.target.value
                      setEnvs(newEnvs)
                    }}
                    onBlur={() => {
                      const newEnvs = [...envs]
                      newEnvs[index].default_value = val.default_value?.trim()
                      setEnvs(newEnvs)
                    }}
                    suffix={
                      val.is_secret && (
                        <IconButton
                          nameIcon={
                            val.feIsSecret
                              ? 'vuesax-outline-eye'
                              : 'vuesax-outline-eye-slash'
                          }
                          colorIcon={colors['neutral']['300']}
                          hoverColor={colors['Primary-Color']}
                          sizeIcon={16}
                          className="select-none"
                          onClick={() => {
                            const newEnvs = [...envs]
                            newEnvs[index].feIsSecret = !val.feIsSecret
                            setEnvs(newEnvs)
                          }}
                        />
                      )
                    }
                    secure={!val.feIsSecret}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }, [loading, initializing, params, envs])

  return (
    <BaseModal isOpen onClose={onClose} isPureModal>
      <div className="relative flex h-[568px] w-[976px] flex-col rounded-[20px] bg-white p-3 shadow-md">
        <Text
          type="subheading"
          variant="medium"
          className="px-1 text-Primary-Color"
          elementType="div"
        >
          Tool Parameters
        </Text>
        {body}
        <Button
          type="primary"
          className="ml-auto mt-3"
          disabled={
            loading || initializing || (!params?.length && !envs?.length)
          }
          onClick={() =>
            onSave?.({
              inputParameters: params
                .filter((param) => {
                  if (param?.default_value === '') {
                    return false
                  }
                  return !isNil(param?.default_value)
                })
                .map((param) => ({
                  name: param.name,
                  value: param.default_value,
                })),
              environmentVariables: envs
                .filter((env) => env?.default_value)
                .map((env) => ({
                  name: env.name,
                  value: env?.default_value,
                })),
            })
          }
          size="small"
        >
          Save
        </Button>

        <IconButton
          className="absolute right-[10px] top-[10px] z-30 border-0 bg-transparent p-0 duration-300 hover:bg-neutral-200"
          nameIcon="x-close"
          sizeIcon={16}
          colorIcon={colors.neutral[500]}
          onClick={onClose}
        />
      </div>
    </BaseModal>
  )
}

interface IModalGeneralToolParameters {
  onClose: () => void
  onSave: ({
    inputParameters,
    environmentVariables,
  }: GeneralToolParamConfigurationData) => void
  toolId: string
  generalToolParamConfigurations?: GeneralToolParamConfigurationData
}

export default memo(ModalGeneralToolParameters)
