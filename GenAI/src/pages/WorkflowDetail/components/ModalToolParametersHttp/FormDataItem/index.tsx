import { useCallback, useRef, useState } from 'react'
import axios from 'axios'
import clsx from 'clsx'
import Input from '@/components/Input'
import Segmented from '@/components/SegmentedNew'
import { IField, findDuplicateKeys } from '..'
import FileSelect from '@/components/FileSelect'
import { FormattedFileUploadProps } from '@/components/FileSelect/components/FileUpload'
import { workflowUploadHttpRequestFormDataApi } from '@/apis/client'
import { HTTP_STATUS_CODE } from '@/constants'

const FIELD_TYPE_LIST = [
  {
    label: 'Text',
    value: 'string',
  },
  {
    label: 'File',
    value: 'file',
  },
]

interface FormDataItemProps {
  index: number
  body: any
  focused: any
  setFocused: any
  validateValue: any
  setValidateValue: any
  setValidate: any
  removeParams: any
  item: any
  validate: any
  validateDuplicate: any
  updateParams: (
    id: string,
    field: IField,
    section: string,
    indexToUpdate: number,
    newAuth: string | Array<any>
  ) => void
  validateDescription: any
  setValidateDescription: any
  setValidateDuplicate: any
}

const FormDataItem = ({
  index,
  body,
  item,
  focused,
  setFocused,
  validate,
  setValidate,
  validateValue,
  setValidateValue,
  validateDescription,
  setValidateDescription,
  validateDuplicate,
  removeParams,
  updateParams,
  setValidateDuplicate,
}: FormDataItemProps) => {
  const rejectPromiseController = useRef<(reason?: any) => void>()
  const [openFileDropdown, setOpenFileDropdown] = useState(false)

  const handleUploadAndSaveFile = useCallback(
    (item: any, index: number, files: FormattedFileUploadProps[]) => {
      const items = files.map(
        async (file) =>
          await new Promise((resolve, reject) => {
            if (file.error) {
              setOpenFileDropdown(true)
              resolve(file)
              return
            }

            workflowUploadHttpRequestFormDataApi({
              body: { file: file.rawFile },
              cancelToken: new axios.CancelToken(function executor(cancel) {
                // An executor function receives a cancel function as a parameter
                rejectPromiseController.current = (reason) => {
                  reject(reason)
                  cancel()
                }
              }),
            }).then((response) => {
              if (response?.code === 'ERR_CANCELED') {
                return
              }

              if (response.status === HTTP_STATUS_CODE.SUCCESS) {
                file.fileUrl = response.data?.data?.file_url
              } else {
                file.error = 'Failed to upload file'
              }
              setOpenFileDropdown(true)
              resolve(file)
            })
          })
      )

      // Upload single file
      Promise.all(items).then((values) =>
        updateParams(item.id, 'body_form_data', 'files', index, values)
      )
    },
    [updateParams]
  )

  const handleRemoveFile = useCallback(
    (
      item: any,
      index: number,
      files: FormattedFileUploadProps[],
      removedFile: FormattedFileUploadProps
    ) => {
      const items = files.filter((file) => {
        if (file.fileUrl && removedFile.fileUrl) {
          return file.fileUrl !== removedFile.fileUrl
        }

        return file !== removedFile
      })

      updateParams(item.id, 'body_form_data', 'files', index, items)
    },
    []
  )

  return (
    <div
      onMouseEnter={() =>
        setFocused({
          type: 'body',
          index,
        })
      }
      onMouseLeave={() =>
        setFocused({
          type: '',
          index: -1,
        })
      }
      className="flex items-center gap-[8px]"
    >
      <div className="flex items-center gap-[2px]">
        <div className="h-[16px] min-h-[16px] w-[16px] min-w-[16px]">
          {index === focused.index && focused.type === 'body' && (
            <div
              onClick={() => {
                setValidateValue((prevState) => ({
                  ...prevState,
                  body_form_data: prevState.body_form_data
                    .filter((body) => body !== index)
                    .map((body) => {
                      if (index < body) return (body -= 1)

                      return body
                    }),
                }))
                setValidate((prevState) => ({
                  ...prevState,
                  body_form_data: prevState.body_form_data
                    .filter((body) => body !== index)
                    .map((body) => {
                      if (index < body) return (body -= 1)

                      return body
                    }),
                }))
                setValidateDescription((prevState) => ({
                  ...prevState,
                  body_form_data: prevState.body_form_data
                    .filter((body) => body !== index)
                    .map((body) => {
                      if (index < body) return (body -= 1)

                      return body
                    }),
                }))
                setValidateDuplicate((prevState) => ({
                  ...prevState,
                  body_form_data: prevState.body_form_data
                    .filter((body) => body !== index)
                    .map((body) => {
                      if (index < body) return (body -= 1)

                      return body
                    }),
                }))
                removeParams(item.id, index, 'body_form_data')
              }}
              className="icon-close-request-config cursor-pointer"
            ></div>
          )}
        </div>

        <Segmented
          options={FIELD_TYPE_LIST}
          value={body.type}
          onChange={(value) => {
            setValidate((prop) => ({
              ...prop,
              body_form_data: [],
            }))
            setValidateValue((prop) => ({
              ...prop,
              body_form_data: [],
            }))
            setValidateDescription((prop) => ({
              ...prop,
              body_form_data: [],
            }))
            updateParams(item.id, 'body_form_data', 'type', index, value)
          }}
        />
        <Input
          onChange={(e) => {
            updateParams(
              item.id,
              'body_form_data',
              'key',
              index,
              e.target.value
            )
          }}
          onBlur={() => {
            const regex = /^[a-zA-Z0-9_-]{1,255}$/
            const value = body.key

            if (value && !regex.test(value)) {
              if (!validate.body_form_data.find((n) => n === index))
                setValidate((prevState) => ({
                  ...prevState,
                  body_form_data: [...prevState.body_form_data, index],
                }))
            } else {
              setValidate((prevState) => ({
                ...prevState,
                body_form_data: prevState.body_form_data.filter(
                  (q) => q !== index
                ),
              }))
            }

            const duplicateBodyFormData = findDuplicateKeys(item.body_form_data)
            setValidateDuplicate((prevState) => ({
              ...prevState,
              body_form_data: duplicateBodyFormData,
            }))
          }}
          isError={
            validate.body_form_data.find((n) => n === index) !== undefined ||
            validateDuplicate.body_form_data.find((n) => n === index) !==
              undefined
          }
          placeholder="Key"
          classNameInputWrapper={clsx(
            'min-w-[198px]',
            item.body_type === 'FORM_DATA' && 'ml-[6px]'
          )}
          value={body.key}
        />
      </div>

      <Input
        onChange={(e) => {
          updateParams(
            item.id,
            'body_form_data',
            'description',
            index,
            e.target.value
          )
        }}
        isError={validateDescription.body_form_data.includes(index)}
        onBlur={() => {
          const value = body.description.trim()
          if (value !== body.description)
            updateParams(item.id, 'body_form_data', 'description', index, value)
        }}
        maxLength={255}
        isFullWidth
        placeholder="Description"
        classNameInputWrapper="min-w-[250px]"
        value={body.description}
      />
      {body?.type === 'file' ? (
        <FileSelect
          inputId={`file-select-finput-${index}`}
          className="w-full"
          contentClassName="w-[200px]"
          open={openFileDropdown}
          setOpen={setOpenFileDropdown}
          selectedFiles={body?.files || []}
          isError={validateValue.body_form_data.includes(index)}
          onChangeSelectedValue={(files) => {
            // Reject previous file processing if this file is uploading
            rejectPromiseController.current?.('Reject previous file processing')
            handleUploadAndSaveFile(item, index, files)
          }}
          onRemoveSelectedValue={(file) =>
            handleRemoveFile(item, index, body.files ?? [], file)
          }
        />
      ) : (
        <Input
          onChange={(e) => {
            updateParams(
              item.id,
              'body_form_data',
              'value',
              index,
              e.target.value
            )
          }}
          isError={validateValue.body_form_data.includes(index)}
          onBlur={() => {
            const value = body.value.trim()
            if (value !== body.value)
              updateParams(item.id, 'body_form_data', 'value', index, value)
          }}
          maxLength={1000}
          isFullWidth
          placeholder="Value"
          className="w-full"
          value={body.value}
        />
      )}
    </div>
  )
}

export default FormDataItem
