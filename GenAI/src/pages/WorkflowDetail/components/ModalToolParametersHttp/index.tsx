import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { colors } from '@/theme'
import { memo, useEffect, useState } from 'react'
import RequestConfig from './RequestConfig'
import {
  HTTPRequestFormDataParameter,
  ToolHTTPRequestParameterAuthenticationTypes,
  ToolHTTPRequestParameterBodyTypes,
  ToolHTTPRequestParameterMethodTypes,
  ToolItemHTTPRequestParameter,
} from '@/apis/client'
import { MessageDialog } from '@/components/DialogMessage'
import { isArray, isObject } from 'lodash'
import { nanoid } from 'nanoid'

interface IModalToolParameters {
  isOpen: boolean
  onClose: () => void
  settingBuiltInToolsHttp?: ToolItemHTTPRequestParameter[] | undefined
  handleSettingBuiltInToolHttpByToolId: (
    toolSettings?: ToolItemHTTPRequestParameter[]
  ) => void
}

interface IHeader {
  auth: string
  token: string
  type?: string
  description?: string
}
interface IQuery {
  key: string
  value: string
  type: string
  description?: string
}
export type IBodyType = 'JSON' | 'RAW_JSON' | 'FORM_DATA'

interface IBody {
  type?: string
  key: string
  value: string
  description: string
}

interface IBodyRaw {
  type?: string
  key: string
  value?: string
  description: string
}

interface IBodyFormData {
  type?: string
  key: string
  value: string
  files: Array<any>
  description: string
  file_name?: string
  file_type?: string
  file_size?: number
}

export interface IRequestConfig {
  body_type: string
  id: any
  method: ToolHTTPRequestParameterMethodTypes
  url: string
  authentication: ToolHTTPRequestParameterAuthenticationTypes
  header?: IHeader[]
  query?: IQuery[]
  body?: IBody[]
  body_raw?: IBodyRaw[]
  body_form_data?: IBodyFormData[]
  prompt: string | null | undefined
}
export type IField = 'header' | 'query' | 'body' | 'body_raw' | 'body_form_data'

type BackupStore = Record<
  any,
  Record<
    ToolHTTPRequestParameterMethodTypes | any,
    Record<ToolHTTPRequestParameterAuthenticationTypes | any, IRequestConfig>
  >
>

const DEFAULT_CONFIG = {
  id: 1,
  method: ToolHTTPRequestParameterMethodTypes.GET,
  url: '',
  authentication: ToolHTTPRequestParameterAuthenticationTypes.BEARER_TOKEN,
  header: [
    {
      auth: 'Authorization',
      token: '',
    },
    {
      auth: '',
      token: '',
      type: 'string',
      description: '',
    },
  ],
  query: [
    {
      key: '',
      value: '',
      type: 'string',
      description: '',
    },
  ],
  body_type: 'RAW_JSON',
  body: [
    {
      type: 'string',
      key: '',
      value: '',
      description: '',
    },
  ],
  body_raw: [
    {
      type: 'object',
      key: 'body',
      value: '',
      description: '',
    },
  ],
  body_form_data: [
    {
      type: 'string',
      key: '',
      value: '',
      files: [],
      description: '',
    },
  ],
  prompt: null,
}
const formatHeader = (
  authentication: ToolHTTPRequestParameterAuthenticationTypes,
  header: any
) => {
  const headerFormat: {
    description: any
    auth: any
    token: string
  }[] = []
  header.forEach(
    (item: { description: any; auth: any; token: string }, i: number) => {
      if (i === 0 && authentication !== 'NO_AUTH') {
        const newHeader = item
        switch (authentication) {
          case 'BEARER_TOKEN':
            newHeader.token = 'Bearer '.concat(item.token)
            break
          case 'API_KEY':
            newHeader.token = 'Token '.concat(item.token)
            break
          case 'BASIC_AUTH':
            newHeader.token = 'Basic '.concat(item.token)
            break
          default:
            break
        }

        headerFormat.push(newHeader)
      } else if (item.auth) {
        headerFormat.push(item)
      }
    }
  )
  return headerFormat
}

const readerHeader = (
  authentication: ToolHTTPRequestParameterAuthenticationTypes,
  header: any
) => {
  let headerFormat: { token: string }[] = []

  if (
    authentication !== ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH &&
    header.find((n: { auth: any }) => n.auth === 'Authorization')
  ) {
    const value = header.find((n: { auth: any }) => n.auth === 'Authorization')
    if (
      value &&
      authentication ===
        ToolHTTPRequestParameterAuthenticationTypes.BEARER_TOKEN
    )
      value.token = value.token.replace('Bearer', '').trim()
    if (
      value &&
      authentication === ToolHTTPRequestParameterAuthenticationTypes.API_KEY
    )
      value.token = value.token.replace('Token', '').trim()
    if (
      value &&
      authentication === ToolHTTPRequestParameterAuthenticationTypes.BASIC_AUTH
    )
      value.token = value.token.replace('Basic', '').trim()

    headerFormat.push(value)

    headerFormat = headerFormat.concat(
      header.filter((n: { auth: any }) => n.auth !== 'Authorization')
    )
  } else {
    headerFormat = header
  }
  if (
    !header.filter((n: { auth: any }) => n.auth !== 'Authorization')?.length
  ) {
    return [
      ...headerFormat,
      {
        type: 'string',
        auth: '',
        token: '',
        description: '',
      },
    ]
  }
  return headerFormat
}
const formatArray = (array: IRequestConfig[]) => {
  return array.map((item) => {
    const header = formatHeader(item.authentication, item.header)

    const result: any = {
      method: item.method,
      url: item.url,
      authentication: item.authentication,
      headers: header.map((acc) => {
        return {
          name: acc.auth,
          value: acc.token,
          description: acc.description,
          type: 'string',
        }
      }),

      prompt: item.prompt,
    }

    if (item.method !== ToolHTTPRequestParameterMethodTypes.POST) {
      // @ts-expect-error: Unreachable code error
      result.query_parameters = item.query
        .filter((acc) => acc.key)
        .map((acc) => {
          return {
            name: acc.key,
            value: acc.value,
            description: acc.description,
            type: 'string',
          }
        })
    }

    if (
      item.method === ToolHTTPRequestParameterMethodTypes.PUT ||
      item.method === ToolHTTPRequestParameterMethodTypes.PATCH ||
      item.method === ToolHTTPRequestParameterMethodTypes.POST
    ) {
      result.body_type =
        item?.body_type ?? ToolHTTPRequestParameterBodyTypes.RAW_JSON

      if (item?.body_type === ToolHTTPRequestParameterBodyTypes.JSON) {
        result.body = item?.body?.map((acc) => {
          return {
            type: acc.type,
            name: acc.key,
            description: acc.description,
            value: acc.value,
          }
        })
      }

      if (item?.body_type === ToolHTTPRequestParameterBodyTypes.RAW_JSON) {
        result.body_raw = {
          type: 'object',
          name: 'body',
          description: item?.body_raw?.[0]?.description,
          value: item?.body_raw?.[0]?.value,
        }
      }

      if (item?.body_type === ToolHTTPRequestParameterBodyTypes.FORM_DATA) {
        result.body_form_data = item?.body_form_data?.map((acc) => {
          const body: HTTPRequestFormDataParameter = {
            type: 'string',
            name: acc.key,
            description: acc.description,
            value: acc.value,
          }

          if (acc?.type === 'file') {
            body.type = 'file'
            body.value = acc.files?.[0]?.fileUrl
            body.file_name = acc.files?.[0]?.name
            body.file_size = acc.files?.[0]?.size
          }

          return body
        })
      }
    }

    return result
  })
}

export const findDuplicateAuths = (arr: any[]) => {
  const keyMap = new Map()
  const duplicateIndexes: number[] = []

  arr.forEach((item, index) => {
    if (!keyMap.has(item.auth)) {
      keyMap.set(item.auth, [])
    }
    if (item.auth) keyMap.get(item.auth)!.push(index)
  })

  keyMap.forEach((indexes) => {
    if (indexes.length > 1) {
      duplicateIndexes.push(...indexes)
    }
  })

  return duplicateIndexes
}

export const findDuplicateKeys = (arr: any[]) => {
  const keyMap = new Map()
  const duplicateIndexes: number[] = []

  arr.forEach((item, index) => {
    if (!keyMap.has(item.key)) {
      keyMap.set(item.key, [])
    }
    if (item.key) {
      keyMap.get(item.key)!.push(index)
    }
  })

  keyMap.forEach((indexes) => {
    if (indexes.length > 1) {
      duplicateIndexes.push(...indexes)
    }
  })

  return duplicateIndexes
}
const ModalToolParametersHttp = ({
  isOpen,
  onClose,
  settingBuiltInToolsHttp,
  handleSettingBuiltInToolHttpByToolId,
}: IModalToolParameters) => {
  const [showRequestConfig, setShowRequestConfig] = useState<any | undefined>(1)
  const [requestConfig, setRequestConfig] = useState<IRequestConfig[]>([
    DEFAULT_CONFIG,
  ])
  const [backupStore, setBackupStore] = useState<BackupStore>({})

  const [error, setError] = useState<number[]>([])

  const [isChange, setIsChange] = useState(false)

  useEffect(() => {
    if (settingBuiltInToolsHttp && isOpen) {
      const request: IRequestConfig[] = []

      settingBuiltInToolsHttp.map((item, i) => {
        const requestItem: any = {
          id: i + 1,
          method: item.method,
          url: item.url,
          authentication: item.authentication,
          prompt: item.prompt,
          body_type: item?.body_type ?? 'RAW_JSON',
        }

        // Headers
        if (isArray(item?.headers)) {
          requestItem.header = readerHeader(
            item.authentication,
            item.headers?.map((header) => ({
              auth: header?.name,
              token: header?.value,
              description: header?.description,
            }))
          )
        } else {
          requestItem.header = readerHeader(item.authentication, [])
        }

        // Query parameters
        if (isArray(item?.query_parameters)) {
          if (item.query_parameters.length > 0) {
            requestItem.query = item.query_parameters?.map((query) => ({
              key: query?.name,
              value: query?.value,
              description: query?.description,
            }))
          } else {
            requestItem.query = [
              {
                key: '',
                value: '',
                description: '',
              },
            ]
          }
        } else {
          requestItem.query = []
        }

        // Body
        if (isArray(item?.body)) {
          if (item.body.length > 0) {
            requestItem.body = item.body.map((body) => ({
              key: body?.name,
              value: body?.value,
              description: body?.description,
              type: body?.type,
            }))
          } else {
            requestItem.body = [
              {
                key: '',
                value: '',
                description: '',
                type: 'string',
              },
            ]
          }
        } else {
          requestItem.body = []
        }

        // Body raw
        if (isObject(item?.body_raw)) {
          requestItem.body_raw = [
            {
              key: item.body_raw?.name,
              value: item.body_raw?.value,
              description: item.body_raw?.description,
              type: item.body_raw?.type,
            },
          ]
        } else {
          requestItem.body_raw = []
        }

        // Body form data
        if (isArray(item?.body_form_data)) {
          if (item.body_form_data.length > 0) {
            requestItem.body_form_data = item.body_form_data.map((body) => {
              const formattedBody = {
                key: body.name,
                value: body?.file_name ? '' : body?.value,
                description: body?.description,
                type: body?.file_name ? 'file' : 'string',
              }

              if (body.file_name) {
                formattedBody.files = [
                  {
                    name: body.file_name,
                    size: body.file_size,
                    fileUrl: body.value,
                  },
                ]
              }

              return formattedBody
            })
          } else {
            requestItem.body_form_data = [
              {
                key: '',
                value: '',
                description: '',
                type: 'string',
                files: [],
              },
            ]
          }
        } else {
          requestItem.body_form_data = []
        }

        request.push(requestItem)
      })

      setRequestConfig(request)
    }
  }, [settingBuiltInToolsHttp, isOpen])

  const validateValue = (value: IRequestConfig[]) => {
    const colunmError: number[] = []
    const regex = /^[a-zA-Z0-9_-]{1,255}$/
    value.map((item) => {
      if (!item.url) {
        colunmError.push(item.id)
      } else if (
        // @ts-expect-error: Unreachable code error
        item.header.find(
          (n) =>
            (!n.token && n.auth === 'Authorization') ||
            (n.auth && !regex.test(n.auth)) ||
            (n.auth !== 'Authorization' &&
              n.auth &&
              regex.test(n.auth) &&
              !n.description) ||
            (n.auth !== 'Authorization' && !n.auth && n.description) ||
            (n.auth !== 'Authorization' &&
              (!n.auth || !n.description) &&
              n.token)
        ) ||
        (item.authentication ===
          ToolHTTPRequestParameterAuthenticationTypes.BASIC_AUTH &&
          // @ts-expect-error: Unreachable code error
          item.header.find(
            (n) =>
              (!n.token.split(':')?.[0] || !n.token.split(':')?.[1]) &&
              n.auth === 'Authorization'
          ))
      ) {
        colunmError.push(item.id)
      } else if (
        item.method !== ToolHTTPRequestParameterMethodTypes.POST &&
        item.query.find((n) => n.key && !regex.test(n.key))
      ) {
        colunmError.push(item.id)
      } else if (
        item.method !== ToolHTTPRequestParameterMethodTypes.POST &&
        item.query.find((n) => n.key && regex.test(n.key) && !n.description)
      ) {
        colunmError.push(item.id)
      } else if (
        item.method !== ToolHTTPRequestParameterMethodTypes.POST &&
        item.query.find((n) => !n.key && n.description)
      ) {
        colunmError.push(item.id)
      } else if (
        item.method !== ToolHTTPRequestParameterMethodTypes.POST &&
        item.query.find((n) => (!n.key || !n.description) && n.value)
      ) {
        colunmError.push(item.id)
      } else if (!item.prompt) {
        colunmError.push(item.id)
        // @ts-expect-error: Unreachable code error
      } else if (findDuplicateAuths(item.header).length > 0) {
        colunmError.push(item.id)
        // @ts-expect-error: Unreachable code error
      } else if (findDuplicateKeys(item.query).length > 0) {
        colunmError.push(item.id)
      } else if (
        item.body_type === ToolHTTPRequestParameterBodyTypes.JSON &&
        findDuplicateKeys(item.body).length > 0
      ) {
        colunmError.push(item.id)
      } else if (
        item.body_type === ToolHTTPRequestParameterBodyTypes.FORM_DATA &&
        findDuplicateKeys(item.body_form_data).length > 0
      ) {
        colunmError.push(item.id)
      } else if (
        item.method === ToolHTTPRequestParameterMethodTypes.PUT ||
        item.method === ToolHTTPRequestParameterMethodTypes.PATCH ||
        item.method === ToolHTTPRequestParameterMethodTypes.POST
      ) {
        if (item.body_type === ToolHTTPRequestParameterBodyTypes.JSON) {
          if (
            item?.body?.find((n) => (n.key && !regex.test(n.key)) || !n.key)
          ) {
            colunmError.push(item.id)
          } else if (item?.body?.find((n) => !n.description)) {
            colunmError.push(item.id)
          }
        } else if (
          item.body_type === ToolHTTPRequestParameterBodyTypes.RAW_JSON
        ) {
          if (item?.body_raw?.find((n) => !n.value || !n.description)) {
            colunmError.push(item.id)
          }
        } else if (
          item.body_type === ToolHTTPRequestParameterBodyTypes.FORM_DATA
        ) {
          if (
            item?.body_form_data?.find(
              (n) => (n.key && !regex.test(n.key)) || !n.key
            )
          ) {
            colunmError.push(item.id)
          } else if (
            item?.body_form_data?.find(
              (n) => (n.type === 'file' && !n.files?.length) || !n.description
            )
          ) {
            colunmError.push(item.id)
          }
        }
        // @ts-expect-error: Unreachable code error
        if (findDuplicateAuths(item.header).length > 0) {
          colunmError.push(item.id)
        }
      }
    })

    setError(colunmError)

    if (colunmError.length > 0) return false
    else return true
  }
  const handleSave = async () => {
    setError([])
    const validate = validateValue(requestConfig)
    if (validate) {
      const formattedHttpTool = formatArray(requestConfig)
      handleSettingBuiltInToolHttpByToolId(formattedHttpTool)
      onClose()
    }
  }

  const updateConfig = (
    id: string,
    field: 'method' | 'url' | 'authentication' | 'prompt',
    value: any
  ) => {
    setIsChange(true)

    if (field === 'method') {
      setRequestConfig((prevConfigList) =>
        prevConfigList.map((config) => {
          if (config.id === id) {
            setBackupStore((prevBackupStore) => {
              const updatedBackup = { ...prevBackupStore }

              if (!updatedBackup[id]) {
                updatedBackup[id] = {}
              }

              // Lưu dữ liệu hiện tại vào các trường backup nếu chưa tồn tại
              if (!updatedBackup?.[id]?.[config.method]) {
                updatedBackup[id][config.method] = {}
              }

              updatedBackup[id][config.method][config.authentication] = config

              return updatedBackup
            })

            if (backupStore[id]?.[value]?.BEARER_TOKEN) {
              return {
                ...backupStore[id]?.[value]?.BEARER_TOKEN,
                method: value,
                authentication: 'BEARER_TOKEN',
              }
            } else if (backupStore[id]?.[value]?.API_KEY) {
              return {
                ...backupStore[id]?.[value]?.API_KEY,
                method: value,
                authentication: 'API_KEY',
              }
            } else if (backupStore[id]?.[value]?.BASIC_AUTH) {
              return {
                ...backupStore[id]?.[value]?.BASIC_AUTH,
                method: value,
                authentication: 'BASIC_AUTH',
              }
            } else if (backupStore[id]?.[value]?.NO_AUTH) {
              return {
                ...backupStore[id]?.[value]?.NO_AUTH,
                method: value,
                authentication: 'NO_AUTH',
              }
            }

            return {
              ...DEFAULT_CONFIG,
              id: id ?? 1,
              method: value,
              authentication: 'BEARER_TOKEN',
              header: [
                {
                  auth: 'Authorization',
                  token: '',
                },
                {
                  auth: '',
                  token: '',
                  description: '',
                },
              ],
            }
          }
          return config
        })
      )

      return
    } else if (field === 'authentication') {
      setRequestConfig((prevConfigList) =>
        prevConfigList.map((config) => {
          if (config.id === id) {
            setBackupStore((prevBackupStore) => {
              const updatedBackup = { ...prevBackupStore }

              if (!updatedBackup[id]) {
                updatedBackup[id] = {}
              }

              if (!updatedBackup?.[id]?.[config.method]) {
                updatedBackup[id][config.method] = {}
              }

              updatedBackup[id][config.method][config.authentication] = config

              return updatedBackup
            })

            if (value === 'NO_AUTH' && config.authentication !== 'NO_AUTH') {
              return {
                ...config,
                authentication: value,
                header: config.header.slice(1),
              }
            }

            if (backupStore[id]?.[config.method]?.[value]) {
              return {
                ...config,
                authentication: value,
                header:
                  config.authentication !== 'NO_AUTH'
                    ? config.header.map((item, index) =>
                        index === 0
                          ? {
                              ...item,
                              token:
                                backupStore[id]?.[config.method]?.[value]
                                  .header[0].token,
                            }
                          : item
                      )
                    : [
                        {
                          auth: 'Authorization',
                          token:
                            backupStore[id]?.[config.method]?.[value].header[0]
                              .token,
                        },
                        ...config.header,
                      ],
              }
            }

            return {
              ...config,
              authentication: value,
              header:
                config.authentication !== 'NO_AUTH'
                  ? config.header.map((item, index) =>
                      index === 0
                        ? {
                            ...item,
                            token: '',
                          }
                        : item
                    )
                  : [
                      {
                        auth: 'Authorization',
                        token: '',
                      },
                      ...config.header,
                    ],
            }
          }
          return config
        })
      )

      return
    }
    setRequestConfig((prevData) =>
      prevData.map((item) =>
        item.id === id
          ? {
              ...item,
              [field]: value,
            }
          : item
      )
    )
  }

  const changeHideHeaderInput = (index: any) => {
    setShowRequestConfig(index === showRequestConfig ? undefined : index)
  }

  const removeRequestConfig = (id: any, index: number) => {
    setIsChange(true)

    if (requestConfig.length > 1) {
      setShowRequestConfig(
        requestConfig[index - 1]?.id || requestConfig[index + 1]?.id
      )
      const newList = requestConfig.filter((n) => n.id !== id)

      setRequestConfig(newList)
    } else {
      setRequestConfig([
        {
          ...DEFAULT_CONFIG,
          id: id,
        },
      ])
    }
  }

  const addParams = (id: string, field: IField) => {
    setIsChange(true)

    let newItem = {
      type: 'string',
      key: '',
      value: '',
      description: '',
    }

    if (field === 'header') {
      newItem = {
        type: 'string',
        auth: '',
        token: '',
        description: '',
      }
    } else if (field === 'body') {
      newItem = {
        type: 'string',
        key: '',
        value: '',
        description: '',
      }
    } else if (field === 'body_form_data') {
      newItem = {
        type: 'string',
        key: '',
        value: '',
        description: '',
      }
    }
    setRequestConfig((prevData) =>
      prevData.map((item) =>
        item.id === id
          ? {
              ...item,
              [field]: [...item[field], newItem],
            }
          : item
      )
    )
  }

  const removeParams = (id: string, indexToRemove: number, field: IField) => {
    if (field) {
      setIsChange(true)

      const isEmpty = requestConfig.find((n) => n.id === id)?.[field]?.length

      const auth = requestConfig.find((n) => n.id === id)?.authentication
      if (
        isEmpty &&
        ((field === 'header' &&
          ((isEmpty > 1 &&
            auth === ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH) ||
            (isEmpty > 2 &&
              auth !== ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH))) ||
          (field !== 'header' && isEmpty > 1))
      ) {
        setRequestConfig((prevData) =>
          prevData.map((item) =>
            item.id === id
              ? {
                  ...item,
                  [field]: item[field].filter(
                    (_: any, index: number) => index !== indexToRemove
                  ),
                }
              : item
          )
        )
      } else {
        setRequestConfig((prevData) =>
          prevData.map((item) =>
            item.id === id
              ? {
                  ...item,
                  [field]: item[field].map((fieldItem, index) => {
                    if (index !== indexToRemove) {
                      return fieldItem
                    }

                    if (field === 'header') {
                      return {
                        auth: '',
                        token: '',
                        type: 'string',
                        description: '',
                      }
                    }

                    if (field === 'body') {
                      return {
                        type: 'string',
                        key: '',
                        value: '',
                        description: '',
                      }
                    }

                    if (field === 'body_raw') {
                      return {
                        type: 'object',
                        key: 'body',
                        value: '',
                        description: '',
                      }
                    }

                    if (field === 'body_form_data') {
                      return {
                        type: 'string',
                        key: '',
                        value: '',
                        files: [],
                        description: '',
                      }
                    }

                    return {
                      key: '',
                      value: '',
                      type: 'string',
                      description: '',
                    }
                  }),
                }
              : item
          )
        )
      }
    }
  }

  const updateParams = (
    id: string,
    field: IField,
    section: string,
    indexToUpdate: number,
    newAuth: string
  ) => {
    setIsChange(true)

    setRequestConfig((prevData) =>
      prevData.map((item) =>
        item.id === id
          ? {
              ...item,
              [field]: item[field].map((fieldItem, index) =>
                index === indexToUpdate
                  ? {
                      ...fieldItem,
                      [section]: newAuth,
                    }
                  : fieldItem
              ),
            }
          : item
      )
    )
  }

  const updateBodyType = (id: string, value: IBodyType) => {
    setIsChange(true)

    setRequestConfig((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          const newItem = {
            ...item,
            body_type: value,
          }

          if (value === ToolHTTPRequestParameterBodyTypes.JSON) {
            if (!item?.body?.length) {
              newItem.body = [
                {
                  type: 'string',
                  key: '',
                  value: '',
                  description: '',
                },
              ]
            }
            return newItem
          } else if (value === ToolHTTPRequestParameterBodyTypes.RAW_JSON) {
            if (!item?.body_raw?.length) {
              newItem.body_raw = [
                {
                  type: 'string',
                  key: '',
                  value: '',
                  description: '',
                },
              ]
            }
            return newItem
          } else if (value === ToolHTTPRequestParameterBodyTypes.FORM_DATA) {
            if (!item?.body_form_data?.length) {
              newItem.body_form_data = [
                {
                  type: 'string',
                  key: '',
                  value: '',
                  files: [],
                  description: '',
                },
              ]
            }
            return newItem
          }
        }

        return item
      })
    )
  }

  const addMoreConfig = () => {
    if (requestConfig.length < 5) {
      setIsChange(true)

      const newConfig = [...requestConfig]
      const randomId = nanoid()

      newConfig.push({
        id: randomId,
        method: 'GET',
        url: '',
        authentication:
          ToolHTTPRequestParameterAuthenticationTypes.BEARER_TOKEN,
        header: [
          {
            auth: 'Authorization',
            token: '',
            type: 'string',
            description: '',
          },
          {
            auth: '',
            token: '',
            type: 'string',
            description: '',
          },
        ],
        query: [
          {
            key: '',
            value: '',
            type: 'string',
            description: '',
          },
        ],
        body_type: 'RAW_JSON',
        body: [
          {
            type: 'string',
            key: '',
            value: '',
            description: '',
          },
        ],
        body_raw: [
          {
            type: 'object',
            key: 'body',
            value: '',
            description: '',
          },
        ],
        body_form_data: [
          {
            type: 'string',
            key: '',
            value: '',
            files: [],
            description: '',
          },
        ],
        prompt: null,
      })

      setRequestConfig(newConfig)

      setShowRequestConfig(randomId)
    }
  }

  const resetHeader = (
    id: string,
    auth: ToolHTTPRequestParameterAuthenticationTypes
  ) => {
    let newHeader: any[] = []

    switch (auth) {
      case ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH:
        newHeader = [
          {
            auth: '',
            token: '',
          },
        ]
        break

      default:
        newHeader = [
          {
            auth: 'Authorization',
            token: '',
          },
          {
            auth: '',
            token: '',
          },
        ]
        break
    }
    setIsChange(true)

    setRequestConfig((prevData) =>
      prevData.map((item) =>
        item.id === id
          ? {
              ...item,
              header: newHeader,
            }
          : item
      )
    )
  }
  return (
    <BaseModal isPureModal isOpen={isOpen} onClose={onClose}>
      <div className="relative flex max-h-[884px] w-[1092px] flex-col gap-[12px] rounded-[20px] bg-white p-3 shadow-md">
        <IconButton
          nameIcon="x-close"
          sizeIcon={16}
          onClick={() => {
            if (isChange) {
              MessageDialog.warning({
                mainMessage: 'Wanna leave?',
                subMessage: 'If continue, your changes may not be saved',
                onClick: () => {
                  setShowRequestConfig(1)
                  setIsChange(false)

                  onClose()
                },
              })
            } else {
              setShowRequestConfig(1)
              onClose()
            }
          }}
          className="absolute right-[10px] top-[10px] duration-300 hover:bg-neutral-200"
          colorIcon={colors.neutral[500]}
        />
        <div className="flex w-full flex-col gap-[4px]">
          <Text
            type="subheading"
            variant="medium"
            className="pl-1 text-Primary-Color"
          >
            Tool Parameters
          </Text>

          <Text
            type="subBody"
            variant="regular"
            className="pl-1 text-Secondary-Color"
          >
            Configure method, authentication type and needed elements
          </Text>
        </div>

        <div className="flex w-full flex-col gap-[4px] overflow-hidden">
          {requestConfig.map((item, i) => {
            const hidden = showRequestConfig !== item.id
            return (
              <RequestConfig
                key={item.id}
                hidden={hidden}
                item={item}
                index={i}
                changeHideHeaderInput={changeHideHeaderInput}
                removeRequestConfig={removeRequestConfig}
                addParams={addParams}
                removeParams={removeParams}
                updateParams={updateParams}
                updateConfig={updateConfig}
                resetHeader={resetHeader}
                error={error}
                setError={setError}
                updateBodyType={updateBodyType}
              />
            )
          })}

          <div className="mt-[4px] flex w-full items-center px-[4px]">
            <div
              onClick={() => addMoreConfig()}
              className="flex cursor-pointer items-center gap-[8px] rounded-[6px] px-[8px] py-[4px] hover:bg-Hover-Color"
            >
              <Icon
                name="Outline-EssentionalUI-AddSquare"
                size={16}
                color={requestConfig.length < 5 ? '#2D0136' : '#C2C2C2'}
              />

              <Text
                type="subBody"
                variant="medium"
                className={
                  requestConfig.length < 5
                    ? 'text-Primary-Color'
                    : 'text-Disable-Text'
                }
              >
                More configuration
              </Text>
            </div>
          </div>
        </div>
        <div className="flex w-full justify-end">
          <Button
            type="primary"
            size="small"
            onClick={handleSave}
            text="Save"
          />
        </div>
      </div>
    </BaseModal>
  )
}

export default memo(ModalToolParametersHttp)
