import { memo, useEffect, useState } from 'react'
import Input from '@/components/Input'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import Tooltip from '@/components/Tooltip'
import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import { colors } from '@/theme'
import {
  IBodyType,
  IField,
  IRequestConfig,
  findDuplicateAuths,
  findDuplicateKeys,
} from '..'
import './RequestConfig.scss'
import {
  ToolHTTPRequestParameterAuthenticationTypes,
  ToolHTTPRequestParameterBodyTypes,
  ToolHTTPRequestParameterMethodTypes,
} from '@/apis/client'
import Select from '../../SelectAssignee/components/Select'
// import { FormattedFileUploadProps } from '@/components/FileSelect/components/FileUpload'
import BodyItem from '../BodyItem'
import clsx from 'clsx'

const BODY_TYPE_LIST = [
  {
    id: 'RAW_JSON',
    value: 'RAW_JSON',
    name: 'Raw JSON',
  },
  {
    id: 'JSO<PERSON>',
    value: 'JSO<PERSON>',
    name: 'J<PERSON><PERSON> Object',
  },
  {
    id: 'FORM_DATA',
    value: 'FORM_DATA',
    name: 'Form-Data',
  },
]

const METHOD_LIST = [
  {
    id: ToolHTTPRequestParameterMethodTypes.GET,
    value: ToolHTTPRequestParameterMethodTypes.GET,
    name: ToolHTTPRequestParameterMethodTypes.GET,
  },
  {
    id: ToolHTTPRequestParameterMethodTypes.POST,
    value: ToolHTTPRequestParameterMethodTypes.POST,
    name: ToolHTTPRequestParameterMethodTypes.POST,
  },
  {
    id: ToolHTTPRequestParameterMethodTypes.PUT,
    value: ToolHTTPRequestParameterMethodTypes.PUT,
    name: ToolHTTPRequestParameterMethodTypes.PUT,
  },
  {
    id: ToolHTTPRequestParameterMethodTypes.PATCH,
    value: ToolHTTPRequestParameterMethodTypes.PATCH,
    name: ToolHTTPRequestParameterMethodTypes.PATCH,
  },
  {
    id: ToolHTTPRequestParameterMethodTypes.DELETE,
    value: ToolHTTPRequestParameterMethodTypes.DELETE,
    name: ToolHTTPRequestParameterMethodTypes.DELETE,
  },
]

const AUTHENTICATION_LIST = [
  {
    id: ToolHTTPRequestParameterAuthenticationTypes.BEARER_TOKEN,
    value: 'Bearer token',
    name: 'Bearer token',
  },
  {
    id: ToolHTTPRequestParameterAuthenticationTypes.API_KEY,
    value: 'API Key',
    name: 'API Key',
  },
  {
    id: ToolHTTPRequestParameterAuthenticationTypes.BASIC_AUTH,
    value: 'Basic auth',
    name: 'Basic auth',
  },
  {
    id: ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH,
    value: 'No auth',
    name: 'No auth',
  },
]
interface IValidate {
  headers: number[]
  query: number[]
  body: number[]
  body_raw: number[]
  body_form_data: number[]
}
const getNameAuthen = (value: string) => {
  switch (value) {
    case ToolHTTPRequestParameterAuthenticationTypes.BEARER_TOKEN:
      return 'Bearer token'
    case ToolHTTPRequestParameterAuthenticationTypes.API_KEY:
      return 'API Key'
    case ToolHTTPRequestParameterAuthenticationTypes.BASIC_AUTH:
      return 'Basic auth'
    case ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH:
      return 'No auth'
    default:
      break
  }
}
const RequestConfig = ({
  hidden,
  item,
  index,
  changeHideHeaderInput,
  removeRequestConfig,
  addParams,
  removeParams,
  updateParams,
  updateConfig,
  updateBodyType,
  error,
  setError,
}: {
  hidden: boolean
  item: IRequestConfig
  index: number
  changeHideHeaderInput: (index: any) => void
  removeRequestConfig: (id: any, index: number) => void

  addParams: (id: string, field: IField) => void
  removeParams: (id: string, indexToRemove: number, field: IField) => void
  updateParams: (
    id: string,
    field: IField,
    section: string,
    indexToUpdate: number,
    newAuth: string | Array<any>
  ) => void
  updateConfig: (
    id: string,
    field: 'method' | 'url' | 'authentication' | 'prompt',
    value: any
  ) => void
  resetHeader: (
    id: string,
    auth: ToolHTTPRequestParameterAuthenticationTypes
  ) => void
  updateBodyType: (id: string, value: IBodyType) => void
  error: number[]
  setError: any
}) => {
  const [focused, setFocused] = useState({
    type: '',
    index: -1,
  })

  const [hiddenHeader, setHiddenHeader] = useState(false)
  const [hiddenQuery, setHiddenQuery] = useState(false)
  const [hiddenBody, setHiddenBody] = useState(false)

  const [validate, setValidate] = useState<IValidate>({
    headers: [],
    query: [],
    body: [],
    body_raw: [],
    body_form_data: [],
  })
  const [validateDuplicate, setValidateDuplicate] = useState<IValidate>({
    headers: [],
    query: [],
    body: [],
    body_raw: [],
    body_form_data: [],
  })

  const [validateValue, setValidateValue] = useState<IValidate>({
    headers: [],
    query: [],
    body: [],
    body_raw: [],
    body_form_data: [],
  })

  const [validateDescription, setValidateDescription] = useState<IValidate>({
    headers: [],
    query: [],
    body: [],
    body_raw: [],
    body_form_data: [],
  })

  const [validatePrompt, setValidatePrompt] = useState(false)
  const [validateUrl, setValidateUrl] = useState(false)
  const [validateUsername, setValidateUsername] = useState(false)
  const [validatePassword, setValidatePassword] = useState(false)

  const [hiddenPassWord, setHiddenPassword] = useState(true)

  useEffect(() => {
    if (error.length > 0) {
      setValidatePrompt(false)
      setValidateUrl(false)
      const regex = /^[a-zA-Z0-9_-]{1,255}$/
      const errorHeader: number[] = []
      const errorQuery: number[] = []
      const errorBody: number[] = []
      const errorBodyRaw: number[] = []
      const errorBodyFormData: number[] = []

      const errorHeaderDuplicate: number[] = []
      const errorQueryDuplicate: number[] = []
      const errorBodyDuplicate: number[] = []
      const errorBodyRawDuplicate: number[] = []
      const errorBodyFormDataDuplicate: number[] = []

      const errorHeaderValue: number[] = []
      const errorQueryValue: number[] = []
      const errorBodyValue: number[] = []
      const errorBodyRawValue: number[] = []
      const errorBodyFormDataValue: number[] = []

      const errorHeaderDescription: number[] = []
      const errorQueryDescription: number[] = []
      const errorBodyDescription: number[] = []
      const errorBodyRawDescription: number[] = []
      const errorBodyFormDataDescription: number[] = []

      item.header.map((n, i) => {
        if (!n.token && n.auth === 'Authorization') {
          errorHeaderValue.push(i)
        }
        if (!n.token?.split(':')?.[0] && n.auth === 'Authorization') {
          setValidateUsername(true)
        }

        if (!n.token?.split(':')?.[1] && n.auth === 'Authorization') {
          setValidatePassword(true)
        }
        if (
          (n.auth && !regex.test(n.auth)) ||
          (n.auth !== 'Authorization' && !n.auth && n.description)
        ) {
          errorHeader.push(i)
        } else if (
          n.auth !== 'Authorization' &&
          n.auth &&
          regex.test(n.auth) &&
          !n.description
        ) {
          errorHeaderDescription.push(i)
        }

        if (n.auth !== 'Authorization') {
          if (!n.auth && n.token) {
            errorHeader.push(i)
          }
          if (!n.description && n.token) {
            errorHeaderDescription.push(i)
          }
        }
      })

      const duplicateHeader = findDuplicateAuths(item.header)
      const duplicateQuery = findDuplicateKeys(item.query)
      const duplicateBody = findDuplicateKeys(item.body)
      const duplicateBodyFormData = findDuplicateKeys(item.body_form_data)

      if (duplicateHeader.length > 0)
        duplicateHeader.map((n) => {
          errorHeaderDuplicate.push(n)
        })
      if (duplicateQuery.length > 0)
        duplicateQuery.map((n) => {
          errorQueryDuplicate.push(n)
        })
      if (duplicateBody.length > 0)
        duplicateBody.map((n) => {
          errorBodyDuplicate.push(n)
        })
      if (duplicateBodyFormData.length > 0)
        duplicateBodyFormData.map((n) => {
          errorBodyFormDataDuplicate.push(n)
        })

      if (item.method !== ToolHTTPRequestParameterMethodTypes.POST) {
        item?.query?.map((n, i) => {
          if ((n.key && !regex.test(n.key)) || (!n.key && n.description)) {
            errorQuery.push(i)
          } else if (n.key && regex.test(n.key) && !n.description) {
            errorQueryDescription.push(i)
          }

          if (!n.key && n.value) {
            errorQuery.push(i)
          }
          if (!n.description && n.value) {
            errorQueryDescription.push(i)
          }
        })
      }
      if (
        item.method === ToolHTTPRequestParameterMethodTypes.PUT ||
        item.method === ToolHTTPRequestParameterMethodTypes.PATCH ||
        item.method === ToolHTTPRequestParameterMethodTypes.POST
      ) {
        if (item.body_type === ToolHTTPRequestParameterBodyTypes.JSON) {
          item.body.map((n, i) => {
            if ((n.key && !regex.test(n.key)) || !n.key) {
              errorBody.push(i)
            }
            if (
              (n.key && regex.test(n.key) && !n.description) ||
              (!n.description && n.value)
            ) {
              errorBodyDescription.push(i)
            }
          })
        } else if (
          item.body_type === ToolHTTPRequestParameterBodyTypes.RAW_JSON
        ) {
          item.body_raw.map((n, i) => {
            if (!n.value) {
              errorBodyRawValue.push(i)
            }
            if (!n.description) {
              errorBodyRawDescription.push(i)
            }
          })
        } else if (
          item.body_type === ToolHTTPRequestParameterBodyTypes.FORM_DATA
        ) {
          item.body_form_data.map((n, i) => {
            if ((n.key && !regex.test(n.key)) || !n.key) {
              errorBodyFormData.push(i)
            }
            if (
              (n.key && regex.test(n.key) && !n.description) ||
              (n.type === 'string' && !n.description && n.value) ||
              (n.type === 'file' && !n.description && n.files?.length)
            ) {
              errorBodyFormDataDescription.push(i)
            }
            if (
              n.type === 'file' &&
              n.key &&
              regex.test(n.key) &&
              !n.files?.length
            ) {
              errorBodyFormDataValue.push(i)
            }
          })
        }
      }

      setValidateValue({
        headers: errorHeaderValue,
        query: errorQueryValue,
        body: errorBodyValue,
        body_raw: errorBodyRawValue,
        body_form_data: errorBodyFormDataValue,
      })
      setValidate({
        headers: errorHeader,
        query: errorQuery,
        body: errorBody,
        body_raw: errorBodyRaw,
        body_form_data: errorBodyFormData,
      })
      setValidateDuplicate({
        headers: errorHeaderDuplicate,
        query: errorQueryDuplicate,
        body: errorBodyDuplicate,
        body_raw: errorBodyRawDuplicate,
        body_form_data: errorBodyFormDataDuplicate,
      })
      setValidateDescription({
        headers: errorHeaderDescription,
        query: errorQueryDescription,
        body: errorBodyDescription,
        body_raw: errorBodyRawDescription,
        body_form_data: errorBodyFormDataDescription,
      })

      if (!item.prompt) setValidatePrompt(true)
      if (!item.url) setValidateUrl(true)

      setError([])
    }
  }, [error])

  const isError = () => {
    if (
      validateValue.body.length ||
      validateValue.headers.length ||
      validateValue.query.length ||
      validate.body.length ||
      validate.headers.length ||
      validate.query.length ||
      validateDuplicate.body.length ||
      validateDuplicate.body_raw.length ||
      validateDuplicate.body_form_data.length ||
      validateDuplicate.headers.length ||
      validateDuplicate.query.length ||
      validateDescription.body.length ||
      validateDescription.body_raw.length ||
      validateDescription.body_form_data.length ||
      validateDescription.headers.length ||
      validateDescription.query.length ||
      validatePrompt ||
      validateUrl ||
      validatePassword ||
      validateUsername
    )
      return true
    return false
  }

  const errorMessageHeaders = () => {
    const regex = /^[a-zA-Z0-9_-]{1,255}$/
    if (
      validate.headers.length > 0 &&
      item.header?.some(
        (header, index) =>
          validate.headers.includes(index) &&
          header.auth &&
          !regex.test(header.auth)
      )
    ) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon name="vuesax-bold-info-circle" size={12} color="#B91C1C" />
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            Spaces, special characters and punctuation are not allowed
          </Text>
        </div>
      )
    }

    if (validateDuplicate.headers.length > 0) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon name="vuesax-bold-info-circle" size={12} color="#B91C1C" />
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            Duplicated header
          </Text>
        </div>
      )
    }

    return undefined
  }

  const errorMessageQuery = () => {
    const regex = /^[a-zA-Z0-9_-]{1,255}$/
    if (
      validate.query.length > 0 &&
      item.query?.some(
        (query, index) =>
          validate.query.includes(index) && query.key && !regex.test(query.key)
      )
    ) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon name="vuesax-bold-info-circle" size={12} color="#B91C1C" />
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            Spaces, special characters and punctuation are not allowed
          </Text>
        </div>
      )
    }

    if (validateDuplicate.query.length > 0) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon name="vuesax-bold-info-circle" size={12} color="#B91C1C" />
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            Duplicated parameter
          </Text>
        </div>
      )
    }

    return undefined
  }

  const errorMessageBody = () => {
    const regex = /^[a-zA-Z0-9_-]{1,255}$/
    if (
      (validate.body.length &&
        item.body?.some(
          (body, index) =>
            validate.body.includes(index) && body.key && !regex.test(body.key)
        )) ||
      validate.body_raw?.length ||
      (validate.body_form_data?.length &&
        item.body_form_data?.some(
          (body, index) =>
            validate.body_form_data.includes(index) &&
            body.key &&
            !regex.test(body.key)
        ))
    ) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon name="vuesax-bold-info-circle" size={12} color="#B91C1C" />
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            Spaces, special characters and punctuation are not allowed
          </Text>
        </div>
      )
    }

    if (
      validateDuplicate.body.length ||
      validateDuplicate.body_raw?.length ||
      validateDuplicate.body_form_data.length
    ) {
      return (
        <div className="flex items-center gap-[4px]">
          <Icon name="vuesax-bold-info-circle" size={12} color="#B91C1C" />
          <Text
            type="supportText"
            variant="medium"
            className="text-Error-Color"
          >
            Duplicated field name
          </Text>
        </div>
      )
    }

    return undefined
  }

  return (
    <div
      className="flex min-h-[26px] w-full flex-col gap-[8px]"
      style={{ overflowY: !hidden ? 'auto' : 'hidden' }}
    >
      <div className="flex w-full items-center justify-between rounded-[4px] px-[4px] py-[4px]">
        <div
          onClick={() => {
            changeHideHeaderInput(item.id)
          }}
          className="flex w-full cursor-pointer items-center gap-[4px] hover:bg-Hover-Color"
        >
          <Icon
            name="vuesax-bold-arrow-rights-2"
            size={16}
            color={isError() ? '#B91C1C' : '#2D0136'}
            className={!hidden ? '' : '-rotate-90'}
          />

          <Text
            type="subBody"
            variant="medium"
            className={isError() ? 'text-Error-Color' : 'text-Primary-Color'}
          >
            Request configuration {index ? index : ''}
          </Text>
        </div>

        {!hidden && (
          <Text
            onClick={() => removeRequestConfig(item.id, index)}
            className="cursor-pointer self-end bg-Main2-02 bg-clip-text px-2 text-Error-Color decoration-1 hover:underline"
            type="subBody"
            variant="medium"
          >
            Remove
          </Text>
        )}
      </div>
      {!hidden && (
        <div className="flex flex-col gap-[8px] overflow-y-auto pl-2 pr-1">
          <div
            className={clsx(
              'flex w-full flex-col gap-[12px] overflow-y-auto',
              item.body_type === ToolHTTPRequestParameterBodyTypes.RAW_JSON &&
                '!gap-2'
            )}
          >
            <div className="flex w-full overflow-y-auto">
              <div className="flex w-[304px] min-w-[304px] flex-col gap-[8px] px-[4px]">
                <div className="flex w-full flex-col gap-[8px]">
                  <Text
                    type="body"
                    variant="medium"
                    className="text-Primary-Color"
                  >
                    Method
                  </Text>
                  <Select
                    contentClassName="!w-[410px]"
                    className="w-full"
                    data={METHOD_LIST}
                    selected={{
                      id: item.method,
                      name: item.method,
                      value: item.method,
                    }}
                    onChangeSelectedValue={(value) => {
                      updateConfig(item.id, 'method', value.value)
                      setValidate({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidateValue({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidateDescription({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidateDuplicate({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidatePrompt(false)
                      setValidateUrl(false)
                      setValidateUsername(false)
                      setValidatePassword(false)
                    }}
                  />
                </div>

                <Input
                  isError={validateUrl}
                  value={item.url}
                  onChange={(e) => {
                    updateConfig(item.id, 'url', e.target.value)
                  }}
                  onBlur={() => {
                    const value = item.url.trim()
                    if (value && validateUrl) setValidateUrl(false)

                    if (value !== item.url) updateConfig(item.id, 'url', value)
                  }}
                  placeholder="Type in here"
                  maxLength={1000}
                  label={
                    <div className="flex items-center gap-[3px] px-[4px]">
                      URL
                      <Tooltip text="">
                        <div>
                          <IconButton
                            nameIcon="Bold-EssentionalUI-InfoCircle"
                            hoverColor={colors['Primary-Color']}
                            colorIcon={'#D4D4D4'}
                            sizeIcon={14}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  }
                />
                <div className="flex w-full flex-col gap-[8px]">
                  <Text
                    type="body"
                    variant="medium"
                    className="text-Primary-Color"
                  >
                    Authentication
                  </Text>
                  <Select
                    contentClassName="!w-[410px]"
                    className="w-full"
                    data={AUTHENTICATION_LIST}
                    selected={{
                      id: item.authentication,
                      name: getNameAuthen(item.authentication),
                      value: getNameAuthen(item.authentication),
                    }}
                    onChangeSelectedValue={(value) => {
                      updateConfig(item.id, 'authentication', value.id)
                      setValidate({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidateValue({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidateDescription({
                        headers: [],
                        query: [],
                        body: [],
                        body_raw: [],
                        body_form_data: [],
                      })
                      setValidateUsername(false)
                      setValidatePassword(false)
                    }}
                  />
                </div>
              </div>

              <div className="genai-scrollbar flex w-full flex-col overflow-y-auto pr-1">
                <div className="flex flex-col gap-[8px]">
                  <div
                    onClick={() => setHiddenHeader(!hiddenHeader)}
                    className="flex w-full cursor-pointer items-center justify-between"
                  >
                    <div className="flex items-center gap-[4px]">
                      <Text
                        type="body"
                        variant="medium"
                        className="pl-[22px] text-Primary-Color"
                      >
                        Headers
                      </Text>
                      {errorMessageHeaders()}
                    </div>
                    <Icon
                      name="vuesax-bold-arrow-rights-2"
                      size={16}
                      color="#2D0136"
                      className={!hiddenHeader ? '' : 'rotate-90'}
                    />
                  </div>
                  {!hiddenHeader && (
                    <div className="flex w-full flex-col gap-[4px]">
                      {item.header.map((head, i) => {
                        if (
                          i === 0 &&
                          item.authentication !==
                            ToolHTTPRequestParameterAuthenticationTypes.NO_AUTH
                        ) {
                          if (
                            item.authentication ===
                            ToolHTTPRequestParameterAuthenticationTypes.BEARER_TOKEN
                          ) {
                            return (
                              <div
                                key={i}
                                onMouseEnter={() =>
                                  setFocused({
                                    type: 'header',
                                    index: i,
                                  })
                                }
                                onMouseLeave={() =>
                                  setFocused({
                                    type: '',
                                    index: -1,
                                  })
                                }
                                className="flex items-center gap-[8px]"
                              >
                                <div className="flex items-center gap-[2px]">
                                  <div className="min-h-[16px] min-w-[16px]"></div>
                                  <Input
                                    onChange={(e) => {
                                      updateParams(
                                        item.id,
                                        'header',
                                        'auth',
                                        i,
                                        e.target.value
                                      )
                                    }}
                                    disabled
                                    placeholder="Authorization"
                                    classNameInputWrapper="w-[198px]"
                                    className="!border-Disable"
                                    value={head.auth}
                                  />
                                </div>
                                <Input
                                  onChange={(e) => {
                                    updateParams(
                                      item.id,
                                      'header',
                                      'token',
                                      i,
                                      e.target.value
                                    )
                                  }}
                                  onBlur={() => {
                                    if (head.token) {
                                      updateParams(
                                        item.id,
                                        'header',
                                        'token',
                                        i,
                                        head.token.trim()
                                      )
                                    }
                                  }}
                                  isError={
                                    validateValue.headers.find(
                                      (n) => n === i
                                    ) !== undefined
                                  }
                                  maxLength={2000}
                                  isFullWidth
                                  placeholder="token"
                                  className="w-full"
                                  value={head.token}
                                />
                              </div>
                            )
                          } else if (
                            item.authentication ===
                            ToolHTTPRequestParameterAuthenticationTypes.API_KEY
                          ) {
                            return (
                              <div
                                key={i}
                                onMouseEnter={() =>
                                  setFocused({
                                    type: 'header',
                                    index: i,
                                  })
                                }
                                onMouseLeave={() =>
                                  setFocused({
                                    type: '',
                                    index: -1,
                                  })
                                }
                                className="flex items-center gap-[8px]"
                              >
                                <div className="flex items-center gap-[2px]">
                                  <div className="min-h-[16px] min-w-[16px]"></div>
                                  <Input
                                    onChange={(e) => {
                                      updateParams(
                                        item.id,
                                        'header',
                                        'auth',
                                        i,
                                        e.target.value
                                      )
                                    }}
                                    disabled
                                    placeholder="Authorization"
                                    className="!border-Disable"
                                    classNameInputWrapper="w-[198px]"
                                    value={head.auth}
                                  />
                                </div>
                                <Input
                                  onChange={(e) => {
                                    updateParams(
                                      item.id,
                                      'header',
                                      'token',
                                      i,
                                      e.target.value
                                    )
                                  }}
                                  isError={
                                    validateValue.headers.find(
                                      (n) => n === i
                                    ) !== undefined
                                  }
                                  onBlur={() => {
                                    if (head.token) {
                                      updateParams(
                                        item.id,
                                        'header',
                                        'token',
                                        i,
                                        head.token.trim()
                                      )
                                    }
                                  }}
                                  maxLength={2000}
                                  isFullWidth
                                  placeholder="api key"
                                  className="w-full"
                                  value={head.token}
                                />
                              </div>
                            )
                          } else if (
                            item.authentication ===
                            ToolHTTPRequestParameterAuthenticationTypes.BASIC_AUTH
                          ) {
                            return (
                              <div
                                key={i}
                                onMouseEnter={() =>
                                  setFocused({
                                    type: 'header',
                                    index: i,
                                  })
                                }
                                onMouseLeave={() =>
                                  setFocused({
                                    type: '',
                                    index: -1,
                                  })
                                }
                                className="flex items-center gap-[8px]"
                              >
                                <div className="flex items-center gap-[2px]">
                                  <div className="min-h-[16px] min-w-[16px]"></div>
                                  <Input
                                    onChange={(e) => {
                                      updateParams(
                                        item.id,
                                        'header',
                                        'auth',
                                        i,
                                        e.target.value
                                      )
                                    }}
                                    disabled
                                    placeholder="Authorization"
                                    className="!border-Disable"
                                    classNameInputWrapper="w-[198px]"
                                    value={head.auth}
                                  />
                                </div>
                                <Input
                                  onChange={(e) => {
                                    const oldValue = head?.token.split(':')?.[1]
                                      ? head?.token.split(':')?.[1]
                                      : ''
                                    const newValue = e.target.value.concat(
                                      ':',
                                      oldValue
                                    )
                                    updateParams(
                                      item.id,
                                      'header',
                                      'token',
                                      i,
                                      newValue
                                    )
                                  }}
                                  onBlur={() => {
                                    const username = head?.token.split(':')?.[0]
                                      ? head?.token.split(':')?.[0]
                                      : ''
                                    const password = head?.token.split(':')?.[1]
                                      ? head?.token.split(':')?.[1]
                                      : ''
                                    if (username) {
                                      const newValue = username
                                        .trim()
                                        .concat(':', password)
                                      updateParams(
                                        item.id,
                                        'header',
                                        'token',
                                        i,
                                        newValue
                                      )
                                    }
                                  }}
                                  isError={validateUsername}
                                  isFullWidth
                                  maxLength={255}
                                  placeholder="username"
                                  className="w-full"
                                  value={head?.token?.split(':')?.[0]}
                                />
                                <Input
                                  type={hiddenPassWord ? 'password' : 'text'}
                                  onChange={(e) => {
                                    const oldValue = head?.token.split(':')?.[0]
                                      ? head?.token.split(':')?.[0]
                                      : ''
                                    const newValue = oldValue.concat(
                                      ':',
                                      e.target.value
                                    )
                                    updateParams(
                                      item.id,
                                      'header',
                                      'token',
                                      i,
                                      newValue
                                    )
                                  }}
                                  isError={validatePassword}
                                  onBlur={() => {
                                    const username = head?.token.split(':')?.[0]
                                      ? head?.token.split(':')?.[0]
                                      : ''
                                    const password = head?.token.split(':')?.[1]
                                      ? head?.token.split(':')?.[1]
                                      : ''
                                    if (password) {
                                      const newValue = username.concat(
                                        ':',
                                        password.trim()
                                      )
                                      updateParams(
                                        item.id,
                                        'header',
                                        'token',
                                        i,
                                        newValue
                                      )
                                    }
                                  }}
                                  maxLength={255}
                                  isFullWidth
                                  placeholder="password"
                                  className="w-full"
                                  value={head?.token?.split(':')?.[1]}
                                  suffix={
                                    <div
                                      className={`${hiddenPassWord ? 'icon-hidden-password' : 'icon-show-password'} cursor-pointer`}
                                      onClick={() => {
                                        setHiddenPassword(!hiddenPassWord)
                                      }}
                                    ></div>
                                  }
                                />
                              </div>
                            )
                          }
                        } else {
                          return (
                            <div
                              key={i}
                              onMouseEnter={() =>
                                setFocused({
                                  type: 'header',
                                  index: i,
                                })
                              }
                              onMouseLeave={() =>
                                setFocused({
                                  type: '',
                                  index: -1,
                                })
                              }
                              className="flex items-center gap-[8px]"
                            >
                              <div className="flex items-center gap-[2px]">
                                <div className="min-h-[16px] min-w-[16px]">
                                  {i === focused.index &&
                                    focused.type === 'header' && (
                                      <div
                                        onClick={() => {
                                          setValidateValue((prevState) => ({
                                            ...prevState,
                                            headers: prevState.headers
                                              .filter((header) => header !== i)
                                              .map((header) => {
                                                if (i < header)
                                                  return (header -= 1)

                                                return header
                                              }),
                                          }))
                                          setValidate((prevState) => ({
                                            ...prevState,
                                            headers: prevState.headers
                                              .filter((header) => header !== i)
                                              .map((header) => {
                                                if (i < header)
                                                  return (header -= 1)

                                                return header
                                              }),
                                          }))
                                          setValidateDuplicate((prevState) => ({
                                            ...prevState,
                                            headers: prevState.headers
                                              .filter((header) => header !== i)
                                              .map((header) => {
                                                if (i < header)
                                                  return (header -= 1)

                                                return header
                                              }),
                                          }))
                                          setValidateDescription(
                                            (prevState) => ({
                                              ...prevState,
                                              headers: prevState.headers
                                                .filter(
                                                  (header) => header !== i
                                                )
                                                .map((header) => {
                                                  if (i < header)
                                                    return (header -= 1)

                                                  return header
                                                }),
                                            })
                                          )
                                          removeParams(item.id, i, 'header')
                                        }}
                                        className="icon-close-request-config cursor-pointer"
                                      ></div>
                                    )}
                                </div>
                                <Input
                                  onChange={(e) => {
                                    updateParams(
                                      item.id,
                                      'header',
                                      'auth',
                                      i,
                                      e.target.value
                                    )
                                  }}
                                  onBlur={() => {
                                    const regex = /^[a-zA-Z0-9_-]{1,255}$/
                                    const value = head.auth

                                    if (value && !regex.test(value)) {
                                      if (
                                        !validate.headers.find((n) => n === i)
                                      )
                                        setValidate((prevState) => ({
                                          ...prevState,
                                          headers: [...prevState.headers, i],
                                        }))
                                    } else {
                                      setValidate((prevState) => ({
                                        ...prevState,
                                        headers: prevState.headers.filter(
                                          (header) => header !== i
                                        ),
                                      }))
                                    }

                                    const duplicateHeader = findDuplicateAuths(
                                      item.header
                                    )

                                    setValidateDuplicate((prevState) => ({
                                      ...prevState,
                                      headers: duplicateHeader,
                                    }))
                                  }}
                                  isError={
                                    validate.headers.includes(i) ||
                                    validateDuplicate.headers.includes(i)
                                  }
                                  maxLength={255}
                                  placeholder="Key"
                                  classNameInputWrapper="w-[198px]"
                                  value={head.auth}
                                />
                              </div>
                              <Input
                                onChange={(e) => {
                                  updateParams(
                                    item.id,
                                    'header',
                                    'description',
                                    i,
                                    e.target.value
                                  )
                                }}
                                isError={validateDescription.headers.includes(
                                  i
                                )}
                                onBlur={() => {
                                  const value = head?.description?.trim()
                                  if (value !== head?.description)
                                    updateParams(
                                      item.id,
                                      'header',
                                      'description',
                                      i,
                                      value || ''
                                    )
                                }}
                                maxLength={255}
                                isFullWidth
                                placeholder="Description"
                                className="w-full"
                                classNameInputWrapper="max-w-[250px]"
                                value={head.description}
                              />
                              <Input
                                onChange={(e) => {
                                  updateParams(
                                    item.id,
                                    'header',
                                    'token',
                                    i,
                                    e.target.value
                                  )
                                }}
                                isError={
                                  validateValue.headers.find((n) => n === i) !==
                                  undefined
                                }
                                onBlur={() => {
                                  const value = head.token.trim()
                                  if (value !== head.token)
                                    updateParams(
                                      item.id,
                                      'header',
                                      'token',
                                      i,
                                      value
                                    )
                                }}
                                maxLength={1000}
                                isFullWidth
                                placeholder="Value"
                                className="w-full"
                                value={head.token}
                              />
                            </div>
                          )
                        }
                      })}

                      <Text
                        onClick={() => addParams(item.id, 'header')}
                        className="cursor-pointer self-end bg-Main2-02 bg-clip-text px-2 text-Primary-Color hover:text-transparent"
                        type="subBody"
                        variant="semibold"
                      >
                        Add
                      </Text>
                    </div>
                  )}
                </div>
                {item.method !== ToolHTTPRequestParameterMethodTypes.POST && (
                  <div className="flex flex-col gap-[8px]">
                    <div
                      onClick={() => setHiddenQuery(!hiddenQuery)}
                      className="flex w-full cursor-pointer items-center justify-between"
                    >
                      <div className="flex items-center gap-[4px]">
                        <Text
                          type="body"
                          variant="medium"
                          className="pl-[22px] text-Primary-Color"
                        >
                          Query parameters
                        </Text>
                        {errorMessageQuery()}
                      </div>
                      <Icon
                        name="vuesax-bold-arrow-rights-2"
                        size={16}
                        color="#2D0136"
                        className={!hiddenQuery ? '' : 'rotate-90'}
                      />
                    </div>

                    {!hiddenQuery && (
                      <div className="flex w-full flex-col gap-[4px]">
                        {item.query.map((query, i) => {
                          return (
                            <div
                              key={i}
                              onMouseEnter={() =>
                                setFocused({
                                  type: 'query',
                                  index: i,
                                })
                              }
                              onMouseLeave={() =>
                                setFocused({
                                  type: '',
                                  index: -1,
                                })
                              }
                              className="flex items-center gap-[8px]"
                            >
                              <div className="flex items-center gap-[2px]">
                                <div className="min-h-[16px] min-w-[16px]">
                                  {i === focused.index &&
                                    focused.type === 'query' && (
                                      <div
                                        onClick={() => {
                                          setValidateValue((prevState) => ({
                                            ...prevState,
                                            query: prevState.query
                                              .filter((q) => q !== i)
                                              .map((q) => {
                                                if (i < q) return (q -= 1)

                                                return q
                                              }),
                                          }))
                                          setValidate((prevState) => ({
                                            ...prevState,
                                            query: prevState.query
                                              .filter((q) => q !== i)
                                              .map((q) => {
                                                if (i < q) return (q -= 1)

                                                return q
                                              }),
                                          }))
                                          setValidateDuplicate((prevState) => ({
                                            ...prevState,
                                            query: prevState.query
                                              .filter((q) => q !== i)
                                              .map((q) => {
                                                if (i < q) return (q -= 1)

                                                return q
                                              }),
                                          }))
                                          setValidateDescription(
                                            (prevState) => ({
                                              ...prevState,
                                              query: prevState.query
                                                .filter((q) => q !== i)
                                                .map((q) => {
                                                  if (i < q) return (q -= 1)

                                                  return q
                                                }),
                                            })
                                          )
                                          removeParams(item.id, i, 'query')
                                        }}
                                        className="icon-close-request-config cursor-pointer"
                                      ></div>
                                    )}
                                </div>
                                <Input
                                  key={`query-key-${i}`}
                                  onChange={(e) => {
                                    updateParams(
                                      item.id,
                                      'query',
                                      'key',
                                      i,
                                      e.target.value
                                    )
                                  }}
                                  onBlur={() => {
                                    const regex = /^[a-zA-Z0-9_-]{1,255}$/
                                    const value = query.key

                                    if (value && !regex.test(value)) {
                                      if (!validate.query.find((n) => n === i))
                                        setValidate((prevState) => ({
                                          ...prevState,
                                          query: [...prevState.query, i],
                                        }))
                                    } else {
                                      setValidate((prevState) => ({
                                        ...prevState,
                                        query: prevState.query.filter(
                                          (q) => q !== i
                                        ),
                                      }))
                                    }

                                    const duplicateQuery = findDuplicateKeys(
                                      item.query
                                    )

                                    setValidateDuplicate((prevState) => ({
                                      ...prevState,
                                      query: duplicateQuery,
                                    }))
                                  }}
                                  isError={
                                    validate.query.find((n) => n === i) !==
                                      undefined ||
                                    validateDuplicate.query.find(
                                      (n) => n === i
                                    ) !== undefined
                                  }
                                  placeholder="Key"
                                  classNameInputWrapper="w-[198px]"
                                  value={query.key}
                                />
                              </div>
                              <Input
                                onChange={(e) => {
                                  updateParams(
                                    item.id,
                                    'query',
                                    'description',
                                    i,
                                    e.target.value
                                  )
                                }}
                                isError={validateDescription.query.includes(i)}
                                onBlur={() => {
                                  const value = query?.description?.trim() ?? ''
                                  if (value !== query.description)
                                    updateParams(
                                      item.id,
                                      'query',
                                      'description',
                                      i,
                                      value
                                    )
                                }}
                                maxLength={255}
                                isFullWidth
                                placeholder="Description"
                                classNameInputWrapper="max-w-[250px]"
                                value={query.description}
                              />
                              <Input
                                onChange={(e) => {
                                  updateParams(
                                    item.id,
                                    'query',
                                    'value',
                                    i,
                                    e.target.value
                                  )
                                }}
                                isError={
                                  validateValue.query.find((n) => n === i) !==
                                  undefined
                                }
                                onBlur={() => {
                                  const value = query.value.trim()
                                  if (value !== query.value)
                                    updateParams(
                                      item.id,
                                      'query',
                                      'value',
                                      i,
                                      value
                                    )
                                }}
                                maxLength={1000}
                                isFullWidth
                                placeholder="Value"
                                className="w-full"
                                value={query.value}
                              />
                            </div>
                          )
                        })}

                        <Text
                          onClick={() => addParams(item.id, 'query')}
                          className="cursor-pointer self-end bg-Main2-02 bg-clip-text px-2 text-Primary-Color hover:text-transparent"
                          type="subBody"
                          variant="semibold"
                        >
                          Add
                        </Text>
                      </div>
                    )}
                  </div>
                )}

                {(item.method === ToolHTTPRequestParameterMethodTypes.PUT ||
                  item.method === ToolHTTPRequestParameterMethodTypes.PATCH ||
                  item.method === ToolHTTPRequestParameterMethodTypes.POST) && (
                  <div className="flex flex-col gap-[8px]">
                    <div
                      onClick={() => setHiddenBody(!hiddenBody)}
                      className="flex w-full cursor-pointer items-center justify-between"
                    >
                      <div className="flex items-center gap-[4px]">
                        <Text
                          type="body"
                          variant="medium"
                          className="pl-[22px] text-Primary-Color"
                        >
                          Body
                        </Text>
                        {errorMessageBody()}
                      </div>
                      <Icon
                        name="vuesax-bold-arrow-rights-2"
                        size={16}
                        color="#2D0136"
                        className={!hiddenBody ? '' : 'rotate-90'}
                      />
                    </div>

                    {!hiddenBody && (
                      <div className="flex w-full flex-col gap-2">
                        <div className="flex items-center gap-[8px] pl-[18px]">
                          <Select
                            className="w-full"
                            data={BODY_TYPE_LIST}
                            selected={BODY_TYPE_LIST.find(
                              (type) => type.value === item.body_type
                            )}
                            onChangeSelectedValue={(value) => {
                              setValidate((prop) => ({
                                ...prop,
                                body: [],
                                body_raw: [],
                                body_form_data: [],
                              }))
                              setValidateValue((prop) => ({
                                ...prop,
                                body: [],
                                body_raw: [],
                                body_form_data: [],
                              }))
                              setValidateDescription((prop) => ({
                                ...prop,
                                body: [],
                                body_raw: [],
                                body_form_data: [],
                              }))
                              updateBodyType(item.id, value.value)
                            }}
                          />
                        </div>

                        <div className="flex w-full flex-col gap-1">
                          <BodyItem
                            focused={focused}
                            setFocused={setFocused}
                            validateValue={validateValue}
                            setValidateValue={setValidateValue}
                            setValidate={setValidate}
                            removeParams={removeParams}
                            updateParams={updateParams}
                            item={item}
                            validate={validate}
                            validateDuplicate={validateDuplicate}
                            validateDescription={validateDescription}
                            setValidateDescription={setValidateDescription}
                            setValidateDuplicate={setValidateDuplicate}
                          />

                          {item.body_type !== 'RAW_JSON' && (
                            <Text
                              onClick={() =>
                                addParams(
                                  item.id,
                                  item.body_type === 'FORM_DATA'
                                    ? 'body_form_data'
                                    : 'body'
                                )
                              }
                              className="cursor-pointer self-end bg-Main2-02 bg-clip-text px-2 text-Primary-Color hover:text-transparent"
                              type="subBody"
                              variant="semibold"
                            >
                              Add
                            </Text>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="h-[1px] min-h-[1px] w-full bg-neutral-200"></div>
          </div>
          <TextArea
            maxLength={1000}
            label="When to use"
            className="!h-[119px] !min-h-[119px] py-[4px]"
            value={item.prompt ?? ''}
            placeholder="Describe when the API request will be sent"
            onChange={(e) => {
              updateConfig(item.id, 'prompt', e)
            }}
            onBlur={() => {
              if (item.prompt) {
                const value = item.prompt.trim()
                if (value !== item.prompt)
                  updateConfig(item.id, 'prompt', value)
              }
            }}
            isError={validatePrompt}
          />
        </div>
      )}
    </div>
  )
}

export default memo(RequestConfig)
