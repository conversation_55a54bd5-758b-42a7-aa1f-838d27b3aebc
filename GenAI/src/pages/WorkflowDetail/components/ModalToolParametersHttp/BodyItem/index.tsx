/* eslint-disable react/jsx-key */
import Input from '@/components/Input'
import TextArea from '@/components/TextArea'
import { Editor } from '@monaco-editor/react'
import clsx from 'clsx'
import { IField, findDuplicateKeys } from '..'
import FormDataItem from '../FormDataItem'

interface BodyItemProps {
  focused: any
  setFocused: any
  validateValue: any
  setValidateValue: any
  setValidate: any
  removeParams: any
  item: any
  validate: any
  validateDuplicate: any
  updateParams: (
    id: string,
    field: IField,
    section: string,
    indexToUpdate: number,
    newAuth: string | Array<any>
  ) => void
  validateDescription: any
  setValidateDescription: any
  setValidateDuplicate: any
}

const BodyItem = ({
  focused,
  setFocused,
  validateValue,
  setValidateValue,
  setValidate,
  removeParams,
  updateParams,
  item,
  validate,
  validateDuplicate,
  validateDescription,
  setValidateDescription,
  setValidateDuplicate,
}: BodyItemProps) => {
  if (item.body_type === 'RAW_JSON') {
    return (
      <>
        {item.body_raw.map((bo, i) => (
          <div className="flex items-center gap-2 pb-1 pl-[18px]">
            <Editor
              height={128}
              options={{
                fontSize: 12,
                lineNumbersMinChars: 2,
                glyphMargin: false,
                lineNumbers: 'on',
                wordWrap: 'on',
                // wordWrapColumnCount: 80,
                automaticLayout: true,
                minimap: {
                  enabled: false,
                },
              }}
              className={clsx(
                'rounded-lg border border-border-base-icon px-3 py-2',
                validateValue.body_raw.includes(i) && '!border-Error-Color'
              )}
              defaultLanguage="json"
              onChange={(value) =>
                updateParams(item.id, 'body_raw', 'value', i, value ?? '')
              }
              value={bo.value}
              // onMount={handleEditorDidMount}
            />
            <TextArea
              className="h-32 min-w-[257px]"
              textAreaClassName="placeholder:text-Placeholder"
              placeholder="Describe what is included in body of the request"
              value={bo.description}
              onChange={(value) => {
                updateParams(item.id, 'body_raw', 'description', i, value)
              }}
              isError={validateDescription.body_raw.includes(i)}
              onBlur={() => {
                const value = bo.description.trim()
                if (value !== bo.description)
                  updateParams(item.id, 'body_raw', 'description', i, value)
              }}
              maxLength={255}
            />
          </div>
        ))}
      </>
    )
  }

  if (item.body_type === 'FORM_DATA') {
    return (
      <>
        {item.body_form_data.map((bo, i) => (
          <FormDataItem
            index={i}
            body={bo}
            item={item}
            focused={focused}
            setFocused={setFocused}
            validate={validate}
            setValidate={setValidate}
            validateValue={validateValue}
            setValidateValue={setValidateValue}
            validateDescription={validateDescription}
            setValidateDescription={setValidateDescription}
            validateDuplicate={validateDuplicate}
            removeParams={removeParams}
            updateParams={updateParams}
            setValidateDuplicate={setValidateDuplicate}
          />
        ))}
      </>
    )
  }

  return (
    <>
      {item.body.map((bo, i) => (
        <div
          onMouseEnter={() =>
            setFocused({
              type: 'body',
              index: i,
            })
          }
          onMouseLeave={() =>
            setFocused({
              type: '',
              index: -1,
            })
          }
          className="flex items-center gap-[8px]"
        >
          <div className="flex items-center gap-[2px]">
            <div className="h-[16px] min-h-[16px] w-[16px] min-w-[16px]">
              {i === focused.index && focused.type === 'body' && (
                <div
                  onClick={() => {
                    setValidateValue((prevState) => ({
                      ...prevState,
                      body: prevState.body
                        .filter((body) => body !== i)
                        .map((body) => {
                          if (i < body) return (body -= 1)

                          return body
                        }),
                    }))

                    setValidateDuplicate((prevState) => ({
                      ...prevState,
                      body: prevState.body
                        .filter((body) => body !== i)
                        .map((body) => {
                          if (i < body) return (body -= 1)

                          return body
                        }),
                    }))
                    setValidate((prevState) => ({
                      ...prevState,
                      body: prevState.body
                        .filter((body) => body !== i)
                        .map((body) => {
                          if (i < body) return (body -= 1)

                          return body
                        }),
                    }))
                    setValidateDescription((prevState) => ({
                      ...prevState,
                      body: prevState.body
                        .filter((body) => body !== i)
                        .map((body) => {
                          if (i < body) return (body -= 1)

                          return body
                        }),
                    }))
                    removeParams(item.id, i, 'body')
                  }}
                  className="icon-close-request-config cursor-pointer"
                ></div>
              )}
            </div>

            <Input
              onChange={(e) => {
                updateParams(item.id, 'body', 'key', i, e.target.value)
              }}
              onBlur={() => {
                const regex = /^[a-zA-Z0-9_-]{1,255}$/
                const value = bo.key

                if (value && !regex.test(value)) {
                  if (!validate.body.find((n) => n === i))
                    setValidate((prevState) => ({
                      ...prevState,
                      body: [...prevState.body, i],
                    }))
                } else {
                  setValidate((prevState) => ({
                    ...prevState,
                    body: prevState.body.filter((q) => q !== i),
                  }))
                }

                const duplicateBody = findDuplicateKeys(item.body)

                setValidateDuplicate((prevState) => ({
                  ...prevState,
                  body: duplicateBody,
                }))
              }}
              isError={
                validate.body.find((n) => n === i) !== undefined ||
                validateDuplicate.body.find((n) => n === i) !== undefined
              }
              placeholder="Key"
              classNameInputWrapper={clsx(
                'min-w-[198px]',
                item.body_type === 'FORM_DATA' && 'ml-[6px]'
              )}
              value={bo.key}
            />
          </div>

          <Input
            onChange={(e) => {
              updateParams(item.id, 'body', 'description', i, e.target.value)
            }}
            isError={validateDescription.body.includes(i)}
            onBlur={() => {
              const value = bo.description.trim()
              if (value !== bo.description)
                updateParams(item.id, 'body', 'description', i, value)
            }}
            maxLength={255}
            isFullWidth
            placeholder="Description"
            classNameInputWrapper="max-w-[250px]"
            value={bo.description}
          />

          <Input
            onChange={(e) => {
              updateParams(item.id, 'body', 'value', i, e.target.value)
            }}
            isError={validateValue.body.find((n) => n === i) !== undefined}
            onBlur={() => {
              const value = bo.value.trim()
              if (value !== bo.value)
                updateParams(item.id, 'body', 'value', i, value)
            }}
            maxLength={1000}
            isFullWidth
            placeholder="Value"
            className="w-full"
            value={bo.value}
          />
        </div>
      ))}
    </>
  )
}

export default BodyItem
