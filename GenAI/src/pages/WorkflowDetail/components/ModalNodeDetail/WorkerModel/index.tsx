import { memo, useContext } from 'react'
import Text from '@/components/Text'
import { ModelsContext } from '@/pages/Workers/contexts'
import Item from './Item'

const WorkerModel = ({ onChangeWorkerModel, modelId, error }: IProps) => {
  const { models } = useContext(ModelsContext)

  return (
    <div className="w-full">
      <Text
        variant="medium"
        className="pl-1 text-Primary-Color"
        elementType="div"
      >
        Add model
      </Text>
      <div className="mt-5 flex w-full gap-3 px-2 pb-[6px]">
        {models.map((model) => (
          <Item
            key={model.id}
            api_type={model.api_type}
            description={model.description}
            name={model.name}
            onClick={() => onChangeWorkerModel?.(model.id)}
            isActive={modelId === model.id}
            error={error}
          />
        ))}
      </div>
    </div>
  )
}

interface IProps {
  onChangeWorkerModel: (model: string) => void
  modelId?: string
  error?: boolean
}

export default memo(WorkerModel)
