import {
  NodeData_Input,
  ToolHTTPRequestParameterBodyTypes,
  ToolHTTPRequestParameterMethodTypes,
  ToolItemHTTPRequestParameter,
  ToolItemPDFGenerateParameter,
  ToolItem_Input,
  ToolParamRecommendParameter_Input,
  externalKnowledgeBaseGetListColumns,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import ModalAddToolkit from '@/components/ModalAddToolkit'
import { ToolkitItem } from '@/components/ModalAddToolkit/ToolkitItem'
import { ToolkitItemMode } from '@/components/ModalAddToolkit/ToolkitItem/ToolkitItem'
import {
  ChangingGeneralToolParam,
  GeneralToolParamConfigurationData,
  GeneralToolParamConfigurations,
  SettingBuiltInTool,
} from '@/components/ModalAddToolkit/types'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE } from '@/constants'
import { TOOL_FUNCTIONALITY, TOOL_TYPE } from '@/pages/Tools/const'
import { colors } from '@/theme'
import clsx from 'clsx'
import { isEmpty, isNil } from 'lodash'
import { memo, useEffect, useMemo, useState } from 'react'
import ModalGeneralToolParameters from '../../ModalGeneralToolParameters'
import ModalToolParameters from '../../ModalToolParameters'
import ModalToolParametersDoc from '../../ModalToolParametersDoc'
import ModalToolParametersExcel from '../../ModalToolParametersExcel'
import ModalToolParametersHttp from '../../ModalToolParametersHttp'
import ModalToolParametersPdf from '../../ModalToolParametersPdf'
import ToolAvatarGroup from './components/ToolAvatarGroup'

interface IProps {
  worker: Partial<NodeData_Input>
  onChangeWorkerToolKits: (tools: ToolItem_Input[]) => void
  className?: string
  limit?: number
  error?: boolean
  settingBuiltInTools: SettingBuiltInTool
  setSettingBuiltInTools: (config: SettingBuiltInTool) => void
  generalToolParamConfigurations: GeneralToolParamConfigurations
  setGeneralToolParamConfigurations: (
    config: GeneralToolParamConfigurations
  ) => void
  settingBuiltInToolsHttp?: any
  setSettingBuiltInToolsHttp: (config: any) => void
  onChangeGeneralTool: ({
    toolId,
    inputParameters,
    environmentVariables,
  }: ChangingGeneralToolParam) => void
}

const WorkerToolKits = ({
  worker,
  onChangeWorkerToolKits,
  className,
  limit,
  error,
  settingBuiltInTools,
  setSettingBuiltInTools,
  generalToolParamConfigurations,
  setGeneralToolParamConfigurations,
  settingBuiltInToolsHttp,
  setSettingBuiltInToolsHttp,
  onChangeGeneralTool,
}: IProps) => {
  const { tools } = worker

  const [openToolList, setOpenToolList] = useState<boolean>(false)
  const [isOpen, setIsOpen] = useState(false)

  const [selectedToolInfo, setSelectedToolInfo] = useState<ToolItem_Input>()
  const [isOpenModalToolParameters, setOpenModalToolParameters] =
    useState(false)

  const selectedTool = useMemo(() => {
    if (selectedToolInfo) {
      return selectedToolInfo.id
    }
    return undefined
  }, [selectedToolInfo])

  const handleSettingBuiltInToolByToolId = (
    toolSettings?: ToolParamRecommendParameter_Input
  ) => {
    setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
      ...prev,
      [selectedTool!]: {
        ...toolSettings,
        isError: false,
      },
    }))
  }

  const handleSettingBuiltInToolHttpByToolId = (
    toolSettings?: Array<ToolItemHTTPRequestParameter>
  ) => {
    if (toolSettings) {
      setSettingBuiltInToolsHttp((prev: any) => ({
        ...prev,
        [selectedTool!]: toolSettings,
      }))

      setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
        ...prev,
        [selectedTool!]: {
          isError: false,
        },
      }))
    }
  }

  const handleSettingAndSubmit = (
    toolSettings?:
      | ToolParamRecommendParameter_Input
      | ToolItemPDFGenerateParameter
  ) => {
    const _tools = tools?.map((tool) => {
      if (tool.id === selectedTool) {
        const _tool = {
          ...tool,
        }
        if (
          tool.tool_functionality === TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
        ) {
          _tool.tool_parameters_slide_generate = toolSettings as any
        } else if (
          tool.tool_functionality === TOOL_FUNCTIONALITY.PDF_GENERATE_FILE
        ) {
          _tool.tool_parameters_pdf_generate = toolSettings as any
        } else if (
          tool.tool_functionality === TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
        ) {
          _tool.tool_parameters_excel_generate = toolSettings as any
        } else if (
          tool.tool_functionality === TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
        ) {
          _tool.tool_parameters_docx_generate = toolSettings as any
        } else {
          _tool.tool_parameters =
            toolSettings as ToolParamRecommendParameter_Input
        }
        return _tool
      }
      return tool
    })

    onChangeWorkerToolKits(_tools!)
    handleSettingBuiltInToolByToolId(toolSettings as any)
  }

  const handleSaveGeneralTool = ({
    inputParameters,
    environmentVariables,
  }: GeneralToolParamConfigurationData) => {
    setOpenModalToolParameters(false)

    const _tools = tools?.map((tool) => {
      if (tool.id === selectedTool) {
        if (!isNil(inputParameters)) {
          tool.input_parameters = inputParameters
        }
        if (!isNil(environmentVariables)) {
          tool.environment_variables = environmentVariables
        }

        return { ...tool }
      }
      return tool
    })

    onChangeWorkerToolKits(_tools!)

    onChangeGeneralTool({
      toolId: selectedTool,
      inputParameters,
      environmentVariables,
    })
  }

  const handleSettingAndSubmitHttp = (
    toolSettings?: Array<ToolItemHTTPRequestParameter>
  ) => {
    const _tools = tools?.map((tool) => {
      if (tool.id === selectedTool) {
        return {
          ...tool,
          tool_parameters_http_request: toolSettings,
        }
      }
      return tool
    })

    onChangeWorkerToolKits(_tools!)

    handleSettingBuiltInToolHttpByToolId(toolSettings)
  }

  const initSettingBuiltInTools = async () => {
    if (tools?.length) {
      tools?.map(async (tool) => {
        if (tool?.tool_type === TOOL_TYPE.BUILT_IN) {
          if (tool?.tool_functionality === TOOL_FUNCTIONALITY.RECOMMENDATION) {
            if (isEmpty(tool?.tool_parameters)) {
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  isError: true,
                },
              }))

              return
            }

            const {
              tool_parameters: {
                criterion_parameter,
                profile_parameter,
                destination_parameter,
                output_parameter,
              },
            } = tool

            if (
              !criterion_parameter?.criterion_source_id ||
              !criterion_parameter?.columns?.length ||
              !profile_parameter?.profile_source_id ||
              !profile_parameter?.columns?.length ||
              !destination_parameter?.destination_source_id ||
              !destination_parameter?.columns?.length ||
              !output_parameter?.output_source_id ||
              !output_parameter?.columns?.length
            ) {
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool?.tool_parameters,
                  isError: true,
                },
              }))

              return
            }

            const [destinationColumnsRes, criterionColumnsRes] =
              await Promise.all([
                externalKnowledgeBaseGetListColumns({
                  query: {
                    external_knowledge_base_id:
                      destination_parameter?.destination_source_id || '',
                  },
                }),
                externalKnowledgeBaseGetListColumns({
                  query: {
                    external_knowledge_base_id:
                      criterion_parameter?.criterion_source_id || '',
                  },
                }),
              ])

            if (
              destinationColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS &&
              criterionColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS
            ) {
              const { data: destinationColumnsData } = destinationColumnsRes
              const { data: criterionColumnsData } = criterionColumnsRes

              const validCriterionColumns =
                criterion_parameter?.columns?.filter((item) =>
                  criterionColumnsData?.data?.find(
                    (column) => column.column_name === item.column_name
                  )
                )

              const validDestinationColumns =
                destination_parameter?.columns?.filter((item) =>
                  destinationColumnsData?.data?.find(
                    (column) => column.column_name === item.column_name
                  )
                )

              const validProfileColumns = profile_parameter?.columns?.filter(
                (item) =>
                  criterionColumnsData?.data?.find(
                    (column) => column.column_name === item.column_name
                  )
              )

              const validOutputColumns = output_parameter?.columns?.filter(
                (item) =>
                  destinationColumnsData?.data?.find(
                    (column) => column.column_name === item.column_name
                  )
              )

              if (
                !validCriterionColumns?.length ||
                !validDestinationColumns?.length ||
                !validOutputColumns?.length ||
                !validProfileColumns?.length
              ) {
                setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                  ...prev,
                  [tool.id]: {
                    ...tool?.tool_parameters,
                    isError: true,
                  },
                }))

                return
              }

              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool?.tool_parameters,
                  isError: false,
                },
              }))
            } else {
              // Error handling
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool?.tool_parameters,
                  isError: true,
                },
              }))
            }
          } else if (
            tool?.tool_functionality === TOOL_FUNCTIONALITY.HTTPREQUEST
          ) {
            if (tool?.tool_parameters_http_request) {
              if (
                tool?.tool_parameters_http_request.find(
                  (n) => !n.url || !n.prompt
                )
              ) {
                setSettingBuiltInTools((prev: any) => ({
                  ...prev,
                  [tool.id]: {
                    isError: true,
                  },
                }))
              } else {
                const regex = /^[a-zA-Z0-9_-]{1,255}$/

                if (
                  tool?.tool_parameters_http_request.find((item) => {
                    if (item?.headers && item?.headers.length) {
                      item?.headers?.map((n: any) => {
                        if (!n.token && n.auth === 'Authorization') {
                          return true
                        }
                        if (
                          !n.token?.split(':')?.[0] &&
                          n.auth === 'Authorization'
                        ) {
                          return true
                        }

                        if (
                          !n.token?.split(':')?.[1] &&
                          n.auth === 'Authorization'
                        ) {
                          return true
                        }
                        if (
                          (n.auth && !regex.test(n.auth)) ||
                          (n.auth !== 'Authorization' &&
                            !n.auth &&
                            n.description)
                        ) {
                          return true
                        } else if (
                          n.auth !== 'Authorization' &&
                          n.auth &&
                          regex.test(n.auth) &&
                          !n.description
                        ) {
                          return true
                        }

                        if (n.auth !== 'Authorization') {
                          if (!n.auth && n.token) {
                            return true
                          }
                          if (!n.description && n.token) {
                            return true
                          }
                        }
                      })
                    }

                    return false
                  })
                ) {
                  setSettingBuiltInTools((prev: any) => ({
                    ...prev,
                    [tool.id]: {
                      isError: true,
                    },
                  }))
                } else if (
                  tool?.tool_parameters_http_request.find((item) => {
                    if (
                      item.method !==
                        ToolHTTPRequestParameterMethodTypes.POST &&
                      item?.query_parameters &&
                      item?.query_parameters.length
                    ) {
                      item?.query_parameters?.map((n) => {
                        if (
                          (n.key && !regex.test(n.key)) ||
                          (!n.key && n.description)
                        ) {
                          return true
                        } else if (
                          n.key &&
                          regex.test(n.key) &&
                          !n.description
                        ) {
                          return true
                        }

                        if (!n.key && n.value) {
                          return true
                        }
                        if (!n.description && n.value) {
                          return true
                        }
                      })
                    }

                    return false
                  })
                ) {
                  setSettingBuiltInTools((prev: any) => ({
                    ...prev,
                    [tool.id]: {
                      isError: true,
                    },
                  }))
                } else if (
                  tool?.tool_parameters_http_request.find((item) => {
                    if (
                      item.method === ToolHTTPRequestParameterMethodTypes.PUT ||
                      item.method ===
                        ToolHTTPRequestParameterMethodTypes.PATCH ||
                      item.method === ToolHTTPRequestParameterMethodTypes.POST
                    ) {
                      if (
                        item.body_type ===
                          ToolHTTPRequestParameterBodyTypes.JSON &&
                        item?.body &&
                        item?.body?.length
                      ) {
                        item?.body?.map((n) => {
                          if ((n.key && !regex.test(n.key)) || !n.key) {
                            return true
                          }
                          if (
                            (n.key && regex.test(n.key) && !n.description) ||
                            (!n.description && n.value)
                          ) {
                            return true
                          }
                        })
                      } else if (
                        item.body_type ===
                          ToolHTTPRequestParameterBodyTypes.RAW_JSON &&
                        item?.body_raw &&
                        item?.body_raw?.length
                      ) {
                        item?.body_raw?.map((n) => {
                          if (!n.value) {
                            return true
                          }
                          if (!n.description) {
                            return true
                          }
                        })
                      } else if (
                        item.body_type ===
                          ToolHTTPRequestParameterBodyTypes.FORM_DATA &&
                        item?.body_form_data &&
                        item?.body_form_data?.length
                      ) {
                        item?.body_form_data?.map((n) => {
                          if ((n?.key && !regex.test(n?.key)) || !n?.key) {
                            return true
                          }
                          if (
                            (n?.key && regex.test(n?.key) && !n.description) ||
                            (n?.type === 'string' &&
                              !n?.description &&
                              n?.value) ||
                            (n?.type === 'file' &&
                              !n.description &&
                              n?.files?.length)
                          ) {
                            return true
                          }
                          if (
                            n?.type === 'file' &&
                            n?.key &&
                            regex.test(n?.key) &&
                            !n?.files?.length
                          ) {
                            return true
                          }
                        })
                      }
                    }

                    return false
                  })
                ) {
                  setSettingBuiltInTools((prev: any) => ({
                    ...prev,
                    [tool.id]: {
                      isError: true,
                    },
                  }))
                }
              }
              setSettingBuiltInToolsHttp((prev: any) => ({
                ...prev,
                [tool.id]: tool?.tool_parameters_http_request,
              }))
            }

            if (isEmpty(tool?.tool_parameters_http_request)) {
              setSettingBuiltInTools((prev: any) => ({
                ...prev,
                [tool.id]: {
                  isError: true,
                },
              }))
            }
          } else if (
            tool?.tool_functionality === TOOL_FUNCTIONALITY.PDF_GENERATE_FILE
          ) {
            if (tool?.tool_parameters_pdf_generate) {
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool.tool_parameters_pdf_generate,
                } as any,
              }))
            }
          } else if (
            tool?.tool_functionality === TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
          ) {
            if (tool?.tool_parameters_excel_generate) {
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool.tool_parameters_excel_generate,
                } as any,
              }))
            }
          } else if (
            tool?.tool_functionality === TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
          ) {
            if (tool?.tool_parameters_docx_generate) {
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool.tool_parameters_docx_generate,
                } as any,
              }))
            }
          } else if (
            tool?.tool_functionality === TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
          ) {
            if (tool?.tool_parameters_slide_generate) {
              setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
                ...prev,
                [tool.id]: {
                  ...tool.tool_parameters_slide_generate,
                } as any,
              }))
            }
          }
        }
      })
    }
  }

  useEffect(() => {
    initSettingBuiltInTools()
  }, [])

  useEffect(() => {
    if (!tools?.length) {
      setOpenToolList(false)
    }
  }, [tools?.length])

  const modalEditToolParameters = useMemo(() => {
    if (!isOpenModalToolParameters) {
      return null
    }

    if (selectedToolInfo?.tool_functionality === TOOL_FUNCTIONALITY.GENERAL) {
      return (
        <ModalGeneralToolParameters
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          toolId={selectedTool!}
          generalToolParamConfigurations={
            generalToolParamConfigurations?.[selectedTool!]
          }
          onSave={handleSaveGeneralTool}
        />
      )
    } else if (
      selectedToolInfo?.tool_functionality === TOOL_FUNCTIONALITY.HTTPREQUEST
    ) {
      return (
        <ModalToolParametersHttp
          isOpen
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          settingBuiltInToolsHttp={settingBuiltInToolsHttp?.[selectedTool!]}
          handleSettingBuiltInToolHttpByToolId={handleSettingAndSubmitHttp}
        />
      )
    } else if (
      selectedToolInfo?.tool_functionality ===
        TOOL_FUNCTIONALITY.PDF_GENERATE_FILE ||
      selectedToolInfo?.tool_functionality ===
        TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
    ) {
      return (
        <ModalToolParametersPdf
          isOpen={isOpenModalToolParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          handleSaveParamsPdf={handleSettingAndSubmit}
          params={settingBuiltInTools?.[selectedTool!]}
        />
      )
    } else if (
      selectedToolInfo?.tool_functionality ===
      TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
    ) {
      return (
        <ModalToolParametersExcel
          isOpen={isOpenModalToolParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          handleSaveParamsExcel={handleSettingAndSubmit}
          params={settingBuiltInTools?.[selectedTool!]}
        />
      )
    } else if (
      selectedToolInfo?.tool_functionality ===
      TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
    ) {
      return (
        <ModalToolParametersDoc
          isOpen={isOpenModalToolParameters}
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          handleSaveParamsDoc={handleSettingAndSubmit}
          params={settingBuiltInTools?.[selectedTool!]}
        />
      )
    } else if (
      selectedToolInfo?.tool_functionality === TOOL_FUNCTIONALITY.RECOMMENDATION
    ) {
      return (
        <ModalToolParameters
          isOpen
          onClose={() => {
            setSelectedToolInfo(undefined)
            setOpenModalToolParameters(false)
          }}
          settingBuiltInTools={settingBuiltInTools?.[selectedTool!]}
          handleSettingBuiltInToolByToolId={handleSettingAndSubmit}
        />
      )
    }

    return <></>
  }, [isOpenModalToolParameters, selectedToolInfo, selectedTool])

  const hasErrorTool = useMemo(
    () => tools?.some((tool) => settingBuiltInTools[tool.id]?.isError),
    [tools, settingBuiltInTools]
  )

  return (
    <div className={clsx('flex h-full w-full', className)}>
      <div
        className={clsx(
          'flex h-full max-h-[207px] w-full flex-col items-center justify-center rounded-lg border border-border-base-icon px-3 py-2 duration-300',
          error && 'border !border-Error-Color',
          !openToolList && 'hover:bg-Hover-Color'
        )}
      >
        <div
          className="flex h-full w-full cursor-pointer items-center justify-between"
          onClick={() => setIsOpen(true)}
        >
          <div className="flex items-center gap-3">
            <Icon
              className="flex"
              name="tool-01"
              size={32}
              color={
                hasErrorTool ? colors['Error-Color'] : colors['Tertiary-Color']
              }
            />
            <div className="flex flex-col">
              <Text
                variant="medium"
                className={clsx(
                  'text-Tertiary-Color',
                  hasErrorTool && '!text-Error-Color'
                )}
              >
                Add toolkits
              </Text>
              <Text
                type="supportText"
                className={clsx(
                  'text-Tertiary-Color',
                  hasErrorTool && '!text-Error-Color'
                )}
              >
                Add tools to support worker execute task
              </Text>
            </div>
          </div>

          {!!tools?.length && (
            <div className="flex items-center gap-2">
              <ToolAvatarGroup className="mr-2" tools={tools} />
              <IconButton
                className={clsx('duration-300 hover:bg-Hover-2')}
                nameIcon={
                  openToolList ? 'Outline-Chevron-Up' : 'Outline-Chevron-Right'
                }
                sizeIcon={16}
                colorIcon={
                  openToolList
                    ? colors['Primary-Color']
                    : colors['Secondary-Color']
                }
                onClick={() => tools?.length && setOpenToolList(!openToolList)}
              />
            </div>
          )}
        </div>

        <div
          className={clsx(
            'my-0 flex h-[0px] w-full bg-border-base-icon opacity-0 duration-500',
            openToolList && '!my-2 !h-[1px] !opacity-100'
          )}
        />
        <div
          className={clsx(
            'genai-scrollbar flex max-h-[0px] w-full flex-col gap-2 overflow-y-auto rounded-lg border-border-base-icon opacity-0 duration-500',
            openToolList && '!max-h-[138px] !opacity-100',
            (tools?.length ?? 0) > 1 && '!duration-700'
          )}
        >
          {tools?.map((tool) => {
            return (
              <ToolkitItem
                key={tool.id}
                mode={ToolkitItemMode.PREVIEW}
                className="ml-[-8px] mr-[-8px] duration-300"
                toolkit={tool}
                isConfigured={true}
                onDelete={() => {
                  onChangeWorkerToolKits(
                    tools.filter((item) => item.id !== tool.id)
                  )
                }}
                openModalToolParameters={() => {
                  setSelectedToolInfo(tool)
                  setOpenModalToolParameters(true)
                }}
                isError={settingBuiltInTools[tool.id]?.isError}
              />
            )
          })}
        </div>
      </div>

      {isOpen && (
        <ModalAddToolkit
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          selected={tools || []}
          onConfirm={(
            selectedTools,
            newSettingBuiltInTools,
            newGeneralToolParamConfigurations,
            newSettingBuiltInToolsHttp
          ) => {
            onChangeWorkerToolKits(
              selectedTools?.map((tool) => {
                const _tool = {
                  ...tool,
                }
                if (!isEmpty(newSettingBuiltInTools?.[tool.id])) {
                  if (
                    tool.tool_functionality ===
                    TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
                  ) {
                    _tool.tool_parameters_slide_generate =
                      newSettingBuiltInTools?.[tool.id]
                  } else if (
                    tool.tool_functionality ===
                    TOOL_FUNCTIONALITY.PDF_GENERATE_FILE
                  ) {
                    _tool.tool_parameters_pdf_generate =
                      newSettingBuiltInTools?.[tool.id]
                  } else if (
                    tool.tool_functionality ===
                    TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
                  ) {
                    _tool.tool_parameters_excel_generate =
                      newSettingBuiltInTools?.[tool.id]
                  } else if (
                    tool.tool_functionality ===
                    TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
                  ) {
                    _tool.tool_parameters_docx_generate =
                      newSettingBuiltInTools?.[tool.id]
                  } else {
                    _tool.tool_parameters = newSettingBuiltInTools?.[tool.id]
                  }
                }

                if (!isEmpty(newGeneralToolParamConfigurations?.[tool.id])) {
                  _tool.input_parameters =
                    newGeneralToolParamConfigurations?.[
                      tool.id
                    ]?.inputParameters
                  _tool.environment_variables =
                    newGeneralToolParamConfigurations?.[
                      tool.id
                    ]?.environmentVariables
                }
                if (!isEmpty(newSettingBuiltInToolsHttp?.[tool.id])) {
                  _tool.tool_parameters_http_request =
                    newSettingBuiltInToolsHttp?.[tool.id]
                }

                return _tool
              })
            )
            setSettingBuiltInTools(newSettingBuiltInTools)
            setGeneralToolParamConfigurations(newGeneralToolParamConfigurations)
            setSettingBuiltInToolsHttp(newSettingBuiltInToolsHttp)
            setIsOpen(false)
            setOpenToolList(true)
          }}
          settingBuiltInTools={settingBuiltInTools}
          generalToolParamConfigurations={generalToolParamConfigurations}
          settingBuiltInToolsHttp={settingBuiltInToolsHttp}
          limitToolkit={limit}
        />
      )}

      {modalEditToolParameters}
    </div>
  )
}

export default memo(WorkerToolKits)
