import {
  ToolItem_Input,
  externalKnowledgeBaseGetListColumns,
} from '@/apis/client'
import Message from '@/components/Message'
import { SettingBuiltInTool } from '@/components/ModalAddToolkit/types'
import { HTTP_STATUS_CODE } from '@/constants'
import { TOOL_FUNCTIONALITY, TOOL_TYPE } from '@/pages/Tools/const'
import { isEmpty } from 'lodash'

interface IProps {
  selectedTools: ToolItem_Input[]
  settingBuiltInTools: SettingBuiltInTool
  setSettingBuiltInTools: (config: SettingBuiltInTool) => void
  settingBuiltInToolsHttp?: any
}

export const validateSettingBuiltInTools = async ({
  selectedTools,
  settingBuiltInTools,
  setSettingBuiltInTools,
  settingBuiltInToolsHttp,
}: IProps) => {
  const listBuiltInTools = selectedTools.filter(
    (tool) => tool.tool_type === TOOL_TYPE.BUILT_IN
  )

  if (!listBuiltInTools?.length) {
    return true
  }

  const invalidTool = []

  for (const tool of listBuiltInTools) {
    if (tool.tool_functionality === TOOL_FUNCTIONALITY.RECOMMENDATION) {
      if (isEmpty(settingBuiltInTools[tool.id])) {
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            isError: true,
          },
        }))
        invalidTool.push(tool)
        continue
      }

      if (settingBuiltInTools[tool.id]?.isError) {
        invalidTool.push(tool)
        continue
      }

      const {
        criterion_parameter,
        profile_parameter,
        destination_parameter,
        output_parameter,
      } = settingBuiltInTools[tool.id]

      if (
        !criterion_parameter?.criterion_source_id ||
        !criterion_parameter?.columns?.length ||
        !profile_parameter?.profile_source_id ||
        !profile_parameter?.columns?.length ||
        !destination_parameter?.destination_source_id ||
        !destination_parameter?.columns?.length ||
        !output_parameter?.output_source_id ||
        !output_parameter?.columns?.length
      ) {
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            isError: true,
          },
        }))
        invalidTool.push(tool)
        continue
      }

      const [destinationColumnsRes, criterionColumnsRes] = await Promise.all([
        externalKnowledgeBaseGetListColumns({
          query: {
            external_knowledge_base_id:
              destination_parameter?.destination_source_id || '',
          },
        }),
        externalKnowledgeBaseGetListColumns({
          query: {
            external_knowledge_base_id:
              criterion_parameter?.criterion_source_id || '',
          },
        }),
      ])

      if (
        destinationColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS &&
        criterionColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS
      ) {
        const { data: destinationColumnsData } = destinationColumnsRes
        const { data: criterionColumnsData } = criterionColumnsRes

        if (
          !destination_parameter?.columns?.some(
            (column: { column_name: string }) =>
              !isEmpty(
                destinationColumnsData?.data?.find(
                  (col) => col.column_name === column.column_name
                )
              )
          ) ||
          !criterion_parameter?.columns?.some(
            (column: { column_name: string }) =>
              !isEmpty(
                criterionColumnsData?.data?.find(
                  (col) => col.column_name === column.column_name
                )
              )
          )
        ) {
          setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
            ...prev,
            [tool.id]: {
              ...settingBuiltInTools[tool.id],
              isError: true,
            },
          }))

          invalidTool.push(tool)
          continue
        }

        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            ...settingBuiltInTools[tool.id],
            isError: false,
          },
        }))
        continue
      } else {
        // Error handling
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            ...settingBuiltInTools[tool.id],
            isError: true,
          },
        }))

        invalidTool.push(tool)
        continue
      }
    } else if (
      tool.tool_functionality === TOOL_FUNCTIONALITY.PDF_GENERATE_FILE ||
      tool.tool_functionality === TOOL_FUNCTIONALITY.SLIDE_GENERATE_FILE
    ) {
      const params = settingBuiltInTools[tool.id]
      if (!params?.prompt) {
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            isError: true,
          },
        }))
        invalidTool.push(tool)
      }
    } else if (
      tool.tool_functionality === TOOL_FUNCTIONALITY.EXCEL_GENERATE_FILE
    ) {
      const params = settingBuiltInTools[tool.id]
      if (!params?.prompt) {
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            isError: true,
          },
        }))
        invalidTool.push(tool)
      }
    } else if (
      tool.tool_functionality === TOOL_FUNCTIONALITY.DOCX_GENERATE_FILE
    ) {
      const params = settingBuiltInTools[tool.id]
      if (!params?.prompt) {
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            isError: true,
          },
        }))
        invalidTool.push(tool)
      }
    } else if (tool.tool_functionality === TOOL_FUNCTIONALITY.HTTPREQUEST) {
      if (isEmpty(settingBuiltInToolsHttp[tool.id])) {
        setSettingBuiltInTools((prev: SettingBuiltInTool) => ({
          ...prev,
          [tool.id]: {
            ...settingBuiltInTools[tool.id],
            isError: true,
          },
        }))
        invalidTool.push(tool)
      }
    }
  }

  if (invalidTool.length) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const _tool of invalidTool) {
      Message.error({
        message: 'Missing or invalid tool parameters',
      })
    }
    return false
  }

  return true
}
