import { KnowledgeBaseItem, NodeData_Input } from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import AddItem from '@/components/AddItem'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import ModalAddKnowledgeBase from '@/components/ModalAddKnowledgeBase'
import ModalAddKnowledgeBaseExternal, {
  CustomExternalKBPublic,
} from '@/components/ModalAddKnowledgeBaseExternal'
import ExternalKbItem from '@/components/ModalAddKnowledgeBaseExternal/components/ExternalKbItem'
import Text from '@/components/Text'
import IconFile from '@/components/Upload/IconFile'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useCallback, useEffect, useState } from 'react'
import KBFileAvatarGroup from './components/KBFileAvatarGroup'

interface IProps {
  worker: Partial<NodeData_Input>
  knowledgeBasePrompt?: string | null
  onChangeWorkerKnowledgeBase: (
    knowledgeBases: KnowledgeBaseItem[] | CustomExternalKBPublic[],
    knowledgeBasePrompt: string
  ) => void
}

const WorkerKnowledgeBase = ({
  worker,
  knowledgeBasePrompt,
  onChangeWorkerKnowledgeBase,
}: IProps) => {
  const [openKBList, setOpenKBList] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [isOpenExternal, setIsOpenExternal] = useState(false)

  const { knowledgeBases } = worker

  const isExternal = knowledgeBases?.some((item) => item.is_external)

  const handleDeleteKnowledgeBase = useCallback(
    (knowledgeBase: KnowledgeBaseItem | CustomExternalKBPublic) => {
      const selectedKnowledgeBases = knowledgeBases?.filter(
        (item) => item.id !== knowledgeBase.id
      )

      if (selectedKnowledgeBases?.length) {
        onChangeWorkerKnowledgeBase(
          selectedKnowledgeBases,
          knowledgeBasePrompt ?? ''
        )
      } else {
        onChangeWorkerKnowledgeBase([], '')
      }
    },
    [knowledgeBases, knowledgeBasePrompt]
  )

  const handleOpenAddingKB = useCallback(
    (e: any) => {
      e.preventDefault()
      e.stopPropagation()

      if (knowledgeBases?.length) {
        if (isExternal) {
          setIsOpenExternal(true)
        } else {
          setIsOpen(true)
        }
      }
    },
    [knowledgeBases, isExternal]
  )

  useEffect(() => {
    if (!knowledgeBases?.length) {
      setOpenKBList(false)
    }
  }, [knowledgeBases])

  return (
    <div
      className={clsx(
        'flex h-full max-h-[207px] w-full flex-col items-center justify-center rounded-lg border border-border-base-icon px-3 py-2 duration-300',
        !openKBList && 'hover:bg-Hover-Color'
      )}
    >
      <div
        className={clsx(
          'flex h-full w-full items-center justify-between',
          knowledgeBases?.length && 'cursor-pointer'
        )}
        onClick={handleOpenAddingKB}
      >
        <div className="flex items-center gap-3">
          <Icon
            className="flex"
            name="database-01"
            size={32}
            color={colors['Tertiary-Color']}
          />
          <div className="flex flex-col">
            <Text variant="medium" className="text-Tertiary-Color">
              Add knowledge base
            </Text>
            <Text type="supportText" className="text-Secondary-Color">
              Choose data source used for querying; searching & replying
            </Text>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {(!knowledgeBases?.length ||
              (knowledgeBases?.length && isExternal)) && (
              <Button
                className="px-2 duration-300 hover:!bg-Hover-2"
                size="small"
                text="Internal source"
                leftIcon={
                  <Icon
                    name="plus"
                    size={16}
                    color={colors['Tertiary-Color']}
                  />
                }
                onClick={() => setIsOpen(true)}
              />
            )}
            {(!knowledgeBases?.length ||
              (knowledgeBases?.length && !isExternal)) && (
              <Button
                className="px-2 duration-300 hover:!bg-Hover-2"
                size="small"
                text="External source"
                leftIcon={
                  <Icon
                    name="plus"
                    size={16}
                    color={colors['Tertiary-Color']}
                  />
                }
                onClick={() => setIsOpenExternal(true)}
              />
            )}
          </div>
          {!!knowledgeBases?.length && (
            <div className="flex items-center gap-2">
              <KBFileAvatarGroup
                className="mr-2"
                knowledgeBases={knowledgeBases}
              />
              <IconButton
                className={clsx('duration-300 hover:bg-Hover-2')}
                nameIcon={
                  openKBList ? 'Outline-Chevron-Up' : 'Outline-Chevron-Right'
                }
                sizeIcon={16}
                colorIcon={
                  openKBList
                    ? colors['Primary-Color']
                    : colors['Secondary-Color']
                }
                onClick={() =>
                  knowledgeBases?.length && setOpenKBList(!openKBList)
                }
              />
            </div>
          )}
        </div>
      </div>

      <div
        className={clsx(
          'my-0 flex h-[0px] w-full bg-border-base-icon opacity-0 duration-500',
          openKBList && '!my-2 !h-[1px] !opacity-100'
        )}
      />
      <div
        className={clsx(
          'genai-scrollbar flex max-h-[0px] w-full flex-col gap-2 overflow-y-auto rounded-lg border-border-base-icon opacity-0 duration-500',
          openKBList && '!max-h-[138px] p-1 !opacity-100',
          (knowledgeBases?.length ?? 0) > 1 && '!duration-700'
        )}
      >
        {!isExternal &&
          knowledgeBases?.map((knowledgeBase) => (
            <AddItem
              key={knowledgeBase.id}
              avatar={''}
              title={knowledgeBase.file_display_name!}
              content={knowledgeBase.file_description!}
              selected
              onClick={() => {}}
              directory={knowledgeBase.directoryName!}
              hideButton
              kbIcon={
                <IconFile size={36} fileExt={knowledgeBase.file_extension!} />
              }
              onDelete={() => handleDeleteKnowledgeBase(knowledgeBase)}
              isDelete
            />
          ))}
        {isExternal &&
          knowledgeBases?.map((knowledgeBase) => (
            <ExternalKbItem
              className="!gap-2 !p-1"
              key={knowledgeBase.id}
              title={knowledgeBase.file_display_name || ''}
              description={knowledgeBase.file_description || ''}
              selected={false}
              onClick={() => {}}
              isPrivate={!!knowledgeBase.is_private}
              onDelete={() => handleDeleteKnowledgeBase(knowledgeBase)}
              isDelete
              withoutAction
            />
          ))}
      </div>

      {isOpen && (
        <ModalAddKnowledgeBase
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          selected={!isExternal ? knowledgeBases || [] : []}
          knowledgeBasePrompt={!isExternal ? knowledgeBasePrompt : ''}
          onConfirm={(
            selected: KnowledgeBaseItem[],
            knowledgeBasePrompt: string
          ) => onChangeWorkerKnowledgeBase(selected, knowledgeBasePrompt)}
        />
      )}
      <ModalAddKnowledgeBaseExternal
        isOpen={isOpenExternal}
        onClose={() => setIsOpenExternal(false)}
        selected={
          isExternal ? ((knowledgeBases || []) as CustomExternalKBPublic[]) : []
        }
        knowledgeBasePrompt={isExternal ? knowledgeBasePrompt : ''}
        onConfirm={(
          selected: CustomExternalKBPublic[],
          knowledgeBasePrompt: string
        ) => onChangeWorkerKnowledgeBase(selected, knowledgeBasePrompt)}
      />
    </div>
  )
}

export default memo(WorkerKnowledgeBase)
