import Icon from '@/assets/icon/Icon'
import Text from '@/components/Text'
import clsx from 'clsx'
import { memo, useMemo } from 'react'

const ConnectKnowledgeBase = ({
  setIsOpen,
  type = 'internal',
}: ConnectKnowledgeBaseProps) => {
  const title = useMemo(() => {
    if (type === 'internal') {
      return 'Connect internal source'
    }
    return 'Connect external source'
  }, [type])

  const description = useMemo(() => {
    if (type === 'internal') {
      return 'Choose knowledge file from your directories'
    }
    return 'Connect to your external data'
  }, [type])

  return (
    <div
      className={clsx(
        'flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-Highlight-Border hover:border-Base-Single-Color hover:bg-Hover-Color'
      )}
      onClick={() => setIsOpen(true)}
    >
      <div className="mb-2 flex rounded-xl bg-Main-Disable-2 p-1">
        <Icon
          name="database-01"
          size={44}
          gradient={['#642B734D', '#C6426E4D']}
        />
      </div>
      <Text variant="semibold" className="text-Primary-Color-Color">
        {title}
      </Text>
      <Text type="supportText" className="text-Secondary-Color">
        {description}
      </Text>
    </div>
  )
}

interface ConnectKnowledgeBaseProps {
  setIsOpen: (value: boolean) => void
  type?: 'internal' | 'external'
}

export default memo(ConnectKnowledgeBase)
