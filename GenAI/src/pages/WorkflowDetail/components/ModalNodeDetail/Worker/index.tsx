import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import IconButton from '@/components/IconButton'
import Text from '@/components/Text'
import { getUrlImage } from '@/helpers'
import { CustomAI } from '@/pages/Workers/assets/icons'
import { ModelsContext } from '@/pages/Workers/contexts'
import { WorkerType } from '@/pages/Workers/types'
import { CustomNodeData } from '@/pages/WorkflowDetail/types'
import { colors } from '@/theme'
import clsx from 'clsx'
import { memo, useContext, useState } from 'react'
import SelectAssignee from '../../SelectAssignee'

interface IProps {
  worker: CustomNodeData
  onChangeWorker: (worker: Partial<CustomNodeData>) => void
  isError?: boolean
}

const Worker = ({ worker, onChangeWorker, isError = false }: IProps) => {
  const [openSelectingAssignee, setOpenSelectingAssignee] = useState(false)
  const { models } = useContext(ModelsContext)

  const {
    workerName,
    workerId,
    workerAvatar,
    workerType,
    workerDescription,
    isNew,
    modelId,
  } = worker

  const getAvatarModel = (id: string | undefined | null) => {
    if (id) {
      const model = models.find((n) => n.id === id)

      return model?.image_url
    }
    return
  }

  return (
    <>
      <div className="flex h-full w-full flex-1 flex-col gap-1">
        <div className="flex items-center gap-1">
          <Text
            variant="medium"
            className="pl-1 text-Tertiary-Color"
            type="subBody"
          >
            Assign worker
          </Text>
          <IconButton
            nameIcon="vuesax-bold-info-circle"
            sizeIcon={16}
            colorIcon={colors['border-base-icon']}
            hoverColor={colors['Primary-Color']}
            tooltipText="Select worker who has the compatible ability to handle task in this workflow node"
          />
        </div>

        {workerId || (isError && !isNew) ? (
          <div
            className={clsx(
              'flex h-[79px] w-full gap-4 overflow-hidden rounded-lg border border-border-base-icon p-2',
              isError && '!border-Error-Color'
            )}
          >
            <div className="flex h-full min-w-fit max-w-[300px] flex-col items-center justify-center">
              {isError ? (
                <div className="flex size-[42px] items-center justify-center rounded-full bg-red-50">
                  <Text
                    value="E"
                    variant="semibold"
                    className="text-Error-Color"
                    type="title"
                  />
                </div>
              ) : (
                <div className="relative">
                  <Avatar
                    size="large"
                    avatarUrl={workerAvatar!}
                    avatarDefault={
                      <div className="flex size-[42px] items-center justify-center rounded-full bg-Background-Color">
                        <Icon
                          name={
                            workerType === WorkerType.HUMAN
                              ? 'Outline-FacesEmotionsStickers-ExpressionlessCircle'
                              : 'face-id-square'
                          }
                          size={28}
                          gradient={['#642B734D', '#C6426E4D']}
                        />
                      </div>
                    }
                  />

                  <div
                    style={{
                      backgroundImage: `url(${getAvatarModel(modelId) ? getUrlImage(getAvatarModel(modelId)) : ''}`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: '12px',
                      backgroundPosition: 'center',
                    }}
                    className="absolute bottom-[-2px] right-[-10px] z-10 flex size-[20px] min-w-[20px] items-center justify-center rounded-full border border-border-base-icon bg-white"
                  />
                </div>
              )}

              <div className="flex w-full items-center justify-center gap-1">
                <div className="flex w-5">
                  <CustomAI size={20} />
                </div>
                <Text
                  type="subBody"
                  variant="medium"
                  className={clsx(
                    'break-words',
                    isError ? 'text-Error-Color' : 'text-Primary-Color'
                  )}
                  elementType="div"
                  ellipsis
                >
                  {isError ? 'Error' : workerName}
                </Text>
                <IconButton
                  className="flex"
                  nameIcon="Outline-Chevron-Right"
                  sizeIcon={14}
                  colorIcon={colors['Primary-Color']}
                  onClick={() => setOpenSelectingAssignee(true)}
                />
              </div>
            </div>
            <Text
              className="genai-scrollbar w-full overflow-auto rounded-lg bg-Base-03 p-2"
              type="subBody"
              elementType="div"
            >
              {workerDescription}
            </Text>
          </div>
        ) : (
          <div
            className={clsx(
              'flex h-full max-h-[207px] w-full flex-col items-center justify-center rounded-lg border border-border-base-icon px-3 py-2 duration-300 hover:bg-Hover-Color',
              isError && '!border-Error-Color'
            )}
          >
            <div
              className="flex h-full w-full cursor-pointer items-center justify-between"
              onClick={() => setOpenSelectingAssignee(true)}
            >
              <div className="flex items-center gap-3">
                <Icon
                  className="flex"
                  name="face-id-square"
                  size={32}
                  color={colors['Tertiary-Color']}
                />
                <div className="flex flex-col">
                  <Text variant="medium" className="text-Tertiary-Color">
                    Add worker
                  </Text>
                  <Text type="supportText" className="text-Secondary-Color">
                    Determine which worker will complete this task and specify
                    what he needs to do
                  </Text>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <SelectAssignee
        open={openSelectingAssignee}
        isSpecialAssignee={isNew}
        assigneeSelected={
          worker?.workerId
            ? {
                id: worker?.workerId,
                name: worker?.workerName,
                worker_type: worker.workerType,
                avatar: worker?.workerAvatar,
                background: worker?.workerDescription,
                llm_model_id: worker?.modelId,
              }
            : undefined
        }
        onClose={() => setOpenSelectingAssignee(false)}
        onConfirm={(value) => {
          onChangeWorker(value)
          setOpenSelectingAssignee(false)
        }}
      />
    </>
  )
}

export default memo(Worker)
