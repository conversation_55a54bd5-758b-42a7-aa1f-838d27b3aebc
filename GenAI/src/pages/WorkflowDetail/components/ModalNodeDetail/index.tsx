/* eslint-disable max-len */

import { memo, useCallback, useEffect, useMemo, useState } from 'react'

import {
  KnowledgeBaseItem,
  ToolItem_Input,
  workflowPromptEnhancementWorkflowApi,
} from '@/apis/client'
import BaseModal from '@/components/BaseModal'
import { MessageDialog } from '@/components/DialogMessage'
import IconButton from '@/components/IconButton'
import Input from '@/components/Input'
import { CustomExternalKBPublic } from '@/components/ModalAddKnowledgeBaseExternal'
import {
  LIMIT_AUTO_PROCESSING_TOOLKIT,
  LIMIT_TOOLKIT,
} from '@/components/ModalAddToolkit/helpers'
import {
  ChangingGeneralToolParam,
  GeneralToolParamConfigurations,
  SettingBuiltInTool,
} from '@/components/ModalAddToolkit/types'
import Tabs from '@/components/Tabs'
import Text from '@/components/Text'
import TextArea from '@/components/TextArea'
import Toggle from '@/components/Toggle'
import { EWorkflowType } from '@/pages/Workflows/const'
import { colors } from '@/theme'
import clsx from 'clsx'
import { CustomNode } from '../../types'
import Worker from './Worker'
import WorkerKnowledgeBase from './WorkerKnowledgeBase'
import WorkerToolKits from './WorkerToolKits'
import Button from '@/components/Button'
import Icon from '@/assets/icon/Icon'
import { validateSettingBuiltInTools } from './helper'
import { HTTP_STATUS_CODE } from '@/constants'

export enum TABS {
  GENERAL = 'General',
  ADVANCED = 'Advanced options',
}

interface IProps<T> {
  workflowType?: EWorkflowType
  currentNode: Partial<T>
  isOpen: boolean
  onClose: () => void
  setCurrentNode: React.Dispatch<Partial<T>>
  handleUpdateNode: () => void
}

const ModalNodeDetail = ({
  currentNode,
  isOpen,
  onClose,
  setCurrentNode,
  handleUpdateNode,
  workflowType = EWorkflowType.conversation,
}: IProps<CustomNode>) => {
  const [enhancedPromptLoading, setEnhancedPromptLoading] = useState(false)

  const [currentTab, setCurrentTab] = useState<TABS>(TABS.GENERAL)
  const [isDirtyModal, setDirtyModal] = useState<boolean>(false)

  const [errors, setErrors] = useState({
    name: false,
    workerResponsibility: false,
    worker: false,
    // model: false,
  })

  const [settingBuiltInTools, setSettingBuiltInTools] =
    useState<SettingBuiltInTool>({})
  const [generalToolParamConfigurations, setGeneralToolParamConfigurations] =
    useState<GeneralToolParamConfigurations>({})
  const [settingBuiltInToolsHttp, setSettingBuiltInToolsHttp] = useState<any>(
    {}
  )

  const handleChangeGeneralTool = useCallback(
    ({
      toolId,
      inputParameters,
      environmentVariables,
    }: ChangingGeneralToolParam) => {
      if (!toolId) {
        return
      }

      setGeneralToolParamConfigurations((prop) => ({
        ...prop,
        [toolId]: {
          inputParameters,
          environmentVariables,
        },
      }))
    },
    []
  )

  const handleCloseModal = useCallback(() => {
    if (isDirtyModal) {
      MessageDialog.warning({
        mainMessage: 'Wanna leave?',
        subMessage: 'If continue, your changes may not be saved',
        onClick: () => {
          onClose()
          setDirtyModal(false)
        },
      })
    } else {
      onClose()
      setDirtyModal(false)
    }
  }, [isDirtyModal])

  const validator = () => {
    let result = true

    if (!currentNode?.data?.name?.trim()) {
      setErrors((prev) => ({
        ...prev,
        name: true,
      }))
      result = false
    } else {
      setErrors((prev) => ({
        ...prev,
        name: false,
      }))
    }

    if (!currentNode?.data?.workerId) {
      setErrors((prev) => ({
        ...prev,
        worker: true,
      }))
      result = false
    } else {
      setErrors((prev) => ({
        ...prev,
        worker: false,
      }))
    }

    if (!currentNode?.data?.workerResponsibility?.trim()) {
      setErrors((prev) => ({
        ...prev,
        workerResponsibility: true,
      }))
      result = false
    } else {
      setErrors((prev) => ({
        ...prev,
        workerResponsibility: false,
      }))
    }

    return result
  }

  const handlePromptEnhancement = useCallback(async () => {
    if (enhancedPromptLoading) {
      return
    }

    setEnhancedPromptLoading(true)

    const res = await workflowPromptEnhancementWorkflowApi({
      body: { prompt: currentNode?.data?.workerResponsibility },
    })

    if (res.status === HTTP_STATUS_CODE.SUCCESS) {
      setDirtyModal(true)

      setCurrentNode({
        ...currentNode,
        data: {
          ...currentNode.data,
          workerResponsibility: res.data?.data.enhanced_prompt ?? '',
        },
      })
    }

    setEnhancedPromptLoading(false)
  }, [enhancedPromptLoading, currentNode])

  const title = useMemo(() => {
    if (workflowType === EWorkflowType.conversation) {
      return 'Node Configuration'
    }
    return 'Processing node configuration'
  }, [workflowType])

  const subTitle = useMemo(() => {
    if (workflowType === EWorkflowType.conversation) {
      return 'Set up worker in each workflow step node to handle task'
    }
    return 'Set up workflow step node to handle task'
  }, [workflowType])

  useEffect(() => {
    if (isOpen && currentNode?.data?.tools?.length) {
      const generalToolConfiguration = currentNode?.data?.tools?.reduce(
        (previous, current) => ({
          ...previous,
          [current.id]: {
            inputParameters: current?.input_parameters ?? [],
            environmentVariables: current?.environment_variables ?? [],
          },
        }),
        {}
      )

      setGeneralToolParamConfigurations(generalToolConfiguration)
    }
  }, [isOpen])

  const renderContent = () => {
    if (
      currentTab === TABS.ADVANCED &&
      workflowType === EWorkflowType.conversation
    ) {
      return (
        <div className="genai-scrollbar flex h-[556px] w-[1280px] overflow-auto bg-white p-5">
          <div className="flex h-fit flex-col items-start gap-5">
            <div className="flex items-center gap-3 self-stretch">
              <div className="flex flex-[1_0_0] flex-col items-start justify-center">
                <Text variant="medium" className="text-Primary-Color">
                  Document citation
                </Text>
                <Text type="subBody" className="text-Secondary-Color">
                  Seamlessly generates citations based on information sourced
                  from the integrated knowledge base
                </Text>
              </div>
              <Toggle
                defaultValue={!!currentNode.data?.advanced?.citation}
                onChange={(value) => {
                  setCurrentNode({
                    ...currentNode,
                    data: {
                      ...currentNode.data,
                      advanced: {
                        related_question:
                          currentNode.data?.advanced?.related_question,
                        citation: value,
                      },
                    },
                  })
                  setDirtyModal(true)
                }}
              />
            </div>

            <div className="flex items-center gap-3 self-stretch">
              <div className="flex flex-[1_0_0] flex-col items-start justify-center">
                <Text variant="medium" className="text-Primary-Color">
                  Related questions suggestion
                </Text>
                <Text type="subBody" className="text-Secondary-Color">
                  Provides contextually relevant questions to enhance user
                  interaction and facilitate deeper exploration of topics
                </Text>
              </div>
              <Toggle
                defaultValue={!!currentNode.data?.advanced?.related_question}
                onChange={(value) => {
                  setCurrentNode({
                    ...currentNode,
                    data: {
                      ...currentNode.data,
                      advanced: {
                        citation: currentNode.data?.advanced?.citation,
                        related_question: value,
                      },
                    },
                  })
                  setDirtyModal(true)
                }}
              />
            </div>
          </div>
        </div>
      )
    }
    return (
      <div
        className={clsx(
          'genai-scrollbar h-fit max-h-[570px] w-[1280px] overflow-auto bg-white px-5',
          workflowType === EWorkflowType.conversation && 'py-2',
          workflowType === EWorkflowType.autoProcessing && '!max-h-[554px]'
        )}
      >
        <Input
          label="Step name"
          labelClassName="!text-Tertiary-Color"
          value={currentNode?.data?.name}
          placeholder="Type in display name for each step in your business process"
          maxLength={50}
          onChange={(e) => {
            setCurrentNode({
              ...currentNode,
              data: {
                ...currentNode.data,
                name: e.target.value,
              },
            })
            setErrors((prev) => ({
              ...prev,
              name: false,
            }))
            setDirtyModal(true)
          }}
          onBlur={() =>
            setCurrentNode({
              ...currentNode,
              data: {
                ...currentNode.data,
                name: currentNode?.data?.name?.trim(),
              },
            })
          }
          isError={errors.name}
        />

        <div className="mb-3 mt-2 flex flex-col gap-1">
          <div className="flex">
            <Text
              variant="medium"
              type="subBody"
              className="pl-1 text-Tertiary-Color"
            >
              Step description
            </Text>
            <Text
              variant="medium"
              type="subBody"
              className="pl-1 text-Secondary-Color-2"
            >
              (Optional)
            </Text>
          </div>
          <Input
            placeholder="Type in description to specify what would be completed in this step"
            value={currentNode?.data?.description ?? ''}
            onChange={(e) => {
              setCurrentNode({
                ...currentNode,
                data: {
                  ...currentNode.data,
                  description: e.target.value,
                },
              })
              setDirtyModal(true)
            }}
            onBlur={() =>
              setCurrentNode({
                ...currentNode,
                data: {
                  ...currentNode.data,
                  description: currentNode?.data?.description?.trim(),
                },
              })
            }
            maxLength={
              workflowType === EWorkflowType.autoProcessing ? 2000 : 255
            }
          />
        </div>

        <div className="mb-3 flex flex-col gap-3">
          <Worker
            worker={{
              workerId: currentNode?.data?.workerId,
              workerName: currentNode?.data?.workerName,
              workerType: currentNode?.data?.workerType,
              workerDescription: currentNode?.data?.workerDescription,
              workerAvatar: currentNode?.data?.workerAvatar,
              isNew: currentNode?.data?.isNew,
              modelId: currentNode?.data?.modelId,
            }}
            onChangeWorker={(value) => {
              setErrors((prev) => ({
                ...prev,
                worker: false,
              }))
              setCurrentNode({
                ...currentNode,
                data: {
                  ...currentNode.data,
                  ...value,
                },
              })
              setDirtyModal(true)
            }}
            isError={
              errors.worker ||
              (!currentNode?.data?.isNew && !currentNode?.data?.workerId) // Node not new & worker not selected
            }
          />

          {/* {currentNode?.data?.workerId ||
          (!currentNode?.data?.isNew && !currentNode?.data?.workerId) ? ( */}
          <div className="flex h-full w-full flex-1 flex-col gap-1">
            <div className="flex items-center gap-1">
              <Text
                variant="medium"
                className="pl-1 text-Tertiary-Color"
                type="subBody"
              >
                Worker responsibility
              </Text>
              <IconButton
                nameIcon="vuesax-bold-info-circle"
                sizeIcon={16}
                colorIcon={colors['border-base-icon']}
                hoverColor={colors['Primary-Color']}
                tooltipText="Define worker responsibility to specify what is his role and what he need to do to complete task in this step. Providing more comprehensive and detailed information will result in higher quality and more accurate task outcomes."
              />
            </div>

            <div className="relative flex w-full">
              <TextArea
                placeholder="eg:
You are a helpful AI assistant. Solve tasks using your coding and language skills.
In the following cases, suggest python code (in a python coding block) or shell script (in a sh coding block) for the user to execute.
When you need to collect info, use the code to output the info you need, for example, browse or search the web, download/read a file, print the content of a web page or a file, get the current date/time, check the operating system"
                onChange={(value) => {
                  setDirtyModal(true)

                  setCurrentNode({
                    ...currentNode,
                    data: {
                      ...currentNode.data,
                      workerResponsibility: value,
                    },
                  })
                }}
                value={currentNode?.data?.workerResponsibility}
                className="h-[141px] w-full"
                textAreaClassName="!h-[90px]"
                onBlur={() => {
                  setCurrentNode({
                    ...currentNode,
                    data: {
                      ...currentNode.data,
                      workerResponsibility:
                        currentNode?.data?.workerResponsibility?.trim(),
                    },
                  })
                  // validating after blurring textarea
                  validator()
                }}
                maxLength={5000}
                isError={errors.workerResponsibility}
              />
              <Button
                size="small"
                className="absolute bottom-2 right-5 w-[102px] px-[8px] py-[3px]"
                type="secondary"
                text="AI Enhance"
                rightIcon={
                  enhancedPromptLoading ? (
                    <img
                      className="relative -top-2 right-2 min-h-8 min-w-8"
                      src={'/assets/gif/loading.gif'}
                    />
                  ) : (
                    <Icon
                      name={
                        !currentNode?.data?.workerResponsibility
                          ? 'stars-04-disabled'
                          : 'stars-04'
                      }
                      size={16}
                    />
                  )
                }
                disabled={!currentNode?.data?.workerResponsibility}
                onClick={handlePromptEnhancement}
              />
            </div>
          </div>
        </div>

        <div className="h-ful flex flex-col gap-3">
          <WorkerToolKits
            worker={{ tools: currentNode?.data?.tools }}
            onChangeWorkerToolKits={(tools: ToolItem_Input[]) => {
              setDirtyModal(true)
              setCurrentNode({
                ...currentNode,
                data: {
                  ...currentNode.data,
                  tools: tools,
                },
              })
            }}
            limit={
              workflowType === EWorkflowType.autoProcessing
                ? LIMIT_AUTO_PROCESSING_TOOLKIT
                : LIMIT_TOOLKIT
            }
            settingBuiltInTools={settingBuiltInTools}
            setSettingBuiltInTools={setSettingBuiltInTools}
            generalToolParamConfigurations={generalToolParamConfigurations}
            setGeneralToolParamConfigurations={
              setGeneralToolParamConfigurations
            }
            settingBuiltInToolsHttp={settingBuiltInToolsHttp}
            setSettingBuiltInToolsHttp={setSettingBuiltInToolsHttp}
            onChangeGeneralTool={handleChangeGeneralTool}
          />

          <WorkerKnowledgeBase
            worker={{ knowledgeBases: currentNode?.data?.knowledgeBases }}
            knowledgeBasePrompt={currentNode?.data?.knowledgeBasePrompt}
            onChangeWorkerKnowledgeBase={(
              knowledgeBases: KnowledgeBaseItem[] | CustomExternalKBPublic[],
              knowledgeBasePrompt: string
            ) => {
              setDirtyModal(true)
              setCurrentNode({
                ...currentNode,
                data: {
                  ...currentNode.data,
                  knowledgeBases: knowledgeBases,
                  knowledgeBasePrompt,
                },
              })
            }}
          />
        </div>
      </div>
    )
  }

  return (
    <BaseModal
      headerClassName="!bg-white !px-5 !pt-4 !pb-3"
      bodyClassName="!bg-white !px-0"
      footerClassName="!bg-white !px-5 !pb-4"
      isOpen={isOpen}
      title={title}
      subTitle={subTitle}
      isShowCloseButton={true}
      hasCancelButton={false}
      onClose={handleCloseModal}
      isInvalid={!isDirtyModal}
      onAgree={async () => {
        const validatorResult = validator()
        const builtInToolResult = await validateSettingBuiltInTools({
          selectedTools: currentNode?.data?.tools ?? [],
          settingBuiltInTools,
          setSettingBuiltInTools,
          settingBuiltInToolsHttp,
        })

        if (validatorResult && builtInToolResult) {
          handleUpdateNode()
          setDirtyModal(false)
        }
      }}
    >
      {workflowType === EWorkflowType.conversation && (
        <Tabs
          tabs={[
            {
              value: 'General',
              label: 'General',
              className: 'px-2 py-1 !w-[63px]',
              textClassName: '!font-medium',
            },
            {
              value: 'Advanced options',
              label: 'Advanced options',
              className: 'px-2 py-1 !w-[125px]',
              textClassName: '!font-medium',
            },
          ]}
          size="small"
          value={currentTab}
          onChange={setCurrentTab}
        />
      )}
      {renderContent()}
    </BaseModal>
  )
}

export default memo(ModalNodeDetail)
