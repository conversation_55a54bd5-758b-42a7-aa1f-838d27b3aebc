import {
  ToolParamRecommendParameter_Input,
  externalKnowledgeBaseGetListColumns,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import IconButton from '@/components/IconButton'
import Message from '@/components/Message'
import Text from '@/components/Text'
import Tooltip from '@/components/Tooltip'
import { HTTP_STATUS_CODE } from '@/constants'
import { colors } from '@/theme'
import { memo, useState } from 'react'
import ListExternalData from './ListExternalData'

interface IModalToolParameters {
  isOpen: boolean
  onClose: () => void
  settingBuiltInTools?: ToolParamRecommendParameter_Input
  handleSettingBuiltInToolByToolId: (
    toolSettings?: ToolParamRecommendParameter_Input
  ) => void
}

const ModalToolParameters = ({
  isOpen,
  onClose,
  settingBuiltInTools,
  handleSettingBuiltInToolByToolId,
}: IModalToolParameters) => {
  const [_settingBuiltInTools, _setSettingBuiltInTools] =
    useState<ToolParamRecommendParameter_Input>(settingBuiltInTools || {})

  const validateSettingAndSave = async () => {
    const {
      profile_parameter,
      destination_parameter,
      output_parameter,
      criterion_parameter,
    } = _settingBuiltInTools

    if (!criterion_parameter?.columns?.length) {
      Message.error({ message: 'Data column in Criterion is required' })
      return false
    }

    if (!profile_parameter?.columns?.length) {
      Message.error({ message: 'Missing mapping data in Criterion' })
      return false
    }

    if (!destination_parameter?.columns?.length) {
      Message.error({
        message: 'Data column in Recommendation Data is required',
      })
      return false
    }

    if (!output_parameter?.columns?.length) {
      Message.error({ message: 'Missing output data in Recommendation Data' })
      return false
    }

    // Auto remove all error columns
    const [destinationColumnsRes, criterionColumnsRes] = await Promise.all([
      externalKnowledgeBaseGetListColumns({
        query: {
          external_knowledge_base_id:
            destination_parameter?.destination_source_id || '',
        },
      }),
      externalKnowledgeBaseGetListColumns({
        query: {
          external_knowledge_base_id:
            criterion_parameter?.criterion_source_id || '',
        },
      }),
    ])

    if (
      destinationColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS &&
      criterionColumnsRes?.status === HTTP_STATUS_CODE.SUCCESS
    ) {
      const { data: destinationColumnsData } = destinationColumnsRes
      const { data: criterionColumnsData } = criterionColumnsRes

      const validCriterionColumns = criterion_parameter?.columns?.filter(
        (item) =>
          criterionColumnsData?.data?.find(
            (column) => column.column_name === item.column_name
          )
      )

      const validDestinationColumns = destination_parameter?.columns?.filter(
        (item) =>
          destinationColumnsData?.data?.find(
            (column) => column.column_name === item.column_name
          )
      )

      const validProfileColumns = profile_parameter?.columns?.filter((item) =>
        criterionColumnsData?.data?.find(
          (column) => column.column_name === item.column_name
        )
      )

      const validOutputColumns = output_parameter?.columns?.filter((item) =>
        destinationColumnsData?.data?.find(
          (column) => column.column_name === item.column_name
        )
      )

      // re-check if valid columns is empty
      if (!validCriterionColumns?.length) {
        Message.error({ message: 'Data column in Criterion is required' })
        return false
      }

      if (!validProfileColumns?.length) {
        Message.error({ message: 'Missing mapping data in Criterion' })
        return false
      }

      if (!validDestinationColumns?.length) {
        Message.error({
          message: 'Data column in Recommendation Data is required',
        })
        return false
      }

      if (!validOutputColumns?.length) {
        Message.error({ message: 'Missing output data in Recommendation Data' })
        return false
      }

      _setSettingBuiltInTools({
        criterion_parameter: {
          ...criterion_parameter,
          columns: validCriterionColumns!,
        },
        destination_parameter: {
          ...destination_parameter,
          columns: validDestinationColumns!,
        },
        profile_parameter: {
          ...profile_parameter,
          columns: validProfileColumns!,
        },
        output_parameter: {
          ...output_parameter,
          columns: validOutputColumns!,
        },
      })

      handleSettingBuiltInToolByToolId({
        criterion_parameter: {
          ...criterion_parameter,
          columns: validCriterionColumns!,
        },
        destination_parameter: {
          ...destination_parameter,
          columns: validDestinationColumns!,
        },
        profile_parameter: {
          ...profile_parameter,
          columns: validProfileColumns!,
        },
        output_parameter: {
          ...output_parameter,
          columns: validOutputColumns!,
        },
      })

      onClose()
    } else {
      if (criterionColumnsRes?.status !== HTTP_STATUS_CODE.SUCCESS) {
        Message.error({ message: 'Data column in Criterion is required' })
        return false
      }

      if (destinationColumnsRes?.status !== HTTP_STATUS_CODE.SUCCESS) {
        Message.error({
          message: 'Data column in Recommendation Data is required',
        })
        return false
      }
    }

    return true
  }

  const handleSave = async () => {
    await validateSettingAndSave()
  }

  const _handleSettingBuiltInTool = (settings: any) => {
    _setSettingBuiltInTools((prev) => ({
      ...prev,
      ...settings,
    }))
  }

  const renderContent = () => {
    return (
      <div className="flex gap-4">
        <div className="flex w-[calc(50%-8px)] flex-col">
          <div className="flex items-center gap-1">
            <Text variant="medium" className="pl-1">
              Criterion
            </Text>
            <Tooltip text="">
              <div>
                <Icon
                  name="vuesax-bold-info-circle"
                  color={colors['border-base-icon']}
                  size={16}
                />
              </div>
            </Tooltip>
          </div>
          <Text className="mb-3 px-1 text-Secondary-Color" type="subBody">
            Choose data column from the external knowledge base that will be
            used as criteria for generating recommendations
          </Text>
          <ListExternalData
            settingBuiltInTools={{
              criterion_parameter: _settingBuiltInTools?.criterion_parameter,
              profile_parameter: _settingBuiltInTools?.profile_parameter,
            }}
            setSettingBuiltInTools={(settings) =>
              _handleSettingBuiltInTool(settings)
            }
            selectedExternalId={
              _settingBuiltInTools?.criterion_parameter?.criterion_source_id
            }
            type="profile"
          />
        </div>

        <div className="flex w-[calc(50%-8px)] flex-col">
          <div className="flex items-center gap-1">
            <Text variant="medium" className="pl-1">
              Recommendation data
            </Text>
            <Tooltip text="">
              <div>
                <Icon
                  name="vuesax-bold-info-circle"
                  color={colors['border-base-icon']}
                  size={16}
                />
              </div>
            </Tooltip>
          </div>
          <Text className="mb-3 px-1 text-Secondary-Color" type="subBody">
            Choose the data column from the external knowledge base for
            recommendations, then mark the data you want to return to your
            clients
          </Text>
          <ListExternalData
            settingBuiltInTools={{
              destination_parameter:
                _settingBuiltInTools?.destination_parameter,
              output_parameter: _settingBuiltInTools?.output_parameter,
            }}
            setSettingBuiltInTools={(settings) =>
              _handleSettingBuiltInTool(settings)
            }
            selectedExternalId={
              _settingBuiltInTools?.destination_parameter?.destination_source_id
            }
            type="destination"
          />
        </div>
      </div>
    )
  }

  return (
    <BaseModal isPureModal isOpen={isOpen} onClose={onClose}>
      <div className="relative flex w-[1074px] flex-col gap-3 rounded-[20px] bg-white p-3 shadow-md">
        <IconButton
          nameIcon="x-close"
          sizeIcon={16}
          onClick={onClose}
          className="absolute right-[10px] top-[10px] duration-300 hover:bg-neutral-200"
          colorIcon={colors.neutral[500]}
        />
        <Text
          type="subheading"
          variant="medium"
          className="pl-1 text-Primary-Color"
        >
          Tool Parameters
        </Text>

        {renderContent()}

        <div className="flex w-full justify-end">
          <Button
            type="primary"
            size="small"
            disabled={
              !_settingBuiltInTools?.criterion_parameter?.criterion_source_id ||
              !_settingBuiltInTools?.destination_parameter
                ?.destination_source_id
            }
            onClick={handleSave}
            text="Save"
          />
        </div>
      </div>
    </BaseModal>
  )
}

export default memo(ModalToolParameters)
