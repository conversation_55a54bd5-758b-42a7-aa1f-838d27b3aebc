import {
  externalKnowledgeBaseFetchMyExternalKnowledgeBaseApi,
  externalKnowledgeBaseReadExternalKnowledgeBaseByIdApi,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import EmptyData from '@/components/EmptyData'
import Input from '@/components/Input'
import NoDataFound from '@/components/NoDataFound'
import Pagination from '@/components/Pagination'
import { DEBOUNCE_TIME, HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { colors } from '@/theme'
import { debounce, isEmpty } from 'lodash'
import { memo, useCallback, useEffect, useState } from 'react'
import ExternalDataItem from './ExternalDataItem'
import ColumnItemSkeleton from './ExternalDataItem/ColumnItemSkeleton'
import ExternalDataItemSkeleton from './ExternalDataItem/ExternalDataItemSkeleton'

interface IProps {
  settingBuiltInTools?: any
  setSettingBuiltInTools: (data: any) => void
  selectedExternalId?: string
  type: 'profile' | 'destination'
}

const ListExternalData = (props: IProps) => {
  const {
    settingBuiltInTools,
    selectedExternalId = '',
    setSettingBuiltInTools,
    type = 'profile',
  } = props

  const [searchKey, setSearchKey] = useState('')
  const [_searchKey, _setSearchKey] = useState('')

  const [listExternalData, setListExternalData] = useState<any>([])

  const [isLoadingDetail, setLoadingDetail] = useState<boolean>(false)
  const [selectedExternalData, setSelectedExternalData] = useState<any>()

  const [isInitial, setInitial] = useState(true)

  const [isLoadingList, setLoadingList] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [totalPage, setTotalPages] = useState<number>(0)

  const [isBlockCall, setBlockCall] = useState(false)

  const handleKeyDown = useCallback(
    (event: any) => {
      if (event.key === 'Enter') {
        fetchListExternalData(_searchKey?.trim(), 1)
      }
    },
    [_searchKey]
  )

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      fetchListExternalData?.(value.trim())
    }, DEBOUNCE_TIME),
    []
  )

  const handleSearch = (newQuery: string) => {
    _setSearchKey(newQuery)
    debouncedSearch(newQuery)
  }

  const onBlur = useCallback(() => {
    _setSearchKey(_searchKey?.trim())
  }, [_searchKey])

  const fetchListExternalData = async (
    search = searchKey,
    page = currentPage
  ) => {
    try {
      if (isBlockCall) {
        return setBlockCall(false)
      }
      if (isLoadingList) return

      setLoadingList(true)

      const res = await externalKnowledgeBaseFetchMyExternalKnowledgeBaseApi({
        query: {
          name: search || undefined,
          page_number: page,
          page_size: PAGE_SIZE.SMALL,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setListExternalData(data?.data?.data ?? [])

        setSearchKey(search)
        setCurrentPage(page)
        setTotalPages(data?.data?.total_pages ?? 0)
      }
    } catch (error) {
      setListExternalData([])
      setTotalPages(0)
    } finally {
      setLoadingList(false)
      if (isInitial) setInitial(false)
    }
  }

  const fetchExternalDataDetail = async () => {
    try {
      if (isLoadingDetail) return

      setLoadingDetail(true)
      const res = await externalKnowledgeBaseReadExternalKnowledgeBaseByIdApi({
        path: {
          external_knowledge_base_id: selectedExternalId,
        },
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res
        setSelectedExternalData(data?.data)
      }
    } catch (error) {
      setSelectedExternalData(undefined)
    } finally {
      setLoadingDetail(false)

      if (isInitial) setInitial(false)
    }
  }

  useEffect(() => {
    if (selectedExternalId) {
      if (isEmpty(selectedExternalData) && isInitial) fetchExternalDataDetail()
    } else {
      fetchListExternalData()
    }
  }, [selectedExternalId, selectedExternalData])

  const handleChangeSettings = (settings: any) => {
    setSettingBuiltInTools({
      ...settingBuiltInTools,
      ...settings,
    })
  }

  const renderExternalDataContent = () => {
    if (isLoadingDetail) {
      return (
        <div className="flex w-full flex-col">
          <ExternalDataItemSkeleton />
          <div className="grid w-full grid-cols-2 gap-2 px-1.5">
            {Array.from({ length: PAGE_SIZE.SMALL }).map((_, index) => (
              <ColumnItemSkeleton key={index} />
            ))}
          </div>
        </div>
      )
    }

    return (
      <ExternalDataItem
        key={selectedExternalData?.id}
        externalData={selectedExternalData}
        isActive
        onClick={() => {
          setSelectedExternalData(undefined)
          setSettingBuiltInTools(
            type === 'profile'
              ? {
                  criterion_parameter: {
                    criterion_source_id: null,
                    criterion_source_kb_id: null,
                    columns: [],
                  },
                  profile_parameter: {
                    profile_source_id: null,
                    profile_source_kb_id: null,
                    columns: [],
                  },
                }
              : {
                  destination_parameter: {
                    destination_source_id: null,
                    destination_source_kb_id: null,
                    columns: [],
                  },
                  output_parameter: {
                    output_source_id: null,
                    output_source_kb_id: null,
                    columns: [],
                  },
                }
          )
        }}
        settingBuiltInTools={settingBuiltInTools}
        type={type}
        handleChangeSettings={handleChangeSettings}
      />
    )
  }

  const renderListExternalData = () => {
    if (isLoadingList) {
      return (
        <>
          {Array.from({ length: PAGE_SIZE.SMALL }).map((_, index) => (
            <ColumnItemSkeleton key={index} />
          ))}
        </>
      )
    }

    if (!listExternalData?.length) {
      if (_searchKey) {
        return (
          <NoDataFound
            size={150}
            textType="subheading"
            subTextType="subBody"
            className="mt-8"
          />
        )
      }
      return <EmptyData type="03" size="small" className="mt-10 self-center" />
    }

    return listExternalData?.map((externalData: any) => {
      const isActive = selectedExternalData?.id === externalData?.id

      return (
        <ExternalDataItem
          key={externalData?.id}
          externalData={externalData}
          isActive={isActive}
          isSearching={!!_searchKey}
          onClick={() => {
            if (isActive) {
              // If search block call api get list
              if (_searchKey) {
                setBlockCall(true)
              }
              setSelectedExternalData(undefined)
              setSettingBuiltInTools(
                type === 'profile'
                  ? {
                      criterion_parameter: {
                        criterion_source_id: null,
                        criterion_source_kb_id: null,
                        columns: [],
                      },
                      profile_parameter: {
                        profile_source_id: null,
                        profile_source_kb_id: null,
                        columns: [],
                      },
                    }
                  : {
                      destination_parameter: {
                        destination_source_id: null,
                        destination_source_kb_id: null,

                        columns: [],
                      },
                      output_parameter: {
                        output_source_id: null,
                        output_source_kb_id: null,

                        columns: [],
                      },
                    }
              )
            } else {
              // Select item will remove search key
              handleSearch('')
              setSelectedExternalData(isActive ? undefined : externalData)
              setSettingBuiltInTools(
                type === 'profile'
                  ? {
                      criterion_parameter: {
                        criterion_source_id: externalData?.id,
                        criterion_source_kb_id: externalData?.knowledge_base_id,
                        columns: [],
                      },
                      profile_parameter: {
                        profile_source_id: externalData?.id,
                        profile_source_kb_id: externalData?.knowledge_base_id,
                        columns: [],
                      },
                    }
                  : {
                      destination_parameter: {
                        destination_source_id: externalData?.id,
                        destination_source_kb_id:
                          externalData?.knowledge_base_id,
                        columns: [],
                      },
                      output_parameter: {
                        output_source_id: externalData?.id,
                        output_source_kb_id: externalData?.knowledge_base_id,
                        columns: [],
                      },
                    }
              )
            }
          }}
          settingBuiltInTools={settingBuiltInTools}
          type={type}
          handleChangeSettings={handleChangeSettings}
        />
      )
    })
  }

  const renderContent = () => {
    return (
      <div className="genai-scrollbar flex h-[400px] w-full flex-col gap-2 overflow-auto p-1">
        {!isEmpty(selectedExternalData) && !_searchKey
          ? renderExternalDataContent()
          : renderListExternalData()}
      </div>
    )
  }

  return (
    <div className="flex w-full flex-col">
      <Input
        placeholder="Search external data"
        value={_searchKey}
        onChange={(e) => handleSearch(e.target.value)}
        onBlur={onBlur}
        suffix={
          <Icon
            name="Icon-Solid-Search"
            size={16}
            color={colors['Placeholder-Text']}
          />
        }
        onPressEnter={handleKeyDown}
        classNameInputWrapper="grow"
        isFullWidth
      />
      <div className="mt-3">{renderContent()}</div>
      {totalPage > 1 &&
      (isEmpty(selectedExternalData) ||
        (!isEmpty(selectedExternalData) && _searchKey)) ? (
        <div className="mt-1 flex justify-end">
          <Pagination
            page={currentPage}
            totalPage={totalPage}
            onChangePage={(page) => fetchListExternalData(searchKey, page)}
            type="mini"
          />
        </div>
      ) : null}
    </div>
  )
}

export default memo(ListExternalData)
