import Icon from '@/assets/icon/Icon'
import IconButton from '@/components/IconButton'
import NoDataFound from '@/components/NoDataFound'
import Popover from '@/components/Popover'
import Text from '@/components/Text'
import { PAGE_SIZE } from '@/constants'
import WorkflowItemSkeleton from '@/pages/Dashboard/components/SessionLog/components/SessionList/WorkflowDropdown/components/WorkflowItemSkeleton'
import { colors } from '@/theme'
import clsx from 'clsx'
import { size } from 'lodash'
import { useEffect, useMemo, useState } from 'react'
import SimpleBar from 'simplebar-react'
import { twMerge } from 'tailwind-merge'
import IcNoDataFound from './assets/noDataFound.svg?react'

export interface ISelectBaseItem {
  id: string
  value?: any
  name?: string
  icon?: any
}

interface SelectProps<T extends ISelectBaseItem> {
  data: T[]
  selected: T | undefined
  placeholder?: string
  getPopupContainer?: (
    triggerNode?: HTMLElement
  ) => Element | DocumentFragment | HTMLElement | ParentNode | null | undefined
  onChangeSelectedValue: (value: any) => void
  className?: string
  overlayClassName?: string
  error?: boolean
  disabled?: boolean
  allowRemove?: boolean
  allowClear?: boolean
  multipleChoice?: boolean
  selectedMultiple?: T[]
  icon?: React.ReactNode
  containerClassName?: string
  onClear?: () => void
  onOpen?: () => void
  loading?: boolean
  optionMaxWidth?: number
  contentClassName?: string
  itemClassName?: string
  placement?:
    | 'top'
    | 'top start'
    | 'top end'
    | 'bottom'
    | 'bottom start'
    | 'bottom end'
    | 'left'
    | 'left start'
    | 'left end'
    | 'right'
    | 'right start'
    | 'right end'
}

const Select = <T extends ISelectBaseItem>({
  data,
  selected,
  placeholder,
  getPopupContainer,
  onChangeSelectedValue,
  className,
  overlayClassName,
  error,
  disabled,
  allowRemove,
  allowClear,
  multipleChoice,
  icon,
  containerClassName,
  selectedMultiple,
  onClear,
  onOpen,
  loading,
  optionMaxWidth,
  placement,
  contentClassName,
  itemClassName,
}: SelectProps<T>) => {
  const [open, setOpen] = useState(false)

  const handleChangeSelected = (value?: T) => {
    if (!multipleChoice) {
      if (!allowRemove) {
        onChangeSelectedValue(value)
      } else {
        if (selected?.id === value?.id) {
          onChangeSelectedValue(undefined)
        } else {
          onChangeSelectedValue(value)
        }
      }
    } else {
      onChangeSelectedValue(value)
    }
  }

  const isSelected = (value: T) => {
    if (multipleChoice) {
      return selectedMultiple?.some((item) => item.id === value.id)
    }
    return selected?.id === value.id
  }

  const selectedName = useMemo(() => {
    if (multipleChoice) {
      const length = selectedMultiple?.length
      if (!length) {
        return 'Empty'
      }
      if (length && length > 1) {
        return `${selectedMultiple?.length} selected`
      }
      return selectedMultiple[0]?.name
    }
    return selected?.name
  }, [selected, selectedMultiple])

  useEffect(() => {
    if (open && onOpen) {
      onOpen()
    }
  }, [open])

  const clearHandler = () => {
    onClear?.()
    setOpen(false)
  }

  return (
    <div className={className}>
      <Popover
        isPure
        overlayClassName={clsx('z-50 !overflow-hidden !p-0', overlayClassName)}
        open={open}
        title="Description"
        getPopupContainer={getPopupContainer}
        onOpenChange={(value) => {
          if (!disabled) {
            setOpen(value)
          }
        }}
        allowCloseWhenClickButton={allowRemove}
        placement={placement}
        content={
          <SimpleBar
            className={clsx(
              '!max-h-[176px] w-[var(--button-width)] rounded-xl border border-border-base-icon p-2 shadow-base',
              contentClassName
            )}
            style={{ maxWidth: optionMaxWidth || 'unset' }}
          >
            {loading && (
              <div className="flex w-full flex-col gap-1">
                {Array.from({ length: PAGE_SIZE.EXTRA_SMALL }).map(
                  (_, index) => (
                    <WorkflowItemSkeleton key={index} />
                  )
                )}
              </div>
            )}
            {!loading && !size(data) && (
              <NoDataFound
                className="flex h-full w-full flex-col items-center justify-center"
                icNodata={
                  <div className="h-[80px ] w-[80px]">
                    <IcNoDataFound />
                  </div>
                }
                textType="body"
                textVariant="semibold"
                subTextType="supportText"
                subTextVariant="regular"
              />
            )}
            {!loading && size(data) > 0 && (
              <div className="flex flex-col gap-1">
                {data.map((item) => (
                  <div
                    key={item.id}
                    className={twMerge(
                      clsx(
                        'group flex cursor-pointer select-none items-center rounded-[6px] px-2 py-[6px]',
                        'hover:bg-Hover-Color data-[focus]:bg-Background-Color',
                        {
                          'bg-Background-Color': isSelected(item),
                        },
                        itemClassName
                      )
                    )}
                    onClick={() => {
                      handleChangeSelected(item)
                      if (!multipleChoice) {
                        setOpen(false)
                      }
                    }}
                  >
                    {item?.icon}
                    <Text
                      className={clsx(
                        'overflow-hidden text-Primary-Color',
                        {
                          'bg-Main-Color bg-clip-text text-transparent':
                            isSelected(item),
                        },
                        {
                          'group-hover:!text-transparent': isSelected(item),
                        }
                      )}
                      type="subBody"
                      variant="medium"
                      elementType="div"
                      ellipsis
                    >
                      {item.name}
                    </Text>
                  </div>
                ))}
              </div>
            )}
          </SimpleBar>
        }
      >
        <div className="relative flex w-full cursor-pointer items-center">
          <div
            className={twMerge(
              clsx(
                'flex h-[34px] w-full items-center rounded-[8px] border border-border-base-icon bg-Input-Field px-3 py-2',
                'hover:border-Main-03',
                'transition duration-75 ease-in',
                { 'border-Focus-Border shadow-focus': open },
                { 'border-Error-Color': error },
                {
                  'bg-white shadow-base hover:border-Base-Neutral-02':
                    allowRemove,
                },
                {
                  'hover:bg-Hover-Color': allowRemove && !open,
                },
                { 'border-Base-Neutral-02 shadow-focus': allowRemove && open },
                containerClassName
              )
            )}
          >
            {icon}
            {selected ? (
              <Text
                className={clsx(
                  'w-full overflow-hidden text-start',
                  disabled ? 'text-Disable-Text' : 'text-Primary-Color'
                )}
                type="subBody"
                variant="regular"
                elementType="div"
                ellipsis
              >
                {selectedName ?? ''}
              </Text>
            ) : (
              <Text
                className="w-full overflow-hidden text-start text-Placeholder-Text"
                type="subBody"
                variant="regular"
                elementType="div"
                ellipsis
              >
                {placeholder ?? ''}
              </Text>
            )}
            {allowClear && (
              <IconButton
                nameIcon="vuesax-bold-close-circle"
                sizeIcon={14}
                colorIcon={colors['border-base-icon']}
                hoverColor={colors['Primary-Color']}
                className="ml-1"
                onClick={clearHandler}
              />
            )}

            <div
              className={twMerge(
                clsx('group ml-2 flex', {
                  'genai_button_icon__active rotate-180 transform': open,
                })
              )}
            >
              <Icon
                name="Outline-Chevron-Down"
                size={20}
                color={
                  allowRemove ? colors['Primary-Color'] : colors.neutral[400]
                }
              />
            </div>
          </div>
        </div>
      </Popover>
    </div>
  )
}

export default Select
