import {
  WorkerPublic,
  WorkersPublic,
  workersReadWorkersA<PERSON>,
} from '@/apis/client'
import Icon from '@/assets/icon/Icon'
import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import EmptyData from '@/components/EmptyData'
import Message from '@/components/Message'
import NoDataFound from '@/components/NoDataFound'
import Pagination from '@/components/Pagination'
import Text from '@/components/Text'
import { HTTP_STATUS_CODE, PAGE_SIZE } from '@/constants'
import { colors } from '@/theme'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import { CustomNodeData } from '../../types'
import AssigneeItem from './components/AssigneeItem'
import AssigneeItemSkeleton from './components/AssigneeItemSkeleton'
import { ISelectBaseItem } from './components/Select'
import ModalUpdateWorker from '@/pages/Workers/components/ModalUpdateWorker'
import {
  CommunicationStyleProvider,
  LanguageProvider,
} from '@/pages/Workers/contexts'
import SearchBar from '@/components/SearchBar'
import { StepCreateWorker, WorkerType } from '@/pages/Workers/types'
import IconButton from '@/components/IconButton'

type WorkerParams = {
  page_number: number
  page_size: number
  name?: string
  worker_type?: ISelectBaseItem
}

const ASSIGNEE_PARAMS_INIT: WorkerParams = {
  page_number: 1,
  page_size: PAGE_SIZE.SMALL,
  name: '',
}

type AssigneeParam = {
  key: number | string
  value?: any
}

interface SelectAssigneeProps {
  open: boolean
  isSpecialAssignee?: boolean
  assigneeSelected?: any
  onConfirm?: (value: Partial<CustomNodeData>) => void
  onClose?: () => void
}

const SelectAssignee = ({
  open,
  isSpecialAssignee = false,
  assigneeSelected,
  onConfirm,
  onClose,
}: SelectAssigneeProps) => {
  const [workerParams, setWorkerParams_] = useState(ASSIGNEE_PARAMS_INIT)
  const [loading, setLoading] = useState(false)
  const [totalPage, setTotalPage] = useState<number>()

  const [workers, setWorkers] = useState<WorkersPublic>([])

  const [workerSelected, setWorkerSelected] = useState<
    WorkerPublic | undefined
  >()

  const [openModalUpdateWorker, setOpenModalUpdateWorker] = useState(false)

  const setWorkerParams = useCallback(
    ({ key, value }: AssigneeParam) =>
      setWorkerParams_((prev) => ({
        ...prev,
        [key]: value,
      })),
    []
  )

  const handleChangeName = useCallback(
    (value: string) =>
      setWorkerParams_((prev) => ({
        ...prev,
        name: value.trim(),
        page_number: 1,
      })),
    []
  )

  const handleChangePage = useCallback(
    (value: number) =>
      setWorkerParams({
        key: 'page_number',
        value,
      }),
    []
  )

  const fetchWorkers = useCallback(async () => {
    try {
      setLoading(true)

      const query: any = {
        page_number: workerParams.page_number,
        page_size: workerParams.page_size,
      }

      if (workerParams.name) {
        query.name = workerParams.name
      }
      if (!isSpecialAssignee && assigneeSelected) {
        query.special_worker_id = assigneeSelected.id
      }

      const res = await workersReadWorkersApi({
        query,
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res

        setWorkers(data?.data?.data ?? [])
        setTotalPage(data?.data?.total_pages)
      } else {
        Message.error({
          message: res.error?.detail ?? 'Something wrong, please retry!',
        })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }, [loading, workerParams, isSpecialAssignee, assigneeSelected])

  const workerListContent = useMemo(() => {
    if (loading)
      return (
        <div className="flex h-[54vh] max-h-[561px] min-h-[421px] w-full overflow-hidden">
          <div className="flex h-full w-full flex-col gap-3 px-1">
            {Array.from({ length: workerParams.page_size }).map((_, index) => (
              <AssigneeItemSkeleton key={index} />
            ))}
          </div>
        </div>
      )

    if (!workers?.length) {
      if (workerParams.name) {
        return (
          <div className="flex h-[54vh] max-h-[561px] min-h-[421px] w-full justify-center">
            <NoDataFound className="mt-[32px]" />
          </div>
        )
      }
      return (
        <div className="flex h-[54vh] max-h-[561px] min-h-[421px] w-full flex-col items-center justify-center">
          <EmptyData size="medium" type="02" className="mt-[32px]" />
        </div>
      )
    }

    return (
      <div className="genai-scrollbar h-[54vh] max-h-[561px] min-h-[421px] w-full overflow-auto">
        <div className="flex w-full flex-wrap gap-3 px-1">
          {workers.map((worker: WorkerPublic) => (
            <AssigneeItem
              key={worker.id}
              active={workerSelected?.id === worker.id}
              llm_model_id={worker.llm_model_id}
              name={worker.name}
              worker_type={worker.worker_type}
              avatar={worker.avatar}
              background={worker.background}
              onClick={() => {
                if (workerSelected?.id === worker.id) {
                  setWorkerSelected(undefined)
                } else {
                  setWorkerSelected(worker)
                }
              }}
              showAvatarModel
            />
          ))}
        </div>
      </div>
    )
  }, [loading, workerSelected, workerParams, workers])

  useEffect(() => {
    fetchWorkers()
  }, [workerParams])

  useEffect(() => {
    if (open) {
      setWorkerSelected(assigneeSelected)
    }
  }, [assigneeSelected])

  useEffect(() => {
    if (!open) {
      setWorkerParams_({ ...ASSIGNEE_PARAMS_INIT })
    }
  }, [open])

  return (
    <LanguageProvider>
      <CommunicationStyleProvider>
        <BaseModal isOpen={open} onClose={onClose} isPureModal>
          <div className="relative flex min-h-[556px] w-[965px] flex-col items-start gap-3 rounded-[20px] bg-white p-3 shadow-md">
            <IconButton
              className="absolute right-[10px] top-[10px] cursor-pointer duration-300 hover:bg-neutral-200"
              nameIcon="x-close"
              sizeIcon={16}
              colorIcon={colors.neutral[500]}
              onClick={onClose}
            />
            <div className="flex w-full flex-col items-start gap-[8px]">
              <Text
                className="px-1 text-Primary-Color"
                type="body"
                variant="medium"
              >
                Select assignee
              </Text>
              <div className="flex w-full items-start gap-2">
                <SearchBar
                  className="w-full"
                  placeholder="Search by worker name"
                  debounceTime={500}
                  onSearch={(value) => handleChangeName(value)}
                />
                <Button
                  leftIcon={
                    <Icon
                      name="plus"
                      size={20}
                      gradient={['#4D175B', '#A32952']}
                    />
                  }
                  type="secondary"
                  text="Worker"
                  onClick={() => setOpenModalUpdateWorker(true)}
                />
              </div>
            </div>

            <div className="flex h-full w-full flex-col items-end gap-1">
              {workerListContent}
              <div className="flex min-h-[24px] w-full items-center justify-end">
                {totalPage! > 1 && (
                  <Pagination
                    type="mini"
                    page={workerParams.page_number}
                    totalPage={totalPage}
                    onChangePage={handleChangePage}
                    className="flex justify-end"
                  />
                )}
              </div>
            </div>

            <div className="flex w-full justify-end">
              <Button
                disabled={!workerSelected?.id}
                type="primary"
                text="Confirm"
                className={twMerge('w-[96px]')}
                onClick={() => {
                  if (workerSelected) {
                    onConfirm?.({
                      workerId: workerSelected.id,
                      workerName: workerSelected.name,
                      workerType: workerSelected.worker_type,
                      workerAvatar: workerSelected.avatar,
                      workerDescription: workerSelected.background,
                      modelId: workerSelected.llm_model_id ?? '',
                    })
                  }
                }}
              />
            </div>
          </div>
        </BaseModal>

        {openModalUpdateWorker && (
          <ModalUpdateWorker
            defaultStep={StepCreateWorker.UPDATE}
            workerType={WorkerType.AI}
            isOpen={openModalUpdateWorker}
            onClose={() => setOpenModalUpdateWorker(false)}
            refreshData={() => fetchWorkers()}
            onComplete={() => {
              setOpenModalUpdateWorker(false)
              fetchWorkers()
            }}
          />
        )}
      </CommunicationStyleProvider>
    </LanguageProvider>
  )
}

export default SelectAssignee
