import {
  EdgeItem,
  NodeItem_Input,
  ProcessingWorkflowConfig_Output,
  ProcessingWorkflowPubicResponse,
  ProcessingWorkflowPublic,
  WorkflowPubicResponse,
  WorkflowPublic,
  WorkflowUpdate,
  processingWorkflowCreateProcessing<PERSON>orkflow<PERSON>pi,
  processingWorkflowReadWorkflowByIdApi,
  processingWorkflowUpdateProcessing<PERSON>orkflow<PERSON>pi,
  workflowCreateWorkflowApi,
  workflowReadWorkflowByIdApi,
  workflowUpdateWorkflowApi,
} from '@/apis/client'
import Button from '@/components/Button'
import NotFound from '@/components/ErrorPage/NotFound'
import Layout from '@/components/Layout'
import Message from '@/components/Message'
import RouteLeaving from '@/components/RouteLeaving'
import Spin from '@/components/Spin'
import WorkflowBreadcrumb from '@/components/WorkflowBreadcrumb/WorkflowBreadcrumb'
import { DEBOUNCE_TIME, HTTP_STATUS_CODE } from '@/constants'
import { WorkflowIntegration } from '@/pages/WorkflowIntegration'
import { rootUrls } from '@/routes/rootUrls'
import {
  type Edge,
  ReactFlowProvider,
  getNodesBounds,
  getViewportForBounds,
} from '@xyflow/react'
import { toPng } from 'html-to-image'
import { isEmpty, isNil } from 'lodash'
import { memo, useContext, useEffect, useMemo, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { v4 as uuid } from 'uuid'
import { ModelsProvider } from '../Workers/contexts'
import { EWorkflowType } from '../Workflows/const'
import ButtonGroup from './components/ButtonGroup'
import ModalAi from './components/ModalAi'
import ModalIntegrateAutoProcessingWorkflow from './components/ModalIntegrateAutoProcessingWorkflow'
import ModalMemory from './components/ModalMemory'
import ModalPlaygroundMultiAgent from './components/ModalPlaygroundMultiAgent'
import {
  NEW_AUTO_PROCESSING_WORKFLOW,
  NEW_WORKFLOW,
  NODE_TYPE,
} from './constants'
import { CustomNode } from './types'
import Workflow from './Workflow'
import { WorkflowDetailContext } from './WorkflowDetailContext'
import { GenerateWorkflowContext } from '@/providers/GenerateWorkflowProvider'

const WorkflowDetail = () => {
  const location = useLocation()
  const { workflowId } = useParams()

  const { isDirtyData, setDirtyData } = useContext(WorkflowDetailContext)

  const navigate = useNavigate()

  const [id, setId] = useState('') // Id of workflow

  const [name, setName] = useState('')
  const [wfData, setWfData] = useState<
    WorkflowPublic | ProcessingWorkflowPublic
  >()

  const [isErrorName, setErrorName] = useState(false)

  const [description, setDescription] = useState('')

  const [nodes, setNodes] = useState<CustomNode[]>([])
  const [edges, setEdges] = useState<Edge[]>([])

  const [isLoading, setLoading] = useState(false)

  const [isLoadingDetail, setLoadingDetail] = useState(false)

  const [errorCode, setErrorCode] = useState<number>()

  const [isOpenedModalPlayground, setOpenedModalPlayground] = useState(false)
  const [isOpenModalIntegrate, setOpenModalIntegrate] = useState(false)
  const [isOpenModalMemory, setOpenModalMemory] = useState(false)
  const [isOpenModalAi, setOpenModalAi] = useState(false)
  const [aiGenerateText, setAiGenerateText] = useState('')
  const { generateText, setGenerateText, model, setModel } = useContext(
    GenerateWorkflowContext
  )

  const fetchWorkflowDetail = async () => {
    try {
      if (isLoadingDetail) return

      setLoadingDetail(true)
      const isAutoProcessing = location.pathname.includes(
        '/workflows/my-auto-processing-workflows'
      )

      const workflowType = isAutoProcessing
        ? EWorkflowType.autoProcessing
        : EWorkflowType.conversation

      const getService = () => {
        if (isAutoProcessing) {
          return processingWorkflowReadWorkflowByIdApi
        }
        return workflowReadWorkflowByIdApi
      }

      const service = getService()

      const res = await service({
        path: {
          workflow_id: workflowId!,
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        const { data } = res
        setWfData(data?.data)
        setId(data?.data?.id ?? '')
        setName(data?.data?.name ?? '')
        setDescription(data?.data?.description ?? '')
        const tData = data?.data as WorkflowPublic
        setAiGenerateText(tData?.ai_gen_prompt ?? '')
        if (isAutoProcessing) {
          const tData = data as ProcessingWorkflowPubicResponse
          const data_nodes = [
            tData?.data?.config?.startNode,
            tData?.data?.config?.endNode,
            ...(tData?.data?.config?.nodes || []),
          ]
          setNodes(
            (data_nodes.map((node) => {
              if (
                node.type === NODE_TYPE.START ||
                node.type === NODE_TYPE.END
              ) {
                return {
                  ...node,
                  deletable: false,
                  selectable: true,

                  data: { ...node.data },
                }
              }

              return {
                ...node,
                data: {
                  ...node.data,
                  workflowType,
                },
              }
            }) as CustomNode[]) ?? []
          )
        } else {
          setNodes(
            (data?.data?.config?.nodes.map((node) => {
              if (
                node.type === NODE_TYPE.START ||
                node.type === NODE_TYPE.END
              ) {
                return {
                  ...node,
                  deletable: false,
                }
              }

              return {
                ...node,
                data: {
                  ...node.data,
                  workflowType,
                },
              }
            }) as CustomNode[]) ?? []
          )
        }
        setEdges((data?.data?.config?.edges as Edge[]) ?? [])
      } else {
        setErrorCode(res.status)
      }
    } catch (error: any) {
      console.log('error', error?.message)
    } finally {
      setLoadingDetail(false)
    }
  }

  useEffect(() => {
    if (generateText && model) {
      setAiGenerateText(generateText)

      aiGeneratedHandler(model)

      setName(model.data.name)
      setDescription(model.data.description)
      setGenerateText('')
      setModel(undefined)
    } else if (
      [NEW_WORKFLOW, NEW_AUTO_PROCESSING_WORKFLOW].includes(workflowId || '')
    ) {
      setNodes([
        {
          id: uuid(),
          position: {
            x: 100,
            y: 100,
          },
          type: NODE_TYPE.START,
          deletable: false,
          selectable: workflowId === NEW_AUTO_PROCESSING_WORKFLOW,
        } as CustomNode,
        {
          id: uuid(),
          position: {
            x: workflowId === NEW_AUTO_PROCESSING_WORKFLOW ? 400 : 200,
            y: workflowId === NEW_AUTO_PROCESSING_WORKFLOW ? 110 : 100,
          },
          type: NODE_TYPE.END,
          deletable: false,
          selectable: workflowId === NEW_AUTO_PROCESSING_WORKFLOW,
        } as CustomNode,
      ])
    } else {
      fetchWorkflowDetail()
    }
  }, [workflowId])

  const handleUpdateWorkflow = async () => {
    try {
      if (isLoading || !isDirtyData) return

      // Validate name
      if (!name) {
        setErrorName(true)
        return
      }

      const agentNodes = nodes.filter((node) => node.type === NODE_TYPE.AGENT)

      // Validate must have agent node
      if (!agentNodes?.length) {
        Message.error({
          message: 'Workflow node is required',
        })
        return
      }

      let isFullConnect = true

      nodes.map((node: any) => {
        const totalEdges = edges.filter(
          (n: any) => n.source === node.id || n.target === node.id
        )

        switch (node.type) {
          case NODE_TYPE.START:
            if (totalEdges.length < 1) {
              isFullConnect = false
            }
            break

          case NODE_TYPE.AGENT:
            if (totalEdges.length < 2) {
              isFullConnect = false
            }
            break

          case NODE_TYPE.END:
            if (totalEdges.length < 1) {
              isFullConnect = false
            }
            break
          default:
            break
        }
      })

      if (!isFullConnect) {
        Message.error({
          message: 'Edge between nodes is missing',
        })
        return
      }

      if (agentNodes.some((node) => !node.data.workerId)) {
        Message.error({
          message: 'Workflow step is invalid',
        })
        return
      }

      if (workflowType === EWorkflowType.autoProcessing) {
        const endNode = nodes.find((node) => node.type === NODE_TYPE.END)
        if (!endNode?.data?.outputSchema) {
          Message.error({
            message: 'Missing output params in End node',
          })
          return
        }
      }

      setLoading(true)

      const getService = () => {
        if (workflowType === EWorkflowType.autoProcessing) {
          if (id) {
            return processingWorkflowUpdateProcessingWorkflowApi
          }
          return processingWorkflowCreateProcessingWorkflowApi
        }
        if (id) {
          return workflowUpdateWorkflowApi
        }
        return workflowCreateWorkflowApi
      }

      const service = getService()

      let thumbnail_base64 = ''

      const nodesBounds = getNodesBounds(nodes)

      const viewport = getViewportForBounds(
        nodesBounds,
        nodesBounds.width,
        nodesBounds.height,
        0.5,
        2,
        0.5
      )

      const workflowElement: HTMLElement = document.querySelector(
        '.react-flow__viewport'
      )!

      if (workflowElement) {
        const customScrollbars = document.querySelectorAll('.custom-scrollbar')

        // Hidden scrollbar when capture thumbnail
        if (customScrollbars.length > 0) {
          customScrollbars.forEach((scrollbar: any) => {
            scrollbar.style.overflow = 'hidden'
          })
        }

        await toPng(workflowElement, {
          width: nodesBounds.width,
          height: nodesBounds.height,
          pixelRatio: 2,
          // skipAutoScale: true,
          // quality: 1,
          style: {
            width: nodesBounds.width?.toString(),
            height: nodesBounds.height?.toString(),
            transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
          },
        }).then((dataUrl) => {
          thumbnail_base64 = dataUrl.replace(/^data:image\/[a-z]+;base64,/, '')
        })

        // Show scrollbar when capture thumbnail
        if (customScrollbars.length > 0) {
          customScrollbars.forEach((scrollbar: any) => {
            scrollbar.style.overflow = 'auto'
          })
        }
      }

      let config: any = {
        nodes: nodes as NodeItem_Input[],
        edges: edges as EdgeItem[],
      }

      if (workflowType === EWorkflowType.autoProcessing) {
        let startNode = nodes.find((node) => node.type === NODE_TYPE.START)

        if (startNode && startNode?.data?.trigger) {
          const formatTrigger: any = startNode.data.trigger
          if (formatTrigger?.triggerType) {
            if (formatTrigger.triggerType === 'Microsoft Outlook') {
              formatTrigger.triggerType = 'outlook'
            } else if (formatTrigger.triggerType === 'Slack') {
              formatTrigger.triggerType = 'slack'
            }
          }

          startNode = {
            ...startNode,
            data: {
              ...startNode.data,
              trigger: startNode.data.trigger_on
                ? startNode.data.trigger
                : formatTrigger,
            },
          }
        }

        config = {
          startNode: startNode,
          endNode: nodes.find((node) => node.type === NODE_TYPE.END),
          nodes: nodes.filter(
            (node) =>
              node.type !== NODE_TYPE.START && node.type !== NODE_TYPE.END
          ) as NodeItem_Input[],
          edges: edges as EdgeItem[],
        }
      }

      const params: {
        body: Partial<WorkflowUpdate>
        path: { workflow_id: string }
      } = {
        body: {
          name,
          description,
          config,
          thumbnail_url: '',
          thumbnail_base64,
          ai_gen_prompt: aiGenerateText,
        },
        path: {
          workflow_id: id!,
        },
      }

      const res = await service(params as any)
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        Message.success({
          message: id
            ? 'Successfully updated workflow'
            : 'Successfully created workflow',
        })
        setDirtyData(false)
        setWfData(res?.data?.data)

        if (!id) {
          setId(res?.data?.data?.id ?? '')
          setTimeout(() => {
            if (workflowType === EWorkflowType.autoProcessing)
              navigate(
                `/workflows/my-auto-processing-workflows/${res?.data?.data?.id}`
              )
            else {
              navigate(`/workflows/my-workflows/${res?.data?.data?.id}`)
            }
          }, DEBOUNCE_TIME)
        } else {
          setNodes(
            nodes.map((node: any) => {
              const newNode = { ...node }

              if (node?.data) {
                newNode.data = {
                  ...node.data,
                  isNew: false,
                }
              }

              return newNode
            })
          )
        }
      } else {
        if (res.status === HTTP_STATUS_CODE.BAD_REQUEST)
          Message.error({ message: res.error!.detail ?? '' })
        else Message.error({ message: 'Something wrong, please retry!' })
      }
    } catch (error) {
      Message.error({ message: 'Something wrong, please retry!' })
    } finally {
      setLoading(false)
    }
  }

  const renderError = () => {
    if (errorCode === HTTP_STATUS_CODE.NOT_FOUND) {
      return <NotFound />
    }
  }

  const defaultWFName = wfData?.name ?? ''

  const playgroundNodes = useMemo(() => {
    if (!nodes) return []

    return wfData?.config.nodes.map((node) => {
      if (node.type === NODE_TYPE.START || node.type === NODE_TYPE.END)
        return {
          ...node,
          deletable: false,
        }

      return node
    }) as CustomNode[]
  }, [wfData])

  const workflowType = useMemo(() => {
    if (location.pathname.includes('/workflows/my-auto-processing-workflows')) {
      return EWorkflowType.autoProcessing
    }
    if (workflowId === NEW_AUTO_PROCESSING_WORKFLOW) {
      return EWorkflowType.autoProcessing
    }
    return EWorkflowType.conversation
  }, [workflowId, location])

  const aiGeneratedHandler = (model: WorkflowPubicResponse | undefined) => {
    setNodes(
      (model?.data?.config?.nodes.map((node) => {
        if (node.type === NODE_TYPE.START || node.type === NODE_TYPE.END) {
          return {
            ...node,
            deletable: false,
          }
        }

        return {
          ...node,
          data: {
            ...node.data,
            workflowType,
          },
        }
      }) as CustomNode[]) ?? []
    )
    setEdges((model?.data?.config?.edges as Edge[]) ?? [])

    setName(model?.data?.name ?? name)
    setDescription(model?.data?.description ?? description)
    setDirtyData(true)
  }

  const isActiveCitation = useMemo(() => {
    if (isNil(wfData) || isEmpty(wfData) || isEmpty(wfData.config?.nodes))
      return false

    return wfData.config?.nodes?.some(
      (node: any) => node.data?.advanced?.citation
    )
  }, [wfData])

  return (
    <ModelsProvider>
      <Layout>
        <RouteLeaving isChanged={isDirtyData} />

        {errorCode ? (
          renderError()
        ) : (
          <div className="workflow-detail flex h-full w-full flex-col gap-3 overflow-hidden">
            {isLoadingDetail ? (
              <div className="flex h-full w-full items-center justify-center">
                <Spin size="large" />
              </div>
            ) : (
              <>
                <WorkflowBreadcrumb
                  urlPath={rootUrls.Workflows}
                  placeholderName="Type in workflow name"
                  placeholderDescription="Type in description to specify what would be completed in this step"
                  onChangeName={(value) => {
                    setName(value)
                    setDirtyData(true)
                    setErrorName(false)
                  }}
                  onChangeDescription={(value) => {
                    setDescription(value)
                    setDirtyData(true)
                  }}
                  valueName={name}
                  valueDescription={description}
                  isErrorName={isErrorName}
                />

                <ReactFlowProvider>
                  <Workflow
                    nodes={nodes}
                    edges={edges}
                    setNodes={setNodes}
                    setEdges={setEdges}
                    setDirtyData={setDirtyData}
                    workflowType={workflowType}
                  />
                </ReactFlowProvider>

                <div className="flex w-full items-center justify-end">
                  <div className="flex gap-3">
                    {id && (
                      <Button
                        type="secondary"
                        onClick={() => {
                          setOpenedModalPlayground(true)
                        }}
                      >
                        Playground
                      </Button>
                    )}

                    <Button
                      onClick={handleUpdateWorkflow}
                      disabled={!isDirtyData}
                      type="primary"
                      loading={isLoading}
                    >
                      Save
                    </Button>
                  </div>
                </div>

                <div className="absolute right-[46px] mt-[80px]">
                  <ButtonGroup
                    id={id}
                    onClickIntegrate={() => setOpenModalIntegrate(true)}
                    onClickMemory={() => setOpenModalMemory(true)}
                    onClickAI={() => setOpenModalAi(true)}
                    workflowType={workflowType}
                    aiGenerateButtonActive={isOpenModalAi}
                    isActiveCitation={isActiveCitation}
                  />
                </div>

                {workflowType === EWorkflowType.conversation &&
                  isOpenModalIntegrate && (
                    <WorkflowIntegration
                      workflowId={id}
                      isOpen={isOpenModalIntegrate}
                      onClose={() => setOpenModalIntegrate(false)}
                    />
                  )}
                {workflowType === EWorkflowType.autoProcessing &&
                  isOpenModalIntegrate && (
                    <ModalIntegrateAutoProcessingWorkflow
                      handleCloseModal={() => setOpenModalIntegrate(false)}
                      id={id}
                      isOpen={isOpenModalIntegrate}
                    />
                  )}
              </>
            )}

            {isOpenedModalPlayground && (
              <ModalPlaygroundMultiAgent
                isOpen={isOpenedModalPlayground}
                workflowId={workflowId!}
                workflowName={defaultWFName}
                workflowType={workflowType}
                nodes={playgroundNodes}
                startNode={
                  (wfData?.config as ProcessingWorkflowConfig_Output)?.startNode
                }
                onClose={() => {
                  setOpenedModalPlayground(false)
                }}
              />
            )}

            {isOpenModalMemory && (
              <ModalMemory
                onClose={() => setOpenModalMemory(false)}
                wfId={id}
                strMemories={(wfData as WorkflowPublic)?.memories}
                setWfData={setWfData}
              />
            )}

            {isOpenModalAi && (
              <ModalAi
                isOpen
                workflowType={workflowType}
                onClose={() => setOpenModalAi(false)}
                onDone={aiGeneratedHandler}
                defaultText={aiGenerateText}
                updateText={(text) => {
                  setAiGenerateText(text)
                  setDirtyData(true)
                }}
              />
            )}
          </div>
        )}
      </Layout>
    </ModelsProvider>
  )
}

export default memo(WorkflowDetail)
