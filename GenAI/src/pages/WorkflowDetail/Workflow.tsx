import {
  Background,
  type Edge,
  MiniMap,
  type OnConnect,
  type On<PERSON>dges<PERSON>hang<PERSON>,
  type <PERSON><PERSON><PERSON><PERSON>hang<PERSON>,
  React<PERSON>low,
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  useReactFlow,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { isArray, isEmpty } from 'lodash'
import {
  DragEventHandler,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react'
import { v4 as uuid } from 'uuid'
import { EWorkflowType } from '../Workflows/const'
import BtnAddNode from './components/BtnAddNode'
import { ConnectionLine, CustomEdge } from './components/Edges'
import ModalConfigEndNode from './components/ModalConfigEndNode'
import ModalConfigStartNode from './components/ModalConfigStartNode'
import ModalNodeDetail from './components/ModalNodeDetail'
import { AgentNode, EndNode, StartNode } from './components/Nodes'
import EndProcessingNode from './components/Nodes/EndProcessingNode'
import StartProcessingNode from './components/Nodes/StartProcessingNode'
import { EDGE_TYPE, NODE_CHANGE, NODE_TYPE } from './constants'
import { addEndMarker } from './helpers'
import './styles.scss'
import { CustomNode } from './types'
import { HTTP_STATUS_CODE } from '@/constants'
import { toolsGetToolParametersApi } from '@/apis/client'
import { TOOL_FUNCTIONALITY } from '../Tools/const'

interface IPropsWorkflow {
  workflowType?: EWorkflowType
  nodes: CustomNode[]
  edges: Edge[]
  setNodes: React.Dispatch<React.SetStateAction<CustomNode[]>>
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>

  setDirtyData: React.Dispatch<React.SetStateAction<boolean>>
}

const Workflow = ({
  nodes,
  edges,
  setNodes,
  setEdges,
  setDirtyData,
  workflowType = EWorkflowType.conversation,
}: IPropsWorkflow) => {
  const initialState: CustomNode = useMemo(
    () => ({
      id: '',
      position: {
        x: 0,
        y: 0,
      },
      data: {
        isNew: false,
        knowledgeBases: [],
        tools: [],
        workflowType,
        allow_file_upload: false,
        trigger_on: false,
      },
      type: undefined,
    }),
    [workflowType]
  )

  const { screenToFlowPosition } = useReactFlow()

  const reactFlowWrapper = useRef(null)

  const [currentNode, setCurrentNode] = useReducer(
    (state: CustomNode, newState: Partial<CustomNode>) => ({
      ...state,
      ...newState,
    }),
    initialState
  )

  const [isOpenModalNodeDetail, setOpenModalNodeDetail] = useState(false)
  const [isModalConfigStartNode, setIsModalConfigStartNode] = useState(false)
  const [isModalConfigEndNode, setIsModalConfigEndNode] = useState(false)

  useEffect(() => {
    // turn off modal detail reset data
    if (!isOpenModalNodeDetail) {
      setCurrentNode(initialState)
    }
  }, [isOpenModalNodeDetail, workflowType])

  const nodeTypes = useMemo(
    () => ({
      [NODE_TYPE.START]:
        workflowType === EWorkflowType.autoProcessing
          ? StartProcessingNode
          : StartNode,
      [NODE_TYPE.END]:
        workflowType === EWorkflowType.autoProcessing
          ? EndProcessingNode
          : EndNode,
      [NODE_TYPE.AGENT]: AgentNode,
    }),
    [workflowType]
  )

  const edgeTypes = useMemo(
    () => ({
      [EDGE_TYPE.CUSTOM_EDGE]: CustomEdge,
    }),
    []
  )

  const onNodesChange: OnNodesChange = useCallback(
    (changes) => {
      if (
        changes?.some((change) =>
          [
            NODE_CHANGE.Position,
            NODE_CHANGE.Remove,
            NODE_CHANGE.Add,
            NODE_CHANGE.Replace,
          ].includes(change.type)
        )
      ) {
        setDirtyData(true)
      }

      setNodes((nds) => applyNodeChanges(changes, nds))
    },
    [setNodes]
  )

  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => {
      setEdges(() => applyEdgeChanges(changes, edges))
    },
    [edges, setEdges]
  )

  const onConnect: OnConnect = (connection) => {
    // Block connect start to end
    if (
      connection.sourceHandle === 'new' &&
      connection.targetHandle === 'end-handle-target'
    ) {
      return
    }

    if (connection.source !== connection.target) {
      const edge_available = edges.filter(
        (edge) =>
          edge.source === connection.source && edge.target === connection.target
      )

      if (!edge_available.length) {
        setEdges(() =>
          addEdge(
            addEndMarker({
              ...connection,
              id: connection.source.concat('_', connection.target),
              type: EDGE_TYPE.CUSTOM_EDGE,
              data: {
                label: 'If done',
              },
            }),
            edges
          )
        )

        setDirtyData(true)
      }
    }
  }

  const onDrop: DragEventHandler<HTMLDivElement> = useCallback(
    (event) => {
      event.preventDefault()

      const type = event.dataTransfer.getData('application/reactflow')

      // check if the dropped element is valid
      if (!type) {
        return
      }

      // project was renamed to screenToFlowPosition
      // and you don't need to subtract the reactFlowBounds.left/top anymore
      // details: https://reactflow.dev/whats-new/2023-11-10
      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      })

      const nodeId = uuid()

      setCurrentNode({
        id: nodeId,
        type,
        position,
        data: {
          isNew: true,
          knowledgeBases: [],
          tools: [],
          workflowType,
          allow_file_upload: false,
          trigger_on: false,
        },
      })

      setOpenModalNodeDetail(true)
    },
    [screenToFlowPosition, workflowType]
  )

  const onDragOver: DragEventHandler<HTMLDivElement> = useCallback((event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onNodeDoubleClick = (event: any, node: CustomNode) => {
    event.stopPropagation()

    // Block choose Start and End node
    if (node?.type && [NODE_TYPE.START, NODE_TYPE.END]?.includes(node.type)) {
      if (workflowType === EWorkflowType.autoProcessing) {
        if (node.type === NODE_TYPE.START) {
          setCurrentNode(node)
          setIsModalConfigStartNode(true)
        } else {
          setCurrentNode(node)
          setIsModalConfigEndNode(true)
        }
        return
      } else {
        return
      }
    }
    setCurrentNode(node)
    setOpenModalNodeDetail(true)
  }

  const onEdgeClick = (event: any) => {
    event.stopPropagation()
  }

  const handleUpdateNode = async () => {
    const isUpdateNode = !isEmpty(
      nodes.find((node) => node.id === currentNode.id)
    )

    if (isArray(currentNode?.data?.tools)) {
      // Automatically saving default value of tool if doesn't open tool configuration
      await Promise.all(
        currentNode?.data?.tools?.map(async (tool) => {
          if (
            tool.tool_functionality === TOOL_FUNCTIONALITY.GENERAL &&
            !isArray(tool.input_parameters) &&
            !isArray(tool.environment_variables)
          ) {
            const res = await toolsGetToolParametersApi({
              path: {
                tool_id: tool.id,
              },
            })
            if (res.status === HTTP_STATUS_CODE.SUCCESS) {
              const inputParameters = res.data?.data?.input_parameters
              const defaultParameters = res.data?.data?.default_parameters

              // Input Parameters
              tool.input_parameters = inputParameters?.map((param) => ({
                name: param.name,
                value:
                  defaultParameters?.find(
                    (defaultParam) =>
                      defaultParam.name === param.name &&
                      defaultParam.type === param.type
                  )?.value ?? '',
              }))

              const environmentVariables = res.data?.data?.environment_variables
              const defaultEnvs = res.data?.data?.default_envs

              // Environment Variables
              tool.environment_variables = environmentVariables?.map((env) => ({
                name: env.name,
                value:
                  (defaultEnvs?.find(
                    (defaultEnv) =>
                      defaultEnv.name === env.name &&
                      defaultEnv.is_secret === env.is_secret
                  )?.value as string) ?? '',
              }))
            }
          }

          return tool
        })
      )
    }

    if (isUpdateNode) {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === currentNode.id) {
            return currentNode
          }

          return node
        })
      )
    } else {
      setNodes((nds) => nds.concat(currentNode))
    }

    setDirtyData(true)
    setOpenModalNodeDetail(false)
  }

  return (
    <>
      <div className="relative h-full w-full" ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes.map((node) => ({
            ...node,
            key: node.id,
          }))}
          edges={edges.map((edge) => ({
            ...edge,
            key: edge.id,
          }))}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          fitViewOptions={{
            minZoom: 0.5,
            maxZoom: 1,
          }}
          connectionLineComponent={ConnectionLine}
          // for dnd
          onDrop={onDrop}
          onDragOver={onDragOver}
          //event
          onNodeDoubleClick={onNodeDoubleClick}
          onEdgeClick={onEdgeClick}
          onEdgesDelete={() => setDirtyData(true)}
          deleteKeyCode={isOpenModalNodeDetail ? '' : ['Backspace', 'Delete']}
        >
          <Background size={2} patternClassName="react-flow__custom-pattern" />
          <MiniMap nodeStrokeWidth={3} zoomable pannable />
        </ReactFlow>
        <div className="absolute left-[12px] top-4">
          <BtnAddNode workflowType={workflowType} />
        </div>
      </div>

      {isOpenModalNodeDetail && (
        <ModalNodeDetail
          isOpen={isOpenModalNodeDetail}
          currentNode={currentNode}
          setCurrentNode={setCurrentNode}
          onClose={() => {
            setOpenModalNodeDetail(false)
          }}
          handleUpdateNode={handleUpdateNode}
          workflowType={workflowType}
        />
      )}
      <ModalConfigStartNode
        isOpen={isModalConfigStartNode}
        handleCloseModal={() => setIsModalConfigStartNode(false)}
        onDone={(data, isOnFileAttach, isOnTriggerEvent, trigger) => {
          const formatTrigger: any = currentNode.data.trigger
          if (formatTrigger?.triggerType) {
            if (formatTrigger.triggerType === 'Microsoft Outlook') {
              formatTrigger.triggerType = 'outlook'
            } else if (formatTrigger.triggerType === 'Slack') {
              formatTrigger.triggerType = 'slack'
            }
          }

          const startNode = {
            ...currentNode,
            data: {
              ...currentNode.data,
              name: 'Start',
              inputParams: data,
              allow_file_upload: isOnFileAttach,
              trigger_on: isOnTriggerEvent,
              trigger: isOnTriggerEvent ? trigger : formatTrigger,
            },
          }
          setNodes((nds) => {
            const index = nds.findIndex((node) => node.type === NODE_TYPE.START)
            if (index !== -1 && nds) {
              nds[index] = startNode
            }
            return nds
          })
          setDirtyData(true)
        }}
        defaultRows={currentNode.data?.inputParams}
        allowFileUpload={currentNode.data?.allow_file_upload}
        triggerOn={currentNode.data?.trigger_on}
        defaultSettings={currentNode.data?.trigger}
      />
      {isModalConfigEndNode && (
        <ModalConfigEndNode
          isOpen={isModalConfigEndNode}
          handleCloseModal={() => setIsModalConfigEndNode(false)}
          defaultValue={currentNode.data?.outputSchema}
          onDone={(data) => {
            const endNode = {
              ...currentNode,
              data: {
                ...currentNode.data,
                name: 'End',
                outputSchema: data,
              },
            }
            setNodes((nds) => {
              const index = nds.findIndex((node) => node.type === NODE_TYPE.END)
              if (index !== -1) {
                nds[index] = endNode
              }
              console.log(nds)
              return nds
            })
            setDirtyData(true)
          }}
        />
      )}
    </>
  )
}

export default memo(Workflow)
