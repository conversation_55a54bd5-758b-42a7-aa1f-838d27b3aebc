import BaseModal from '@/components/BaseModal'
import Button from '@/components/Button'
import ComingSoon from '@/components/ComingSoon'

interface UpdateSubscriptionProps {
  isOpen: boolean
  onClose: () => void
}

const UpdateSubscription = ({ isOpen, onClose }: UpdateSubscriptionProps) => {
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <div className="flex w-[498px] flex-col items-center justify-center gap-6 rounded-3xl bg-white p-3 shadow-md">
        <ComingSoon size="sm" />
        <Button type="secondary" onClick={onClose}>
          Close
        </Button>
      </div>
    </BaseModal>
  )
}

export default UpdateSubscription
