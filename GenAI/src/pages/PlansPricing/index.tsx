import Icon from '@/assets/icon/Icon'
import Avatar from '@/components/Avatar'
import UserPopoverContent from '@/components/Header/UserPopoverContent'
import ProgressBar from '@/components/ProgressBar'
import Text from '@/components/Text'
import { formatBytes } from '@/helpers'
import { DATE_TIME_FORMAT, convertUTCToLocalTime } from '@/helpers/dateTime'
import { useMyProfile } from '@/hooks/useMyProfile'
import { rootUrls } from '@/routes/rootUrls'
import { colors } from '@/theme'
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import { useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import IconToken from './IconToken'
import UpdateSubscription from './components/UpdateSubscription'

const PlansPricing = () => {
  const [isOpen, setIsOpen] = useState(false)
  const navigate = useNavigate()
  const { myProfile, mySubscriptionPlan, fetchMySubscriptionPlan } =
    useMyProfile()

  const priceWithCurrency = useMemo(() => {
    if (
      mySubscriptionPlan?.price !== undefined &&
      mySubscriptionPlan?.currency
    ) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: mySubscriptionPlan?.currency,
        minimumFractionDigits: 0,
      }).format(mySubscriptionPlan?.price)
    }

    return `${mySubscriptionPlan?.price}${mySubscriptionPlan?.currency}`
  }, [mySubscriptionPlan?.price, mySubscriptionPlan?.currency])

  const durationTime = useMemo(() => {
    if (mySubscriptionPlan?.duration || mySubscriptionPlan?.duration === 0) {
      return `${mySubscriptionPlan.duration} ${mySubscriptionPlan.duration_unit}${mySubscriptionPlan.duration > 1 ? 's' : ''}`
    }

    return ''
  }, [mySubscriptionPlan?.duration, mySubscriptionPlan?.duration_unit])

  const startDate = useMemo(() => {
    if (mySubscriptionPlan?.start_date) {
      return convertUTCToLocalTime(
        mySubscriptionPlan.start_date,
        DATE_TIME_FORMAT.DDMMMYY,
        false
      )
    }
  }, [mySubscriptionPlan?.start_date])

  const endDate = useMemo(() => {
    if (mySubscriptionPlan?.end_date) {
      return convertUTCToLocalTime(
        mySubscriptionPlan.end_date,
        DATE_TIME_FORMAT.DDMMMYY,
        false
      )
    }
  }, [mySubscriptionPlan?.end_date])

  const remainingToken = useMemo(() => {
    if (mySubscriptionPlan?.remaining_token) {
      return mySubscriptionPlan.remaining_token > 0
        ? mySubscriptionPlan.remaining_token
        : 0
    }

    return 0
  }, [mySubscriptionPlan?.remaining_token])

  const remainingTokenPercent = useMemo(() => {
    if (remainingToken && mySubscriptionPlan?.token_quantity) {
      return (remainingToken / mySubscriptionPlan.token_quantity) * 100
    }

    return 0
  }, [remainingToken, mySubscriptionPlan?.token_quantity])

  const tokenQuantity = useMemo(() => {
    if (
      mySubscriptionPlan?.token_quantity ||
      mySubscriptionPlan?.token_quantity === 0
    ) {
      return mySubscriptionPlan.token_quantity.toLocaleString('en-US')
    }

    return ''
  }, [mySubscriptionPlan?.token_quantity])

  const remainingStorage = useMemo(() => {
    if (
      mySubscriptionPlan?.remaining_storage &&
      mySubscriptionPlan?.remaining_storage > 0
    ) {
      return Number(
        formatBytes({
          bytes:
            mySubscriptionPlan.remaining_storage > 0
              ? mySubscriptionPlan.remaining_storage
              : 0,
          unit: mySubscriptionPlan?.storage_unit,
          convertWithSize: false,
        })
      )
    }

    return 0
  }, [mySubscriptionPlan?.remaining_storage, mySubscriptionPlan?.storage_unit])

  const remainingStoragePercent = useMemo(() => {
    if (remainingStorage && mySubscriptionPlan?.storage_quantity) {
      return (remainingStorage / mySubscriptionPlan.storage_quantity) * 100
    }

    return 0
  }, [remainingStorage, mySubscriptionPlan?.storage_quantity])

  const storageQuantity = useMemo(() => {
    if (
      mySubscriptionPlan?.storage_quantity ||
      mySubscriptionPlan?.storage_quantity === 0
    ) {
      return `${mySubscriptionPlan.storage_quantity}${mySubscriptionPlan.storage_unit}`
    }
    return ''
  }, [mySubscriptionPlan?.storage_quantity, mySubscriptionPlan?.storage_unit])

  useEffect(() => {
    fetchMySubscriptionPlan()
  }, [])

  return (
    <>
      <UpdateSubscription isOpen={isOpen} onClose={() => setIsOpen(false)} />
      <div className="flex h-full flex-col items-center gap-[20px] bg-Base-01 px-5 py-3">
        <div className="flex w-full items-center justify-between pl-[12px]">
          <img
            onClick={() => navigate(rootUrls.Home, { replace: true })}
            className="cursor-pointer"
            src="/assets/images/genai-logo.svg"
            width={35.8}
          />

          <Popover className="group">
            {({ open }) => (
              <>
                <PopoverButton className="flex items-center gap-2 focus-visible:outline-none">
                  <Avatar
                    className={
                      open
                        ? 'border-[2px] border-Base-Neutral'
                        : 'border-[2px] border-Base-01'
                    }
                    avatarUrl={myProfile?.avatar || ''}
                    name={myProfile?.first_name ?? myProfile?.username ?? ''}
                  />
                </PopoverButton>
                {open && (
                  <PopoverPanel
                    anchor="bottom end"
                    className="z-20 mt-[5px] flex flex-col !overflow-hidden rounded-[8px] border-[0.5px] border-border-base-icon bg-white shadow-md"
                  >
                    <UserPopoverContent myProfile={myProfile} />
                  </PopoverPanel>
                )}
              </>
            )}
          </Popover>
        </div>

        <div className="flex h-full w-[776px] flex-col items-center gap-8 px-8">
          <Text type="title" variant="semibold" className="text-Primary-Color">
            Plans & Pricing
          </Text>
          <div className="flex w-[712px] flex-col gap-2 rounded-2xl bg-white px-2 pb-[30px] pt-2 shadow">
            <div className="flex flex-col items-center gap-[30px] px-3 py-2">
              <div className="flex w-full justify-between">
                <div className="flex min-h-[54px] flex-col">
                  <div className="flex items-center gap-3">
                    <Text
                      type="title"
                      variant="semibold"
                      className="text-linear-gradient-main-color"
                    >
                      {mySubscriptionPlan?.package_name}
                    </Text>
                    {mySubscriptionPlan?.price !== undefined && (
                      <div className="flex items-center justify-center rounded-full bg-Main-Disable-2 px-3 py-[2px]">
                        <Text
                          type="subBody"
                          variant="semibold"
                          className="text-linear-gradient-main6-color"
                        >
                          {priceWithCurrency}
                        </Text>
                      </div>
                    )}
                  </div>
                  {durationTime && (
                    <Text
                      type="subBody"
                      variant="medium"
                      className="text-Primary-Color"
                    >
                      {durationTime}
                    </Text>
                  )}
                </div>
                <Text
                  type="subBody"
                  variant="semibold"
                  elementType="div"
                  className="text-linear-gradient-main-color hover:text-linear-gradient-main2-color h-fit cursor-pointer duration-300"
                  onClick={() => setIsOpen(true)}
                >
                  Upgrade
                </Text>
              </div>

              <div className="flex w-full justify-between px-4">
                <div className="items-between flex flex-col gap-[4px]">
                  <Text
                    type="supportText"
                    className="text-center text-Secondary-Color"
                  >
                    Start date
                  </Text>
                  <Text
                    type="subBody"
                    variant="medium"
                    className="text-linear-gradient-main2-color text-center"
                  >
                    {startDate}
                  </Text>
                </div>

                <div className="items-between flex flex-col gap-[4px]">
                  <Text
                    type="supportText"
                    className="text-center text-Secondary-Color"
                  >
                    End date
                  </Text>
                  <Text
                    type="subBody"
                    variant="medium"
                    className="text-linear-gradient-main2-color text-center"
                  >
                    {endDate}
                  </Text>
                </div>

                <div className="items-between flex flex-col items-center gap-[4px]">
                  <Text
                    type="supportText"
                    className="text-center text-Secondary-Color"
                  >
                    Storage capacity
                  </Text>
                  <div className="flex items-start gap-1">
                    <Icon
                      name="Custom-storage"
                      size={16}
                      gradient={['#4D175B', '#A32952']}
                    />
                    <Text
                      type="subBody"
                      variant="medium"
                      className="text-linear-gradient-main2-color text-center"
                    >
                      {storageQuantity}
                    </Text>
                  </div>
                </div>

                <div className="items-between flex flex-col gap-[4px]">
                  <Text
                    type="supportText"
                    className="text-center text-Secondary-Color"
                  >
                    Token
                  </Text>
                  <div className="flex items-start gap-1">
                    <IconToken />
                    <div className="flex items-center">
                      <Text
                        type="subBody"
                        variant="medium"
                        className="text-linear-gradient-main2-color text-center"
                      >
                        {tokenQuantity}
                      </Text>
                      <Text
                        type="helperText"
                        variant="medium"
                        className="text-Secondary-Color"
                      >
                        /{mySubscriptionPlan?.token_unit}
                      </Text>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="h-[1px] bg-neutral-100" />

            <div className="flex flex-col items-center gap-3 px-3 py-2">
              <Text
                type="subBody"
                variant="semibold"
                className="text-Primary-Color"
              >
                Plan usage
              </Text>
              <div className="flex gap-[80px] px-3">
                <div className="flex w-52 flex-col gap-2">
                  <Text type="supportText" className="text-Primary-Color">
                    Remaining token
                  </Text>
                  <div className="flex flex-col gap-[4px]">
                    <ProgressBar percents={remainingTokenPercent} />
                    <div className="flex items-center justify-end gap-1">
                      <IconToken size={12} color={colors['Secondary-Color']} />
                      <Text type="supportText" className="text-Secondary-Color">
                        {remainingToken?.toLocaleString('en-US')}
                      </Text>
                    </div>
                  </div>
                </div>

                <div className="flex w-52 flex-col gap-2">
                  <Text type="supportText" className="text-Primary-Color">
                    Remaining storage
                  </Text>
                  <div className="flex flex-col gap-[4px]">
                    <ProgressBar percents={remainingStoragePercent} />
                    <div className="flex items-center justify-end gap-1">
                      <Icon
                        name="Custom-storage"
                        size={12}
                        color={colors['Secondary-Color']}
                      />
                      <Text type="supportText" className="text-Secondary-Color">
                        {`${remainingStorage}${mySubscriptionPlan?.storage_unit}`}
                      </Text>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex w-full px-3">
          <div
            onClick={() => navigate(-1)}
            className="flex cursor-pointer items-center gap-[4px] rounded pr-[8px] hover:bg-Hover-2"
          >
            <Icon name="icon-fill-caret-small-left" size={24} color="#2D0136" />
            <Text
              type="subBody"
              variant="medium"
              className="text-Primary-Color"
            >
              Back
            </Text>
          </div>
        </div>
      </div>
    </>
  )
}

export default PlansPricing
