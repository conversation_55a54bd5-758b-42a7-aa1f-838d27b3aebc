import { GOOGLE_CLIENT_ID } from '@/constants'
import AIFoundation from '@/pages/AIFoundation'
import ChatEmbedIframe from '@/pages/ChatEmbedIframe'
import ChatEmbedIframeWithLogin from '@/pages/ChatEmbedIframeWithLogin'
import { CiteMindChat, CiteMindLanding } from '@/pages/CiteMind'
import { ViewDocumentProvider } from '@/pages/CiteMind/contexts/ViewDocumentContext'
import ErrorPage from '@/pages/error'
import ExtensionUtilities from '@/pages/ExtensionUtilities'
import KnowlegedBase from '@/pages/knowledgeBase'
import KnowledgeBaseDirectory from '@/pages/knowledgeBaseDirectory'
import KnowledgeBaseExternalDetail from '@/pages/KnowledgeBaseExternalDetail'
import Login from '@/pages/login'
import PlansPricing from '@/pages/PlansPricing'
import Profile from '@/pages/profile'
import Register from '@/pages/register'
import ResetPassword from '@/pages/ResetPassword'
import ToolCategory from '@/pages/toolCategory'
import ToolMarketplacePage from '@/pages/toolMarketplace'
import ToolCollection from '@/pages/Tools/ToolCollection'
import { Workers } from '@/pages/Workers'
import { WorkflowDetail, WorkflowDetailProvider } from '@/pages/WorkflowDetail'
import { Workflows } from '@/pages/Workflows'
import WorkflowsTemplate from '@/pages/WorkflowsTemplate'
import { GoogleOAuthProvider } from '@react-oauth/google'
import {
  RouteObject,
  RouterProvider,
  createBrowserRouter,
} from 'react-router-dom'
import { LoggedInRoute } from './LoggedInRoute'
import { ProtectedRoute } from './ProtectedRoute'
import { rootUrls } from './rootUrls'
import HomeChat from '@/components/HomeChat'
import { Dashboard } from '@/pages/Dashboard'

const routesForPublic = [
  {
    path: rootUrls.ChatEmbedIframe,
    element: <ChatEmbedIframe />,
  },
  {
    path: rootUrls.ChatEmbedIframeWithLogin,
    element: <ChatEmbedIframeWithLogin />,
  },
  {
    path: rootUrls.CiteMindLanding,
    element: <CiteMindLanding />,
  },
  {
    path: rootUrls.CiteMindChat,
    element: (
      <ViewDocumentProvider>
        <CiteMindChat />
      </ViewDocumentProvider>
    ),
  },
]

const routesForAuthenticatedOnly: RouteObject[] = [
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        path: rootUrls.Home,
        element: <HomeChat />,
        errorElement: <ErrorPage />,
      },
      {
        path: rootUrls.Dashboard,
        element: <Dashboard />,
        errorElement: <ErrorPage />,
      },
      {
        path: rootUrls.Workers,
        element: <Workers />,
      },
      {
        path: rootUrls.ToolCategory,
        element: <ToolCategory />,
      },
      {
        path: rootUrls.ToolMarketplace,
        element: <ToolMarketplacePage />,
      },
      {
        path: rootUrls.ToolCollection,
        element: <ToolCollection />,
      },
      {
        path: rootUrls.AIFoundation,
        element: <AIFoundation />,
      },
      {
        path: rootUrls.Profile,
        element: <Profile />,
      },
      {
        path: rootUrls.PlansPricing,
        element: <PlansPricing />,
      },
      {
        path: rootUrls.KnowledgeBaseDirectory,
        element: <KnowledgeBaseDirectory />,
      },
      {
        path: rootUrls.KnowledgeBase,
        element: <KnowlegedBase />,
      },
      {
        path: rootUrls.ExternalKnowledgeBase,
        element: <KnowledgeBaseExternalDetail />,
      },
      {
        path: rootUrls.ExternalKnowledgeBaseDetail,
        element: <KnowledgeBaseExternalDetail />,
      },
      {
        path: rootUrls.WorkflowMarketplace,
        element: <WorkflowsTemplate />,
      },
      {
        path: rootUrls.WorkflowDetail,
        element: (
          <WorkflowDetailProvider>
            <WorkflowDetail />
          </WorkflowDetailProvider>
        ),
      },
      {
        path: rootUrls.WorkflowProcessingDetail,
        element: (
          <WorkflowDetailProvider>
            <WorkflowDetail />
          </WorkflowDetailProvider>
        ),
      },
      {
        path: rootUrls.Workflows,
        element: <Workflows />,
      },
      {
        path: rootUrls.ExtensionUtilities,
        element: <ExtensionUtilities />,
      },
    ],

    // errorElement: <Navigate to={rootUrls.Home} replace />,
  },
]

const routesForNotAuthenticatedOnly = [
  {
    path: rootUrls.Login,
    element: <LoggedInRoute />,
    children: [
      {
        path: '',

        element: (
          <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
            <Login />
          </GoogleOAuthProvider>
        ),
      },
    ],
  },
  {
    path: rootUrls.Register,
    element: <Register />,
  },
  {
    path: rootUrls.ResetPassword,
    element: <ResetPassword />,
  },
]

export default function Routes() {
  const router = createBrowserRouter([
    ...routesForPublic,
    ...routesForNotAuthenticatedOnly,
    ...routesForAuthenticatedOnly,
  ])

  return <RouterProvider router={router} />
}
