export const rootUrls = {
  Login: '/login',
  Home: '/',
  Dashboard: '/dashboard',
  Register: '/register',
  SystemDesign: '/system-design',
  TestButton: '/test/button',
  TestInput: '/test/input',
  TestDropdown: '/test/dropdown',
  TestPopover: '/test/popover',
  TestIcon: '/test/icon',
  TestText: '/test/typography',
  TestListIcon: '/test/list-icon',
  TestTooltip: '/test/tooltip',
  TestMessage: '/test/message',
  ToolCategory: '/tools/tool-category',
  ToolMarketplace: '/marketplace/tools',
  ToolCollection: '/tools/tool-collection',
  TestModalPlayground: '/test/modal-playground',
  TestTabUpload: '/test/tab-upload',
  Workers: '/workers',
  AIFoundation: '/ai-foundation',
  Profile: '/profile',
  PlansPricing: '/plans-pricing',
  KnowledgeBase: '/knowledge-base',
  KnowledgeBaseDirectory: '/knowledge-base/:id',
  ExternalKnowledgeBase: '/external-knowledge-base',
  ExternalKnowledgeBaseDetail: '/external-knowledge-base/:id',
  Workflows: '/workflows/my-workflows',
  WorkflowDetail: '/workflows/my-workflows/:workflowId',
  WorkflowProcessingDetail:
    '/workflows/my-auto-processing-workflows/:workflowId',
  WorkflowMarketplace: '/marketplace/workflows',
  ChatEmbedIframe: '/application/:appID/widget/:widgetID',
  ChatEmbedIframeWithLogin: '/application-widget/:appID/widget/:widgetID',
  ResetPassword: '/reset-password',

  //Cite mind
  CiteMindLanding: '/utilities/citemind',
  CiteMindChat: '/utilities/citemind/:workflowId',

  ExtensionUtilities: '/applications',
}

export const supperAdminUrls: string[] = Object.values(rootUrls)

export const userUrls: string[] = [
  rootUrls.ToolMarketplace,
  rootUrls.ToolCollection,
  rootUrls.AIFoundation,
  rootUrls.Workers,
  rootUrls.KnowledgeBaseDirectory,
  rootUrls.Workflows,
  rootUrls.WorkflowDetail,
  rootUrls.WorkflowProcessingDetail,
  rootUrls.KnowledgeBase,
  rootUrls.WorkflowMarketplace,
  rootUrls.ExternalKnowledgeBase,
  rootUrls.ExternalKnowledgeBaseDetail,
  rootUrls.Home,
  rootUrls.ExtensionUtilities,
  rootUrls.Dashboard,
]
