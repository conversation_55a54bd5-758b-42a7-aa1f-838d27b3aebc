import { toolGroupGetToolGroups } from '@/apis/client'
import Message from '@/components/Message'
import { convertDataFromToolGroupPublicToModifyToolGroupPublic } from '@/components/ModalAddToolkit/helpers'
import { ModifyToolGroupPublic } from '@/components/ModalAddToolkit/types'
import { HTTP_STATUS_CODE } from '@/constants'
import { useMyProfile } from '@/hooks/useMyProfile'
import { createContext, useEffect, useState } from 'react'

export type Context = {
  groupTool?: ModifyToolGroupPublic[]
  isFetchingGroupTool: boolean
}

export const GroupToolContext = createContext<Context>({} as Context)

const GroupToolProvider = ({ children }: any) => {
  const [groupTool, setGroupTool] = useState<ModifyToolGroupPublic[]>()
  const [isLoading, setIsLoading] = useState(false)

  const { myProfile } = useMyProfile()

  useEffect(() => {
    if (myProfile?.id) {
      const fetchGroupTool = async () => {
        try {
          if (isLoading) return

          setIsLoading(true)

          const response = await toolGroupGetToolGroups()

          if (response?.status === HTTP_STATUS_CODE.SUCCESS) {
            setGroupTool(
              response?.data?.data?.map((item) => {
                return convertDataFromToolGroupPublicToModifyToolGroupPublic(
                  item,
                  myProfile?.id
                )
              })
            )
          }
        } catch (error) {
          Message.error({ message: 'Something went wrong' })
        } finally {
          setIsLoading(false)
        }
      }

      fetchGroupTool()
    }
  }, [myProfile?.id])

  return (
    <GroupToolContext.Provider
      value={{
        groupTool,
        isFetchingGroupTool: isLoading,
      }}
    >
      {children}
    </GroupToolContext.Provider>
  )
}

export default GroupToolProvider
