import { createContext, useEffect, useMemo, useState } from 'react'
import { loginLoginAccessToken } from '@/apis/client'
import { getAccessTokenLocalStorage } from '@/helpers'
import { MessageDialog } from '@/components/DialogMessage'
import ExternalKbWsService from '@/pages/knowledgeBase/service/ExternalKbWsService'
import { ID_EXTENSION } from '@/constants'

export type Context = {
  accessToken: string | null
  setAccessToken: (newToken: string) => void
  refreshAccessToken: () => Promise<any | undefined>
  login: ({ email, password }: any) => Promise<any | undefined>
  loginGoogleSSO: ({ google_code }: any) => Promise<any | undefined>
  logout: () => void
}

export const AuthContext = createContext<Context>({} as Context)

const AuthProvider = ({ children }: any) => {
  // State to hold the authentication token
  const [accessToken, setAccessToken_] = useState(getAccessTokenLocalStorage())

  // Function to set the authentication token
  const setAccessToken = (newToken: string) => {
    setAccessToken_(newToken)
  }

  // Function to login
  const login = async ({ email, password }: any) => {
    try {
      const response: any = await loginLoginAccessToken({
        body: {
          grant_type: 'password',
          username: email,
          password,
        },
      })

      if (response?.data?.access_token) {
        if (response.data?.access_token) {
          setAccessToken(response.data.access_token)
        }
        if (response.data?.refresh_token) {
          localStorage.setItem('refresh_token', response.data.refresh_token)
        }
      }

      return response
    } catch (error) {
      console.error('error', error)
    }

    return undefined
  }

  // Function to login by google sso
  const loginGoogleSSO = async ({ google_code }: any) => {
    try {
      const response: any = await loginLoginAccessToken({
        body: {
          grant_type: 'google_third_party',
          google_code: google_code,
        },
      })

      if (response?.data?.access_token) {
        if (response.data?.access_token) {
          setAccessToken(response.data.access_token)
        }
        if (response.data?.refresh_token) {
          localStorage.setItem('refresh_token', response.data.refresh_token)
        }
      }

      return response
    } catch (error) {
      console.error('error', error)
    }

    return undefined
  }

  // Function to logout
  const logout = () => {
    // Close the websocket connection because I not close it in the useWSExternalDataSource.ts
    ExternalKbWsService.getInstance().close()

    // Gửi message đến extension
    if (typeof chrome !== 'undefined' && chrome && chrome.runtime) {
      chrome.runtime.sendMessage(ID_EXTENSION, { action: 'logout' })
    }
    setAccessToken('')
    localStorage.removeItem('refresh_token')
  }

  // Function to refresh the authentication token
  const refreshAccessToken = async () => {
    const refreshToken = localStorage.getItem('refresh_token')

    if (refreshToken) {
      try {
        const response: any = await loginLoginAccessToken({
          body: {
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
          },
        })

        if (response?.data?.access_token) {
          if (response?.data?.access_token) {
            setAccessToken(response?.data.access_token)
          }
          if (response?.data?.refresh_token) {
            localStorage.setItem('refresh_token', response?.data.refresh_token)
          }
        } else {
          MessageDialog.require({
            mainMessage: 'Your session has expired!',
            subMessage: 'Please log in again to continue accessing the system',
            duration: 5,
            onClick: () => {
              logout()
            },
          })
        }

        return response
      } catch (error) {
        console.error('error', error)
        logout()
      }
    } else {
      setAccessToken('')
    }
  }

  useEffect(() => {
    if (accessToken) {
      localStorage.setItem('access_token', accessToken)
    } else {
      localStorage.removeItem('access_token')
    }
  }, [accessToken])

  // Memoized value of the authentication context
  const contextValue: Context = useMemo(
    () => ({
      accessToken,
      setAccessToken,
      refreshAccessToken,
      login,
      loginGoogleSSO,
      logout,
    }),
    [accessToken]
  )

  // Provide the authentication context to the children components
  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  )
}

export default AuthProvider
