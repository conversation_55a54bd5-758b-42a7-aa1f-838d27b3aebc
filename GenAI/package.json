{"name": "genai", "private": true, "version": "0.0.0", "type": "module", "license": "GenAI", "scripts": {"dev": "vite", "prod": "npm run openapi-ts:prod && vite", "build": "yarn run openapi-ts:prod && yarn lint --fix && tsc --noEmit && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "openapi-ts:dev": "openapi-ts -f openapi-ts.config.dev.ts", "openapi-ts:prod": "openapi-ts -f openapi-ts.config.prod.ts", "prepare": "cd .. && husky GenAI/.husky", "postinstall": "cd .. && husky GenAI/.husky", "commitlint": "commitlint --edit"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ckeditor/ckeditor5-react": "^9.0.0", "@headlessui/react": "^2.1.2", "@hey-api/client-axios": "^0.2.3", "@hey-api/client-fetch": "^0.2.1", "@monaco-editor/react": "^4.6.0", "@react-oauth/google": "^0.12.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/full-screen": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "@react-pdf-viewer/search": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "@react-pdf-viewer/zoom": "^3.12.0", "@types/react-modal": "^3.16.3", "@xyflow/react": "^12.2.1", "antd": "5.23.2", "axios": "^1.7.4", "axios-cancel": "^0.2.2", "ckeditor5": "^43.0.0", "clsx": "^2.1.1", "highlight.js": "^11.10.0", "html-to-image": "^1.11.11", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "link-preview-js": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "nanoid": "^5.0.7", "pdfjs-dist": "3.4.120", "rc-tooltip": "6.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-modal": "^3.16.1", "react-outside-click-handler": "^1.3.0", "react-router-dom": "^6.26.0", "react-svg": "^16.1.34", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "simplebar-react": "^3.2.6", "styled-components": "^6.1.12", "tailwind-merge": "^2.4.0", "use-debounce": "^10.0.3", "uuid": "^10.0.0", "vite-plugin-svgr": "^4.2.0", "xss": "^1.0.15", "zustand": "^4.5.5"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@hey-api/openapi-ts": "^0.52.8", "@types/axios-cancel": "^0.2.5", "@types/chrome": "^0.0.279", "@types/lodash": "^4.17.7", "@types/node": "^22.1.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-outside-click-handler": "^1.3.4", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-unused-imports": "^4.1.3", "husky": "^9.1.7", "postcss": "^8.4.41", "postcss-import": "^16.1.0", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "sass": "^1.77.8", "tailwindcss": "^3.4.7", "typescript": "^5.2.2", "vite": "^5.3.4", "vitest": "^3.1.1"}}